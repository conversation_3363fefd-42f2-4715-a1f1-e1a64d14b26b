# 养护运维模块静态预制数据说明

## 概述

本文档说明了为长沙市智慧桥隧管理平台养护运维模块创建的静态预制数据，这些数据用于前端开发和演示，方便开发人员在后端接口未实现时进行前端开发和测试。

⚠️ **重要提醒**：这些都是临时的静态数据，当后端接口实现后，需要删除相关文件并替换为真实API调用。

## 文件结构

```
src/
├── data/
│   └── mock/
│       └── maintenance.js          # 主要的静态数据文件
├── api/
│   └── mock/
│       └── maintenance.js          # API模拟服务
├── utils/
│   └── mock-helper.js              # 模拟数据辅助工具
├── components/
│   └── Maintenance/
│       └── ProjectList.vue         # 使用模拟数据的组件示例
└── MOCK_DATA_README.md             # 本说明文档
```

## 静态数据文件详情

### 1. 主数据文件 (`src/data/mock/maintenance.js`)

包含以下预制数据：

#### 基础枚举数据
- **PROJECT_TYPES**: 项目类型（月度养护、保洁项目、应急养护、预防养护）
- **PROJECT_STATUS**: 项目状态（草稿、审批中、审批通过、审批拒绝）
- **TASK_STATUS**: 任务状态（未完成、审核中、退回、复核中、已完成）
- **EMERGENCY_STATUS**: 应急维修状态（待处理、处理审批中、退回、已处理）

#### 组织机构和人员数据
- **MANAGEMENT_UNITS**: 管理单位（3个单位）
- **MAINTENANCE_UNITS**: 养护单位（4个单位）
- **PROJECT_MANAGERS**: 项目负责人（10个人员）
- **MAINTENANCE_STAFF**: 养护人员（6个人员）

#### 基础设施数据
- **BRIDGES_DATA**: 桥梁基础数据（5座桥梁）
- **TUNNELS_DATA**: 隧道基础数据（2条隧道）

#### 病害相关数据
- **DISEASE_TYPES**: 病害类型（6种类型）
- **DISEASE_LOCATIONS**: 病害部位（7个部位）
- **DISEASES_DATA**: 病害数据（3条病害记录）

#### 项目相关数据
- **MAINTENANCE_PROJECTS**: 养护项目列表（4个项目）
- **PROJECT_ITEMS**: 项目配置数据
- **PROJECT_BRIDGES**: 项目关联桥梁数据
- **PROJECT_DISEASES**: 项目关联病害数据

#### 维修相关数据
- **MAINTENANCE_TASKS**: 养护维修任务（3个任务）
- **EMERGENCY_REPAIRS**: 应急维修数据（3条记录）

#### 审批相关数据
- **APPROVAL_HISTORY**: 审批历史记录（3条记录）
- **EXTENSION_APPLICATIONS**: 延期申请数据（1条申请）

#### 模板和统计数据
- **MAINTENANCE_TEMPLATES**: 常用养护项目模板（9个模板）
- **PROJECT_STATISTICS**: 项目统计数据
- **TASK_COMPLETION_STATS**: 任务完成统计

### 2. API模拟服务 (`src/api/mock/maintenance.js`)

提供完整的API模拟函数，包括：

#### 项目管理API
- `getMaintenanceProjectList()` - 获取养护项目列表
- `getMaintenanceProjectDetail()` - 获取项目详情
- `createMaintenanceProject()` - 创建养护项目
- `updateMaintenanceProject()` - 更新养护项目
- `deleteMaintenanceProject()` - 删除养护项目
- `submitProjectApproval()` - 提交项目审批

#### 基础数据API
- `getBridgesList()` - 获取桥梁列表
- `getTunnelsList()` - 获取隧道列表
- `getDiseasesList()` - 获取病害列表
- `getManagementUnitsList()` - 获取管理单位列表
- `getMaintenanceUnitsList()` - 获取养护单位列表
- `getProjectManagersList()` - 获取项目负责人列表
- `getMaintenanceStaffList()` - 获取养护人员列表

#### 养护维修API
- `getMaintenanceTasksList()` - 获取养护维修任务列表
- `getEmergencyRepairsList()` - 获取应急维修列表

#### 审批相关API
- `getApprovalHistory()` - 获取审批历史
- `approveProject()` - 项目审批

#### 延期申请API
- `getExtensionApplicationsList()` - 获取延期申请列表
- `submitExtensionApplication()` - 提交延期申请
- `approveExtensionApplication()` - 审批延期申请

#### 统计和字典API
- `getProjectStatistics()` - 获取项目统计数据
- `getMaintenanceTemplates()` - 获取常用模板
- `getProjectTypes()` - 获取项目类型字典
- `getProjectStatus()` - 获取项目状态字典
- `getTaskStatus()` - 获取任务状态字典
- `getEmergencyStatus()` - 获取应急维修状态字典

### 3. 辅助工具 (`src/utils/mock-helper.js`)

提供便利的开发工具：

#### 核心功能
- **USE_MOCK_DATA**: 模拟数据开关（开发环境自动启用）
- **apiCall()**: API调用包装器，自动切换模拟/真实API
- **mockResponse**: 统一的响应格式化工具
- **mockPermissionCheck**: 模拟权限检查
- **mockFileUpload**: 模拟文件上传
- **mockDataGenerator**: 模拟数据生成器
- **mockDebugTools**: 开发环境调试工具

## 使用方法

### 1. 在组件中使用模拟数据

```javascript
// 导入API模拟服务和辅助工具
import maintenanceApi from '@/api/mock/maintenance'
import { apiCall } from '@/utils/mock-helper'

export default {
  methods: {
    async getProjectList() {
      try {
        // 使用apiCall包装器，开发环境自动使用模拟数据
        const response = await apiCall(
          maintenanceApi.getMaintenanceProjectList, // 模拟API
          null, // 真实API（暂未实现）
          this.queryParams // API参数
        )
        
        if (response.code === 200) {
          this.projectList = response.rows
          this.total = response.total
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
      }
    }
  }
}
```

### 2. 权限检查

```javascript
import { mockPermissionCheck } from '@/utils/mock-helper'

// 检查权限
if (mockPermissionCheck.hasPermission('maintenance:project:add')) {
  // 显示新增按钮
}

// 检查角色
if (mockPermissionCheck.hasRole(['admin', 'company_admin'])) {
  // 显示管理员功能
}

// 开发环境切换用户角色进行测试
mockPermissionCheck.switchRole('bridge_supervisor')
```

### 3. 文件上传模拟

```javascript
import { mockFileUpload } from '@/utils/mock-helper'

// 模拟文件上传
const result = await mockFileUpload.uploadFile(file)
if (result.code === 200) {
  console.log('文件上传成功:', result.data)
}
```

### 4. 调试工具

```javascript
import { mockDebugTools } from '@/utils/mock-helper'

// 显示当前模拟数据状态
mockDebugTools.showStatus()

// 导出当前模拟数据用于调试
mockDebugTools.exportData()

// 清除模拟数据缓存
mockDebugTools.clearCache()
```

## 数据特点和设计原则

### 1. 真实性
- 所有数据都基于《长沙市智慧桥隧管理平台-养护运维前端设计文档.md》创建
- 人员姓名、桥梁名称、地址等都使用了合理的中文名称
- 手机号、日期时间等格式符合实际使用习惯

### 2. 完整性
- 涵盖了设计文档中提到的所有数据类型
- 包含了完整的业务流程数据（从项目创建到审批完成）
- 提供了足够的数据量用于分页、筛选等功能测试

### 3. 关联性
- 数据之间保持正确的关联关系
- 项目与桥梁、病害、人员等数据正确关联
- 审批流程数据与项目状态保持一致

### 4. 可扩展性
- 提供了数据生成器工具，可以快速生成更多测试数据
- 支持动态修改数据，模拟真实的数据变化

## 替换为真实API的步骤

当后端接口实现后，按以下步骤替换：

### 1. 创建真实API文件
```javascript
// src/api/maintenance.js
import request from '@/utils/request'

export function getMaintenanceProjectList(params) {
  return request({
    url: '/maintenance/projects/list',
    method: 'get',
    params
  })
}

// ... 其他API函数
```

### 2. 更新组件中的导入
```javascript
// 替换前
import maintenanceApi from '@/api/mock/maintenance'

// 替换后  
import maintenanceApi from '@/api/maintenance'
```

### 3. 更新apiCall调用
```javascript
// 替换前
const response = await apiCall(
  maintenanceApi.getMaintenanceProjectList, // 模拟API
  null, // 真实API
  this.queryParams
)

// 替换后
const response = await apiCall(
  null, // 模拟API
  maintenanceApi.getMaintenanceProjectList, // 真实API
  this.queryParams
)
```

### 4. 删除模拟数据文件
- 删除 `src/data/mock/maintenance.js`
- 删除 `src/api/mock/maintenance.js`
- 删除 `src/utils/mock-helper.js`
- 删除 `MOCK_DATA_README.md`

### 5. 更新环境配置
```javascript
// src/utils/mock-helper.js 中的开关
export const USE_MOCK_DATA = false // 设置为false
```

## 注意事项

### 1. 开发环境配置
- 模拟数据默认在开发环境(`NODE_ENV === 'development'`)启用
- 生产环境会自动禁用模拟数据
- 可以通过`USE_MOCK_DATA`开关手动控制

### 2. 数据持久化
- 模拟数据仅在内存中存储，页面刷新后会重置
- 如需持久化，可以结合localStorage或sessionStorage

### 3. 性能考虑
- 模拟API包含延迟(`mockApiDelay`)，模拟真实网络请求
- 可以调整延迟时间以适应开发需求

### 4. 权限测试
- 提供了4种用户角色的权限模拟
- 可以通过`mockPermissionCheck.switchRole()`切换角色进行测试

### 5. 文件上传
- 模拟文件上传使用`URL.createObjectURL`创建临时预览链接
- 仅用于前端展示，不会真正上传到服务器

## 常见问题

### Q: 如何添加更多测试数据？
A: 可以直接修改`src/data/mock/maintenance.js`中的数组，或使用`mockDataGenerator`工具生成。

### Q: 如何模拟API错误响应？
A: 在模拟API函数中返回错误的响应码和消息，例如：
```javascript
return {
  code: 500,
  msg: '服务器内部错误',
  data: null
}
```

### Q: 如何在生产环境禁用模拟数据？
A: 模拟数据会根据`NODE_ENV`自动禁用，也可以手动设置`USE_MOCK_DATA = false`。

### Q: 模拟数据支持分页吗？
A: 支持，使用`mockPagination`工具函数处理分页逻辑。

### Q: 如何模拟不同用户角色的权限？
A: 使用`mockPermissionCheck.switchRole()`切换角色，然后重新测试相关功能。

## 联系方式

如有问题或建议，请联系前端开发团队。

---

**最后更新时间**: 2025年9月22日  
**文档版本**: 1.0.0
