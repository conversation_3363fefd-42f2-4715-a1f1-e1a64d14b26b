{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnZhciBfcmVnZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3IuanMiKSk7CnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1dvcmsvcWlhby9CQi9CcmlkZ2VGcm9udGVuZC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwp2YXIgX2F1ZGl0ID0gcmVxdWlyZSgiQC9hcGkvbWFpbnRlbmFuY2UvYXVkaXQiKTsKdmFyIF9TdGF0dXNUYWcgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvY29tcG9uZW50cy9NYWludGVuYW5jZS9TdGF0dXNUYWciKSk7CnZhciBfQmFzaWNJbmZvVmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vLi4vcmVwYWlycy9kZXRhaWwvY29tcG9uZW50cy9CYXNpY0luZm9WaWV3IikpOwp2YXIgX1Rhc2tzSW5mb1ZpZXcgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uLy4uL3JlcGFpcnMvZGV0YWlsL2NvbXBvbmVudHMvVGFza3NJbmZvVmlldyIpKTsKdmFyIF9EaXNlYXNlc0luZm9WaWV3ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuLi8uLi9yZXBhaXJzL2RldGFpbC9jb21wb25lbnRzL0Rpc2Vhc2VzSW5mb1ZpZXciKSk7CnZhciBfQXVkaXRIaXN0b3J5VmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnRzL0F1ZGl0SGlzdG9yeVZpZXciKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnTWFpbnRlbmFuY2VBdWRpdERldGFpbCcsCiAgY29tcG9uZW50czogewogICAgU3RhdHVzVGFnOiBfU3RhdHVzVGFnLmRlZmF1bHQsCiAgICBCYXNpY0luZm9WaWV3OiBfQmFzaWNJbmZvVmlldy5kZWZhdWx0LAogICAgVGFza3NJbmZvVmlldzogX1Rhc2tzSW5mb1ZpZXcuZGVmYXVsdCwKICAgIERpc2Vhc2VzSW5mb1ZpZXc6IF9EaXNlYXNlc0luZm9WaWV3LmRlZmF1bHQsCiAgICBBdWRpdEhpc3RvcnlWaWV3OiBfQXVkaXRIaXN0b3J5Vmlldy5kZWZhdWx0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGFjdGl2ZVRhYjogJ2Jhc2ljJywKICAgICAgcHJvamVjdERhdGE6IHt9LAogICAgICBwcm9qZWN0SWQ6ICcnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuaYvuekuueXheWus+WFu+aKpOagh+etvgogICAgc2hvd0Rpc2Vhc2VUYWI6IGZ1bmN0aW9uIHNob3dEaXNlYXNlVGFiKCkgewogICAgICByZXR1cm4gdGhpcy5wcm9qZWN0RGF0YS5wcm9qZWN0VHlwZSA9PT0gJ21vbnRobHknOwogICAgfSwKICAgIC8vIOaYr+WQpuWPr+S7peWuoeaguAogICAgY2FuQXVkaXQ6IGZ1bmN0aW9uIGNhbkF1ZGl0KCkgewogICAgICB2YXIgdXNlclJvbGUgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJSb2xlIHx8ICdjb21wYW55X2FkbWluJzsKICAgICAgaWYgKHVzZXJSb2xlID09PSAnY29tcGFueV9hZG1pbicpIHsKICAgICAgICByZXR1cm4gdGhpcy5wcm9qZWN0RGF0YS5hdWRpdFN0YXR1cyA9PT0gJ3ByaW1hcnlfYXVkaXQnOwogICAgICB9IGVsc2UgaWYgKHVzZXJSb2xlID09PSAnYnJpZGdlX2NlbnRlcicpIHsKICAgICAgICByZXR1cm4gdGhpcy5wcm9qZWN0RGF0YS5hdWRpdFN0YXR1cyA9PT0gJ3NlY29uZGFyeV9hdWRpdCc7CiAgICAgIH0KICAgICAgcmV0dXJuIGZhbHNlOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0KSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQubikgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBfdGhpcy5wcm9qZWN0SWQgPSBfdGhpcy4kcm91dGUucGFyYW1zLmlkOwogICAgICAgICAgICBfY29udGV4dC5uID0gMTsKICAgICAgICAgICAgcmV0dXJuIF90aGlzLmxvYWRQcm9qZWN0RGV0YWlsKCk7CiAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hKDIpOwogICAgICAgIH0KICAgICAgfSwgX2NhbGxlZSk7CiAgICB9KSkoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWKoOi9vemhueebruivpuaDhQogICAgbG9hZFByb2plY3REZXRhaWw6IGZ1bmN0aW9uIGxvYWRQcm9qZWN0RGV0YWlsKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICB2YXIgcmVzcG9uc2UsIF90OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLncoZnVuY3Rpb24gKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnAgPSBfY29udGV4dDIubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnAgPSAwOwogICAgICAgICAgICAgIF90aGlzMi5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDE7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYXVkaXQuZ2V0QXVkaXREZXRhaWwpKF90aGlzMi5wcm9qZWN0SWQpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICBfdGhpczIucHJvamVjdERhdGEgPSByZXNwb25zZS5kYXRhIHx8IHt9OwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gMzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMjsKICAgICAgICAgICAgICBfdCA9IF9jb250ZXh0Mi52OwogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign5Yqg6L296aG555uu6K+m5oOF5aSx6LSlJyk7CiAgICAgICAgICAgICAgX3RoaXMyLmhhbmRsZUJhY2soKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMzsKICAgICAgICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuZigzKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgbnVsbCwgW1swLCAyLCAzLCA0XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliIfmjaLmoIfnrb4KICAgIHN3aXRjaFRhYjogZnVuY3Rpb24gc3dpdGNoVGFiKHRhYikgewogICAgICB0aGlzLmFjdGl2ZVRhYiA9IHRhYjsKICAgIH0sCiAgICAvLyDov5Tlm57liJfooagKICAgIGhhbmRsZUJhY2s6IGZ1bmN0aW9uIGhhbmRsZUJhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvbWFpbnRlbmFuY2UvYXVkaXQnKTsKICAgIH0sCiAgICAvLyDlrqHmoLjpobnnm64KICAgIGhhbmRsZUF1ZGl0OiBmdW5jdGlvbiBoYW5kbGVBdWRpdCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9tYWludGVuYW5jZS9wcm9qZWN0cy9hcHByb3ZlLyIuY29uY2F0KHRoaXMucHJvamVjdElkKSk7CiAgICB9LAogICAgLy8g6I635Y+W6aG555uu57G75Z6L5paH5pysCiAgICBnZXRQcm9qZWN0VHlwZVRleHQ6IGZ1bmN0aW9uIGdldFByb2plY3RUeXBlVGV4dCh0eXBlKSB7CiAgICAgIHZhciB0eXBlTWFwID0gewogICAgICAgIG1vbnRobHk6ICfmnIjluqblhbvmiqQnLAogICAgICAgIGNsZWFuaW5nOiAn5L+d5rSB6aG555uuJywKICAgICAgICBlbWVyZ2VuY3k6ICflupTmgKXlhbvmiqQnLAogICAgICAgIHByZXZlbnRpdmU6ICfpooTpmLLlhbvmiqQnCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8IHR5cGU7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_audit", "require", "_StatusTag", "_interopRequireDefault", "_BasicInfoView", "_TasksInfoView", "_DiseasesInfoView", "_AuditHistoryView", "name", "components", "StatusTag", "BasicInfoView", "TasksInfoView", "DiseasesInfoView", "AuditHistoryView", "data", "loading", "activeTab", "projectData", "projectId", "computed", "showDiseaseTab", "projectType", "can<PERSON><PERSON><PERSON>", "userRole", "$store", "getters", "auditStatus", "created", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "w", "_context", "n", "$route", "params", "id", "loadProjectDetail", "a", "methods", "_this2", "_callee2", "response", "_t", "_context2", "p", "getAuditDetail", "v", "$message", "error", "handleBack", "f", "switchTab", "tab", "$router", "push", "handleAudit", "concat", "getProjectTypeText", "type", "typeMap", "monthly", "cleaning", "emergency", "preventive"], "sources": ["src/views/maintenance/audit/detail/index.vue"], "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题栏 -->\n        <div class=\"page-header\">\n          <div class=\"header-left\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-arrow-left\"\n              class=\"back-btn\"\n              @click=\"handleBack\"\n            >\n              返回\n            </el-button>\n            <h2>审核详情</h2>\n          </div>\n          \n          <div class=\"header-right\">\n            <el-button\n              v-if=\"canAudit\"\n              type=\"primary\"\n              @click=\"handleAudit\"\n            >\n              审核\n            </el-button>\n          </div>\n        </div>\n        \n        <!-- 项目基本信息 -->\n        <div class=\"project-summary\">\n          <el-row :gutter=\"24\">\n            <el-col :span=\"18\">\n              <div class=\"summary-info\">\n                <h3>{{ projectData.projectName }}</h3>\n                <div class=\"meta-info\">\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-folder\"></i>\n                    {{ getProjectTypeText(projectData.projectType) }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-user\"></i>\n                    {{ projectData.submitter }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-time\"></i>\n                    {{ projectData.submitTime }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-office-building\"></i>\n                    {{ projectData.maintenanceUnit }}\n                  </span>\n                </div>\n              </div>\n            </el-col>\n            \n            <el-col :span=\"6\">\n              <div class=\"summary-status\">\n                <div class=\"status-item\">\n                  <label>当前状态:</label>\n                  <status-tag :status=\"projectData.auditStatus\" type=\"audit\" />\n                </div>\n                <div class=\"status-item\">\n                  <label>当前审核人:</label>\n                  <span>{{ projectData.currentAuditor || '-' }}</span>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'basic' }\"\n              @click=\"switchTab('basic')\"\n            >\n              基本信息\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tasks' }\"\n              @click=\"switchTab('tasks')\"\n            >\n              养护项目\n            </div>\n            <div \n              v-if=\"showDiseaseTab\"\n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'diseases' }\"\n              @click=\"switchTab('diseases')\"\n            >\n              病害养护\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'history' }\"\n              @click=\"switchTab('history')\"\n            >\n              审核历史\n            </div>\n          </div>\n        </div>\n        \n        <!-- 标签内容 -->\n        <div class=\"tab-content\">\n          <!-- 基本信息 -->\n          <div v-if=\"activeTab === 'basic'\" class=\"basic-info\">\n            <basic-info-view :project-data=\"projectData\" />\n          </div>\n          \n          <!-- 养护项目 -->\n          <div v-if=\"activeTab === 'tasks'\" class=\"tasks-info\">\n            <tasks-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 病害养护 -->\n          <div v-if=\"activeTab === 'diseases' && showDiseaseTab\" class=\"diseases-info\">\n            <diseases-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 审核历史 -->\n          <div v-if=\"activeTab === 'history'\" class=\"history-info\">\n            <audit-history-view :project-id=\"projectId\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getAuditDetail } from '@/api/maintenance/audit'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport BasicInfoView from '../../repairs/detail/components/BasicInfoView'\nimport TasksInfoView from '../../repairs/detail/components/TasksInfoView'\nimport DiseasesInfoView from '../../repairs/detail/components/DiseasesInfoView'\nimport AuditHistoryView from './components/AuditHistoryView'\n\nexport default {\n  name: 'MaintenanceAuditDetail',\n  components: {\n    StatusTag,\n    BasicInfoView,\n    TasksInfoView,\n    DiseasesInfoView,\n    AuditHistoryView\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'basic',\n      projectData: {},\n      projectId: ''\n    }\n  },\n  computed: {\n    // 是否显示病害养护标签\n    showDiseaseTab() {\n      return this.projectData.projectType === 'monthly'\n    },\n    \n    // 是否可以审核\n    canAudit() {\n      const userRole = this.$store.getters.userRole || 'company_admin'\n      \n      if (userRole === 'company_admin') {\n        return this.projectData.auditStatus === 'primary_audit'\n      } else if (userRole === 'bridge_center') {\n        return this.projectData.auditStatus === 'secondary_audit'\n      }\n      \n      return false\n    }\n  },\n  async created() {\n    this.projectId = this.$route.params.id\n    await this.loadProjectDetail()\n  },\n  methods: {\n    // 加载项目详情\n    async loadProjectDetail() {\n      try {\n        this.loading = true\n        const response = await getAuditDetail(this.projectId)\n        this.projectData = response.data || {}\n      } catch (error) {\n        this.$message.error('加载项目详情失败')\n        this.handleBack()\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n    },\n    \n    // 返回列表\n    handleBack() {\n      this.$router.push('/maintenance/audit')\n    },\n    \n    // 审核项目\n    handleAudit() {\n      this.$router.push(`/maintenance/projects/approve/${this.projectId}`)\n    },\n    \n    // 获取项目类型文本\n    getProjectTypeText(type) {\n      const typeMap = {\n        monthly: '月度养护',\n        cleaning: '保洁项目',\n        emergency: '应急养护',\n        preventive: '预防养护'\n      }\n      return typeMap[type] || type\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.page-header {\n  @extend .common-page-header;\n}\n\n.project-summary {\n  padding: 24px;\n  background: rgba(59, 130, 246, 0.1);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  border-radius: 8px;\n  margin-bottom: 24px;\n  \n  .summary-info {\n    h3 {\n      color: #ffffff;\n      font-size: 20px;\n      font-weight: bold;\n      margin-bottom: 12px;\n    }\n    \n    .meta-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 24px;\n      \n      .meta-item {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        color: #9ca3af;\n        font-size: 14px;\n        \n        i {\n          color: #3b82f6;\n        }\n      }\n    }\n  }\n  \n  .summary-status {\n    .status-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 12px;\n      \n      label {\n        color: #9ca3af;\n        min-width: 80px;\n        margin-right: 12px;\n      }\n      \n      span {\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.tab-content {\n  padding: 24px;\n  min-height: 400px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;AA8IA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,cAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,iBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,iBAAA,GAAAJ,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAAH,WAAA,CAAAI,WAAA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAA;MACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,OAAA,CAAAF,QAAA;MAEA,IAAAA,QAAA;QACA,YAAAN,WAAA,CAAAS,WAAA;MACA,WAAAH,QAAA;QACA,YAAAN,WAAA,CAAAS,WAAA;MACA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;MAAA,WAAAF,aAAA,CAAAD,OAAA,IAAAI,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YACAR,KAAA,CAAAV,SAAA,GAAAU,KAAA,CAAAS,MAAA,CAAAC,MAAA,CAAAC,EAAA;YAAAJ,QAAA,CAAAC,CAAA;YAAA,OACAR,KAAA,CAAAY,iBAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,CAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IACA;IACAF,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,MAAA;MAAA,WAAAd,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAY,SAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAf,aAAA,CAAAD,OAAA,IAAAI,CAAA,WAAAa,SAAA;UAAA,kBAAAA,SAAA,CAAAC,CAAA,GAAAD,SAAA,CAAAX,CAAA;YAAA;cAAAW,SAAA,CAAAC,CAAA;cAEAL,MAAA,CAAA5B,OAAA;cAAAgC,SAAA,CAAAX,CAAA;cAAA,OACA,IAAAa,qBAAA,EAAAN,MAAA,CAAAzB,SAAA;YAAA;cAAA2B,QAAA,GAAAE,SAAA,CAAAG,CAAA;cACAP,MAAA,CAAA1B,WAAA,GAAA4B,QAAA,CAAA/B,IAAA;cAAAiC,SAAA,CAAAX,CAAA;cAAA;YAAA;cAAAW,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAG,CAAA;cAEAP,MAAA,CAAAQ,QAAA,CAAAC,KAAA;cACAT,MAAA,CAAAU,UAAA;YAAA;cAAAN,SAAA,CAAAC,CAAA;cAEAL,MAAA,CAAA5B,OAAA;cAAA,OAAAgC,SAAA,CAAAO,CAAA;YAAA;cAAA,OAAAP,SAAA,CAAAN,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IAEA;IAEA;IACAW,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAxC,SAAA,GAAAwC,GAAA;IACA;IAEA;IACAH,UAAA,WAAAA,WAAA;MACA,KAAAI,OAAA,CAAAC,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAF,OAAA,CAAAC,IAAA,kCAAAE,MAAA,MAAA1C,SAAA;IACA;IAEA;IACA2C,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,OAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACA,OAAAJ,OAAA,CAAAD,IAAA,KAAAA,IAAA;IACA;EACA;AACA", "ignoreList": []}]}