{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue", "mtime": 1758808367674}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_projects", "require", "name", "props", "visible", "type", "Boolean", "default", "multiple", "selectedData", "Array", "infrastructureType", "String", "data", "loading", "bridgeList", "selected<PERSON><PERSON><PERSON>", "total", "queryParams", "pageNum", "pageSize", "district", "managementUnit", "districtOptions", "label", "value", "unitOptions", "computed", "dialogVisible", "get", "set", "val", "$emit", "watch", "initData", "getList", "immediate", "handler", "methods", "_toConsumableArray2", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "response", "bridgeData", "_t", "w", "_context", "p", "n", "getBridgeList", "v", "rows", "list", "map", "bridge", "index", "_objectSpread2", "project", "maintainer", "code", "concat", "padStart", "road", "id", "console", "error", "$message", "$nextTick", "$refs", "bridgeTable", "length", "for<PERSON>ach", "isSelected", "some", "selected", "toggleRowSelection", "f", "a", "handleQuery", "reset<PERSON><PERSON>y", "selectBridge", "push", "removeSelected", "findIndex", "item", "splice", "clearSelected", "isSelectable", "row", "handleSelectionChange", "selection", "confirmSelection", "getMaintenanceLevelType", "level", "typeMap", "handleSizeChange", "handleCurrentChange"], "sources": ["src/components/Maintenance/BridgeSelector.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"关联桥梁\"\n    :visible.sync=\"dialogVisible\"\n    custom-class=\"bridge-selector-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\n    :close-on-click-modal=\"false\"\n    :modal-append-to-body=\"true\"\n    :append-to-body=\"true\"\n    top=\"5vh\"\n    destroy-on-close\n  >\n    <div class=\"dialog-content\">\n      <!-- 搜索表单 - 复用通用搜索表单样式 -->\n      <div class=\"search-form\">\n        <el-form :model=\"queryParams\" inline>\n          <el-form-item label=\"桥梁名称\">\n            <el-input\n              v-model=\"queryParams.name\"\n              placeholder=\"输入桥梁名称或编号\"\n              clearable\n              style=\"width: 200px\"\n              @keyup.enter.native=\"handleQuery\"\n              @clear=\"handleQuery\"\n            />\n          </el-form-item>\n          \n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      \n      \n      <!-- 桥梁列表 - 复用通用表格样式 -->\n      <div class=\"common-table\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"bridgeList\"\n          class=\"maintenance-table\"\n          style=\"width: 100%\"\n          :row-style=\"{ height: '32px' }\"\n          size=\"small\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column\n            type=\"selection\"\n            width=\"55\"\n            :selectable=\"isSelectable\"\n          />\n          \n          <el-table-column prop=\"project\" label=\"养护项目\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"maintainer\" label=\"养护人员\" width=\"100\" align=\"center\" />\n          \n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n          \n          <el-table-column prop=\"name\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"code\" label=\"桥梁编号\" width=\"120\" align=\"center\" />\n          \n          <el-table-column prop=\"road\" label=\"所在道路\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        </el-table>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"dialogVisible = false\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\">\n        确定 ({{ selectedBridges.length }})\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getBridgeList } from '@/api/maintenance/projects'\n\nexport default {\n  name: 'BridgeSelector',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    multiple: {\n      type: Boolean,\n      default: true\n    },\n    selectedData: {\n      type: Array,\n      default: () => []\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge' // bridge, tunnel\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      bridgeList: [],\n      selectedBridges: [],\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: 'bridge',\n        name: '',\n        district: '',\n        managementUnit: ''\n      },\n      \n      // 选项数据\n      districtOptions: [\n        { label: '岳麓区', value: 'yuelu' },\n        { label: '芙蓉区', value: 'furong' },\n        { label: '天心区', value: 'tianxin' },\n        { label: '开福区', value: 'kaifu' },\n        { label: '雨花区', value: 'yuhua' },\n        { label: '望城区', value: 'wangcheng' }\n      ],\n      \n      unitOptions: [\n        { label: '长沙市桥梁管理处', value: 'changsha_bridge' },\n        { label: '长沙市隧道管理处', value: 'changsha_tunnel' },\n        { label: '岳麓区市政局', value: 'yuelu_municipal' },\n        { label: '芙蓉区市政局', value: 'furong_municipal' }\n      ]\n    }\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.visible\n      },\n      set(val) {\n        this.$emit('update:visible', val)\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initData()\n        this.getList()\n      }\n    },\n    \n    infrastructureType: {\n      immediate: true,\n      handler(val) {\n        this.queryParams.infrastructureType = val\n      }\n    }\n  },\n  methods: {\n    // 初始化数据\n    initData() {\n      this.selectedBridges = [...this.selectedData]\n      this.queryParams.infrastructureType = this.infrastructureType\n    },\n    \n    // 获取桥梁列表\n    async getList() {\n      try {\n        this.loading = true\n        const response = await getBridgeList(this.queryParams)\n        if (response.data) {\n          let bridgeData = response.data.rows || response.data.list || []\n          \n          // 为每条数据添加结构图所需的字段\n          this.bridgeList = bridgeData.map((bridge, index) => ({\n            ...bridge,\n            project: bridge.project || 'XXXXXX大桥', // 养护项目\n            maintainer: bridge.maintainer || ['黄昭言', '刘雨桐', '罗砚秋'][index % 3], // 养护人员\n            code: bridge.code || `CS-B-${String(index + 1).padStart(3, '0')}`, // 桥梁编号\n            road: bridge.road || '枫林一路', // 所在道路\n            managementUnit: bridge.managementUnit || '桥隧中心' // 管理单位\n          }))\n          this.total = response.data.total || 0\n        } else {\n          // 如果接口失败，使用模拟数据展示结构\n          this.bridgeList = [\n            {\n              id: 1,\n              project: 'XXXXXX大桥',\n              maintainer: '黄昭言',\n              name: 'XXX大桥',\n              code: 'CS-B-001',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 2,\n              project: 'XXXXXX大桥',\n              maintainer: '刘雨桐',\n              name: '刘雨桐',\n              code: 'CS-B-002', \n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 3,\n              project: 'XXXXXX大桥',\n              maintainer: '罗砚秋',\n              name: '罗砚秋',\n              code: 'CS-B-003',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            }\n          ]\n          this.total = 3\n        }\n      } catch (error) {\n        console.error('获取桥梁列表失败:', error)\n        this.$message.error('获取桥梁列表失败')\n        \n        // 错误时也显示模拟数据以展示结构\n        this.bridgeList = [\n          {\n            id: 1,\n            project: 'XXXXXX大桥',\n            maintainer: '黄昭言',\n            name: 'XXX大桥',\n            code: 'CS-B-001',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 2,\n            project: 'XXXXXX大桥',\n            maintainer: '刘雨桐',\n            name: '刘雨桐',\n            code: 'CS-B-002',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 3,\n            project: 'XXXXXX大桥',\n            maintainer: '罗砚秋',\n            name: '罗砚秋',\n            code: 'CS-B-003',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          }\n        ]\n        this.total = 3\n        \n        // 设置已选中的行\n        this.$nextTick(() => {\n          if (this.$refs.bridgeTable && this.selectedData && this.selectedData.length > 0) {\n            this.bridgeList.forEach(bridge => {\n              const isSelected = this.selectedData.some(selected => selected.id === bridge.id)\n              if (isSelected) {\n                this.$refs.bridgeTable.toggleRowSelection(bridge, true)\n              }\n            })\n          }\n        })\n        \n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: this.infrastructureType,\n        name: '',\n        district: '',\n        managementUnit: ''\n      }\n      this.getList()\n    },\n    \n    // 选择桥梁\n    selectBridge(bridge) {\n      if (!this.multiple) {\n        this.selectedBridges = [bridge]\n      } else {\n        if (!this.isSelected(bridge)) {\n          this.selectedBridges.push(bridge)\n        }\n      }\n    },\n    \n    // 移除选择\n    removeSelected(bridge) {\n      const index = this.selectedBridges.findIndex(item => item.id === bridge.id)\n      if (index > -1) {\n        this.selectedBridges.splice(index, 1)\n      }\n    },\n    \n    // 清空选择\n    clearSelected() {\n      this.selectedBridges = []\n    },\n    \n    // 判断是否已选择\n    isSelected(bridge) {\n      return this.selectedBridges.some(item => item.id === bridge.id)\n    },\n    \n    // 判断是否可选择\n    isSelectable(row) {\n      return true // 允许选择和取消选择\n    },\n    \n    // 表格选择变化\n    handleSelectionChange(selection) {\n      if (this.multiple) {\n        // 直接使用当前页面的选择结果\n        this.selectedBridges = selection\n      }\n    },\n    \n    // 确认选择\n    confirmSelection() {\n      this.$emit('confirm', this.selectedBridges)\n      this.dialogVisible = false\n    },\n    \n    // 获取养护等级类型\n    getMaintenanceLevelType(level) {\n      const typeMap = {\n        '一级': 'danger',\n        '二级': 'warning',\n        '三级': 'info',\n        '四级': 'success'\n      }\n      return typeMap[level] || 'info'\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n:deep(.bridge-selector-dialog) {\n  // 弹窗样式覆盖 - 遵循设计文档6.7.7规范\n  .el-dialog {\n    background: #091A4B !important; // 与主要背景色一致\n    border-radius: 12px !important;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;\n    border: none !important;\n    width: 70vw !important;\n    max-width: 1200px !important;\n    min-width: 800px !important;\n  }\n  \n  .el-dialog__header {\n    background: #091A4B !important;\n    border-bottom: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    height: 56px !important; // 按设计文档规范\n    \n    .el-dialog__title {\n      color: #f8fafc !important; // 6.7.1 主要文字颜色\n      font-size: 18px !important;\n      font-weight: normal !important; // 正常字重\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n    }\n    \n    .el-dialog__close {\n      color: #94a3b8 !important;\n      font-size: 18px !important;\n      width: 32px !important;\n      height: 32px !important;\n      line-height: 32px !important;\n      \n      &:hover {\n        color: #ffffff !important;\n        background: #ef4444 !important;\n        border-radius: 50% !important;\n      }\n    }\n  }\n  \n  .el-dialog__body {\n    background: #091A4B !important;\n    padding: 0 !important; // 移除默认padding，让内容区域自行控制\n    color: #f8fafc !important;\n    max-height: calc(90vh - 120px) !important; // 按设计文档规范\n    overflow-y: auto !important;\n  }\n  \n  .el-dialog__footer {\n    background: #091A4B !important;\n    border-top: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    text-align: right !important;\n  }\n  \n  .selector-content {\n    padding: 24px !important; // 内容区域统一内边距\n    \n    // 搜索筛选区域 - 遵循6.7.8输入框组件样式\n    .search-section {\n      padding: 20px !important;\n      background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n      border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n      border-radius: 8px !important;\n      margin-bottom: 20px !important;\n      \n      // 搜索输入框样式\n      .el-input {\n        .el-input__inner {\n          background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n          border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n          border-radius: 8px !important; // 6.7.8 输入框圆角\n          height: 40px !important; // 6.7.8 输入框高度\n          font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          font-size: 16px !important;\n          font-weight: normal !important;\n          \n          &::placeholder {\n            color: rgba(255, 255, 255, 0.5) !important; // 6.7.8 占位符颜色\n          }\n          \n          &:focus {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n        }\n      }\n    }\n    \n    \n    // 桥梁列表表格 - 遵循6.7.4表格组件样式\n    .bridge-list {\n      background: transparent !important;\n      border: none !important;\n      border-radius: 8px !important;\n      \n      // 表格样式覆盖 - 完全遵循maintenance-theme.scss的表格样式\n      .el-table {\n        background: transparent !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n        border: none !important;\n        \n        // 无边框设计\n        &::before {\n          display: none !important;\n        }\n        \n        // 表头样式 - 完全按照设计文档6.7.4规范\n        .el-table__header-wrapper {\n          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n          \n          th {\n            background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n            color: #FFF !important;\n            font-size: 16px !important;\n            font-weight: 400 !important; // 正常字重\n            line-height: 140% !important; // 22.4px\n            height: 44px !important;\n            padding: 14px 12px !important;\n            border: 1px solid rgba(255, 255, 255, 0.1) !important;\n            text-align: center !important;\n            white-space: nowrap !important;\n            \n            &:last-child {\n              border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n            }\n          }\n        }\n        \n        // 数据行样式 - 完全按照设计文档6.7.4规范\n        .el-table__body-wrapper {\n          background: transparent !important;\n          \n          tr {\n            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            \n            // 移除双数行的背景色差异\n            &:nth-child(odd) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            &:nth-child(even) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            // 悬停效果\n            &:hover {\n              background: rgba(255, 255, 255, 0.05) !important;\n              \n              td {\n                background: rgba(255, 255, 255, 0.05) !important;\n              }\n            }\n            \n            // 最后一行无底部边框\n            &:last-child {\n              td {\n                border-bottom: none !important;\n              }\n            }\n            \n            td {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n              color: #FFF !important;\n              font-size: 14px !important;\n              font-weight: 400 !important;\n              line-height: 120% !important; // 16.8px\n              padding: 4px 12px !important;\n              border: 1px solid rgba(255, 255, 255, 0.1) !important;\n              text-align: center !important;\n              height: 32px !important;\n              \n              &:last-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n              }\n            }\n          }\n        }\n        \n        // 空状态样式\n        .el-table__empty-block {\n          background: transparent !important;\n          \n          .el-table__empty-text {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n          }\n        }\n      }\n      \n      // 复选框样式\n      .el-checkbox {\n        .el-checkbox__inner {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.2) !important;\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important;\n          }\n          \n          &.is-checked {\n            background: #5C9DFF !important; // 6.7.1 蓝色强调色\n            border-color: #5C9DFF !important;\n          }\n        }\n        \n        .el-checkbox__label {\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n        }\n      }\n      \n      .bridge-type {\n        display: flex !important;\n        align-items: center !important;\n        gap: 4px !important;\n        \n        i {\n          color: #5C9DFF !important; // 6.7.1 蓝色强调色\n        }\n      }\n      \n      .selected-btn {\n        color: #10b981 !important; // 绿色表示已选择\n        \n        &:hover {\n          color: #34d399 !important;\n        }\n      }\n      \n      // 分页容器样式\n      .pagination-container {\n        padding: 20px 0 !important;\n        text-align: center !important;\n        background: transparent !important;\n        \n        // 分页组件样式 - 遵循设计文档规范\n        .el-pagination {\n          .el-pagination__total,\n          .el-pagination__jump {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n            font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          }\n          \n          .btn-prev,\n          .btn-next,\n          .el-pager li {\n            background: transparent !important;\n            border: 1px solid #4b5563 !important;\n            color: #94a3b8 !important;\n            border-radius: 4px !important;\n            margin: 0 2px !important;\n            \n            &:hover {\n              background: #374151 !important;\n              color: #ffffff !important;\n              border-color: rgba(255, 255, 255, 0.4) !important;\n            }\n            \n            &.active {\n              background: #5C9DFF !important; // 6.7.1 蓝色强调色\n              color: #ffffff !important;\n              border-color: #5C9DFF !important;\n            }\n          }\n          \n          .el-pagination__jump {\n            .el-input__inner {\n              background: rgba(255, 255, 255, 0.1) !important;\n              border-color: rgba(255, 255, 255, 0.2) !important;\n              color: #f8fafc !important;\n              \n              &:focus {\n                border-color: rgba(255, 255, 255, 0.4) !important;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  \n  // 底部按钮样式 - 遵循设计文档规范\n  .dialog-footer {\n    text-align: right !important;\n    \n    .el-button {\n      min-width: 80px !important;\n      height: 36px !important;\n      border-radius: 6px !important;\n      font-size: 14px !important;\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n      font-weight: normal !important;\n      padding: 0 16px !important;\n      margin-left: 12px !important;\n      \n      // 默认按钮样式\n      &.el-button--default {\n        background: rgba(255, 255, 255, 0.1) !important;\n        border: 1px solid rgba(255, 255, 255, 0.3) !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        \n        &:hover {\n          background: rgba(255, 255, 255, 0.15) !important;\n          border-color: rgba(255, 255, 255, 0.4) !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.3) !important;\n          color: #f8fafc !important;\n        }\n      }\n      \n      // 主要按钮样式\n      &.el-button--primary {\n        background: #5C9DFF !important; // 6.7.1 蓝色强调色\n        border-color: #5C9DFF !important;\n        color: #ffffff !important;\n        \n        &:hover {\n          background: #74a7f5 !important; // 6.7.1 浅蓝色\n          border-color: #74a7f5 !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: #5C9DFF !important;\n          border-color: #5C9DFF !important;\n          color: #ffffff !important;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA2FA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAE,YAAA;MACAJ,IAAA,EAAAK,KAAA;MACAH,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAI,kBAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,eAAA;MACAC,KAAA;MAEA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAT,kBAAA;QACAT,IAAA;QACAmB,QAAA;QACAC,cAAA;MACA;MAEA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEAC,WAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAE,QAAA;IACAC,aAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAzB,OAAA;MACA;MACA0B,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,mBAAAD,GAAA;MACA;IACA;EACA;EACAE,KAAA;IACA7B,OAAA,WAAAA,QAAA2B,GAAA;MACA,IAAAA,GAAA;QACA,KAAAG,QAAA;QACA,KAAAC,OAAA;MACA;IACA;IAEAxB,kBAAA;MACAyB,SAAA;MACAC,OAAA,WAAAA,QAAAN,GAAA;QACA,KAAAb,WAAA,CAAAP,kBAAA,GAAAoB,GAAA;MACA;IACA;EACA;EACAO,OAAA;IACA;IACAJ,QAAA,WAAAA,SAAA;MACA,KAAAlB,eAAA,OAAAuB,mBAAA,CAAAhC,OAAA,OAAAE,YAAA;MACA,KAAAS,WAAA,CAAAP,kBAAA,QAAAA,kBAAA;IACA;IAEA;IACAwB,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MAAA,WAAAC,kBAAA,CAAAlC,OAAA,mBAAAmC,aAAA,CAAAnC,OAAA,IAAAoC,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,UAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAnC,OAAA,IAAAyC,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAEAV,KAAA,CAAA1B,OAAA;cAAAmC,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAC,uBAAA,EAAAZ,KAAA,CAAAtB,WAAA;YAAA;cAAA2B,QAAA,GAAAI,QAAA,CAAAI,CAAA;cACA,IAAAR,QAAA,CAAAhC,IAAA;gBACAiC,UAAA,GAAAD,QAAA,CAAAhC,IAAA,CAAAyC,IAAA,IAAAT,QAAA,CAAAhC,IAAA,CAAA0C,IAAA,QAEA;gBACAf,KAAA,CAAAzB,UAAA,GAAA+B,UAAA,CAAAU,GAAA,WAAAC,MAAA,EAAAC,KAAA;kBAAA,WAAAC,cAAA,CAAApD,OAAA,MAAAoD,cAAA,CAAApD,OAAA,MACAkD,MAAA;oBACAG,OAAA,EAAAH,MAAA,CAAAG,OAAA;oBAAA;oBACAC,UAAA,EAAAJ,MAAA,CAAAI,UAAA,0BAAAH,KAAA;oBAAA;oBACAI,IAAA,EAAAL,MAAA,CAAAK,IAAA,YAAAC,MAAA,CAAAnD,MAAA,CAAA8C,KAAA,MAAAM,QAAA;oBAAA;oBACAC,IAAA,EAAAR,MAAA,CAAAQ,IAAA;oBAAA;oBACA3C,cAAA,EAAAmC,MAAA,CAAAnC,cAAA;kBAAA;gBAAA,CACA;gBACAkB,KAAA,CAAAvB,KAAA,GAAA4B,QAAA,CAAAhC,IAAA,CAAAI,KAAA;cACA;gBACA;gBACAuB,KAAA,CAAAzB,UAAA,IACA;kBACAmD,EAAA;kBACAN,OAAA;kBACAC,UAAA;kBACA3D,IAAA;kBACA4D,IAAA;kBACAG,IAAA;kBACA3C,cAAA;gBACA,GACA;kBACA4C,EAAA;kBACAN,OAAA;kBACAC,UAAA;kBACA3D,IAAA;kBACA4D,IAAA;kBACAG,IAAA;kBACA3C,cAAA;gBACA,GACA;kBACA4C,EAAA;kBACAN,OAAA;kBACAC,UAAA;kBACA3D,IAAA;kBACA4D,IAAA;kBACAG,IAAA;kBACA3C,cAAA;gBACA,EACA;gBACAkB,KAAA,CAAAvB,KAAA;cACA;cAAAgC,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAc,OAAA,CAAAC,KAAA,cAAArB,EAAA;cACAP,KAAA,CAAA6B,QAAA,CAAAD,KAAA;;cAEA;cACA5B,KAAA,CAAAzB,UAAA,IACA;gBACAmD,EAAA;gBACAN,OAAA;gBACAC,UAAA;gBACA3D,IAAA;gBACA4D,IAAA;gBACAG,IAAA;gBACA3C,cAAA;cACA,GACA;gBACA4C,EAAA;gBACAN,OAAA;gBACAC,UAAA;gBACA3D,IAAA;gBACA4D,IAAA;gBACAG,IAAA;gBACA3C,cAAA;cACA,GACA;gBACA4C,EAAA;gBACAN,OAAA;gBACAC,UAAA;gBACA3D,IAAA;gBACA4D,IAAA;gBACAG,IAAA;gBACA3C,cAAA;cACA,EACA;cACAkB,KAAA,CAAAvB,KAAA;;cAEA;cACAuB,KAAA,CAAA8B,SAAA;gBACA,IAAA9B,KAAA,CAAA+B,KAAA,CAAAC,WAAA,IAAAhC,KAAA,CAAA/B,YAAA,IAAA+B,KAAA,CAAA/B,YAAA,CAAAgE,MAAA;kBACAjC,KAAA,CAAAzB,UAAA,CAAA2D,OAAA,WAAAjB,MAAA;oBACA,IAAAkB,UAAA,GAAAnC,KAAA,CAAA/B,YAAA,CAAAmE,IAAA,WAAAC,QAAA;sBAAA,OAAAA,QAAA,CAAAX,EAAA,KAAAT,MAAA,CAAAS,EAAA;oBAAA;oBACA,IAAAS,UAAA;sBACAnC,KAAA,CAAA+B,KAAA,CAAAC,WAAA,CAAAM,kBAAA,CAAArB,MAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAAR,QAAA,CAAAC,CAAA;cAGAV,KAAA,CAAA1B,OAAA;cAAA,OAAAmC,QAAA,CAAA8B,CAAA;YAAA;cAAA,OAAA9B,QAAA,CAAA+B,CAAA;UAAA;QAAA,GAAApC,OAAA;MAAA;IAEA;IAEA;IACAqC,WAAA,WAAAA,YAAA;MACA,KAAA/D,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IAEA;IACA+C,UAAA,WAAAA,WAAA;MACA,KAAAhE,WAAA;QACAC,OAAA;QACAC,QAAA;QACAT,kBAAA,OAAAA,kBAAA;QACAT,IAAA;QACAmB,QAAA;QACAC,cAAA;MACA;MACA,KAAAa,OAAA;IACA;IAEA;IACAgD,YAAA,WAAAA,aAAA1B,MAAA;MACA,UAAAjD,QAAA;QACA,KAAAQ,eAAA,IAAAyC,MAAA;MACA;QACA,UAAAkB,UAAA,CAAAlB,MAAA;UACA,KAAAzC,eAAA,CAAAoE,IAAA,CAAA3B,MAAA;QACA;MACA;IACA;IAEA;IACA4B,cAAA,WAAAA,eAAA5B,MAAA;MACA,IAAAC,KAAA,QAAA1C,eAAA,CAAAsE,SAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArB,EAAA,KAAAT,MAAA,CAAAS,EAAA;MAAA;MACA,IAAAR,KAAA;QACA,KAAA1C,eAAA,CAAAwE,MAAA,CAAA9B,KAAA;MACA;IACA;IAEA;IACA+B,aAAA,WAAAA,cAAA;MACA,KAAAzE,eAAA;IACA;IAEA;IACA2D,UAAA,WAAAA,WAAAlB,MAAA;MACA,YAAAzC,eAAA,CAAA4D,IAAA,WAAAW,IAAA;QAAA,OAAAA,IAAA,CAAArB,EAAA,KAAAT,MAAA,CAAAS,EAAA;MAAA;IACA;IAEA;IACAwB,YAAA,WAAAA,aAAAC,GAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,SAAArF,QAAA;QACA;QACA,KAAAQ,eAAA,GAAA6E,SAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA9D,KAAA,iBAAAhB,eAAA;MACA,KAAAY,aAAA;IACA;IAEA;IACAmE,uBAAA,WAAAA,wBAAAC,KAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,KAAA;IACA;IAEA;IACAE,gBAAA,WAAAA,iBAAAnE,GAAA;MACA,KAAAb,WAAA,CAAAE,QAAA,GAAAW,GAAA;MACA,KAAAI,OAAA;IACA;IAEA;IACAgE,mBAAA,WAAAA,oBAAApE,GAAA;MACA,KAAAb,WAAA,CAAAC,OAAA,GAAAY,GAAA;MACA,KAAAI,OAAA;IACA;EACA;AACA", "ignoreList": []}]}