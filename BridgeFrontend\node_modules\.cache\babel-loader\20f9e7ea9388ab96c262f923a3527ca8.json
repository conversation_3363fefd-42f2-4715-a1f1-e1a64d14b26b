{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\RepairDetailView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\RepairDetailView.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfQmFzaWNJbmZvVmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9kZXRhaWwvQmFzaWNJbmZvVmlldy52dWUiKSk7CnZhciBfUHJvamVjdHNWaWV3ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2RldGFpbC9Qcm9qZWN0c1ZpZXcudnVlIikpOwp2YXIgX0Rpc2Vhc2VzVmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9kZXRhaWwvRGlzZWFzZXNWaWV3LnZ1ZSIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0MiA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUmVwYWlyRGV0YWlsVmlldycsCiAgY29tcG9uZW50czogewogICAgQmFzaWNJbmZvVmlldzogX0Jhc2ljSW5mb1ZpZXcuZGVmYXVsdCwKICAgIFByb2plY3RzVmlldzogX1Byb2plY3RzVmlldy5kZWZhdWx0LAogICAgRGlzZWFzZXNWaWV3OiBfRGlzZWFzZXNWaWV3LmRlZmF1bHQKICB9LAogIHByb3BzOiB7CiAgICByZXBhaXJEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHt9OwogICAgICB9CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlVGFiOiAncHJvamVjdHMnIC8vIOm7mOiupOaYvuekuuWFu+aKpOmhueebrnRhYu+8jOS+v+S6jua1i+ivlQogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["_BasicInfoView", "_interopRequireDefault", "require", "_ProjectsView", "_DiseasesView", "name", "components", "BasicInfoView", "ProjectsView", "DiseasesView", "props", "repairData", "type", "Object", "default", "data", "activeTab"], "sources": ["src/views/maintenance/repairs/components/RepairDetailView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"repair-detail-view maintenance-theme\">\r\n    <div class=\"page-container\">\r\n      <div class=\"card-container\">\r\n        <!-- 导航标签页 -->\r\n        <el-tabs v-model=\"activeTab\" class=\"detail-tabs\">\r\n          <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n            <basic-info-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"养护项目\" name=\"projects\">\r\n            <projects-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"病害养护\" name=\"diseases\">\r\n            <diseases-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BasicInfoView from './detail/BasicInfoView.vue'\r\nimport ProjectsView from './detail/ProjectsView.vue'\r\nimport DiseasesView from './detail/DiseasesView.vue'\r\n\r\nexport default {\r\n  name: 'RepairDetailView',\r\n  components: {\r\n    BasicInfoView,\r\n    ProjectsView,\r\n    DiseasesView\r\n  },\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'projects' // 默认显示养护项目tab，便于测试\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.repair-detail-view {\r\n  .page-container {\r\n    padding: 0;\r\n  }\r\n  \r\n  .card-container {\r\n    background: #1a2332;\r\n    border: 1px solid #374151;\r\n    border-radius: 8px;\r\n    padding: 24px;\r\n  }\r\n  \r\n  .detail-tabs {\r\n    @extend .common-element-tabs;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAsBA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;AACA", "ignoreList": []}]}