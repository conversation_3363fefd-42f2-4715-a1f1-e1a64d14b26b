{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue", "mtime": 1758804563532}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "chartData", "type", "Array", "default", "loading", "Boolean", "height", "String", "computed", "displayData", "length", "bridgeName", "count", "slice", "maxCount", "Math", "max", "apply", "_toConsumableArray2", "map", "item", "value", "methods", "getProgressWidth"], "sources": ["src/views/inspection/statistics/components/Top10RankingChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"top10-ranking-chart\" v-loading=\"loading\">\r\n    <div class=\"ranking-list\">\r\n      <div\r\n        v-for=\"(item, index) in displayData\"\r\n        :key=\"index\"\r\n        class=\"ranking-item\"\r\n        :class=\"{ 'top-three': index < 3 }\"\r\n      >\r\n        <div class=\"rank-badge\" :class=\"`rank-${index + 1}`\">\r\n          TOP{{ index + 1 }}\r\n        </div>\r\n        <div class=\"bridge-info\">\r\n          <span class=\"bridge-name\">{{ item.bridgeName || item.name }}</span>\r\n          <div class=\"progress-container\">\r\n            <div \r\n              class=\"progress-bar\"\r\n              :style=\"{ width: `${getProgressWidth(item.count || item.value)}%` }\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <span class=\"count\">{{ item.count || item.value }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Top10RankingChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    loading: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    }\r\n  },\r\n  computed: {\r\n    displayData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return [\r\n          { bridgeName: 'XXXX病害', count: 932 },\r\n          { bridgeName: 'XXXX病害', count: 899 },\r\n          { bridgeName: 'XXXX病害', count: 702 },\r\n          { bridgeName: 'XXXX病害', count: 543 },\r\n          { bridgeName: 'XXXX病害', count: 208 }\r\n        ]\r\n      }\r\n      return this.chartData.slice(0, 5)\r\n    },\r\n    maxCount() {\r\n      if (this.displayData.length === 0) return 1\r\n      return Math.max(...this.displayData.map(item => item.count || item.value || 0))\r\n    }\r\n  },\r\n  methods: {\r\n    getProgressWidth(count) {\r\n      if (this.maxCount === 0) return 0\r\n      return Math.max((count / this.maxCount) * 100, 5) // 最小宽度5%\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.top10-ranking-chart {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与右侧容器设置保持一致\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n  \r\n  .ranking-list {\r\n    position: relative;\r\n    z-index: 3; // 确保内容在伪元素之上\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    width: 100%;\r\n    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间\r\n    justify-content: center;\r\n    \r\n    .ranking-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 6px 0;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover {\r\n        background-color: rgba(64, 158, 255, 0.05);\r\n        border-radius: 4px;\r\n        transform: translateX(2px);\r\n      }\r\n      \r\n      &.top-three {\r\n        .rank-badge {\r\n          background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\r\n          color: #fff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      \r\n      .rank-badge {\r\n        width: 50px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #40E0D0;\r\n        color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 10px;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        \r\n        &.rank-1 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-2 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-3 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-4 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-5 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n      }\r\n      \r\n      .bridge-info {\r\n        flex: 1;\r\n        min-width: 0;\r\n        \r\n        .bridge-name {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #ffffff;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          margin-bottom: 2px;\r\n        }\r\n        \r\n        .progress-container {\r\n          width: 100%;\r\n          height: 6px;\r\n          background-color: #f5f7fa;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n          \r\n          .progress-bar {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);\r\n            border-radius: 3px;\r\n            transition: width 0.6s ease;\r\n            animation: progressAnimation 1s ease-out;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .count {\r\n        font-size: 11px;\r\n        color: #ffffff;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        min-width: 20px;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes progressAnimation {\r\n  from {\r\n    width: 0%;\r\n  }\r\n  to {\r\n    width: var(--target-width);\r\n  }\r\n}\r\n\r\n// 响应式优化\r\n@media (max-width: 768px) {\r\n  .top10-ranking-chart {\r\n    .ranking-list {\r\n      .ranking-item {\r\n        .bridge-info {\r\n          .bridge-name {\r\n            font-size: 11px;\r\n          }\r\n        }\r\n        \r\n        .count {\r\n          font-size: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4BA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,UAAAT,SAAA,SAAAA,SAAA,CAAAU,MAAA;QACA,QACA;UAAAC,UAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,KAAA;QAAA,EACA;MACA;MACA,YAAAZ,SAAA,CAAAa,KAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,SAAAL,WAAA,CAAAC,MAAA;MACA,OAAAK,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAAf,OAAA,OAAAM,WAAA,CAAAU,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,KAAA,IAAAQ,IAAA,CAAAC,KAAA;MAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAX,KAAA;MACA,SAAAE,QAAA;MACA,OAAAC,IAAA,CAAAC,GAAA,CAAAJ,KAAA,QAAAE,QAAA;IACA;EACA;AACA", "ignoreList": []}]}