{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQyID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdCYXNpY0luZm9WaWV3JywKICBwcm9wczogewogICAgcmVwYWlyRGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuaYvuekuuW3peS9nOmHj+Wtl+autSAtIOS4juWFu+aKpOmhueebruS/neaMgeS4gOiHtAogICAgc2hvd1dvcmtsb2FkOiBmdW5jdGlvbiBzaG93V29ya2xvYWQoKSB7CiAgICAgIHJldHVybiBbJ2NsZWFuaW5nJywgJ2VtZXJnZW5jeScsICdwcmV2ZW50aXZlJ10uaW5jbHVkZXModGhpcy5yZXBhaXJEYXRhLnByb2plY3RUeXBlKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGdldFByb2plY3RUeXBlTGFiZWw6IGZ1bmN0aW9uIGdldFByb2plY3RUeXBlTGFiZWwodHlwZSkgewogICAgICB2YXIgdHlwZU1hcCA9IHsKICAgICAgICAnbW9udGhseSc6ICfmnIjluqblhbvmiqQnLAogICAgICAgICdwcmV2ZW50aXZlJzogJ+mihOmYsuWFu+aKpCcsCiAgICAgICAgJ2VtZXJnZW5jeSc6ICflupTmgKXlhbvmiqQnLAogICAgICAgICdjbGVhbmluZyc6ICfkv53mtIHpobnnm64nCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8IHR5cGU7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "props", "repairData", "type", "Object", "default", "computed", "showWorkload", "includes", "projectType", "methods", "getProjectTypeLabel", "typeMap"], "sources": ["src/views/maintenance/repairs/components/detail/BasicInfoView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"basic-info-view\">\r\n    <el-form\r\n      :model=\"repairData\"\r\n      label-width=\"120px\"\r\n      class=\"maintenance-form\"\r\n    >\r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目名称\">\r\n            <el-input\r\n              :value=\"repairData.projectName\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目类型\">\r\n            <el-input\r\n              :value=\"getProjectTypeLabel(repairData.projectType)\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目开始时间\">\r\n            <el-input\r\n              :value=\"repairData.startDate\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目结束时间\">\r\n            <el-input\r\n              :value=\"repairData.endDate\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"管理单位\">\r\n            <el-input\r\n              :value=\"repairData.managementUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"监理单位\">\r\n            <el-input\r\n              :value=\"repairData.supervisionUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"养护单位\">\r\n            <el-input\r\n              :value=\"repairData.maintenanceUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目负责人\">\r\n            <el-input\r\n              :value=\"repairData.manager\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"联系方式\">\r\n            <el-input\r\n              :value=\"repairData.contactPhone\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\" v-if=\"showWorkload\">\r\n          <el-form-item label=\"工作量\">\r\n            <el-input\r\n              :value=\"repairData.workload\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"项目内容\">\r\n            <el-input\r\n              :value=\"repairData.projectContent\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              readonly\r\n              class=\"readonly-textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"附件\">\r\n            <div class=\"attachment-list\">\r\n              <div\r\n                v-for=\"(file, index) in repairData.attachments\"\r\n                :key=\"index\"\r\n                class=\"attachment-item\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ file.name }}</span>\r\n                <span class=\"file-size\">({{ file.size }})</span>\r\n              </div>\r\n              <div v-if=\"!repairData.attachments || repairData.attachments.length === 0\" class=\"no-attachments\">\r\n                暂无附件\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoView',\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示工作量字段 - 与养护项目保持一致\r\n    showWorkload() {\r\n      return ['cleaning', 'emergency', 'preventive'].includes(this.repairData.projectType)\r\n    }\r\n  },\r\n  methods: {\r\n    getProjectTypeLabel(type) {\r\n      const typeMap = {\r\n        'monthly': '月度养护',\r\n        'preventive': '预防养护',\r\n        'emergency': '应急养护',\r\n        'cleaning': '保洁项目'\r\n      }\r\n      return typeMap[type] || type\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.basic-info-view {\r\n  @extend .readonly-form;\r\n  \r\n  .attachment-list {\r\n    @extend .common-attachment-list;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4JA;EACAA,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,+CAAAC,QAAA,MAAAN,UAAA,CAAAO,WAAA;IACA;EACA;EACAC,OAAA;IACAC,mBAAA,WAAAA,oBAAAR,IAAA;MACA,IAAAS,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAT,IAAA,KAAAA,IAAA;IACA;EACA;AACA", "ignoreList": []}]}