{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue", "mtime": 1758810696266}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_repairs", "require", "_StatusTag", "_interopRequireDefault", "_EmergencyDetailDialog", "name", "components", "StatusTag", "EmergencyDetailDialog", "data", "loading", "activeTab", "emergencyList", "selectedEmergency", "showDetailDialog", "total", "queryParams", "pageNum", "pageSize", "bridgeName", "status", "diseaseType", "manager", "maintenanceUnit", "infrastructureType", "bridgeOptions", "managerOptions", "unitOptions", "statusOptions", "label", "value", "diseaseTypes", "created", "getList", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "p", "n", "getEmergencyRepairList", "v", "list", "$message", "error", "f", "a", "switchTab", "tab", "handleQuery", "reset<PERSON><PERSON>y", "handleView", "row", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["src/views/maintenance/repairs/emergency/index.vue"], "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题 -->\n        <div class=\"page-header\">\n          <h2>应急维修管理</h2>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'bridge' }\"\n              @click=\"switchTab('bridge')\"\n            >\n              <i class=\"el-icon-s-home\"></i>\n              桥梁养护维修\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tunnel' }\"\n              @click=\"switchTab('tunnel')\"\n            >\n              <i class=\"el-icon-place\"></i>\n              隧道养护维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级导航 -->\n        <div class=\"sub-navigation\">\n          <div class=\"sub-tab-container\">\n            <div \n              class=\"sub-tab-item\"\n              @click=\"$router.push('/maintenance/repairs')\"\n            >\n              养护/保洁项目\n            </div>\n            <div \n              class=\"sub-tab-item is-active\"\n            >\n              应急维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 筛选表单 -->\n        <div class=\"filter-form\">\n          <el-form :model=\"queryParams\" inline>\n            <el-form-item label=\"桥梁名称\">\n              <el-select\n                v-model=\"queryParams.bridgeName\"\n                placeholder=\"请选择桥梁\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"bridge in bridgeOptions\"\n                  :key=\"bridge.value\"\n                  :label=\"bridge.label\"\n                  :value=\"bridge.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"状态\">\n              <el-select\n                v-model=\"queryParams.status\"\n                placeholder=\"请选择状态\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"status in statusOptions\"\n                  :key=\"status.value\"\n                  :label=\"status.label\"\n                  :value=\"status.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"病害类型\">\n              <el-select\n                v-model=\"queryParams.diseaseType\"\n                placeholder=\"请选择类型\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"type in diseaseTypes\"\n                  :key=\"type.value\"\n                  :label=\"type.label\"\n                  :value=\"type.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"负责人\">\n              <el-select\n                v-model=\"queryParams.manager\"\n                placeholder=\"请选择负责人\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"manager in managerOptions\"\n                  :key=\"manager.value\"\n                  :label=\"manager.label\"\n                  :value=\"manager.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"养护单位\">\n              <el-select\n                v-model=\"queryParams.maintenanceUnit\"\n                placeholder=\"请选择单位\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"unit in unitOptions\"\n                  :key=\"unit.value\"\n                  :label=\"unit.label\"\n                  :value=\"unit.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item>\n              <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n              <el-button @click=\"resetQuery\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        \n        <!-- 数据表格 -->\n        <div class=\"table-container\">\n          <el-table\n            v-loading=\"loading\"\n            :data=\"emergencyList\"\n            class=\"maintenance-table\"\n          >\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n            \n            <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n            \n            <el-table-column prop=\"reporter\" label=\"上报人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"reportTime\" label=\"上报时间\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"contactPhone\" label=\"联系方式\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <status-tag :status=\"scope.row.status\" type=\"task\" />\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <span class=\"disease-count\">{{ scope.row.diseaseCount }}</span>\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"manager\" label=\"负责人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"maintenanceUnit\" label=\"养护单位\" min-width=\"150\" show-overflow-tooltip />\n            \n            <el-table-column label=\"操作\" width=\"80\" align=\"center\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"handleView(scope.row)\"\n                >\n                  查看\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <!-- 应急维修详情弹窗 -->\n    <emergency-detail-dialog\n      :visible.sync=\"showDetailDialog\"\n      :emergency-data=\"selectedEmergency\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getEmergencyRepairList } from '@/api/maintenance/repairs'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport EmergencyDetailDialog from './components/EmergencyDetailDialog'\n\nexport default {\n  name: 'MaintenanceEmergencyRepairs',\n  components: {\n    StatusTag,\n    EmergencyDetailDialog\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'bridge', // bridge, tunnel\n      emergencyList: [],\n      selectedEmergency: {},\n      showDetailDialog: false,\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: 'bridge'\n      },\n      \n      // 选项数据\n      bridgeOptions: [],\n      managerOptions: [],\n      unitOptions: [],\n      \n      // 状态选项\n      statusOptions: [\n        { label: '待处理', value: 'pending' },\n        { label: '处理审批中', value: 'in_review' },\n        { label: '退回', value: 'rejected' },\n        { label: '已处理', value: 'completed' }\n      ],\n      \n      // 病害类型选项\n      diseaseTypes: [\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\n        { label: '照明设施缺失', value: 'lighting_missing' },\n        { label: '护栏损坏', value: 'guardrail_damage' },\n        { label: '桥面破损', value: 'deck_damage' },\n        { label: '排水不畅', value: 'drainage_poor' }\n      ]\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 获取应急维修列表\n    async getList() {\n      this.loading = true\n      try {\n        this.queryParams.infrastructureType = this.activeTab\n        \n        const response = await getEmergencyRepairList(this.queryParams)\n        this.emergencyList = response.data.list || []\n        this.total = response.data.total || 0\n        this.bridgeOptions = response.data.bridgeOptions || []\n        this.managerOptions = response.data.managerOptions || []\n        this.unitOptions = response.data.unitOptions || []\n      } catch (error) {\n        this.$message.error('获取应急维修列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: this.activeTab\n      }\n      this.getList()\n    },\n    \n    // 查看详情\n    handleView(row) {\n      this.selectedEmergency = row\n      this.showDetailDialog = true\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.sub-navigation {\n  @extend .common-secondary-navigation;\n  \n  .sub-tab-container {\n    @extend .sub-tab-container;\n  }\n}\n\n.filter-form {\n  padding: 24px;\n  background: #1e3a8a;\n  border-bottom: 1px solid #4b5563;\n}\n\n.table-container {\n  padding: 0 24px;\n  \n  .disease-count {\n    color: #3b82f6;\n    font-weight: bold;\n  }\n}\n\n.pagination-container {\n  padding: 16px 24px;\n  text-align: center;\n}\n</style>\n"], "mappings": ";;;;;;;;;AAuNA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,sBAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,qBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,KAAA;MAEA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,WAAA;QACAC,OAAA;QACAC,eAAA;QACAC,kBAAA;MACA;MAEA;MACAC,aAAA;MACAC,cAAA;MACAC,WAAA;MAEA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MAEA;MACAC,YAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cACAX,KAAA,CAAAzB,OAAA;cAAAkC,QAAA,CAAAC,CAAA;cAEAV,KAAA,CAAAnB,WAAA,CAAAQ,kBAAA,GAAAW,KAAA,CAAAxB,SAAA;cAAAiC,QAAA,CAAAE,CAAA;cAAA,OAEA,IAAAC,+BAAA,EAAAZ,KAAA,CAAAnB,WAAA;YAAA;cAAAyB,QAAA,GAAAG,QAAA,CAAAI,CAAA;cACAb,KAAA,CAAAvB,aAAA,GAAA6B,QAAA,CAAAhC,IAAA,CAAAwC,IAAA;cACAd,KAAA,CAAApB,KAAA,GAAA0B,QAAA,CAAAhC,IAAA,CAAAM,KAAA;cACAoB,KAAA,CAAAV,aAAA,GAAAgB,QAAA,CAAAhC,IAAA,CAAAgB,aAAA;cACAU,KAAA,CAAAT,cAAA,GAAAe,QAAA,CAAAhC,IAAA,CAAAiB,cAAA;cACAS,KAAA,CAAAR,WAAA,GAAAc,QAAA,CAAAhC,IAAA,CAAAkB,WAAA;cAAAiB,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAb,KAAA,CAAAe,QAAA,CAAAC,KAAA;YAAA;cAAAP,QAAA,CAAAC,CAAA;cAEAV,KAAA,CAAAzB,OAAA;cAAA,OAAAkC,QAAA,CAAAQ,CAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEA;IACAc,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAA5C,SAAA,GAAA4C,GAAA;MACA,KAAAvC,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IAEA;IACAuB,WAAA,WAAAA,YAAA;MACA,KAAAxC,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IAEA;IACAwB,UAAA,WAAAA,WAAA;MACA,KAAAzC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,WAAA;QACAC,OAAA;QACAC,eAAA;QACAC,kBAAA,OAAAb;MACA;MACA,KAAAsB,OAAA;IACA;IAEA;IACAyB,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA9C,iBAAA,GAAA8C,GAAA;MACA,KAAA7C,gBAAA;IACA;IAEA;IACA8C,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAA7C,WAAA,CAAAE,QAAA,GAAA2C,GAAA;MACA,KAAA5B,OAAA;IACA;IAEA;IACA6B,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAA7C,WAAA,CAAAC,OAAA,GAAA4C,GAAA;MACA,KAAA5B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}