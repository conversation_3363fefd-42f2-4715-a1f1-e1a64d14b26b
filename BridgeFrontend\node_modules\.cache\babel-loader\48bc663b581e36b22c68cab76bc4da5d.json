{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_DiseaseDetailDialog", "_interopRequireDefault", "require", "name", "components", "DiseaseDetailDialog", "props", "projectData", "type", "Object", "default", "data", "loading", "showDetailDialog", "selectedDisease", "filterForm", "bridgeName", "diseaseType", "status", "tableData", "id", "index", "diseaseCode", "disease<PERSON>art", "completionTime", "responsible", "bridgeOptions", "label", "value", "diseaseTypeOptions", "statusOptions", "computed", "totalCount", "length", "completedCount", "filter", "item", "mounted", "loadTableData", "methods", "handleSearch", "_this", "setTimeout", "console", "log", "$message", "success", "handleReset", "handleViewDetail", "row", "_this2"], "sources": ["src/views/maintenance/repairs/components/detail/DiseasesView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"diseases-view\">\r\n    <!-- 筛选条件 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-row\">\r\n        <el-select\r\n          v-model=\"filterForm.bridgeName\"\r\n          placeholder=\"桥梁名称\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"bridge in bridgeOptions\"\r\n            :key=\"bridge.value\"\r\n            :label=\"bridge.label\"\r\n            :value=\"bridge.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.diseaseType\"\r\n          placeholder=\"病害类型\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"type in diseaseTypeOptions\"\r\n            :key=\"type.value\"\r\n            :label=\"type.label\"\r\n            :value=\"type.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.status\"\r\n          placeholder=\"状态\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"status in statusOptions\"\r\n            :key=\"status.value\"\r\n            :label=\"status.label\"\r\n            :value=\"status.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <div class=\"filter-actions\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 完成量统计 -->\r\n    <div class=\"completion-stats\">\r\n      完成量 {{ completedCount }}/{{ totalCount }}\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        class=\"diseases-table\"\r\n        header-row-class-name=\"table-header\"\r\n        row-class-name=\"table-row\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column prop=\"index\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" />\r\n        <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\r\n        <el-table-column prop=\"diseasePart\" label=\"病害部位\" min-width=\"100\" />\r\n        <el-table-column prop=\"diseaseType\" label=\"病害类型\" min-width=\"120\" />\r\n        <el-table-column prop=\"completionTime\" label=\"完成时间\" width=\"160\" align=\"center\" />\r\n        <el-table-column prop=\"responsible\" label=\"负责人\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              link\r\n              @click=\"handleViewDetail(scope.row)\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 病害详情弹窗 -->\r\n    <DiseaseDetailDialog\r\n      v-model=\"showDetailDialog\"\r\n      :disease-data=\"selectedDisease\"\r\n      @refresh=\"loadTableData\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DiseaseDetailDialog from './DiseaseDetailDialog.vue'\r\n\r\nexport default {\r\n  name: 'DiseasesView',\r\n  components: {\r\n    DiseaseDetailDialog\r\n  },\r\n  props: {\r\n    projectData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      showDetailDialog: false,\r\n      selectedDisease: null,\r\n      filterForm: {\r\n        bridgeName: '',\r\n        diseaseType: '',\r\n        status: ''\r\n      },\r\n      tableData: [\r\n        {\r\n          id: 1,\r\n          index: 1,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '989',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '王景深',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 2,\r\n          index: 2,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '988',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '刘志强',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 3,\r\n          index: 3,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '987',\r\n          diseasePart: '照明设施',\r\n          diseaseType: '照明设施缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '赵临洲',\r\n          status: 'completed'\r\n        }\r\n      ],\r\n      bridgeOptions: [\r\n        { label: 'XXXXXX大桥', value: 'bridge1' },\r\n        { label: 'YYYYYY大桥', value: 'bridge2' },\r\n        { label: 'ZZZZZZ大桥', value: 'bridge3' }\r\n      ],\r\n      diseaseTypeOptions: [\r\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\r\n        { label: '照明设施缺失', value: 'lighting_missing' },\r\n        { label: '护栏损坏', value: 'guardrail_damage' },\r\n        { label: '路面破损', value: 'pavement_damage' }\r\n      ],\r\n      statusOptions: [\r\n        { label: '已完成', value: 'completed' },\r\n        { label: '进行中', value: 'in_progress' },\r\n        { label: '待处理', value: 'pending' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    totalCount() {\r\n      return this.tableData.length\r\n    },\r\n    completedCount() {\r\n      return this.tableData.filter(item => item.status === 'completed').length\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTableData()\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.loading = true\r\n      // 模拟搜索延迟\r\n      setTimeout(() => {\r\n        // 这里应该调用API进行搜索\r\n        console.log('搜索条件:', this.filterForm)\r\n        this.loading = false\r\n        this.$message.success('查询完成')\r\n      }, 1000)\r\n    },\r\n    handleReset() {\r\n      this.filterForm.bridgeName = ''\r\n      this.filterForm.diseaseType = ''\r\n      this.filterForm.status = ''\r\n      this.loadTableData()\r\n    },\r\n    handleViewDetail(row) {\r\n      this.selectedDisease = row\r\n      this.showDetailDialog = true\r\n    },\r\n    loadTableData() {\r\n      this.loading = true\r\n      // 模拟API调用\r\n      setTimeout(() => {\r\n        // 这里应该调用API获取数据\r\n        this.loading = false\r\n      }, 500)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.diseases-view {\r\n  padding: 20px;\r\n  color: #e5e7eb;\r\n\r\n  .filter-section {\r\n    margin-bottom: 20px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n\r\n      .filter-select {\r\n        width: 180px;\r\n\r\n        :deep(.el-input__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          .el-input__inner {\r\n            color: #e5e7eb;\r\n            background: transparent;\r\n\r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n          }\r\n        }\r\n\r\n        :deep(.el-select__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          &.is-focused {\r\n            border-color: #3b82f6;\r\n          }\r\n\r\n          .el-select__placeholder {\r\n            color: #9ca3af;\r\n          }\r\n\r\n          .el-select__selected-item {\r\n            color: #e5e7eb;\r\n          }\r\n\r\n          .el-select__caret {\r\n            color: #9ca3af;\r\n          }\r\n        }\r\n      }\r\n\r\n      .filter-actions {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .el-button {\r\n          &.el-button--primary {\r\n            background: #3b82f6;\r\n            border-color: #3b82f6;\r\n            color: #ffffff;\r\n\r\n            &:hover {\r\n              background: #2563eb;\r\n              border-color: #2563eb;\r\n            }\r\n          }\r\n\r\n          &:not(.el-button--primary) {\r\n            background: #374151;\r\n            border-color: #4b5563;\r\n            color: #e5e7eb;\r\n\r\n            &:hover {\r\n              background: #4b5563;\r\n              border-color: #6b7280;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .completion-stats {\r\n    margin-bottom: 16px;\r\n    font-size: 14px;\r\n    color: #d1d5db;\r\n  }\r\n\r\n  .table-section {\r\n    .diseases-table {\r\n      @extend .common-table;\r\n    }\r\n  }\r\n}\r\n\r\n// 下拉选项样式\r\n:deep(.el-select-dropdown) {\r\n  background: #374151;\r\n  border: 1px solid #4b5563;\r\n\r\n  .el-select-dropdown__item {\r\n    color: #e5e7eb;\r\n\r\n    &:hover {\r\n      background: #4b5563;\r\n    }\r\n\r\n    &.is-selected {\r\n      background: #3b82f6;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;AAmGA,IAAAA,oBAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,mBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACAC,SAAA,GACA;QACAC,EAAA;QACAC,KAAA;QACAL,UAAA;QACAM,WAAA;QACAC,WAAA;QACAN,WAAA;QACAO,cAAA;QACAC,WAAA;QACAP,MAAA;MACA,GACA;QACAE,EAAA;QACAC,KAAA;QACAL,UAAA;QACAM,WAAA;QACAC,WAAA;QACAN,WAAA;QACAO,cAAA;QACAC,WAAA;QACAP,MAAA;MACA,GACA;QACAE,EAAA;QACAC,KAAA;QACAL,UAAA;QACAM,WAAA;QACAC,WAAA;QACAN,WAAA;QACAO,cAAA;QACAC,WAAA;QACAP,MAAA;MACA,EACA;MACAQ,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,kBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,aAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAG,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAb,SAAA,CAAAc,MAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAAf,SAAA,CAAAgB,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlB,MAAA;MAAA,GAAAe,MAAA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAA7B,OAAA;MACA;MACA8B,UAAA;QACA;QACAC,OAAA,CAAAC,GAAA,UAAAH,KAAA,CAAA1B,UAAA;QACA0B,KAAA,CAAA7B,OAAA;QACA6B,KAAA,CAAAI,QAAA,CAAAC,OAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAhC,UAAA,CAAAC,UAAA;MACA,KAAAD,UAAA,CAAAE,WAAA;MACA,KAAAF,UAAA,CAAAG,MAAA;MACA,KAAAoB,aAAA;IACA;IACAU,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAnC,eAAA,GAAAmC,GAAA;MACA,KAAApC,gBAAA;IACA;IACAyB,aAAA,WAAAA,cAAA;MAAA,IAAAY,MAAA;MACA,KAAAtC,OAAA;MACA;MACA8B,UAAA;QACA;QACAQ,MAAA,CAAAtC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}