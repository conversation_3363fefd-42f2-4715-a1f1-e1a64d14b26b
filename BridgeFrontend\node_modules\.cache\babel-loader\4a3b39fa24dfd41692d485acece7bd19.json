{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\router\\module\\data-center.js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\router\\module\\data-center.js", "mtime": 1758804563518}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758366985497}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnZhciBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1dvcmsvcWlhby9CQi9CcmlkZ2VGcm9udGVuZC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIikpOwovKioNCiAqIOaVsOaNruS4reW/g+i3r+eUsemFjee9rg0KICogQHBhcmFtIHsqfSBCYXNlTGF5b3V0IOWfuuehgOW4g+WxgOe7hOS7tg0KICogQHBhcmFtIHsqfSBMYXlvdXQg6I+c5Y2V5biD5bGA57uE5Lu2ICANCiAqLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBmdW5jdGlvbiBfZGVmYXVsdChCYXNlTGF5b3V0LCBMYXlvdXQpIHsKICByZXR1cm4gW3sKICAgIG5hbWU6ICdEYXRhQ2VudGVyJywKICAgIHBhdGg6ICcvZGF0YS1jZW50ZXInLAogICAgaGlkZGVuOiBmYWxzZSwKICAgIHJlZGlyZWN0OiAnL2RhdGEtY2VudGVyL2NvcnJlbGF0aW9uLWFuYWx5c2lzJywKICAgIGNvbXBvbmVudDogTGF5b3V0LAogICAgYWx3YXlzU2hvdzogdHJ1ZSwKICAgIG1ldGE6IHsKICAgICAgdGl0bGU6ICfmlbDmja7kuK3lv4MnLAogICAgICBpY29uOiAnc3lzdGVtJywKICAgICAgbm9DYWNoZTogZmFsc2UsCiAgICAgIGxpbms6IG51bGwKICAgIH0sCiAgICBjaGlsZHJlbjogW3sKICAgICAgcGF0aDogJ2NvcnJlbGF0aW9uLWFuYWx5c2lzJywKICAgICAgY29tcG9uZW50OiBmdW5jdGlvbiBjb21wb25lbnQoKSB7CiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgcmV0dXJuICgwLCBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZDIuZGVmYXVsdCkocmVxdWlyZSgnQC92aWV3cy9kYXRhLWNlbnRlci9jb3JyZWxhdGlvbi1hbmFseXNpcy9pbmRleCcpKTsKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgbmFtZTogJ0NvcnJlbGF0aW9uQW5hbHlzaXMnLAogICAgICBtZXRhOiB7CiAgICAgICAgdGl0bGU6ICflhbPogZTmgKfliIbmnpAnCiAgICAgIH0KICAgIH1dCiAgfV07Cn07"}, {"version": 3, "names": ["_default", "exports", "default", "BaseLayout", "Layout", "name", "path", "hidden", "redirect", "component", "alwaysShow", "meta", "title", "icon", "noCache", "link", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "require"], "sources": ["D:/Work/qiao/BB/BridgeFrontend/src/router/module/data-center.js"], "sourcesContent": ["/**\r\n * 数据中心路由配置\r\n * @param {*} BaseLayout 基础布局组件\r\n * @param {*} Layout 菜单布局组件  \r\n */\r\nexport default (BaseLayout, Layout) => {\r\n  return [\r\n    {\r\n      name: 'DataCenter',\r\n      path: '/data-center',\r\n      hidden: false,\r\n      redirect: '/data-center/correlation-analysis',\r\n      component: Layout,\r\n      alwaysShow: true,\r\n      meta: {\r\n        title: '数据中心',\r\n        icon: 'system',\r\n        noCache: false,\r\n        link: null\r\n      },\r\n      children: [\r\n        {\r\n          path: 'correlation-analysis',\r\n          component: () => import('@/views/data-center/correlation-analysis/index'),\r\n          name: 'CorrelationAnalysis',\r\n          meta: { title: '关联性分析' }\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAJA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKe,SAAAF,SAACG,UAAU,EAAEC,MAAM,EAAK;EACrC,OAAO,CACL;IACEC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,mCAAmC;IAC7CC,SAAS,EAAEL,MAAM;IACjBM,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE;MACJC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,CACR;MACEV,IAAI,EAAE,sBAAsB;MAC5BG,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAAQ,OAAA,CAAAC,OAAA,GAAAC,IAAA;UAAA,WAAAC,wBAAA,CAAAlB,OAAA,EAAAmB,OAAA,CAAe,gDAAgD;QAAA;MAAA,CAAC;MACzEhB,IAAI,EAAE,qBAAqB;MAC3BM,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAQ;IACzB,CAAC;EAEL,CAAC,CACF;AACH,CAAC", "ignoreList": []}]}