{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue", "mtime": 1758811136136}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "value", "type", "Object", "default", "projectType", "String", "required", "readonly", "Boolean", "data", "projectList", "watch", "handler", "newVal", "projects", "Array", "isArray", "length", "JSON", "stringify", "_toConsumableArray2", "immediate", "deep", "newType", "oldType", "_this", "$nextTick", "initializeProject", "emitChange", "mounted", "methods", "_this2", "$emit", "newProject", "frequency", "addProject", "push", "removeProject", "index", "splice", "$message", "warning", "validateProject", "project", "duplicateIndex", "findIndex", "p", "i", "getPlaceholder", "placeholders", "monthly", "cleaning", "emergency", "preventive", "validate", "validProjects", "filter", "trim", "error", "invalidFrequency", "some"], "sources": ["src/views/maintenance/projects/create/components/ProjectConfig.vue"], "sourcesContent": ["<template>\r\n  <div class=\"project-config\">\r\n    <!-- 添加项目按钮 - 按原型图结构 -->\r\n    <div v-if=\"!readonly\" class=\"add-project-row\">\r\n      <el-button \r\n        type=\"primary\" \r\n        icon=\"el-icon-plus\"\r\n        class=\"add-project-btn\"\r\n        @click=\"addProject\"\r\n      >\r\n        添加项目\r\n      </el-button>\r\n    </div>\r\n    \r\n    <!-- 项目列表 - 按原型图每行一个项目的结构 -->\r\n    <div class=\"project-list\">\r\n      <div \r\n        v-for=\"(project, index) in projectList\" \r\n        :key=\"index\"\r\n        class=\"project-row\"\r\n      >\r\n        <div class=\"project-input-container\">\r\n          <el-input\r\n            v-model=\"project.name\"\r\n            :placeholder=\"getPlaceholder()\"\r\n            class=\"project-name-input\"\r\n            :disabled=\"readonly\"\r\n            @blur=\"validateProject(index)\"\r\n            @input=\"emitChange\"\r\n          />\r\n          \r\n          <!-- 保洁项目的频次配置 - 按原型图布局 -->\r\n          <template v-if=\"projectType === 'cleaning'\">\r\n            <el-input\r\n              v-model=\"project.frequency\"\r\n              type=\"number\"\r\n              min=\"1\"\r\n              max=\"365\"\r\n              class=\"frequency-input\"\r\n              :disabled=\"readonly\"\r\n              @input=\"emitChange\"\r\n            />\r\n            <span class=\"frequency-unit\">天/1次</span>\r\n          </template>\r\n        </div>\r\n        \r\n        <el-button\r\n          v-if=\"!readonly\"\r\n          type=\"text\"\r\n          class=\"cancel-btn\"\r\n          @click=\"removeProject(index)\"\r\n        >\r\n          取消\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProjectConfig',\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectType: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      projectList: []\r\n    }\r\n  },\r\n  watch: {\r\n    // 只监听外部传入的value，单向数据流\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && newVal.projects && Array.isArray(newVal.projects) && newVal.projects.length > 0) {\r\n          // 只在数据真正不同时才更新，避免循环\r\n          if (JSON.stringify(newVal.projects) !== JSON.stringify(this.projectList)) {\r\n            this.projectList = [...newVal.projects]\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    // 监听项目类型变化，重新初始化\r\n    projectType: {\r\n      handler(newType, oldType) {\r\n        if (newType !== oldType && this.projectList.length > 0) {\r\n          this.projectList = []\r\n          this.$nextTick(() => {\r\n            this.initializeProject()\r\n            this.emitChange()\r\n          })\r\n        }\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时确保至少有一个空项目\r\n    if (this.projectList.length === 0) {\r\n      this.initializeProject()\r\n      this.emitChange()\r\n    }\r\n  },\r\n  methods: {\r\n    // 统一的数据更新方法\r\n    emitChange() {\r\n      this.$nextTick(() => {\r\n        this.$emit('input', {\r\n          projects: this.projectList\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 初始化项目（仅在组件初始化时使用）\r\n    initializeProject() {\r\n      if (this.projectList.length === 0) {\r\n        const newProject = {\r\n          name: '',\r\n          frequency: this.projectType === 'cleaning' ? 7 : null\r\n        }\r\n        this.projectList = [newProject]\r\n      }\r\n    },\r\n    \r\n    // 添加项目（用户点击按钮时使用）\r\n    addProject() {\r\n      const newProject = {\r\n        name: '',\r\n        frequency: this.projectType === 'cleaning' ? 7 : null\r\n      }\r\n      this.projectList.push(newProject)\r\n      this.emitChange()\r\n    },\r\n    \r\n    // 移除项目\r\n    removeProject(index) {\r\n      if (this.projectList.length > 1) {\r\n        this.projectList.splice(index, 1)\r\n        this.emitChange()\r\n      } else {\r\n        this.$message.warning('至少需要保留一个项目')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证项目\r\n    validateProject(index) {\r\n      const project = this.projectList[index]\r\n      if (!project.name) {\r\n        return\r\n      }\r\n      \r\n      // 检查重复\r\n      const duplicateIndex = this.projectList.findIndex((p, i) => \r\n        i !== index && p.name === project.name\r\n      )\r\n      \r\n      if (duplicateIndex !== -1) {\r\n        this.$message.warning('项目名称不能重复')\r\n        project.name = ''\r\n      }\r\n    },\r\n    \r\n    // 获取输入框占位符\r\n    getPlaceholder() {\r\n      const placeholders = {\r\n        monthly: '请输入养护项目名称',\r\n        cleaning: '请输入保洁项目名称',\r\n        emergency: '请输入应急项目名称',\r\n        preventive: '请输入预防养护项目名称'\r\n      }\r\n      return placeholders[this.projectType] || '请输入项目名称'\r\n    },\r\n    \r\n    \r\n    // 表单验证\r\n    validate() {\r\n      // 检查是否有有效项目\r\n      const validProjects = this.projectList.filter(project => project.name.trim())\r\n      \r\n      if (validProjects.length === 0) {\r\n        this.$message.error('请至少添加一个项目')\r\n        return false\r\n      }\r\n      \r\n      // 检查保洁项目的频次\r\n      if (this.projectType === 'cleaning') {\r\n        const invalidFrequency = validProjects.some(project => \r\n          !project.frequency || project.frequency < 1 || project.frequency > 365\r\n        )\r\n        \r\n        if (invalidFrequency) {\r\n          this.$message.error('请设置正确的清洁频次（1-365天）')\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n\r\n.project-config {\r\n  @extend .common-project-config;\r\n  padding: 20px;\r\n  .add-project-row {\r\n    margin-bottom: 24px; // 增加与下方内容的间距\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    \r\n    // 按钮样式已通过公共样式提供\r\n  }\r\n  \r\n  // 项目列表区域\r\n  .project-list {\r\n    .project-row {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      gap: 12px; // 设置各元素之间的间距\r\n      \r\n      .project-input-container {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        \r\n        .project-name-input {\r\n          flex: 1;\r\n          min-width: 200px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            \r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 保洁项目的频次输入框\r\n        .frequency-input {\r\n          width: 80px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            text-align: center;\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        .frequency-unit {\r\n          color: #e5e7eb;\r\n          font-size: 14px;\r\n          white-space: nowrap;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n      \r\n      .cancel-btn {\r\n        color: #ef4444;\r\n        font-size: 14px;\r\n        min-width: 60px;\r\n        height: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        \r\n        &:hover {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n        \r\n        &:focus {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 确保最后一行不会有多余的margin\r\n    .project-row:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  // 只读模式下的样式调整\r\n  &.readonly {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n      \r\n      .frequency-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 确保在深色主题下有良好的视觉效果\r\n.maintenance-theme .project-config,\r\n.inspection-theme .project-config {\r\n  .add-project-btn {\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  }\r\n  \r\n  .project-row {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner),\r\n      .frequency-input :deep(.el-input__inner) {\r\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA4DA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACA;IACAX,KAAA;MACAY,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,QAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAH,MAAA,CAAAC,QAAA,KAAAD,MAAA,CAAAC,QAAA,CAAAG,MAAA;UACA;UACA,IAAAC,IAAA,CAAAC,SAAA,CAAAN,MAAA,CAAAC,QAAA,MAAAI,IAAA,CAAAC,SAAA,MAAAT,WAAA;YACA,KAAAA,WAAA,OAAAU,mBAAA,CAAAjB,OAAA,EAAAU,MAAA,CAAAC,QAAA;UACA;QACA;MACA;MACAO,SAAA;MACAC,IAAA;IACA;IAEA;IACAlB,WAAA;MACAQ,OAAA,WAAAA,QAAAW,OAAA,EAAAC,OAAA;QAAA,IAAAC,KAAA;QACA,IAAAF,OAAA,KAAAC,OAAA,SAAAd,WAAA,CAAAO,MAAA;UACA,KAAAP,WAAA;UACA,KAAAgB,SAAA;YACAD,KAAA,CAAAE,iBAAA;YACAF,KAAA,CAAAG,UAAA;UACA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,SAAAnB,WAAA,CAAAO,MAAA;MACA,KAAAU,iBAAA;MACA,KAAAC,UAAA;IACA;EACA;EACAE,OAAA;IACA;IACAF,UAAA,WAAAA,WAAA;MAAA,IAAAG,MAAA;MACA,KAAAL,SAAA;QACAK,MAAA,CAAAC,KAAA;UACAlB,QAAA,EAAAiB,MAAA,CAAArB;QACA;MACA;IACA;IAEA;IACAiB,iBAAA,WAAAA,kBAAA;MACA,SAAAjB,WAAA,CAAAO,MAAA;QACA,IAAAgB,UAAA;UACAnC,IAAA;UACAoC,SAAA,OAAA9B,WAAA;QACA;QACA,KAAAM,WAAA,IAAAuB,UAAA;MACA;IACA;IAEA;IACAE,UAAA,WAAAA,WAAA;MACA,IAAAF,UAAA;QACAnC,IAAA;QACAoC,SAAA,OAAA9B,WAAA;MACA;MACA,KAAAM,WAAA,CAAA0B,IAAA,CAAAH,UAAA;MACA,KAAAL,UAAA;IACA;IAEA;IACAS,aAAA,WAAAA,cAAAC,KAAA;MACA,SAAA5B,WAAA,CAAAO,MAAA;QACA,KAAAP,WAAA,CAAA6B,MAAA,CAAAD,KAAA;QACA,KAAAV,UAAA;MACA;QACA,KAAAY,QAAA,CAAAC,OAAA;MACA;IACA;IAGA;IACAC,eAAA,WAAAA,gBAAAJ,KAAA;MACA,IAAAK,OAAA,QAAAjC,WAAA,CAAA4B,KAAA;MACA,KAAAK,OAAA,CAAA7C,IAAA;QACA;MACA;;MAEA;MACA,IAAA8C,cAAA,QAAAlC,WAAA,CAAAmC,SAAA,WAAAC,CAAA,EAAAC,CAAA;QAAA,OACAA,CAAA,KAAAT,KAAA,IAAAQ,CAAA,CAAAhD,IAAA,KAAA6C,OAAA,CAAA7C,IAAA;MAAA,CACA;MAEA,IAAA8C,cAAA;QACA,KAAAJ,QAAA,CAAAC,OAAA;QACAE,OAAA,CAAA7C,IAAA;MACA;IACA;IAEA;IACAkD,cAAA,WAAAA,eAAA;MACA,IAAAC,YAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACA,OAAAJ,YAAA,MAAA7C,WAAA;IACA;IAGA;IACAkD,QAAA,WAAAA,SAAA;MACA;MACA,IAAAC,aAAA,QAAA7C,WAAA,CAAA8C,MAAA,WAAAb,OAAA;QAAA,OAAAA,OAAA,CAAA7C,IAAA,CAAA2D,IAAA;MAAA;MAEA,IAAAF,aAAA,CAAAtC,MAAA;QACA,KAAAuB,QAAA,CAAAkB,KAAA;QACA;MACA;;MAEA;MACA,SAAAtD,WAAA;QACA,IAAAuD,gBAAA,GAAAJ,aAAA,CAAAK,IAAA,WAAAjB,OAAA;UAAA,OACA,CAAAA,OAAA,CAAAT,SAAA,IAAAS,OAAA,CAAAT,SAAA,QAAAS,OAAA,CAAAT,SAAA;QAAA,CACA;QAEA,IAAAyB,gBAAA;UACA,KAAAnB,QAAA,CAAAkB,KAAA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}