{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue", "mtime": 1758809365043}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfcmVnZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9Xb3JrL3FpYW8vQkIvQnJpZGdlRnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3IuanMiKSk7CnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1dvcmsvcWlhby9CQi9CcmlkZ2VGcm9udGVuZC9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIik7CnZhciBfcHJvamVjdHMgPSByZXF1aXJlKCJAL2FwaS9tYWludGVuYW5jZS9wcm9qZWN0cyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQyID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdBcHByb3ZhbEluZm8nLAogIGNvbXBvbmVudHM6IHt9LAogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfSwKICAgIHByb2plY3RJZDogewogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgLy8g5piv5ZCm5pi+56S65a6h5om56KGo5Y2V77yI5a6h5om55qih5byPIHZzIOafpeeci+aooeW8j++8iQogICAgc2hvd0FwcHJvdmFsRm9ybTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDlrqHmibnljoblj7LorrDlvZUKICAgICAgYXBwcm92YWxIaXN0b3J5OiBbXSwKICAgICAgLy8g5a6h5om56KGo5Y2V5pWw5o2uCiAgICAgIGFwcHJvdmFsRm9ybURhdGE6IHsKICAgICAgICBjb21tZW50OiAnJwogICAgICB9CiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuacieWuoeaJueWOhuWPsuiusOW9lQogICAgaGFzQXBwcm92YWxIaXN0b3J5OiBmdW5jdGlvbiBoYXNBcHByb3ZhbEhpc3RvcnkoKSB7CiAgICAgIHJldHVybiB0aGlzLmFwcHJvdmFsSGlzdG9yeSAmJiB0aGlzLmFwcHJvdmFsSGlzdG9yeS5sZW5ndGggPiAwOwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCAmJiBPYmplY3Qua2V5cyhuZXdWYWwpLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMuaW5pdGlhbGl6ZURhdGEobmV3VmFsKTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgIHByb2plY3RJZDogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICAgIHRoaXMubG9hZEFwcHJvdmFsSGlzdG9yeSgpOwogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJbmlbDmja4KICAgIGluaXRpYWxpemVEYXRhOiBmdW5jdGlvbiBpbml0aWFsaXplRGF0YShkYXRhKSB7CiAgICAgIGlmIChkYXRhICYmIGRhdGEuYXBwcm92YWxIaXN0b3J5KSB7CiAgICAgICAgdGhpcy5hcHByb3ZhbEhpc3RvcnkgPSBkYXRhLmFwcHJvdmFsSGlzdG9yeTsKICAgICAgfQogICAgfSwKICAgIC8vIOWKoOi9veWuoeaJueWOhuWPsgogICAgbG9hZEFwcHJvdmFsSGlzdG9yeTogZnVuY3Rpb24gbG9hZEFwcHJvdmFsSGlzdG9yeSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciByZXNwb25zZSwgX3Q7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnAgPSBfY29udGV4dC5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoX3RoaXMucHJvamVjdElkKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uID0gMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSAxOwogICAgICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3Byb2plY3RzLmdldFByb2plY3RBcHByb3ZhbEhpc3RvcnkpKF90aGlzLnByb2plY3RJZCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0LnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgX3RoaXMuYXBwcm92YWxIaXN0b3J5ID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQubiA9IDQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfY29udGV4dC5wID0gMzsKICAgICAgICAgICAgICBfdCA9IF9jb250ZXh0LnY7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295a6h5om55Y6G5Y+y5aSx6LSlOicsIF90KTsKICAgICAgICAgICAgICBfdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295a6h5om55Y6G5Y+y5aSx6LSlJyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dC5wID0gNDsKICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmYoNCk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzEsIDMsIDQsIDVdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOWFrOWFseaWueazle+8muWuoeaJuemAmui/h++8iOS+m+eItue7hOS7tuiwg+eUqO+8iQogICAgYXBwcm92ZVByb2plY3Q6IGZ1bmN0aW9uIGFwcHJvdmVQcm9qZWN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICB2YXIgcmVzcG9uc2UsIF90MjsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wID0gX2NvbnRleHQyLm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGlmIChfdGhpczIuYXBwcm92YWxGb3JtRGF0YS5jb21tZW50LnRyaW0oKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcign6K+36L6T5YWl5aSE55CG5oSP6KeBJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5hKDIsIGZhbHNlKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMTsKICAgICAgICAgICAgICBfY29udGV4dDIubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMi4kY29uZmlybSgn56Gu6K6k6YCa6L+H6K+l55Sz6K+377yfJywgJ+ehruiupOaTjeS9nCcsIHsKICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfdGhpczIubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3Byb2plY3RzLmFwcHJvdmVQcm9qZWN0KShfdGhpczIucHJvamVjdElkLCB7CiAgICAgICAgICAgICAgICByZXN1bHQ6ICdhcHByb3ZlZCcsCiAgICAgICAgICAgICAgICBjb21tZW50OiBfdGhpczIuYXBwcm92YWxGb3JtRGF0YS5jb21tZW50CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0Mi52OwogICAgICAgICAgICAgIGlmICghKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uuc3VjY2Vzcygn5a6h5om56YCa6L+H5oiQ5YqfJyk7CiAgICAgICAgICAgICAgX3RoaXMyLiRlbWl0KCdhcHByb3ZhbC1zdWJtaXR0ZWQnLCB7CiAgICAgICAgICAgICAgICByZXN1bHQ6ICdhcHByb3ZlZCcsCiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX3RoaXMyLmFwcHJvdmFsRm9ybURhdGEuY29tbWVudCA9ICcnOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gNDsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmxvYWRBcHByb3ZhbEhpc3RvcnkoKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyLCB0cnVlKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmk43kvZzlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gODsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gNzsKICAgICAgICAgICAgICBfdDIgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICBpZiAoX3QyLm1lc3NhZ2UgIT09ICdjYW5jZWwnKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCflrqHmibnlpLHotKU6JywgX3QyKTsKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5lcnJvcihfdDIubWVzc2FnZSB8fCAn5a6h5om55aSx6LSlJyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuYSgyLCBmYWxzZSk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfY29udGV4dDIucCA9IDg7CiAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmYoOCk7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMSwgNywgOCwgOV1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5YWs5YWx5pa55rOV77ya5a6h5om56YCA5Zue77yI5L6b54i257uE5Lu26LCD55So77yJCiAgICByZWplY3RQcm9qZWN0OiBmdW5jdGlvbiByZWplY3RQcm9qZWN0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgcmVzcG9uc2UsIF90MzsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wID0gX2NvbnRleHQzLm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGlmIChfdGhpczMuYXBwcm92YWxGb3JtRGF0YS5jb21tZW50LnRyaW0oKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcign6K+36L6T5YWl5aSE55CG5oSP6KeBJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIsIGZhbHNlKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF9jb250ZXh0My5wID0gMTsKICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy4kY29uZmlybSgn56Gu6K6k6YCA5Zue6K+l55Sz6K+377yfJywgJ+ehruiupOaTjeS9nCcsIHsKICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu6K6kJywKICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfdGhpczMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3Byb2plY3RzLmFwcHJvdmVQcm9qZWN0KShfdGhpczMucHJvamVjdElkLCB7CiAgICAgICAgICAgICAgICByZXN1bHQ6ICdyZXR1cm5lZCcsCiAgICAgICAgICAgICAgICBjb21tZW50OiBfdGhpczMuYXBwcm92YWxGb3JtRGF0YS5jb21tZW50CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIGlmICghKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2Vzcygn6YCA5Zue5oiQ5YqfJyk7CiAgICAgICAgICAgICAgX3RoaXMzLiRlbWl0KCdhcHByb3ZhbC1zdWJtaXR0ZWQnLCB7CiAgICAgICAgICAgICAgICByZXN1bHQ6ICdyZXR1cm5lZCcsCiAgICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX3RoaXMzLmFwcHJvdmFsRm9ybURhdGEuY29tbWVudCA9ICcnOwogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNDsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzLmxvYWRBcHByb3ZhbEhpc3RvcnkoKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuYSgyLCB0cnVlKTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5tZXNzYWdlIHx8ICfmk43kvZzlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gODsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0My5wID0gNzsKICAgICAgICAgICAgICBfdDMgPSBfY29udGV4dDMudjsKICAgICAgICAgICAgICBpZiAoX3QzLm1lc3NhZ2UgIT09ICdjYW5jZWwnKSB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfpgIDlm57lpLHotKU6JywgX3QzKTsKICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihfdDMubWVzc2FnZSB8fCAn6YCA5Zue5aSx6LSlJyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuYSgyLCBmYWxzZSk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfY29udGV4dDMucCA9IDg7CiAgICAgICAgICAgICAgX3RoaXMzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmYoOCk7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMsIG51bGwsIFtbMSwgNywgOCwgOV1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g6aqM6K+B5pa55rOV77yI5Li65LqG5L+d5oyB5o6l5Y+j5LiA6Ie05oCn77yJCiAgICB2YWxpZGF0ZTogZnVuY3Rpb24gdmFsaWRhdGUoKSB7CiAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUodHJ1ZSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_projects", "require", "name", "components", "props", "value", "type", "Object", "default", "projectId", "String", "Number", "showApprovalForm", "Boolean", "data", "loading", "approvalHistory", "approvalFormData", "comment", "computed", "hasApprovalHistory", "length", "watch", "handler", "newVal", "keys", "initializeData", "immediate", "deep", "loadApprovalHistory", "methods", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "p", "n", "a", "getProjectApprovalHistory", "v", "code", "console", "error", "$message", "f", "approveProject", "_this2", "_callee2", "_t2", "_context2", "trim", "$confirm", "confirmButtonText", "cancelButtonText", "result", "success", "$emit", "Error", "message", "rejectProject", "_this3", "_callee3", "_t3", "_context3", "validate", "Promise", "resolve"], "sources": ["src/views/maintenance/projects/create/components/ApprovalInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"approval-info\">\r\n    <!-- 审批记录 -->\r\n    <div class=\"approval-records\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        审批记录\r\n      </div>\r\n      \r\n      <el-table\r\n        :data=\"approvalHistory\"\r\n        class=\"approval-table\"\r\n        border\r\n      >\r\n        <el-table-column\r\n          prop=\"id\"\r\n          label=\"序号\"\r\n          min-width=\"60\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"stepName\"\r\n          label=\"审批环节\"\r\n          min-width=\"140\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"approver\"\r\n          label=\"处理人\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"审批状态\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"comment\"\r\n          label=\"审批意见\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"department\"\r\n          label=\"处理人部门\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"receiveTime\"\r\n          label=\"接收时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"processTime\"\r\n          label=\"办理时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n      </el-table>\r\n    </div>\r\n    \r\n    <!-- 无审批记录时显示提示 -->\r\n    <div v-if=\"!hasApprovalHistory\" class=\"no-data\">\r\n      <p>暂无审批记录</p>\r\n    </div>\r\n    \r\n    <!-- 审批处理（仅在审批模式下显示） -->\r\n    <div v-if=\"showApprovalForm\" class=\"approval-process\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-edit\"></i>\r\n        审批处理\r\n      </div>\r\n      \r\n      <div class=\"process-form\">\r\n        <div class=\"comment-label\">*处理意见:</div>\r\n        <el-input\r\n          v-model=\"approvalFormData.comment\"\r\n          type=\"textarea\"\r\n          :rows=\"6\"\r\n          placeholder=\"请输入\"\r\n          class=\"comment-input\"\r\n        />\r\n      </div>\r\n      \r\n    </div>\r\n    \r\n    <!-- 注意：审批信息组件内不显示任何操作按钮，所有按钮都统一在父组件中控制 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { approveProject, getProjectApprovalHistory } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ApprovalInfo',\r\n  components: {\r\n  },\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    // 是否显示审批表单（审批模式 vs 查看模式）\r\n    showApprovalForm: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      \r\n      // 审批历史记录\r\n      approvalHistory: [],\r\n      \r\n      // 审批表单数据\r\n      approvalFormData: {\r\n        comment: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否有审批历史记录\r\n    hasApprovalHistory() {\r\n      return this.approvalHistory && this.approvalHistory.length > 0\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          this.initializeData(newVal)\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    projectId: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.loadApprovalHistory()\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    initializeData(data) {\r\n      if (data && data.approvalHistory) {\r\n        this.approvalHistory = data.approvalHistory\r\n      }\r\n    },\r\n    \r\n    // 加载审批历史\r\n    async loadApprovalHistory() {\r\n      if (!this.projectId) return\r\n      \r\n      try {\r\n        this.loading = true\r\n        const response = await getProjectApprovalHistory(this.projectId)\r\n        if (response.code === 200) {\r\n          this.approvalHistory = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载审批历史失败:', error)\r\n        this.$message.error('加载审批历史失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批通过（供父组件调用）\r\n    async approveProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认通过该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'approved',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('审批通过成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'approved',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('审批失败:', error)\r\n          this.$message.error(error.message || '审批失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批退回（供父组件调用）\r\n    async rejectProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认退回该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'returned',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('退回成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'returned',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('退回失败:', error)\r\n          this.$message.error(error.message || '退回失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证方法（为了保持接口一致性）\r\n    validate() {\r\n      return Promise.resolve(true)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.approval-info {\r\n  color: #ffffff;\r\n  \r\n  \r\n  // 无数据提示\r\n  .no-data {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n    color: #9ca3af;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .section-title {\r\n    color: #ffffff;\r\n    font-size: 16px;\r\n    font-weight: normal;\r\n    margin-bottom: 20px;\r\n    text-align: left;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      color: #409eff;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  \r\n  // 审批记录表格\r\n  .approval-records {\r\n    margin-bottom: 32px;\r\n    \r\n    .approval-table {\r\n      width: 100%;\r\n      \r\n      :deep(.el-table) {\r\n        background: transparent;\r\n        color: #ffffff;\r\n        width: 100%;\r\n        \r\n        th {\r\n          background: rgba(255, 255, 255, 0.05);\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          font-weight: normal;\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        td {\r\n          background: transparent;\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          white-space: pre-line; // 支持换行显示\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        &::before {\r\n          background: rgba(255, 255, 255, 0.1);\r\n        }\r\n        \r\n        .el-table__empty-block {\r\n          background: transparent;\r\n          color: #9ca3af;\r\n        }\r\n        \r\n        // 确保表格占满容器宽度\r\n        .el-table__header-wrapper,\r\n        .el-table__body-wrapper {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 审批处理区域\r\n  .approval-process {\r\n    margin-bottom: 32px;\r\n    \r\n    .process-form {\r\n      margin-bottom: 24px;\r\n      \r\n      .comment-label {\r\n        color: #ffffff;\r\n        margin-bottom: 8px;\r\n        font-size: 14px;\r\n      }\r\n      \r\n      .comment-input {\r\n        :deep(.el-textarea__inner) {\r\n          background: #374151;\r\n          border: 1px solid #9ca3af;\r\n          color: #ffffff;\r\n          \r\n          &::placeholder {\r\n            color: #9ca3af;\r\n          }\r\n          \r\n          &:focus {\r\n            border-color: #409eff;\r\n            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;AAsGA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,UAAA,GACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,SAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAH,OAAA;IACA;IACA;IACAI,gBAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MAEA;MACAC,eAAA;MAEA;MACAC,gBAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,YAAAJ,eAAA,SAAAA,eAAA,CAAAK,MAAA;IACA;EACA;EACAC,KAAA;IACAjB,KAAA;MACAkB,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAjB,MAAA,CAAAkB,IAAA,CAAAD,MAAA,EAAAH,MAAA;UACA,KAAAK,cAAA,CAAAF,MAAA;QACA;MACA;MACAG,SAAA;MACAC,IAAA;IACA;IAEAnB,SAAA;MACAc,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAK,mBAAA;QACA;MACA;MACAF,SAAA;IACA;EACA;EACAG,OAAA;IACA;IACAJ,cAAA,WAAAA,eAAAZ,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAE,eAAA;QACA,KAAAA,eAAA,GAAAF,IAAA,CAAAE,eAAA;MACA;IACA;IAEA;IACAa,mBAAA,WAAAA,oBAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAxB,OAAA,mBAAAyB,aAAA,CAAAzB,OAAA,IAAA0B,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAzB,OAAA,IAAA8B,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAA,IACAV,KAAA,CAAAtB,SAAA;gBAAA8B,QAAA,CAAAE,CAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAG,CAAA;YAAA;cAAAH,QAAA,CAAAC,CAAA;cAGAT,KAAA,CAAAhB,OAAA;cAAAwB,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAE,mCAAA,EAAAZ,KAAA,CAAAtB,SAAA;YAAA;cAAA2B,QAAA,GAAAG,QAAA,CAAAK,CAAA;cACA,IAAAR,QAAA,CAAAS,IAAA;gBACAd,KAAA,CAAAf,eAAA,GAAAoB,QAAA,CAAAtB,IAAA;cACA;cAAAyB,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAK,CAAA;cAEAE,OAAA,CAAAC,KAAA,cAAAV,EAAA;cACAN,KAAA,CAAAiB,QAAA,CAAAD,KAAA;YAAA;cAAAR,QAAA,CAAAC,CAAA;cAEAT,KAAA,CAAAhB,OAAA;cAAA,OAAAwB,QAAA,CAAAU,CAAA;YAAA;cAAA,OAAAV,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IAEA;IAEA;IACAe,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAnB,kBAAA,CAAAxB,OAAA,mBAAAyB,aAAA,CAAAzB,OAAA,IAAA0B,CAAA,UAAAkB,SAAA;QAAA,IAAAhB,QAAA,EAAAiB,GAAA;QAAA,WAAApB,aAAA,CAAAzB,OAAA,IAAA8B,CAAA,WAAAgB,SAAA;UAAA,kBAAAA,SAAA,CAAAd,CAAA,GAAAc,SAAA,CAAAb,CAAA;YAAA;cAAA,IACAU,MAAA,CAAAlC,gBAAA,CAAAC,OAAA,CAAAqC,IAAA;gBAAAD,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACAU,MAAA,CAAAH,QAAA,CAAAD,KAAA;cAAA,OAAAO,SAAA,CAAAZ,CAAA,IACA;YAAA;cAAAY,SAAA,CAAAd,CAAA;cAAAc,SAAA,CAAAb,CAAA;cAAA,OAIAU,MAAA,CAAAK,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACApD,IAAA;cACA;YAAA;cAEA6C,MAAA,CAAApC,OAAA;cAAAuC,SAAA,CAAAb,CAAA;cAAA,OAEA,IAAAS,wBAAA,EAAAC,MAAA,CAAA1C,SAAA;gBACAkD,MAAA;gBACAzC,OAAA,EAAAiC,MAAA,CAAAlC,gBAAA,CAAAC;cACA;YAAA;cAHAkB,QAAA,GAAAkB,SAAA,CAAAV,CAAA;cAAA,MAKAR,QAAA,CAAAS,IAAA;gBAAAS,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACAU,MAAA,CAAAH,QAAA,CAAAY,OAAA;cACAT,MAAA,CAAAU,KAAA;gBACAF,MAAA;gBACA7C,IAAA,EAAAsB,QAAA,CAAAtB;cACA;cACAqC,MAAA,CAAAlC,gBAAA,CAAAC,OAAA;cAAAoC,SAAA,CAAAb,CAAA;cAAA,OACAU,MAAA,CAAAtB,mBAAA;YAAA;cAAA,OAAAyB,SAAA,CAAAZ,CAAA,IACA;YAAA;cAAA,MAEA,IAAAoB,KAAA,CAAA1B,QAAA,CAAA2B,OAAA;YAAA;cAAAT,SAAA,CAAAb,CAAA;cAAA;YAAA;cAAAa,SAAA,CAAAd,CAAA;cAAAa,GAAA,GAAAC,SAAA,CAAAV,CAAA;cAGA,IAAAS,GAAA,CAAAU,OAAA;gBACAjB,OAAA,CAAAC,KAAA,UAAAM,GAAA;gBACAF,MAAA,CAAAH,QAAA,CAAAD,KAAA,CAAAM,GAAA,CAAAU,OAAA;cACA;cAAA,OAAAT,SAAA,CAAAZ,CAAA,IACA;YAAA;cAAAY,SAAA,CAAAd,CAAA;cAEAW,MAAA,CAAApC,OAAA;cAAA,OAAAuC,SAAA,CAAAL,CAAA;YAAA;cAAA,OAAAK,SAAA,CAAAZ,CAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IAEA;IAEA;IACAY,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjC,kBAAA,CAAAxB,OAAA,mBAAAyB,aAAA,CAAAzB,OAAA,IAAA0B,CAAA,UAAAgC,SAAA;QAAA,IAAA9B,QAAA,EAAA+B,GAAA;QAAA,WAAAlC,aAAA,CAAAzB,OAAA,IAAA8B,CAAA,WAAA8B,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,CAAA,GAAA4B,SAAA,CAAA3B,CAAA;YAAA;cAAA,IACAwB,MAAA,CAAAhD,gBAAA,CAAAC,OAAA,CAAAqC,IAAA;gBAAAa,SAAA,CAAA3B,CAAA;gBAAA;cAAA;cACAwB,MAAA,CAAAjB,QAAA,CAAAD,KAAA;cAAA,OAAAqB,SAAA,CAAA1B,CAAA,IACA;YAAA;cAAA0B,SAAA,CAAA5B,CAAA;cAAA4B,SAAA,CAAA3B,CAAA;cAAA,OAIAwB,MAAA,CAAAT,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACApD,IAAA;cACA;YAAA;cAEA2D,MAAA,CAAAlD,OAAA;cAAAqD,SAAA,CAAA3B,CAAA;cAAA,OAEA,IAAAS,wBAAA,EAAAe,MAAA,CAAAxD,SAAA;gBACAkD,MAAA;gBACAzC,OAAA,EAAA+C,MAAA,CAAAhD,gBAAA,CAAAC;cACA;YAAA;cAHAkB,QAAA,GAAAgC,SAAA,CAAAxB,CAAA;cAAA,MAKAR,QAAA,CAAAS,IAAA;gBAAAuB,SAAA,CAAA3B,CAAA;gBAAA;cAAA;cACAwB,MAAA,CAAAjB,QAAA,CAAAY,OAAA;cACAK,MAAA,CAAAJ,KAAA;gBACAF,MAAA;gBACA7C,IAAA,EAAAsB,QAAA,CAAAtB;cACA;cACAmD,MAAA,CAAAhD,gBAAA,CAAAC,OAAA;cAAAkD,SAAA,CAAA3B,CAAA;cAAA,OACAwB,MAAA,CAAApC,mBAAA;YAAA;cAAA,OAAAuC,SAAA,CAAA1B,CAAA,IACA;YAAA;cAAA,MAEA,IAAAoB,KAAA,CAAA1B,QAAA,CAAA2B,OAAA;YAAA;cAAAK,SAAA,CAAA3B,CAAA;cAAA;YAAA;cAAA2B,SAAA,CAAA5B,CAAA;cAAA2B,GAAA,GAAAC,SAAA,CAAAxB,CAAA;cAGA,IAAAuB,GAAA,CAAAJ,OAAA;gBACAjB,OAAA,CAAAC,KAAA,UAAAoB,GAAA;gBACAF,MAAA,CAAAjB,QAAA,CAAAD,KAAA,CAAAoB,GAAA,CAAAJ,OAAA;cACA;cAAA,OAAAK,SAAA,CAAA1B,CAAA,IACA;YAAA;cAAA0B,SAAA,CAAA5B,CAAA;cAEAyB,MAAA,CAAAlD,OAAA;cAAA,OAAAqD,SAAA,CAAAnB,CAAA;YAAA;cAAA,OAAAmB,SAAA,CAAA1B,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IAGA;IACAG,QAAA,WAAAA,SAAA;MACA,OAAAC,OAAA,CAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}