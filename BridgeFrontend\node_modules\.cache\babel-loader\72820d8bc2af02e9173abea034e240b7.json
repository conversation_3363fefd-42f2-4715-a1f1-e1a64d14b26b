{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue", "mtime": 1758806998018}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_StepNavigation", "_interopRequireDefault", "require", "_BasicInfo", "_ProjectConfig", "_DiseaseConfig", "_BridgeConfig", "_ImplementationConfig", "_CompletionInfo", "_ApprovalInfo", "_projects", "name", "components", "StepNavigation", "BasicInfo", "ProjectConfig", "DiseaseConfig", "BridgeConfig", "ImplementationConfig", "CompletionInfo", "ApprovalInfo", "props", "visible", "type", "Boolean", "default", "mode", "String", "required", "validator", "value", "includes", "projectId", "Number", "infrastructureType", "data", "loading", "currentStep", "projectType", "formData", "basicInfo", "projectConfig", "diseaseConfig", "implementationConfig", "bridgeConfig", "completionInfo", "approvalInfo", "computed", "dialogVisible", "get", "set", "$emit", "modalTitle", "titles", "create", "edit", "view", "approve", "currentProjectId", "isEditMode", "isViewMode", "isApprovalMode", "isReadonlyMode", "allowStepClick", "clickableSteps", "Array", "from", "length", "steps", "_", "i", "baseSteps", "title", "projectSteps", "concat", "push", "showProjectConfig", "showDiseaseConfig", "canApprove", "watch", "newVal", "_this", "$nextTick", "forceApplyDialogStyles", "setTimeout", "_this2", "created", "_this3", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "loadProjectData", "a", "methods", "_this4", "_callee2", "mockProject", "_context2", "getMockProjectData", "getLastStepIndex", "error", "$message", "handleClose", "projectName", "startDate", "endDate", "managementUnit", "supervisionUnit", "maintenanceUnit", "manager", "contactPhone", "workload", "projectContent", "attachments", "projects", "frequency", "diseases", "id", "level", "works", "completionDate", "acceptanceDate", "qualityGrade", "remark", "bridges", "location", "span", "approvalHistory", "<PERSON><PERSON><PERSON>", "approver", "status", "comment", "department", "receiveTime", "processTime", "handleProjectTypeChange", "_this5", "handleStepClick", "stepIndex", "_this6", "_callee3", "<PERSON><PERSON><PERSON><PERSON>", "_context3", "validateStepByIndex", "v", "warning", "handlePrevStep", "handleNextStep", "_this7", "_callee4", "_context4", "validateCurrentStep", "_this8", "_callee5", "_context5", "_this9", "_callee6", "componentName", "result", "_t", "_context6", "p", "getStepComponentByIndex", "$refs", "validate", "then", "console", "handleEdit", "handleSave", "_this0", "_callee7", "_t2", "_context7", "_objectSpread2", "updateProject", "success", "createProject", "f", "handleSubmit", "_this1", "_callee8", "_t3", "_context8", "validateAllSteps", "handleApprovalSubmitted", "_this10", "handleApprovalCancel", "handleApprove", "_this11", "_callee9", "_context9", "approveProject", "handleReject", "_this12", "_callee0", "_context0", "rejectProject", "_this13", "dialog", "document", "querySelector", "applyDarkThemeToAllAreas", "dialogElement", "arguments", "undefined", "dialogStyles", "Object", "keys", "for<PERSON>ach", "property", "style", "setProperty", "header", "headerStyles", "body", "bodyStyles"], "sources": ["src/components/Maintenance/ProjectDialog.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    :title=\"modalTitle\"\r\n    :visible.sync=\"dialogVisible\"\r\n    :before-close=\"handleClose\"\r\n    :append-to-body=\"true\"\r\n    :modal-append-to-body=\"true\"\r\n    :close-on-click-modal=\"false\"\r\n    :show-close=\"false\"\r\n    custom-class=\"project-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\r\n    top=\"5vh\"\r\n  >\r\n    <!-- 自定义关闭按钮 -->\r\n    <div class=\"custom-close-btn\" @click=\"handleClose\">\r\n      <svg width=\"36\" height=\"36\" viewBox=\"0 0 36 36\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z\" fill=\"white\"/>\r\n      </svg>\r\n    </div>\r\n\r\n    <!-- 步骤导航 -->\r\n    <step-navigation\r\n      :steps=\"steps\"\r\n      :current-step=\"currentStep\"\r\n      :allow-click=\"allowStepClick\"\r\n      :clickable-steps=\"clickableSteps\"\r\n      @step-click=\"handleStepClick\"\r\n    />\r\n    \r\n    <!-- 步骤内容 -->\r\n    <div class=\"step-content\">\r\n      <!-- 基本信息 -->\r\n      <basic-info\r\n        v-if=\"currentStep === 0\"\r\n        ref=\"basicInfo\"\r\n        v-model=\"formData.basicInfo\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n        @project-type-change=\"handleProjectTypeChange\"\r\n      />\r\n      \r\n      <!-- 养护项目配置 -->\r\n      <project-config\r\n        v-if=\"currentStep === 1 && showProjectConfig\"\r\n        ref=\"projectConfig\"\r\n        v-model=\"formData.projectConfig\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 病害养护配置 -->\r\n      <disease-config\r\n        v-if=\"(currentStep === 2 && projectType === 'monthly') || (currentStep === 1 && projectType === 'preventive')\"\r\n        ref=\"diseaseConfig\"\r\n        v-model=\"formData.diseaseConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 实施信息配置 - 预防养护第三步 -->\r\n      <implementation-config\r\n        v-if=\"currentStep === 2 && projectType === 'preventive'\"\r\n        ref=\"implementationConfig\"\r\n        v-model=\"formData.implementationConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 竣工信息配置 - 预防养护第四步 -->\r\n      <completion-info\r\n        v-if=\"currentStep === 3 && projectType === 'preventive'\"\r\n        ref=\"completionInfo\"\r\n        v-model=\"formData.completionInfo\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 养护桥梁配置 - 只在非审批/查看/修改模式下显示 -->\r\n      <bridge-config\r\n        v-if=\"currentStep === getLastStepIndex() && projectType !== 'preventive' && !isApprovalMode && !isViewMode && !isEditMode\"\r\n        ref=\"bridgeConfig\"\r\n        v-model=\"formData.bridgeConfig\"\r\n        :infrastructure-type=\"infrastructureType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 审批信息 - 审批模式、查看模式、修改模式的最后一步显示 -->\r\n      <approval-info\r\n        v-if=\"currentStep === getLastStepIndex() && (isApprovalMode || isViewMode || isEditMode)\"\r\n        ref=\"approvalInfo\"\r\n        v-model=\"formData.approvalInfo\"\r\n        :project-id=\"currentProjectId\"\r\n        :show-approval-form=\"canApprove\"\r\n        @approval-submitted=\"handleApprovalSubmitted\"\r\n        @cancel=\"handleApprovalCancel\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 弹框底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <template v-if=\"isReadonlyMode && !isApprovalMode\">\r\n        <!-- 只读模式只显示关闭按钮 -->\r\n        <el-button @click=\"handleClose\">关闭</el-button>\r\n        <el-button v-if=\"isViewMode\" type=\"primary\" @click=\"handleEdit\">编辑</el-button>\r\n      </template>\r\n      <template v-else-if=\"isApprovalMode\">\r\n        <!-- 审批模式显示审批按钮 -->\r\n        <el-button @click=\"handleReject\" class=\"reject-btn\">退回</el-button>\r\n        <el-button type=\"primary\" @click=\"handleApprove\" class=\"approve-btn\">通过</el-button>\r\n      </template>\r\n      <template v-else-if=\"isEditMode && currentStep === getLastStepIndex()\">\r\n        <!-- 修改模式：在审批信息页只显示保存/提交，不显示上一步和更新 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\">提交</el-button>\r\n      </template>\r\n      <template v-else>\r\n        <!-- 非修改模式或非最后一步：显示正常的步骤按钮 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep > 0\"\r\n          @click=\"handlePrevStep\"\r\n        >\r\n          上一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep < getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleNextStep\"\r\n        >\r\n          下一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep === getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleSubmit\"\r\n        >\r\n          {{ isEditMode ? '更新' : '提交' }}\r\n        </el-button>\r\n      </template>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport StepNavigation from '@/components/Maintenance/StepNavigation'\r\nimport BasicInfo from '@/views/maintenance/projects/create/components/BasicInfo'\r\nimport ProjectConfig from '@/views/maintenance/projects/create/components/ProjectConfig'\r\nimport DiseaseConfig from '@/views/maintenance/projects/create/components/DiseaseConfig'\r\nimport BridgeConfig from '@/views/maintenance/projects/create/components/BridgeConfig'\r\nimport ImplementationConfig from '@/views/maintenance/projects/create/components/ImplementationConfig'\r\nimport CompletionInfo from '@/views/maintenance/projects/create/components/CompletionInfo'\r\nimport ApprovalInfo from '@/views/maintenance/projects/create/components/ApprovalInfo'\r\nimport { getProjectDetail, createProject, updateProject } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ProjectDialog',\r\n  components: {\r\n    StepNavigation,\r\n    BasicInfo,\r\n    ProjectConfig,\r\n    DiseaseConfig,\r\n    BridgeConfig,\r\n    ImplementationConfig,\r\n    CompletionInfo,\r\n    ApprovalInfo\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    mode: {\r\n      type: String,\r\n      required: true,\r\n      validator: value => ['create', 'edit', 'view', 'approve'].includes(value)\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    infrastructureType: {\r\n      type: String,\r\n      default: 'bridge'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      currentStep: 0,\r\n      projectType: 'monthly', // 默认为月度养护\r\n      \r\n      // 表单数据\r\n      formData: {\r\n        basicInfo: {},\r\n        projectConfig: {},\r\n        diseaseConfig: {},\r\n        implementationConfig: {},\r\n        bridgeConfig: {},\r\n        completionInfo: {},\r\n        approvalInfo: {}\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    dialogVisible: {\r\n      get() {\r\n        return this.visible\r\n      },\r\n      set(value) {\r\n        this.$emit('update:visible', value)\r\n      }\r\n    },\r\n\r\n    modalTitle() {\r\n      const titles = {\r\n        create: '新增养护项目',\r\n        edit: '编辑养护项目',\r\n        view: '查看养护项目',\r\n        approve: '审批养护项目'\r\n      }\r\n      return titles[this.mode] || '养护项目'\r\n    },\r\n\r\n    currentProjectId() {\r\n      return this.projectId\r\n    },\r\n    \r\n    isEditMode() {\r\n      return this.mode === 'edit'\r\n    },\r\n    \r\n    isViewMode() {\r\n      return this.mode === 'view'\r\n    },\r\n    \r\n    isApprovalMode() {\r\n      return this.mode === 'approve'\r\n    },\r\n    \r\n    // 是否为只读模式（查看或审批）\r\n    isReadonlyMode() {\r\n      return this.isViewMode || this.isApprovalMode\r\n    },\r\n    \r\n    // 是否允许步骤点击\r\n    allowStepClick() {\r\n      // 查看和审批模式始终允许点击\r\n      if (this.isViewMode || this.isApprovalMode) {\r\n        return true\r\n      }\r\n      // 新增和修改模式允许有条件的点击（由clickableSteps控制具体哪些步骤可点击）\r\n      return true\r\n    },\r\n    \r\n    // 可点击的步骤列表\r\n    clickableSteps() {\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        // 查看、审批和修改模式所有步骤都可点击\r\n        return Array.from({ length: this.steps.length }, (_, i) => i)\r\n      }\r\n      \r\n      // 新增模式：当前步骤及之前的步骤可点击\r\n      return Array.from({ length: this.currentStep + 1 }, (_, i) => i)\r\n    },\r\n    \r\n    // 根据项目类型确定步骤\r\n    steps() {\r\n      const baseSteps = [\r\n        { title: '基本信息' }\r\n      ]\r\n      \r\n      let projectSteps = []\r\n      \r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息 → 养护项目 → 病害养护 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '养护项目' },\r\n          { title: '病害养护' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'cleaning') {\r\n        // 保洁项目：基本信息 → 保洁项目 → 保洁桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '保洁项目' },\r\n          { title: '保洁桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'emergency') {\r\n        // 应急养护：基本信息 → 应急项目 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '应急项目' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息 → 关联病害 → 实施信息 → 竣工信息\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '关联病害' },\r\n          { title: '实施信息' },\r\n          { title: '竣工信息' }\r\n        ]\r\n      } else {\r\n        // 默认步骤\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '项目配置' },\r\n          { title: '关联对象' }\r\n        ]\r\n      }\r\n      \r\n      // 审批模式、查看模式、修改模式下添加审批信息步骤\r\n      if (this.isApprovalMode || this.isViewMode || this.isEditMode) {\r\n        projectSteps.push({ title: '审批信息' })\r\n      }\r\n      \r\n      return projectSteps\r\n    },\r\n    \r\n    // 是否显示项目配置步骤\r\n    showProjectConfig() {\r\n      return ['monthly', 'cleaning', 'emergency'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否显示病害配置步骤\r\n    showDiseaseConfig() {\r\n      return ['monthly', 'preventive'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否可以进行审批操作\r\n    canApprove() {\r\n      // 在实际项目中，这里应该根据当前用户权限和项目状态来判断\r\n      // 目前简化为审批模式下就可以审批\r\n      return this.isApprovalMode\r\n    }\r\n  },\r\n  watch: {\r\n    visible(newVal) {\r\n      if (newVal) {\r\n        // 立即应用样式，避免白色闪烁\r\n        this.$nextTick(() => {\r\n          this.forceApplyDialogStyles()\r\n        })\r\n        \r\n        // 延迟再次应用，确保Element UI后续的样式修改被覆盖\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 200)\r\n        \r\n        // 额外延迟应用表格样式，确保表格渲染完成后样式正确\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 500)\r\n        \r\n        // 最终确保样式正确应用\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 1000)\r\n      }\r\n    },\r\n    \r\n    // 监听Tab切换，确保每个Tab的样式都正确\r\n    currentStep(newVal) {\r\n      this.$nextTick(() => {\r\n        this.forceApplyDialogStyles()\r\n      })\r\n      \r\n      // 延迟应用，确保Tab内容渲染完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 100)\r\n      \r\n      // 额外延迟，确保表格数据加载完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 300)\r\n    }\r\n  },\r\n  async created() {\r\n    if (this.isEditMode || this.isViewMode || this.isApprovalMode) {\r\n      await this.loadProjectData()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载项目数据（编辑模式和查看模式）\r\n    async loadProjectData() {\r\n      try {\r\n        this.loading = true\r\n        \r\n        // 临时使用静态数据，因为后端接口未公布\r\n        // 实际项目中应该使用：const response = await getProjectDetail(this.currentProjectId)\r\n        const mockProject = this.getMockProjectData()\r\n        \r\n        this.projectType = mockProject.projectType\r\n        this.formData = {\r\n          basicInfo: mockProject.basicInfo || {},\r\n          projectConfig: mockProject.projectConfig || {},\r\n          diseaseConfig: mockProject.diseaseConfig || {},\r\n          implementationConfig: mockProject.implementationConfig || {},\r\n          completionInfo: mockProject.completionInfo || {},\r\n          bridgeConfig: mockProject.bridgeConfig || {},\r\n          approvalInfo: mockProject.approvalInfo || {}\r\n        }\r\n        \r\n        // 审批模式下默认跳转到审批信息页面（最后一步）\r\n        if (this.isApprovalMode) {\r\n          this.$nextTick(() => {\r\n            this.currentStep = this.getLastStepIndex()\r\n          })\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载项目数据失败')\r\n        this.handleClose()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 获取模拟项目数据\r\n    getMockProjectData() {\r\n      return {\r\n        projectType: 'monthly',\r\n        basicInfo: {\r\n          projectName: '2024年度桥梁月度养护项目',\r\n          projectType: 'monthly',\r\n          startDate: '2024-01-01',\r\n          endDate: '2024-12-31',\r\n          managementUnit: '市桥梁管理处',\r\n          supervisionUnit: '市交通局',\r\n          maintenanceUnit: '桥梁养护有限公司',\r\n          manager: '张三',\r\n          contactPhone: '13800138000',\r\n          workload: '100万元',\r\n          projectContent: '对辖区内桥梁进行月度常规养护，包括清洁、检查、小修等工作',\r\n          attachments: []\r\n        },\r\n        projectConfig: {\r\n          projects: [\r\n            { name: '桥面清洁', frequency: 30 },\r\n            { name: '栏杆维护', frequency: 60 },\r\n            { name: '伸缩缝检查', frequency: 90 }\r\n          ]\r\n        },\r\n        diseaseConfig: {\r\n          diseases: [\r\n            { id: 1, name: '桥面破损', level: '轻微' },\r\n            { id: 2, name: '栏杆松动', level: '一般' }\r\n          ]\r\n        },\r\n        implementationConfig: {\r\n          works: [\r\n            { name: '施工准备' },\r\n            { name: '现场作业' },\r\n            { name: '质量检查' }\r\n          ]\r\n        },\r\n        completionInfo: {\r\n          completionDate: '2024-12-31',\r\n          acceptanceDate: '2025-01-15',\r\n          qualityGrade: '合格',\r\n          remark: '项目按期完成，质量达标'\r\n        },\r\n        bridgeConfig: {\r\n          bridges: [\r\n            { id: 1, name: '人民桥', location: '人民路', span: '50m' },\r\n            { id: 2, name: '胜利桥', location: '胜利路', span: '30m' }\r\n          ]\r\n        },\r\n        approvalInfo: {\r\n          approvalHistory: [\r\n            {\r\n              id: 1,\r\n              stepName: '开始申请',\r\n              approver: '黄昭言',\r\n              status: '通过',\r\n              comment: '无异议',\r\n              department: '养护公司',\r\n              receiveTime: '2025-09-18\\n10:43',\r\n              processTime: '2025-09-18\\n10:43'\r\n            },\r\n            {\r\n              id: 2,\r\n              stepName: '养护项目审批(一级)',\r\n              approver: '刘雨桐',\r\n              status: '通过',\r\n              comment: '项目方案合理，同意开展',\r\n              department: '技术部门',\r\n              receiveTime: '2025-09-18\\n11:00',\r\n              processTime: '2025-09-18\\n11:30'\r\n            },\r\n            {\r\n              id: 3,\r\n              stepName: '养护项目审批(二级)',\r\n              approver: '罗砚秋',\r\n              status: '通过',\r\n              comment: '预算合理，批准执行',\r\n              department: '财务部门',\r\n              receiveTime: '2025-09-18\\n14:00',\r\n              processTime: '2025-09-18\\n15:20'\r\n            }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取最后一步的索引\r\n    getLastStepIndex() {\r\n      return this.steps.length - 1\r\n    },\r\n    \r\n    // 项目类型变化\r\n    handleProjectTypeChange(type) {\r\n      if (this.projectType === type) {\r\n        return // 防止重复触发\r\n      }\r\n      \r\n      this.projectType = type\r\n      \r\n      // 使用nextTick确保步骤计算完成后再检查当前步骤\r\n      this.$nextTick(() => {\r\n        // 重置后续步骤的数据\r\n        this.formData.projectConfig = {}\r\n        this.formData.diseaseConfig = {}\r\n        this.formData.bridgeConfig = {}\r\n        \r\n        // 如果当前步骤超出新的步骤范围，回到第一步\r\n        if (this.currentStep >= this.steps.length) {\r\n          this.currentStep = 0\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 步骤点击\r\n    async handleStepClick(stepIndex) {\r\n      // 查看、审批和修改模式可以自由切换\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 新增模式的逻辑\r\n      // 如果点击的是当前步骤或之前的步骤，允许直接切换\r\n      if (stepIndex <= this.currentStep) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 如果跳转到后面的步骤，需要验证从当前步骤到目标步骤的所有步骤\r\n      for (let i = this.currentStep; i < stepIndex; i++) {\r\n        const isValid = await this.validateStepByIndex(i)\r\n        if (!isValid) {\r\n          this.$message.warning(`请先完成第${i + 1}步的必填信息`)\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 所有验证通过，跳转到目标步骤\r\n      this.currentStep = stepIndex\r\n    },\r\n    \r\n    // 上一步\r\n    handlePrevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--\r\n      }\r\n    },\r\n    \r\n    // 下一步\r\n    async handleNextStep() {\r\n      const isValid = await this.validateCurrentStep()\r\n      if (!isValid) {\r\n        return\r\n      }\r\n      \r\n      if (this.currentStep < this.getLastStepIndex()) {\r\n        this.currentStep++\r\n      }\r\n    },\r\n    \r\n    // 验证当前步骤\r\n    async validateCurrentStep() {\r\n      return await this.validateStepByIndex(this.currentStep)\r\n    },\r\n    \r\n    // 验证指定步骤\r\n    async validateStepByIndex(stepIndex) {\r\n      const componentName = this.getStepComponentByIndex(stepIndex)\r\n      \r\n      if (componentName && this.$refs[componentName]) {\r\n        try {\r\n          const result = this.$refs[componentName].validate()\r\n          // 如果返回Promise，等待结果\r\n          if (result && typeof result.then === 'function') {\r\n            return await result\r\n          }\r\n          // 如果返回boolean，直接返回\r\n          return result\r\n        } catch (error) {\r\n          console.error('验证失败:', error)\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    // 根据步骤索引获取组件名\r\n    getStepComponentByIndex(stepIndex) {\r\n      if (stepIndex === 0) {\r\n        return 'basicInfo'\r\n      }\r\n      \r\n      // 根据项目类型和步骤索引确定组件\r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息(0) → 养护项目(1) → 病害养护(2) → 养护桥梁(3)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'diseaseConfig'\r\n        if (stepIndex === 3) return 'bridgeConfig'\r\n      } else if (this.projectType === 'cleaning' || this.projectType === 'emergency') {\r\n        // 保洁/应急：基本信息(0) → 项目配置(1) → 桥梁配置(2)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'bridgeConfig'\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息(0) → 关联病害(1) → 实施信息(2) → 竣工信息(3)\r\n        if (stepIndex === 1) return 'diseaseConfig'\r\n        if (stepIndex === 2) return 'implementationConfig'\r\n        if (stepIndex === 3) return 'completionInfo' // 竣工信息\r\n        return null // 预防养护项目没有第4步\r\n      }\r\n      \r\n      return null\r\n    },\r\n    \r\n    // 切换到编辑模式\r\n    handleEdit() {\r\n      this.$emit('edit', this.currentProjectId)\r\n    },\r\n    \r\n    // 保存草稿\r\n    async handleSave() {\r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'draft',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('保存成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('保存成功')\r\n        }\r\n        \r\n        this.$emit('save', data)\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 提交项目\r\n    async handleSubmit() {\r\n      // 验证所有步骤\r\n      if (!this.validateAllSteps()) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'pending',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('更新成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('提交成功')\r\n        }\r\n        \r\n        this.$emit('submit', data)\r\n        this.handleClose()\r\n      } catch (error) {\r\n        this.$message.error(this.isEditMode ? '更新失败' : '提交失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 验证所有步骤\r\n    validateAllSteps() {\r\n      // 这里应该验证所有步骤的数据\r\n      return true\r\n    },\r\n    \r\n    // 处理审批提交结果\r\n    handleApprovalSubmitted(data) {\r\n      const { result } = data\r\n      \r\n      if (result === 'approved') {\r\n        this.$message.success('审批通过成功')\r\n      } else if (result === 'rejected') {\r\n        this.$message.success('已驳回申请')\r\n      } else if (result === 'returned') {\r\n        this.$message.success('已退回修改')\r\n      }\r\n      \r\n      this.$emit('approval-completed', data)\r\n      \r\n      // 审批完成后关闭页面\r\n      setTimeout(() => {\r\n        this.handleClose()\r\n      }, 1500)\r\n    },\r\n    \r\n    // 处理审批取消\r\n    handleApprovalCancel() {\r\n      // 可以选择关闭页面或回到上一步\r\n      this.handleClose()\r\n    },\r\n    \r\n    // 处理审批通过\r\n    async handleApprove() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.approveProject()\r\n        if (result) {\r\n          // 审批成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理审批退回\r\n    async handleReject() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.rejectProject()\r\n        if (result) {\r\n          // 退回成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 应用深色主题样式\r\n    forceApplyDialogStyles() {\r\n      // 参考巡检弹框的实现，强制应用深色主题样式\r\n      setTimeout(() => {\r\n        const dialog = document.querySelector('.el-dialog.project-dialog') ||\r\n                       document.querySelector('.el-dialog.inspection-dialog-base') ||\r\n                       document.querySelector('.el-dialog')\r\n        \r\n        if (dialog) {\r\n          // 应用深色主题到各个区域\r\n          this.applyDarkThemeToAllAreas(dialog)\r\n        } else {\r\n          // 如果还没找到，再次尝试\r\n          setTimeout(() => this.forceApplyDialogStyles(), 200)\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 应用深色主题到弹框的所有区域 - 参考巡检弹框实现\r\n    applyDarkThemeToAllAreas(dialogElement = null) {\r\n      // 如果没有传入dialog元素，尝试查找\r\n      const dialog = dialogElement || document.querySelector('.el-dialog')\r\n      \r\n      if (!dialog) {\r\n        return\r\n      }\r\n      \r\n      // 首先强制设置弹框本身的尺寸\r\n      const dialogStyles = {\r\n        'width': '70%',\r\n        'min-width': '800px',\r\n        'height': '85vh',\r\n        'max-height': '85vh',\r\n        'margin-top': '7.5vh',\r\n        'position': 'relative'\r\n      }\r\n      Object.keys(dialogStyles).forEach(property => {\r\n        dialog.style.setProperty(property, dialogStyles[property], 'important')\r\n      })\r\n      \r\n      // Header区域 - 使用dialog元素作为基础查找\r\n      const header = dialog.querySelector('.el-dialog__header')\r\n      \r\n      if (header) {\r\n        const headerStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'border-bottom': '1px solid #ffffff', // 白色分割线，与巡检日志一致\r\n          'padding': '20px 24px'\r\n        }\r\n        Object.keys(headerStyles).forEach(property => {\r\n          header.style.setProperty(property, headerStyles[property], 'important')\r\n        })\r\n        \r\n        const title = header.querySelector('.el-dialog__title')\r\n        if (title) {\r\n          title.style.setProperty('color', '#ffffff', 'important')\r\n          title.style.setProperty('font-weight', '600', 'important')\r\n          title.style.setProperty('font-size', '18px', 'important')\r\n        }\r\n      }\r\n      \r\n      // Body区域 - 强制设置固定高度和滚动\r\n      const body = dialog.querySelector('.el-dialog__body')\r\n      if (body) {\r\n        const bodyStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'height': 'calc(85vh - 140px)',\r\n          'max-height': 'calc(85vh - 140px)',\r\n          'overflow-y': 'auto',\r\n          'padding': '0',\r\n          'box-sizing': 'border-box'\r\n        }\r\n        Object.keys(bodyStyles).forEach(property => {\r\n          body.style.setProperty(property, bodyStyles[property], 'important')\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 关闭弹框\r\n    handleClose() {\r\n      this.currentStep = 0\r\n      this.$emit('update:visible', false)\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 导入巡检主题样式和maintenance主题样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n@import '@/styles/components/dialog.scss';\r\n\r\n// 项目弹框使用公共样式，无需自定义样式\r\n// 通过 common-dialog、inspection-tabs、inspection-table 等类继承公共样式\r\n\r\n// 项目弹框固定尺寸样式 - 强制覆盖所有可能的样式\r\n.project-dialog-fixed-size {\r\n  // 最高优先级样式设置\r\n  :deep(.el-dialog) {\r\n    width: 70% !important;\r\n    min-width: 800px !important;\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    margin-top: 7.5vh !important;\r\n    position: relative !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n      overflow-y: auto !important;\r\n      padding: 0 !important;\r\n      box-sizing: border-box !important;\r\n    }\r\n  }\r\n  \r\n  // 响应式设计\r\n  @media (max-width: 1200px) {\r\n    :deep(.el-dialog) {\r\n      width: 80% !important;\r\n      min-width: 700px !important;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    :deep(.el-dialog) {\r\n      width: 95% !important;\r\n      min-width: auto !important;\r\n      height: 90vh !important;\r\n      max-height: 90vh !important;\r\n      margin-top: 5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(90vh - 120px) !important;\r\n        max-height: calc(90vh - 120px) !important;\r\n      }\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 480px) {\r\n    :deep(.el-dialog) {\r\n      width: 98% !important;\r\n      height: 95vh !important;\r\n      max-height: 95vh !important;\r\n      margin-top: 2.5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(95vh - 100px) !important;\r\n        max-height: calc(95vh - 100px) !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 针对特定样式类的强制覆盖 - 确保即使有公共样式也能生效\r\n.project-dialog-fixed-size.common-dialog-wide {\r\n  :deep(.el-dialog) {\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 强制应用到所有可能的Element UI弹框类名\r\n.el-dialog.project-dialog-fixed-size,\r\n.el-dialog__wrapper .project-dialog-fixed-size,\r\n.project-dialog-fixed-size .el-dialog {\r\n  height: 85vh !important;\r\n  max-height: 85vh !important;\r\n  \r\n  .el-dialog__body {\r\n    height: calc(85vh - 140px) !important;\r\n    max-height: calc(85vh - 140px) !important;\r\n    overflow-y: auto !important;\r\n  }\r\n}\r\n\r\n.project-dialog {\r\n  .step-content {\r\n    padding: 24px;\r\n    min-height: 400px;\r\n    background: var(--inspection-bg-primary, #091A4B);\r\n    color: #f1f5f9;\r\n    // 内容区域可以滚动，移除min-height限制\r\n    height: auto;\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  // 确保关闭按钮样式正确应用\r\n  .custom-close-btn {\r\n    position: absolute !important;\r\n    top: 20px !important;\r\n    right: 24px !important;\r\n    width: 36px !important;\r\n    height: 36px !important;\r\n    cursor: pointer !important;\r\n    z-index: 100 !important;\r\n    transition: all 0.3s ease !important;\r\n\r\n    svg {\r\n      width: 100% !important;\r\n      height: 100% !important;\r\n      transition: all 0.3s ease !important;\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1) !important;\r\n      opacity: 0.8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA+IA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,aAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,qBAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,eAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,aAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAS,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,oBAAA,EAAAA,6BAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,YAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,QAAA;MACAC,SAAA,WAAAA,UAAAC,KAAA;QAAA,6CAAAC,QAAA,CAAAD,KAAA;MAAA;IACA;IACAE,SAAA;MACAT,IAAA,GAAAI,MAAA,EAAAM,MAAA;MACAR,OAAA;IACA;IACAS,kBAAA;MACAX,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MAAA;;MAEA;MACAC,QAAA;QACAC,SAAA;QACAC,aAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAA3B,OAAA;MACA;MACA4B,GAAA,WAAAA,IAAApB,KAAA;QACA,KAAAqB,KAAA,mBAAArB,KAAA;MACA;IACA;IAEAsB,UAAA,WAAAA,WAAA;MACA,IAAAC,MAAA;QACAC,MAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,OAAAJ,MAAA,MAAA3B,IAAA;IACA;IAEAgC,gBAAA,WAAAA,iBAAA;MACA,YAAA1B,SAAA;IACA;IAEA2B,UAAA,WAAAA,WAAA;MACA,YAAAjC,IAAA;IACA;IAEAkC,UAAA,WAAAA,WAAA;MACA,YAAAlC,IAAA;IACA;IAEAmC,cAAA,WAAAA,eAAA;MACA,YAAAnC,IAAA;IACA;IAEA;IACAoC,cAAA,WAAAA,eAAA;MACA,YAAAF,UAAA,SAAAC,cAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAA;MACA;MACA,SAAAH,UAAA,SAAAC,cAAA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAG,cAAA,WAAAA,eAAA;MACA,SAAAJ,UAAA,SAAAC,cAAA,SAAAF,UAAA;QACA;QACA,OAAAM,KAAA,CAAAC,IAAA;UAAAC,MAAA,OAAAC,KAAA,CAAAD;QAAA,aAAAE,CAAA,EAAAC,CAAA;UAAA,OAAAA,CAAA;QAAA;MACA;;MAEA;MACA,OAAAL,KAAA,CAAAC,IAAA;QAAAC,MAAA,OAAA9B,WAAA;MAAA,aAAAgC,CAAA,EAAAC,CAAA;QAAA,OAAAA,CAAA;MAAA;IACA;IAEA;IACAF,KAAA,WAAAA,MAAA;MACA,IAAAG,SAAA,IACA;QAAAC,KAAA;MAAA,EACA;MAEA,IAAAC,YAAA;MAEA,SAAAnC,WAAA;QACA;QACAmC,YAAA,MAAAC,MAAA,CACAH,SAAA,GACA;UAAAC,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;MACA,gBAAAlC,WAAA;QACA;QACAmC,YAAA,MAAAC,MAAA,CACAH,SAAA,GACA;UAAAC,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;MACA,gBAAAlC,WAAA;QACA;QACAmC,YAAA,MAAAC,MAAA,CACAH,SAAA,GACA;UAAAC,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;MACA,gBAAAlC,WAAA;QACA;QACAmC,YAAA,MAAAC,MAAA,CACAH,SAAA,GACA;UAAAC,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;MACA;QACA;QACAC,YAAA,MAAAC,MAAA,CACAH,SAAA,GACA;UAAAC,KAAA;QAAA,GACA;UAAAA,KAAA;QAAA,GACA;MACA;;MAEA;MACA,SAAAX,cAAA,SAAAD,UAAA,SAAAD,UAAA;QACAc,YAAA,CAAAE,IAAA;UAAAH,KAAA;QAAA;MACA;MAEA,OAAAC,YAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAA;MACA,4CAAA7C,QAAA,MAAAO,WAAA;IACA;IAEA;IACAuC,iBAAA,WAAAA,kBAAA;MACA,iCAAA9C,QAAA,MAAAO,WAAA;IACA;IAEA;IACAwC,UAAA,WAAAA,WAAA;MACA;MACA;MACA,YAAAjB,cAAA;IACA;EACA;EACAkB,KAAA;IACAzD,OAAA,WAAAA,QAAA0D,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,MAAA;QACA;QACA,KAAAE,SAAA;UACAD,KAAA,CAAAE,sBAAA;QACA;;QAEA;QACAC,UAAA;UACAH,KAAA,CAAAE,sBAAA;QACA;;QAEA;QACAC,UAAA;UACAH,KAAA,CAAAE,sBAAA;QACA;;QAEA;QACAC,UAAA;UACAH,KAAA,CAAAE,sBAAA;QACA;MACA;IACA;IAEA;IACA9C,WAAA,WAAAA,YAAA2C,MAAA;MAAA,IAAAK,MAAA;MACA,KAAAH,SAAA;QACAG,MAAA,CAAAF,sBAAA;MACA;;MAEA;MACAC,UAAA;QACAC,MAAA,CAAAF,sBAAA;MACA;;MAEA;MACAC,UAAA;QACAC,MAAA,CAAAF,sBAAA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAC,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAC,QAAA;MAAA,WAAAF,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAA,MACAP,MAAA,CAAA5B,UAAA,IAAA4B,MAAA,CAAA3B,UAAA,IAAA2B,MAAA,CAAA1B,cAAA;cAAAgC,QAAA,CAAAC,CAAA;cAAA;YAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACAP,MAAA,CAAAQ,eAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,CAAA;QAAA;MAAA,GAAAL,OAAA;IAAA;EAEA;EACAM,OAAA;IACA;IACAF,eAAA,WAAAA,gBAAA;MAAA,IAAAG,MAAA;MAAA,WAAAV,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAS,SAAA;QAAA,IAAAC,WAAA;QAAA,WAAAX,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAS,SAAA;UAAA,kBAAAA,SAAA,CAAAP,CAAA;YAAA;cACA;gBACAI,MAAA,CAAA9D,OAAA;;gBAEA;gBACA;gBACAgE,WAAA,GAAAF,MAAA,CAAAI,kBAAA;gBAEAJ,MAAA,CAAA5D,WAAA,GAAA8D,WAAA,CAAA9D,WAAA;gBACA4D,MAAA,CAAA3D,QAAA;kBACAC,SAAA,EAAA4D,WAAA,CAAA5D,SAAA;kBACAC,aAAA,EAAA2D,WAAA,CAAA3D,aAAA;kBACAC,aAAA,EAAA0D,WAAA,CAAA1D,aAAA;kBACAC,oBAAA,EAAAyD,WAAA,CAAAzD,oBAAA;kBACAE,cAAA,EAAAuD,WAAA,CAAAvD,cAAA;kBACAD,YAAA,EAAAwD,WAAA,CAAAxD,YAAA;kBACAE,YAAA,EAAAsD,WAAA,CAAAtD,YAAA;gBACA;;gBAEA;gBACA,IAAAoD,MAAA,CAAArC,cAAA;kBACAqC,MAAA,CAAAhB,SAAA;oBACAgB,MAAA,CAAA7D,WAAA,GAAA6D,MAAA,CAAAK,gBAAA;kBACA;gBACA;cACA,SAAAC,KAAA;gBACAN,MAAA,CAAAO,QAAA,CAAAD,KAAA;gBACAN,MAAA,CAAAQ,WAAA;cACA;gBACAR,MAAA,CAAA9D,OAAA;cACA;YAAA;cAAA,OAAAiE,SAAA,CAAAL,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IAEA;IACAG,kBAAA,WAAAA,mBAAA;MACA;QACAhE,WAAA;QACAE,SAAA;UACAmE,WAAA;UACArE,WAAA;UACAsE,SAAA;UACAC,OAAA;UACAC,cAAA;UACAC,eAAA;UACAC,eAAA;UACAC,OAAA;UACAC,YAAA;UACAC,QAAA;UACAC,cAAA;UACAC,WAAA;QACA;QACA5E,aAAA;UACA6E,QAAA,GACA;YAAA3G,IAAA;YAAA4G,SAAA;UAAA,GACA;YAAA5G,IAAA;YAAA4G,SAAA;UAAA,GACA;YAAA5G,IAAA;YAAA4G,SAAA;UAAA;QAEA;QACA7E,aAAA;UACA8E,QAAA,GACA;YAAAC,EAAA;YAAA9G,IAAA;YAAA+G,KAAA;UAAA,GACA;YAAAD,EAAA;YAAA9G,IAAA;YAAA+G,KAAA;UAAA;QAEA;QACA/E,oBAAA;UACAgF,KAAA,GACA;YAAAhH,IAAA;UAAA,GACA;YAAAA,IAAA;UAAA,GACA;YAAAA,IAAA;UAAA;QAEA;QACAkC,cAAA;UACA+E,cAAA;UACAC,cAAA;UACAC,YAAA;UACAC,MAAA;QACA;QACAnF,YAAA;UACAoF,OAAA,GACA;YAAAP,EAAA;YAAA9G,IAAA;YAAAsH,QAAA;YAAAC,IAAA;UAAA,GACA;YAAAT,EAAA;YAAA9G,IAAA;YAAAsH,QAAA;YAAAC,IAAA;UAAA;QAEA;QACApF,YAAA;UACAqF,eAAA,GACA;YACAV,EAAA;YACAW,QAAA;YACAC,QAAA;YACAC,MAAA;YACAC,OAAA;YACAC,UAAA;YACAC,WAAA;YACAC,WAAA;UACA,GACA;YACAjB,EAAA;YACAW,QAAA;YACAC,QAAA;YACAC,MAAA;YACAC,OAAA;YACAC,UAAA;YACAC,WAAA;YACAC,WAAA;UACA,GACA;YACAjB,EAAA;YACAW,QAAA;YACAC,QAAA;YACAC,MAAA;YACAC,OAAA;YACAC,UAAA;YACAC,WAAA;YACAC,WAAA;UACA;QAEA;MACA;IACA;IAEA;IACAnC,gBAAA,WAAAA,iBAAA;MACA,YAAAnC,KAAA,CAAAD,MAAA;IACA;IAEA;IACAwE,uBAAA,WAAAA,wBAAApH,IAAA;MAAA,IAAAqH,MAAA;MACA,SAAAtG,WAAA,KAAAf,IAAA;QACA;MACA;MAEA,KAAAe,WAAA,GAAAf,IAAA;;MAEA;MACA,KAAA2D,SAAA;QACA;QACA0D,MAAA,CAAArG,QAAA,CAAAE,aAAA;QACAmG,MAAA,CAAArG,QAAA,CAAAG,aAAA;QACAkG,MAAA,CAAArG,QAAA,CAAAK,YAAA;;QAEA;QACA,IAAAgG,MAAA,CAAAvG,WAAA,IAAAuG,MAAA,CAAAxE,KAAA,CAAAD,MAAA;UACAyE,MAAA,CAAAvG,WAAA;QACA;MACA;IACA;IAEA;IACAwG,eAAA,WAAAA,gBAAAC,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvD,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAsD,SAAA;QAAA,IAAA1E,CAAA,EAAA2E,OAAA;QAAA,WAAAxD,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAsD,SAAA;UAAA,kBAAAA,SAAA,CAAApD,CAAA;YAAA;cAAA,MAEAiD,MAAA,CAAAnF,UAAA,IAAAmF,MAAA,CAAAlF,cAAA,IAAAkF,MAAA,CAAApF,UAAA;gBAAAuF,SAAA,CAAApD,CAAA;gBAAA;cAAA;cACAiD,MAAA,CAAA1G,WAAA,GAAAyG,SAAA;cAAA,OAAAI,SAAA,CAAAlD,CAAA;YAAA;cAAA,MAMA8C,SAAA,IAAAC,MAAA,CAAA1G,WAAA;gBAAA6G,SAAA,CAAApD,CAAA;gBAAA;cAAA;cACAiD,MAAA,CAAA1G,WAAA,GAAAyG,SAAA;cAAA,OAAAI,SAAA,CAAAlD,CAAA;YAAA;cAKA1B,CAAA,GAAAyE,MAAA,CAAA1G,WAAA;YAAA;cAAA,MAAAiC,CAAA,GAAAwE,SAAA;gBAAAI,SAAA,CAAApD,CAAA;gBAAA;cAAA;cAAAoD,SAAA,CAAApD,CAAA;cAAA,OACAiD,MAAA,CAAAI,mBAAA,CAAA7E,CAAA;YAAA;cAAA2E,OAAA,GAAAC,SAAA,CAAAE,CAAA;cAAA,IACAH,OAAA;gBAAAC,SAAA,CAAApD,CAAA;gBAAA;cAAA;cACAiD,MAAA,CAAAtC,QAAA,CAAA4C,OAAA,kCAAA3E,MAAA,CAAAJ,CAAA;cAAA,OAAA4E,SAAA,CAAAlD,CAAA;YAAA;cAHA1B,CAAA;cAAA4E,SAAA,CAAApD,CAAA;cAAA;YAAA;cAQA;cACAiD,MAAA,CAAA1G,WAAA,GAAAyG,SAAA;YAAA;cAAA,OAAAI,SAAA,CAAAlD,CAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IAEA;IACAM,cAAA,WAAAA,eAAA;MACA,SAAAjH,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAkH,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhE,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAA+D,SAAA;QAAA,IAAAR,OAAA;QAAA,WAAAxD,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,CAAA;YAAA;cAAA4D,SAAA,CAAA5D,CAAA;cAAA,OACA0D,MAAA,CAAAG,mBAAA;YAAA;cAAAV,OAAA,GAAAS,SAAA,CAAAN,CAAA;cAAA,IACAH,OAAA;gBAAAS,SAAA,CAAA5D,CAAA;gBAAA;cAAA;cAAA,OAAA4D,SAAA,CAAA1D,CAAA;YAAA;cAIA,IAAAwD,MAAA,CAAAnH,WAAA,GAAAmH,MAAA,CAAAjD,gBAAA;gBACAiD,MAAA,CAAAnH,WAAA;cACA;YAAA;cAAA,OAAAqH,SAAA,CAAA1D,CAAA;UAAA;QAAA,GAAAyD,QAAA;MAAA;IACA;IAEA;IACAE,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAApE,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAmE,SAAA;QAAA,WAAApE,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAkE,SAAA;UAAA,kBAAAA,SAAA,CAAAhE,CAAA;YAAA;cAAAgE,SAAA,CAAAhE,CAAA;cAAA,OACA8D,MAAA,CAAAT,mBAAA,CAAAS,MAAA,CAAAvH,WAAA;YAAA;cAAA,OAAAyH,SAAA,CAAA9D,CAAA,IAAA8D,SAAA,CAAAV,CAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IACA;IAEA;IACAV,mBAAA,WAAAA,oBAAAL,SAAA;MAAA,IAAAiB,MAAA;MAAA,WAAAvE,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAsE,SAAA;QAAA,IAAAC,aAAA,EAAAC,MAAA,EAAAC,EAAA;QAAA,WAAA1E,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAwE,SAAA;UAAA,kBAAAA,SAAA,CAAAC,CAAA,GAAAD,SAAA,CAAAtE,CAAA;YAAA;cACAmE,aAAA,GAAAF,MAAA,CAAAO,uBAAA,CAAAxB,SAAA;cAAA,MAEAmB,aAAA,IAAAF,MAAA,CAAAQ,KAAA,CAAAN,aAAA;gBAAAG,SAAA,CAAAtE,CAAA;gBAAA;cAAA;cAAAsE,SAAA,CAAAC,CAAA;cAEAH,MAAA,GAAAH,MAAA,CAAAQ,KAAA,CAAAN,aAAA,EAAAO,QAAA,IACA;cAAA,MACAN,MAAA,WAAAA,MAAA,CAAAO,IAAA;gBAAAL,SAAA,CAAAtE,CAAA;gBAAA;cAAA;cAAAsE,SAAA,CAAAtE,CAAA;cAAA,OACAoE,MAAA;YAAA;cAAA,OAAAE,SAAA,CAAApE,CAAA,IAAAoE,SAAA,CAAAhB,CAAA;YAAA;cAAA,OAAAgB,SAAA,CAAApE,CAAA,IAGAkE,MAAA;YAAA;cAAAE,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAhB,CAAA;cAEAsB,OAAA,CAAAlE,KAAA,UAAA2D,EAAA;cAAA,OAAAC,SAAA,CAAApE,CAAA,IACA;YAAA;cAAA,OAAAoE,SAAA,CAAApE,CAAA,IAIA;UAAA;QAAA,GAAAgE,QAAA;MAAA;IACA;IAEA;IACAM,uBAAA,WAAAA,wBAAAxB,SAAA;MACA,IAAAA,SAAA;QACA;MACA;;MAEA;MACA,SAAAxG,WAAA;QACA;QACA,IAAAwG,SAAA;QACA,IAAAA,SAAA;QACA,IAAAA,SAAA;MACA,gBAAAxG,WAAA,wBAAAA,WAAA;QACA;QACA,IAAAwG,SAAA;QACA,IAAAA,SAAA;MACA,gBAAAxG,WAAA;QACA;QACA,IAAAwG,SAAA;QACA,IAAAA,SAAA;QACA,IAAAA,SAAA;QACA;MACA;MAEA;IACA;IAEA;IACA6B,UAAA,WAAAA,WAAA;MACA,KAAAxH,KAAA,cAAAO,gBAAA;IACA;IAEA;IACAkH,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAArF,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAoF,SAAA;QAAA,IAAA3I,IAAA,EAAA4I,GAAA;QAAA,WAAAtF,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAoF,SAAA;UAAA,kBAAAA,SAAA,CAAAX,CAAA,GAAAW,SAAA,CAAAlF,CAAA;YAAA;cAAAkF,SAAA,CAAAX,CAAA;cAEAQ,MAAA,CAAAzI,OAAA;cACAD,IAAA,OAAA8I,cAAA,CAAAxJ,OAAA,MAAAwJ,cAAA,CAAAxJ,OAAA,MACAoJ,MAAA,CAAAtI,QAAA;gBACA+F,MAAA;gBACApG,kBAAA,EAAA2I,MAAA,CAAA3I;cAAA;cAAA,KAGA2I,MAAA,CAAAlH,UAAA;gBAAAqH,SAAA,CAAAlF,CAAA;gBAAA;cAAA;cAAAkF,SAAA,CAAAlF,CAAA;cAAA,OACA,IAAAoF,uBAAA,EAAAL,MAAA,CAAAnH,gBAAA,EAAAvB,IAAA;YAAA;cACA0I,MAAA,CAAApE,QAAA,CAAA0E,OAAA;cAAAH,SAAA,CAAAlF,CAAA;cAAA;YAAA;cAAAkF,SAAA,CAAAlF,CAAA;cAAA,OAEA,IAAAsF,uBAAA,EAAAjJ,IAAA;YAAA;cACA0I,MAAA,CAAApE,QAAA,CAAA0E,OAAA;YAAA;cAGAN,MAAA,CAAA1H,KAAA,SAAAhB,IAAA;cAAA6I,SAAA,CAAAlF,CAAA;cAAA;YAAA;cAAAkF,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAA5B,CAAA;cAEAyB,MAAA,CAAApE,QAAA,CAAAD,KAAA;YAAA;cAAAwE,SAAA,CAAAX,CAAA;cAEAQ,MAAA,CAAAzI,OAAA;cAAA,OAAA4I,SAAA,CAAAK,CAAA;YAAA;cAAA,OAAAL,SAAA,CAAAhF,CAAA;UAAA;QAAA,GAAA8E,QAAA;MAAA;IAEA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAA/F,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAA8F,SAAA;QAAA,IAAArJ,IAAA,EAAAsJ,GAAA;QAAA,WAAAhG,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAA8F,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA,GAAAqB,SAAA,CAAA5F,CAAA;YAAA;cAAA,IAEAyF,MAAA,CAAAI,gBAAA;gBAAAD,SAAA,CAAA5F,CAAA;gBAAA;cAAA;cAAA,OAAA4F,SAAA,CAAA1F,CAAA;YAAA;cAAA0F,SAAA,CAAArB,CAAA;cAKAkB,MAAA,CAAAnJ,OAAA;cACAD,IAAA,OAAA8I,cAAA,CAAAxJ,OAAA,MAAAwJ,cAAA,CAAAxJ,OAAA,MACA8J,MAAA,CAAAhJ,QAAA;gBACA+F,MAAA;gBACApG,kBAAA,EAAAqJ,MAAA,CAAArJ;cAAA;cAAA,KAGAqJ,MAAA,CAAA5H,UAAA;gBAAA+H,SAAA,CAAA5F,CAAA;gBAAA;cAAA;cAAA4F,SAAA,CAAA5F,CAAA;cAAA,OACA,IAAAoF,uBAAA,EAAAK,MAAA,CAAA7H,gBAAA,EAAAvB,IAAA;YAAA;cACAoJ,MAAA,CAAA9E,QAAA,CAAA0E,OAAA;cAAAO,SAAA,CAAA5F,CAAA;cAAA;YAAA;cAAA4F,SAAA,CAAA5F,CAAA;cAAA,OAEA,IAAAsF,uBAAA,EAAAjJ,IAAA;YAAA;cACAoJ,MAAA,CAAA9E,QAAA,CAAA0E,OAAA;YAAA;cAGAI,MAAA,CAAApI,KAAA,WAAAhB,IAAA;cACAoJ,MAAA,CAAA7E,WAAA;cAAAgF,SAAA,CAAA5F,CAAA;cAAA;YAAA;cAAA4F,SAAA,CAAArB,CAAA;cAAAoB,GAAA,GAAAC,SAAA,CAAAtC,CAAA;cAEAmC,MAAA,CAAA9E,QAAA,CAAAD,KAAA,CAAA+E,MAAA,CAAA5H,UAAA;YAAA;cAAA+H,SAAA,CAAArB,CAAA;cAEAkB,MAAA,CAAAnJ,OAAA;cAAA,OAAAsJ,SAAA,CAAAL,CAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1F,CAAA;UAAA;QAAA,GAAAwF,QAAA;MAAA;IAEA;IAEA;IACAG,gBAAA,WAAAA,iBAAA;MACA;MACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAzJ,IAAA;MAAA,IAAA0J,OAAA;MACA,IAAA3B,MAAA,GAAA/H,IAAA,CAAA+H,MAAA;MAEA,IAAAA,MAAA;QACA,KAAAzD,QAAA,CAAA0E,OAAA;MACA,WAAAjB,MAAA;QACA,KAAAzD,QAAA,CAAA0E,OAAA;MACA,WAAAjB,MAAA;QACA,KAAAzD,QAAA,CAAA0E,OAAA;MACA;MAEA,KAAAhI,KAAA,uBAAAhB,IAAA;;MAEA;MACAiD,UAAA;QACAyG,OAAA,CAAAnF,WAAA;MACA;IACA;IAEA;IACAoF,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAApF,WAAA;IACA;IAEA;IACAqF,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxG,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAuG,SAAA;QAAA,IAAA/B,MAAA;QAAA,WAAAzE,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAAsG,SAAA;UAAA,kBAAAA,SAAA,CAAApG,CAAA;YAAA;cAAA,KACAkG,OAAA,CAAAzB,KAAA,CAAAzH,YAAA;gBAAAoJ,SAAA,CAAApG,CAAA;gBAAA;cAAA;cAAAoG,SAAA,CAAApG,CAAA;cAAA,OACAkG,OAAA,CAAAzB,KAAA,CAAAzH,YAAA,CAAAqJ,cAAA;YAAA;cAAAjC,MAAA,GAAAgC,SAAA,CAAA9C,CAAA;cACA,IAAAc,MAAA;gBACA;gBACA;cAAA;YACA;cAAA,OAAAgC,SAAA,CAAAlG,CAAA;UAAA;QAAA,GAAAiG,QAAA;MAAA;IAEA;IAEA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7G,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAA4G,SAAA;QAAA,IAAApC,MAAA;QAAA,WAAAzE,aAAA,CAAAhE,OAAA,IAAAmE,CAAA,WAAA2G,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,CAAA;YAAA;cAAA,KACAuG,OAAA,CAAA9B,KAAA,CAAAzH,YAAA;gBAAAyJ,SAAA,CAAAzG,CAAA;gBAAA;cAAA;cAAAyG,SAAA,CAAAzG,CAAA;cAAA,OACAuG,OAAA,CAAA9B,KAAA,CAAAzH,YAAA,CAAA0J,aAAA;YAAA;cAAAtC,MAAA,GAAAqC,SAAA,CAAAnD,CAAA;cACA,IAAAc,MAAA;gBACA;gBACA;cAAA;YACA;cAAA,OAAAqC,SAAA,CAAAvG,CAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA;IAEA;IAEA;IACAnH,sBAAA,WAAAA,uBAAA;MAAA,IAAAsH,OAAA;MACA;MACArH,UAAA;QACA,IAAAsH,MAAA,GAAAC,QAAA,CAAAC,aAAA,iCACAD,QAAA,CAAAC,aAAA,yCACAD,QAAA,CAAAC,aAAA;QAEA,IAAAF,MAAA;UACA;UACAD,OAAA,CAAAI,wBAAA,CAAAH,MAAA;QACA;UACA;UACAtH,UAAA;YAAA,OAAAqH,OAAA,CAAAtH,sBAAA;UAAA;QACA;MACA;IACA;IAEA;IACA0H,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,aAAA,GAAAC,SAAA,CAAA5I,MAAA,QAAA4I,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA;MACA,IAAAL,MAAA,GAAAI,aAAA,IAAAH,QAAA,CAAAC,aAAA;MAEA,KAAAF,MAAA;QACA;MACA;;MAEA;MACA,IAAAO,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAF,YAAA,EAAAG,OAAA,WAAAC,QAAA;QACAX,MAAA,CAAAY,KAAA,CAAAC,WAAA,CAAAF,QAAA,EAAAJ,YAAA,CAAAI,QAAA;MACA;;MAEA;MACA,IAAAG,MAAA,GAAAd,MAAA,CAAAE,aAAA;MAEA,IAAAY,MAAA;QACA,IAAAC,YAAA;UACA;UACA;UACA;UAAA;UACA;QACA;QACAP,MAAA,CAAAC,IAAA,CAAAM,YAAA,EAAAL,OAAA,WAAAC,QAAA;UACAG,MAAA,CAAAF,KAAA,CAAAC,WAAA,CAAAF,QAAA,EAAAI,YAAA,CAAAJ,QAAA;QACA;QAEA,IAAA7I,KAAA,GAAAgJ,MAAA,CAAAZ,aAAA;QACA,IAAApI,KAAA;UACAA,KAAA,CAAA8I,KAAA,CAAAC,WAAA;UACA/I,KAAA,CAAA8I,KAAA,CAAAC,WAAA;UACA/I,KAAA,CAAA8I,KAAA,CAAAC,WAAA;QACA;MACA;;MAEA;MACA,IAAAG,IAAA,GAAAhB,MAAA,CAAAE,aAAA;MACA,IAAAc,IAAA;QACA,IAAAC,UAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QACAT,MAAA,CAAAC,IAAA,CAAAQ,UAAA,EAAAP,OAAA,WAAAC,QAAA;UACAK,IAAA,CAAAJ,KAAA,CAAAC,WAAA,CAAAF,QAAA,EAAAM,UAAA,CAAAN,QAAA;QACA;MACA;IACA;IAEA;IACA3G,WAAA,WAAAA,YAAA;MACA,KAAArE,WAAA;MACA,KAAAc,KAAA;MACA,KAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}