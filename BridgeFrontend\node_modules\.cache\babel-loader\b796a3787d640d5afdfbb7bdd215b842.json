{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue", "mtime": 1758804563531}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "props", "chartData", "type", "Array", "default", "height", "String", "loading", "Boolean", "data", "chartInstance", "mounted", "_this", "$nextTick", "initChart", "window", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "removeEventListener", "watch", "handler", "updateChart", "deep", "methods", "element", "$refs", "regionChart", "init", "_this2", "getChartData", "option", "tooltip", "trigger", "axisPointer", "formatter", "params", "result", "concat", "for<PERSON>ach", "param", "seriesName", "value", "legend", "right", "top", "textStyle", "color", "fontSize", "grid", "left", "bottom", "containLabel", "xAxis", "regions", "axisTick", "alignWithLabel", "axisLabel", "interval", "rotate", "margin", "baseline", "nameTextStyle", "yAxis", "min", "max", "series", "plannedData", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "<PERSON><PERSON><PERSON><PERSON>", "label", "show", "position", "actualData", "setOption", "setTimeout", "resize", "length", "getDefaultData", "map", "item", "region", "planned", "actual"], "sources": ["src/views/inspection/statistics/components/RegionChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"regionChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'RegionChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      const element = this.$refs.regionChart\r\n      if (element) {\r\n        this.chartInstance = echarts.init(element)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let result = `${params[0].name}<br/>`\r\n            params.forEach(param => {\r\n              result += `${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['计划巡检', '实际巡检'],\r\n          right: '10%',\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '4%',\r\n          bottom: '15%', // 🔧 减少底部边距，给图表更多空间\r\n          top: '15%', // 🔧 适度增加顶部边距，为图例留出空间\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: data.regions,\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          },\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 🔧 不旋转标签，保持水平显示\r\n            color: '#ffffff',\r\n            fontSize: 11,\r\n            margin: 8, // 🔧 减少标签与轴的距离\r\n            textStyle: {\r\n              baseline: 'top'\r\n            }\r\n          },\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数量',\r\n          min: 0,\r\n          max: 200,\r\n          interval: 20,\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          },\r\n          axisLabel: {\r\n            formatter: '{value}',\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '计划巡检',\r\n            type: 'bar',\r\n            data: data.plannedData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#87CEEB'\r\n                }, {\r\n                  offset: 1, color: '#B0E0E6'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          },\r\n          {\r\n            name: '实际巡检',\r\n            type: 'bar',\r\n            data: data.actualData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#4682B4'\r\n                }, {\r\n                  offset: 1, color: '#5F9EA0'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n      \r\n      // 强制重新渲染图表\r\n      setTimeout(() => {\r\n        if (this.chartInstance) {\r\n          this.chartInstance.resize()\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      const regions = this.chartData.map(item => item.region)\r\n      const plannedData = this.chartData.map(item => item.planned || 0)\r\n      const actualData = this.chartData.map(item => item.actual || 0)\r\n      \r\n      return { regions, plannedData, actualData }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      return {\r\n        regions: ['岳麓区', '开福区', '天心区', '芙蓉区', '雨花区'],\r\n        plannedData: [140, 180, 120, 120, 180],\r\n        actualData: [37, 128, 94, 60, 18]\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 200px !important; // 🔧 减少最小高度，适应flex布局\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"regionChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 200px; // 🔧 减少最小高度，适应更紧凑的布局\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAWA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,OAAA;MACAL,IAAA,EAAAM,OAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAR,aAAA;MACA,KAAAA,aAAA,CAAAS,OAAA;IACA;IACAJ,MAAA,CAAAK,mBAAA,gBAAAH,YAAA;EACA;EACAI,KAAA;IACApB,SAAA;MACAqB,OAAA,WAAAA,QAAA;QACA,KAAAC,WAAA;MACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAX,SAAA,WAAAA,UAAA;MACA,IAAAY,OAAA,QAAAC,KAAA,CAAAC,WAAA;MACA,IAAAF,OAAA;QACA,KAAAhB,aAAA,GAAAd,OAAA,CAAAiC,IAAA,CAAAH,OAAA;QACA,KAAAH,WAAA;MACA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAO,MAAA;MACA,UAAApB,aAAA;MAEA,IAAAD,IAAA,QAAAsB,YAAA;MAEA,IAAAC,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAjC,IAAA;UACA;UACAkC,SAAA,WAAAA,UAAAC,MAAA;YACA,IAAAC,MAAA,MAAAC,MAAA,CAAAF,MAAA,IAAAtC,IAAA;YACAsC,MAAA,CAAAG,OAAA,WAAAC,KAAA;cACAH,MAAA,OAAAC,MAAA,CAAAE,KAAA,CAAAC,UAAA,QAAAH,MAAA,CAAAE,KAAA,CAAAE,KAAA;YACA;YACA,OAAAL,MAAA;UACA;QACA;QACAM,MAAA;UACAnC,IAAA;UACAoC,KAAA;UACAC,GAAA;UACAC,SAAA;YACAC,KAAA;YACAC,QAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAN,KAAA;UACAO,MAAA;UAAA;UACAN,GAAA;UAAA;UACAO,YAAA;QACA;QACAC,KAAA;UACApD,IAAA;UACAO,IAAA,EAAAA,IAAA,CAAA8C,OAAA;UACAC,QAAA;YACAC,cAAA;UACA;UACAC,SAAA;YACAC,QAAA;YACAC,MAAA;YAAA;YACAZ,KAAA;YACAC,QAAA;YACAY,MAAA;YAAA;YACAd,SAAA;cACAe,QAAA;YACA;UACA;UACAC,aAAA;YACAf,KAAA;UACA;QACA;QACAgB,KAAA;UACA9D,IAAA;UACAH,IAAA;UACAkE,GAAA;UACAC,GAAA;UACAP,QAAA;UACAI,aAAA;YACAf,KAAA;UACA;UACAU,SAAA;YACAtB,SAAA;YACAY,KAAA;UACA;QACA;QACAmB,MAAA,GACA;UACApE,IAAA;UACAG,IAAA;UACAO,IAAA,EAAAA,IAAA,CAAA2D,WAAA;UACAC,SAAA;YACArB,KAAA;cACA9C,IAAA;cACAoE,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA;gBACAC,MAAA;gBAAA3B,KAAA;cACA;gBACA2B,MAAA;gBAAA3B,KAAA;cACA;YACA;UACA;UACA4B,QAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;YACA3C,SAAA;YACAa,QAAA;YACAD,KAAA;UACA;QACA,GACA;UACAjD,IAAA;UACAG,IAAA;UACAO,IAAA,EAAAA,IAAA,CAAAuE,UAAA;UACAX,SAAA;YACArB,KAAA;cACA9C,IAAA;cACAoE,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA;gBACAC,MAAA;gBAAA3B,KAAA;cACA;gBACA2B,MAAA;gBAAA3B,KAAA;cACA;YACA;UACA;UACA4B,QAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;YACA3C,SAAA;YACAa,QAAA;YACAD,KAAA;UACA;QACA;MAEA;MAEA,KAAAtC,aAAA,CAAAuE,SAAA,CAAAjD,MAAA;;MAEA;MACAkD,UAAA;QACA,IAAApD,MAAA,CAAApB,aAAA;UACAoB,MAAA,CAAApB,aAAA,CAAAyE,MAAA;QACA;MACA;IACA;IAEA;IACApD,YAAA,WAAAA,aAAA;MACA,UAAA9B,SAAA,SAAAA,SAAA,CAAAmF,MAAA;QACA,YAAAC,cAAA;MACA;MAEA,IAAA9B,OAAA,QAAAtD,SAAA,CAAAqF,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;MACA,IAAApB,WAAA,QAAAnE,SAAA,CAAAqF,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAE,OAAA;MAAA;MACA,IAAAT,UAAA,QAAA/E,SAAA,CAAAqF,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAG,MAAA;MAAA;MAEA;QAAAnC,OAAA,EAAAA,OAAA;QAAAa,WAAA,EAAAA,WAAA;QAAAY,UAAA,EAAAA;MAAA;IACA;IAEA;IACAK,cAAA,WAAAA,eAAA;MACA;QACA9B,OAAA;QACAa,WAAA;QACAY,UAAA;MACA;IACA;IAEA;IACA/D,YAAA,WAAAA,aAAA;MACA,SAAAP,aAAA;QACA,KAAAA,aAAA,CAAAyE,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}