{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\utils\\inspection\\defaultData.js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\utils\\inspection\\defaultData.js", "mtime": 1758804563521}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1758366985497}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["defaultInspectionRecords", "exports", "list", "id", "bridgeName", "inspectors", "inspectionUnit", "contactNumbers", "monthlyInspectionCount", "lastInspectionTime", "total", "pageNum", "pageSize", "defaultInspectionCalendar", "bridgeId", "month", "logs", "date", "inspectionType", "time", "inspector", "logId", "defaultDiseaseList", "reportAttribute", "diseaseCode", "disease<PERSON>art", "diseaseType", "diseaseStatus", "diseaseLocation", "diseaseCount", "diseaseDescription", "reporter", "reportTime", "unit", "contactNumber", "defaultStatisticsData", "shouldInspectCount", "actualInspectCount", "completionRate", "centerInspectCount", "totalDamageCount", "unprocessedCount", "disposingCount", "processedCount", "trendData", "count", "regionData", "region", "planned", "actual", "damageTypeData", "type", "bridgeRanking", "defaultSelectOptions", "bridgeOptions", "value", "label", "inspectionUnitOptions", "reportAttributeOptions", "diseasePartOptions", "diseaseTypeOptions", "diseaseStatusOptions", "judgeTypeOptions", "reviewResultOptions", "getDefaultData", "key", "fallbackD<PERSON>", "arguments", "length", "undefined", "dataMap", "inspectionRecords", "inspectionCalendar", "diseaseList", "statisticsData", "selectOptions", "handleApiError", "apiName", "params", "console", "warn", "concat", "Promise", "resolve", "data"], "sources": ["D:/Work/qiao/BB/BridgeFrontend/src/utils/inspection/defaultData.js"], "sourcesContent": ["/**\r\n * 巡检中心模块默认数据配置\r\n * 用于接口失败时的降级数据处理\r\n */\r\n\r\n// 默认巡检记录数据（匹配设计稿）\r\nexport const defaultInspectionRecords = {\r\n  list: [\r\n    {\r\n      id: '001',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '吴亮吉 徐建国',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '13580037492',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-01-15 09:30'\r\n    },\r\n    {\r\n      id: '002',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '刘亮吉 徐承宇',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '15210087395',\r\n      monthlyInspectionCount: 1,\r\n      lastInspectionTime: '2024-01-20 14:15'\r\n    },\r\n    {\r\n      id: '003',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '孙昭吉 吴海燕',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '15910018495',\r\n      monthlyInspectionCount: 2,\r\n      lastInspectionTime: '2024-01-22 10:45'\r\n    },\r\n    {\r\n      id: '004',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '吴利非 徐知夏',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '13122238579',\r\n      monthlyInspectionCount: 3,\r\n      lastInspectionTime: '2024-01-25 16:20'\r\n    },\r\n    {\r\n      id: '005',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '徐统桐 郭浩然',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '13720089685',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-01-28 08:30'\r\n    },\r\n    {\r\n      id: '006',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '张秀英 吴建国',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '18202038579',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-02-01 11:15'\r\n    },\r\n    {\r\n      id: '007',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '李昭宁 吴亮吉',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '18310049683',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-02-05 13:45'\r\n    },\r\n    {\r\n      id: '008',\r\n      bridgeName: '三汊矶大桥',\r\n      inspectors: '张昕雪 张知夏',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '17821229583',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-02-08 15:30'\r\n    },\r\n    {\r\n      id: '009',\r\n      bridgeName: '橘子洲大桥',\r\n      inspectors: '王敏华 李建军',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '13912345678',\r\n      monthlyInspectionCount: 1,\r\n      lastInspectionTime: '2024-02-10 09:15'\r\n    },\r\n    {\r\n      id: '010',\r\n      bridgeName: '银盆岭大桥',\r\n      inspectors: '陈志刚 刘美玲',\r\n      inspectionUnit: '长沙市桥梁管理处',\r\n      contactNumbers: '15987654321',\r\n      monthlyInspectionCount: 0,\r\n      lastInspectionTime: '2024-02-12 14:45'\r\n    }\r\n  ],\r\n  total: 152,\r\n  pageNum: 1,\r\n  pageSize: 10\r\n}\r\n\r\n// 默认巡检日历数据\r\nexport const defaultInspectionCalendar = {\r\n  bridgeId: 1,\r\n  bridgeName: '橘子洲大桥',\r\n  month: '2023-06',\r\n  logs: [\r\n    {\r\n      date: '2023-06-01',\r\n      inspectionType: '日常巡检',\r\n      time: '09:20',\r\n      inspector: '李华',\r\n      logId: 'log001'\r\n    },\r\n    {\r\n      date: '2023-06-05',\r\n      inspectionType: '经常巡检',\r\n      time: '14:30',\r\n      inspector: '张明',\r\n      logId: 'log002'\r\n    }\r\n  ]\r\n}\r\n\r\n// 默认病害数据 - 匹配设计文档示例\r\nexport const defaultDiseaseList = {\r\n  list: [\r\n    {\r\n      id: 1,\r\n      bridgeName: '橘子洲大桥',\r\n      reportAttribute: 'daily',\r\n      diseaseCode: 'JZZ202500001',\r\n      diseasePart: '伸缩缝',\r\n      diseaseType: '伸缩缝失效',\r\n      diseaseStatus: 'judging',\r\n      diseaseLocation: 'XXX位置',\r\n      diseaseCount: 2,\r\n      diseaseDescription: 'XXXXXX',\r\n      reporter: '张三',\r\n      reportTime: '2025-07-12 12:30',\r\n      unit: 'XXX单位',\r\n      contactNumber: '13139103912'\r\n    },\r\n    {\r\n      id: 2,\r\n      bridgeName: '橘子洲大桥',\r\n      reportAttribute: 'center',\r\n      diseaseCode: 'JZZ202500002',\r\n      diseasePart: '照明设施',\r\n      diseaseType: '照明设施缺失',\r\n      diseaseStatus: 'planning',\r\n      diseaseLocation: 'XXX位置',\r\n      diseaseCount: 1,\r\n      diseaseDescription: 'XXXXXX',\r\n      reporter: '张三',\r\n      reportTime: '2025-07-12 12:30',\r\n      unit: 'XXX单位',\r\n      contactNumber: '13139103912'\r\n    },\r\n    {\r\n      id: 3,\r\n      bridgeName: '橘子洲大桥',\r\n      reportAttribute: 'inspection',\r\n      diseaseCode: 'JZZ202500003',\r\n      diseasePart: '伸缩缝',\r\n      diseaseType: '伸缩缝失效',\r\n      diseaseStatus: 'disposing',\r\n      diseaseLocation: 'XXX位置',\r\n      diseaseCount: 1,\r\n      diseaseDescription: 'XXXXXX',\r\n      reporter: '张三',\r\n      reportTime: '2025-07-12 12:30',\r\n      unit: 'XXX单位',\r\n      contactNumber: '13139103912'\r\n    },\r\n    {\r\n      id: 4,\r\n      bridgeName: '三汊矶大桥',\r\n      reportAttribute: 'superior',\r\n      diseaseCode: 'SCJ202500004',\r\n      diseasePart: '照明设施',\r\n      diseaseType: '照明设施缺失',\r\n      diseaseStatus: 'reviewing',\r\n      diseaseLocation: 'XXX位置',\r\n      diseaseCount: 1,\r\n      diseaseDescription: 'XXXXXX',\r\n      reporter: '张三',\r\n      reportTime: '2025-07-12 12:30',\r\n      unit: 'XXX单位',\r\n      contactNumber: '13139103912'\r\n    },\r\n    {\r\n      id: 5,\r\n      bridgeName: '三汊矶大桥',\r\n      reportAttribute: 'daily',\r\n      diseaseCode: 'SCJ202500005',\r\n      diseasePart: '照明设施',\r\n      diseaseType: '照明设施缺失',\r\n      diseaseStatus: 'archived',\r\n      diseaseLocation: 'XXX位置',\r\n      diseaseCount: 1,\r\n      diseaseDescription: 'XXXXXX',\r\n      reporter: '张三',\r\n      reportTime: '2025-07-12 12:30',\r\n      unit: 'XXX单位',\r\n      contactNumber: '13139103912'\r\n    },\r\n    {\r\n      id: 6,\r\n      bridgeName: '银盆岭大桥',\r\n      reportAttribute: 'regular',\r\n      diseaseCode: 'YPL202500006',\r\n      diseasePart: '桥面铺装',\r\n      diseaseType: '裂缝',\r\n      diseaseStatus: 'judging',\r\n      diseaseLocation: '主跨中部',\r\n      diseaseCount: 3,\r\n      diseaseDescription: '桥面出现多条纵向裂缝，长度约2-5米',\r\n      reporter: '李四',\r\n      reportTime: '2025-07-13 09:15',\r\n      unit: '长沙市桥梁管理处',\r\n      contactNumber: '13912345678'\r\n    },\r\n    {\r\n      id: 7,\r\n      bridgeName: '猴子石大桥',\r\n      reportAttribute: 'center',\r\n      diseaseCode: 'HZS202500007',\r\n      diseasePart: '护栏',\r\n      diseaseType: '护栏损坏',\r\n      diseaseStatus: 'planning',\r\n      diseaseLocation: '右侧人行道',\r\n      diseaseCount: 1,\r\n      diseaseDescription: '护栏立柱倾斜，存在安全隐患',\r\n      reporter: '王五',\r\n      reportTime: '2025-07-13 14:20',\r\n      unit: '湖南省交通工程检测中心',\r\n      contactNumber: '15987654321'\r\n    },\r\n    {\r\n      id: 8,\r\n      bridgeName: '福元路大桥',\r\n      reportAttribute: 'inspection',\r\n      diseaseCode: 'FYL202500008',\r\n      diseasePart: '支座',\r\n      diseaseType: '支座老化',\r\n      diseaseStatus: 'disposing',\r\n      diseaseLocation: '2#墩支座',\r\n      diseaseCount: 2,\r\n      diseaseDescription: '橡胶支座出现老化开裂现象',\r\n      reporter: '赵六',\r\n      reportTime: '2025-07-14 10:30',\r\n      unit: '长沙市市政工程公司',\r\n      contactNumber: '13712345678'\r\n    },\r\n    {\r\n      id: 9,\r\n      bridgeName: '湘江大桥',\r\n      reportAttribute: 'superior',\r\n      diseaseCode: 'XJ202500009',\r\n      diseasePart: '桥墩',\r\n      diseaseType: '混凝土剥落',\r\n      diseaseStatus: 'reviewing',\r\n      diseaseLocation: '3#桥墩',\r\n      diseaseCount: 1,\r\n      diseaseDescription: '桥墩表面混凝土出现剥落，露出钢筋',\r\n      reporter: '孙七',\r\n      reportTime: '2025-07-14 16:45',\r\n      unit: '湖南省桥梁检测研究院',\r\n      contactNumber: '18812345678'\r\n    },\r\n    {\r\n      id: 10,\r\n      bridgeName: '营盘路大桥',\r\n      reportAttribute: 'daily',\r\n      diseaseCode: 'YPL202500010',\r\n      diseasePart: '排水系统',\r\n      diseaseType: '排水不畅',\r\n      diseaseStatus: 'archived',\r\n      diseaseLocation: '桥面排水口',\r\n      diseaseCount: 4,\r\n      diseaseDescription: '多处排水口堵塞，影响桥面排水',\r\n      reporter: '周八',\r\n      reportTime: '2025-07-15 08:20',\r\n      unit: '长沙市桥梁管理处',\r\n      contactNumber: '13612345678'\r\n    }\r\n  ],\r\n  total: 24,\r\n  pageNum: 1,\r\n  pageSize: 10\r\n}\r\n\r\n// 默认统计数据\r\nexport const defaultStatisticsData = {\r\n  // 基础指标\r\n  shouldInspectCount: 1234,\r\n  actualInspectCount: 912,\r\n  completionRate: 74,\r\n  centerInspectCount: 517,\r\n  \r\n  // 病害统计\r\n  totalDamageCount: 512,\r\n  unprocessedCount: 40,\r\n  disposingCount: 60,\r\n  processedCount: 412,\r\n  \r\n  // 趋势数据 - 12个月的数据\r\n  trendData: [\r\n    { month: '1月', count: 120 },\r\n    { month: '2月', count: 95 },\r\n    { month: '3月', count: 140 },\r\n    { month: '4月', count: 168 },\r\n    { month: '5月', count: 145 },\r\n    { month: '6月', count: 190 },\r\n    { month: '7月', count: 210 },\r\n    { month: '8月', count: 185 },\r\n    { month: '9月', count: 195 },\r\n    { month: '10月', count: 175 },\r\n    { month: '11月', count: 155 },\r\n    { month: '12月', count: 165 }\r\n  ],\r\n  \r\n  // 地区巡检数据 - 各区计划巡检与实际巡检对比\r\n  regionData: [\r\n    { region: '岳麓区', planned: 140, actual: 37 },\r\n    { region: '开福区', planned: 180, actual: 128 },\r\n    { region: '天心区', planned: 120, actual: 94 },\r\n    { region: '芙蓉区', planned: 120, actual: 60 },\r\n    { region: '雨花区', planned: 180, actual: 18 }\r\n  ],\r\n  \r\n  // 病害类型分布 - 更丰富的病害类型\r\n  damageTypeData: [\r\n    { type: '抗浮', count: 581 },\r\n    { type: '下沉', count: 140 },\r\n    { type: 'TOP', count: 95 },\r\n    { type: '排包', count: 80 },\r\n    { type: 'a581', count: 65 }\r\n  ],\r\n  \r\n  // TOP10病害桥梁 - 完整的10个桥梁数据\r\n  bridgeRanking: [\r\n    { bridgeName: 'XXXX病害', count: 932 },\r\n    { bridgeName: 'XXXX病害', count: 899 },\r\n    { bridgeName: 'XXXX病害', count: 702 },\r\n    { bridgeName: 'XXXX病害', count: 543 },\r\n    { bridgeName: 'XXXX病害', count: 208 }\r\n  ]\r\n}\r\n\r\n// 默认下拉选项数据\r\nexport const defaultSelectOptions = {\r\n  // 桥梁名称选项\r\n  bridgeOptions: [\r\n    { value: '1', label: '橘子洲大桥' },\r\n    { value: '2', label: '银盆岭大桥' },\r\n    { value: '3', label: '猴子石大桥' },\r\n    { value: '4', label: '三汊矶大桥' },\r\n    { value: '5', label: '福元路大桥' }\r\n  ],\r\n  \r\n  // 巡检单位选项\r\n  inspectionUnitOptions: [\r\n    { value: '1', label: '长沙市桥梁管理处' },\r\n    { value: '2', label: '湖南省交通工程检测中心' },\r\n    { value: '3', label: '长沙市市政工程公司' },\r\n    { value: '4', label: '湖南省桥梁检测研究院' }\r\n  ],\r\n  \r\n  // 上报属性选项\r\n  reportAttributeOptions: [\r\n    { value: 'daily', label: '日常巡检' },\r\n    { value: 'regular', label: '经常巡检' },\r\n    { value: 'center', label: '中心巡检' },\r\n    { value: 'inspection', label: '定检' },\r\n    { value: 'superior', label: '上级交办' }\r\n  ],\r\n  \r\n  // 病害部位选项\r\n  diseasePartOptions: [\r\n    { value: 'expansion_joint', label: '伸缩缝' },\r\n    { value: 'lighting', label: '照明设施' },\r\n    { value: 'bridge_surface', label: '桥面板' },\r\n    { value: 'bridge_connection', label: '桥路连接位置' },\r\n    { value: 'drainage', label: '排水系统' }\r\n  ],\r\n  \r\n  // 病害类型选项\r\n  diseaseTypeOptions: [\r\n    { value: 'crack', label: '裂缝' },\r\n    { value: 'settlement', label: '沉降' },\r\n    { value: 'expansion_failure', label: '伸缩缝失效' },\r\n    { value: 'lighting_missing', label: '照明设施缺失' },\r\n    { value: 'drainage_block', label: '排水堵塞' }\r\n  ],\r\n  \r\n  // 病害状态选项\r\n  diseaseStatusOptions: [\r\n    { value: 'judging', label: '判定中' },\r\n    { value: 'planning', label: '计划中' },\r\n    { value: 'disposing', label: '处置中' },\r\n    { value: 'reviewing', label: '复核中' },\r\n    { value: 'archived', label: '已归档' }\r\n  ],\r\n  \r\n  // 判定类型选项\r\n  judgeTypeOptions: [\r\n    { value: 'daily_maintenance', label: '日常养护及小修' },\r\n    { value: 'emergency_repair', label: '应急维修' },\r\n    { value: 'out_of_scope', label: '养护范围外' }\r\n  ],\r\n  \r\n  // 复核结果选项\r\n  reviewResultOptions: [\r\n    { value: 'pass', label: '通过' },\r\n    { value: 'reject', label: '不通过' }\r\n  ]\r\n}\r\n\r\n/**\r\n * 获取默认数据的工具函数\r\n * @param {string} key 数据key\r\n * @param {object} fallbackData 降级数据\r\n */\r\nexport function getDefaultData(key, fallbackData = null) {\r\n  const dataMap = {\r\n    inspectionRecords: defaultInspectionRecords,\r\n    inspectionCalendar: defaultInspectionCalendar,\r\n    diseaseList: defaultDiseaseList,\r\n    statisticsData: defaultStatisticsData,\r\n    selectOptions: defaultSelectOptions\r\n  }\r\n  \r\n  return dataMap[key] || fallbackData\r\n}\r\n\r\n/**\r\n * API请求失败时的数据处理\r\n * @param {string} apiName API名称\r\n * @param {object} params 请求参数\r\n */\r\nexport function handleApiError(apiName, params = {}) {\r\n  console.warn(`API ${apiName} 请求失败，使用默认数据`)\r\n  \r\n  switch (apiName) {\r\n    case 'getInspectionRecords':\r\n      return Promise.resolve({ data: defaultInspectionRecords })\r\n    case 'getInspectionLogs':\r\n      return Promise.resolve({ data: defaultInspectionCalendar })\r\n    case 'getDiseaseList':\r\n      return Promise.resolve({ data: defaultDiseaseList })\r\n    case 'getInspectionStatistics':\r\n      return Promise.resolve({ data: defaultStatisticsData })\r\n    default:\r\n      return Promise.resolve({ data: {} })\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA;AACO,IAAMA,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,GAAG;EACtCE,IAAI,EAAE,CACJ;IACEC,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,EACD;IACEN,EAAE,EAAE,KAAK;IACTC,UAAU,EAAE,OAAO;IACnBC,UAAU,EAAE,SAAS;IACrBC,cAAc,EAAE,UAAU;IAC1BC,cAAc,EAAE,aAAa;IAC7BC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE;EACtB,CAAC,CACF;EACDC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACO,IAAMC,yBAAyB,GAAAZ,OAAA,CAAAY,yBAAA,GAAG;EACvCC,QAAQ,EAAE,CAAC;EACXV,UAAU,EAAE,OAAO;EACnBW,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,CACJ;IACEC,IAAI,EAAE,YAAY;IAClBC,cAAc,EAAE,MAAM;IACtBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,YAAY;IAClBC,cAAc,EAAE,MAAM;IACtBC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE;EACT,CAAC;AAEL,CAAC;;AAED;AACO,IAAMC,kBAAkB,GAAArB,OAAA,CAAAqB,kBAAA,GAAG;EAChCpB,IAAI,EAAE,CACJ;IACEC,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,QAAQ;IACrBC,aAAa,EAAE,UAAU;IACzBC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,YAAY;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,WAAW;IAC1BC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,UAAU;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,QAAQ;IACrBC,aAAa,EAAE,WAAW;IAC1BC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,QAAQ;IACrBC,aAAa,EAAE,UAAU;IACzBC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,QAAQ;IAC5BC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,OAAO;IACbC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,SAAS;IACxBC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,oBAAoB;IACxCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,UAAU;IAChBC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,QAAQ;IACzBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE,UAAU;IACzBC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,eAAe;IACnCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,aAAa;IACnBC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,YAAY;IAC7BC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE,WAAW;IAC1BC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,cAAc;IAClCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,WAAW;IACjBC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLC,UAAU,EAAE,MAAM;IAClBmB,eAAe,EAAE,UAAU;IAC3BC,WAAW,EAAE,aAAa;IAC1BC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,WAAW;IAC1BC,eAAe,EAAE,MAAM;IACvBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,kBAAkB;IACtCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,YAAY;IAClBC,aAAa,EAAE;EACjB,CAAC,EACD;IACE/B,EAAE,EAAE,EAAE;IACNC,UAAU,EAAE,OAAO;IACnBmB,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,MAAM;IACnBC,aAAa,EAAE,UAAU;IACzBC,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,CAAC;IACfC,kBAAkB,EAAE,gBAAgB;IACpCC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,IAAI,EAAE,UAAU;IAChBC,aAAa,EAAE;EACjB,CAAC,CACF;EACDxB,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACO,IAAMuB,qBAAqB,GAAAlC,OAAA,CAAAkC,qBAAA,GAAG;EACnC;EACAC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,GAAG;EACvBC,cAAc,EAAE,EAAE;EAClBC,kBAAkB,EAAE,GAAG;EAEvB;EACAC,gBAAgB,EAAE,GAAG;EACrBC,gBAAgB,EAAE,EAAE;EACpBC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE,GAAG;EAEnB;EACAC,SAAS,EAAE,CACT;IAAE7B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAG,CAAC,EAC1B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,IAAI;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC3B;IAAE9B,KAAK,EAAE,KAAK;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC5B;IAAE9B,KAAK,EAAE,KAAK;IAAE8B,KAAK,EAAE;EAAI,CAAC,EAC5B;IAAE9B,KAAK,EAAE,KAAK;IAAE8B,KAAK,EAAE;EAAI,CAAC,CAC7B;EAED;EACAC,UAAU,EAAE,CACV;IAAEC,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC3C;IAAEF,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5C;IAAEF,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC3C;IAAEF,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAC3C;IAAEF,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,CAC5C;EAED;EACAC,cAAc,EAAE,CACd;IAAEC,IAAI,EAAE,IAAI;IAAEN,KAAK,EAAE;EAAI,CAAC,EAC1B;IAAEM,IAAI,EAAE,IAAI;IAAEN,KAAK,EAAE;EAAI,CAAC,EAC1B;IAAEM,IAAI,EAAE,KAAK;IAAEN,KAAK,EAAE;EAAG,CAAC,EAC1B;IAAEM,IAAI,EAAE,IAAI;IAAEN,KAAK,EAAE;EAAG,CAAC,EACzB;IAAEM,IAAI,EAAE,MAAM;IAAEN,KAAK,EAAE;EAAG,CAAC,CAC5B;EAED;EACAO,aAAa,EAAE,CACb;IAAEhD,UAAU,EAAE,QAAQ;IAAEyC,KAAK,EAAE;EAAI,CAAC,EACpC;IAAEzC,UAAU,EAAE,QAAQ;IAAEyC,KAAK,EAAE;EAAI,CAAC,EACpC;IAAEzC,UAAU,EAAE,QAAQ;IAAEyC,KAAK,EAAE;EAAI,CAAC,EACpC;IAAEzC,UAAU,EAAE,QAAQ;IAAEyC,KAAK,EAAE;EAAI,CAAC,EACpC;IAAEzC,UAAU,EAAE,QAAQ;IAAEyC,KAAK,EAAE;EAAI,CAAC;AAExC,CAAC;;AAED;AACO,IAAMQ,oBAAoB,GAAApD,OAAA,CAAAoD,oBAAA,GAAG;EAClC;EACAC,aAAa,EAAE,CACb;IAAEC,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC9B;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC9B;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC9B;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC9B;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAQ,CAAC,CAC/B;EAED;EACAC,qBAAqB,EAAE,CACrB;IAAEF,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAW,CAAC,EACjC;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAc,CAAC,EACpC;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAY,CAAC,EAClC;IAAED,KAAK,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAa,CAAC,CACpC;EAED;EACAE,sBAAsB,EAAE,CACtB;IAAEH,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAO,CAAC,EACjC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAO,CAAC,EACnC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAO,CAAC,EAClC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAC,EACpC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAO,CAAC,CACrC;EAED;EACAG,kBAAkB,EAAE,CAClB;IAAEJ,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC1C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAO,CAAC,EACpC;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAM,CAAC,EACzC;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC/C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAO,CAAC,CACrC;EAED;EACAI,kBAAkB,EAAE,CAClB;IAAEL,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC/B;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAC,EACpC;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC9C;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC9C;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC3C;EAED;EACAK,oBAAoB,EAAE,CACpB;IAAEN,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAM,CAAC,EAClC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAM,CAAC,EACnC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAM,CAAC,EACpC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAM,CAAC,EACpC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAM,CAAC,CACpC;EAED;EACAM,gBAAgB,EAAE,CAChB;IAAEP,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChD;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5C;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAC,CAC1C;EAED;EACAO,mBAAmB,EAAE,CACnB;IAAER,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC9B;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAM,CAAC;AAErC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,SAASQ,cAAcA,CAACC,GAAG,EAAuB;EAAA,IAArBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACrD,IAAMG,OAAO,GAAG;IACdC,iBAAiB,EAAEvE,wBAAwB;IAC3CwE,kBAAkB,EAAE3D,yBAAyB;IAC7C4D,WAAW,EAAEnD,kBAAkB;IAC/BoD,cAAc,EAAEvC,qBAAqB;IACrCwC,aAAa,EAAEtB;EACjB,CAAC;EAED,OAAOiB,OAAO,CAACL,GAAG,CAAC,IAAIC,YAAY;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASU,cAAcA,CAACC,OAAO,EAAe;EAAA,IAAbC,MAAM,GAAAX,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjDY,OAAO,CAACC,IAAI,QAAAC,MAAA,CAAQJ,OAAO,wEAAc,CAAC;EAE1C,QAAQA,OAAO;IACb,KAAK,sBAAsB;MACzB,OAAOK,OAAO,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAEpF;MAAyB,CAAC,CAAC;IAC5D,KAAK,mBAAmB;MACtB,OAAOkF,OAAO,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAEvE;MAA0B,CAAC,CAAC;IAC7D,KAAK,gBAAgB;MACnB,OAAOqE,OAAO,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAE9D;MAAmB,CAAC,CAAC;IACtD,KAAK,yBAAyB;MAC5B,OAAO4D,OAAO,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAEjD;MAAsB,CAAC,CAAC;IACzD;MACE,OAAO+C,OAAO,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAE,CAAC;MAAE,CAAC,CAAC;EACxC;AACF", "ignoreList": []}]}