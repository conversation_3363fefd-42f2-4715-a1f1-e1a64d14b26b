{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue", "mtime": 1758804563534}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "name", "props", "chartData", "type", "Array", "default", "chartType", "String", "height", "loading", "Boolean", "data", "chartInstance", "mounted", "initChart", "window", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "removeEventListener", "watch", "handler", "updateChart", "deep", "methods", "$refs", "trendChart", "init", "getChartData", "option", "tooltip", "trigger", "axisPointer", "label", "backgroundColor", "legend", "top", "textStyle", "color", "fontSize", "grid", "left", "right", "bottom", "containLabel", "xAxis", "boundaryGap", "categories", "axisLabel", "nameTextStyle", "yAxis", "max", "interval", "formatter", "series", "smooth", "districtData", "shi<PERSON><PERSON>u", "itemStyle", "lineStyle", "width", "<PERSON><PERSON><PERSON><PERSON>u", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>u", "<PERSON><PERSON><PERSON>u", "setOption", "length", "getDefaultData", "resize"], "sources": ["src/views/inspection/statistics/components/TrendChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"trendChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'Trend<PERSON><PERSON>',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    chartType: {\r\n      type: String,\r\n      default: 'line' // line | bar\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    },\r\n    chartType() {\r\n      this.updateChart()\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      if (this.$refs.trendChart) {\r\n        this.chartInstance = echarts.init(this.$refs.trendChart)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['市府区', '美湖区', '龙泉区', '开福区', '芙蓉区'],\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '12%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            boundaryGap: this.chartType === 'bar',\r\n            data: data.categories,\r\n            axisLabel: {\r\n              color: '#ffffff'\r\n            },\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '次数',\r\n            max: 900,\r\n            interval: 100,\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            },\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '市府区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.shifuqu,\r\n            itemStyle: {\r\n              color: '#40E0D0'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '美湖区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.meihuqu,\r\n            itemStyle: {\r\n              color: '#FFD700'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '龙泉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.longquanqu,\r\n            itemStyle: {\r\n              color: '#FFFFFF'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '开福区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.kaifuqu,\r\n            itemStyle: {\r\n              color: '#FF6B6B'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '芙蓉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.furongqu,\r\n            itemStyle: {\r\n              color: '#FF8C00'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      // 如果有传入数据，处理为区域数据格式\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      // 如果chartData有区域数据，使用传入的数据\r\n      const districtData = {\r\n        shifuqu: this.chartData.shifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: this.chartData.meihuqu || [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: this.chartData.longquanqu || [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: this.chartData.kaifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: this.chartData.furongqu || [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      const districtData = {\r\n        shifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与DamageTypeChart保持一致的最小高度\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"trendChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 280px; // 🔧 与DamageTypeChart内部图表保持一致的最小高度\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAWA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,MAAA;MACAL,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAI,OAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,SAAAN,aAAA;MACA,KAAAA,aAAA,CAAAO,OAAA;IACA;IACAJ,MAAA,CAAAK,mBAAA,gBAAAH,YAAA;EACA;EACAI,KAAA;IACAnB,SAAA;MACAoB,OAAA,WAAAA,QAAA;QACA,KAAAC,WAAA;MACA;MACAC,IAAA;IACA;IACAlB,SAAA,WAAAA,UAAA;MACA,KAAAiB,WAAA;IACA;EACA;EACAE,OAAA;IACA;IACAX,SAAA,WAAAA,UAAA;MACA,SAAAY,KAAA,CAAAC,UAAA;QACA,KAAAf,aAAA,GAAAf,OAAA,CAAA+B,IAAA,MAAAF,KAAA,CAAAC,UAAA;QACA,KAAAJ,WAAA;MACA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MACA,UAAAX,aAAA;MAEA,IAAAD,IAAA,QAAAkB,YAAA;MAEA,IAAAC,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA9B,IAAA;YACA+B,KAAA;cACAC,eAAA;YACA;UACA;QACA;QACAC,MAAA;UACAzB,IAAA;UACA0B,GAAA;UACAC,SAAA;YACAC,KAAA;YACAC,QAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAP,GAAA;UACAQ,YAAA;QACA;QACAC,KAAA,GACA;UACA3C,IAAA;UACA4C,WAAA,OAAAzC,SAAA;UACAK,IAAA,EAAAA,IAAA,CAAAqC,UAAA;UACAC,SAAA;YACAV,KAAA;UACA;UACAW,aAAA;YACAX,KAAA;UACA;QACA,EACA;QACAY,KAAA,GACA;UACAhD,IAAA;UACAH,IAAA;UACAoD,GAAA;UACAC,QAAA;UACAH,aAAA;YACAX,KAAA;UACA;UACAU,SAAA;YACAK,SAAA;YACAf,KAAA;UACA;QACA,EACA;QACAgB,MAAA,GACA;UACAvD,IAAA;UACAG,IAAA;UACAqD,MAAA;UACA7C,IAAA,EAAAA,IAAA,CAAA8C,YAAA,CAAAC,OAAA;UACAC,SAAA;YACApB,KAAA;UACA;UACAqB,SAAA;YACAC,KAAA;UACA;QACA,GACA;UACA7D,IAAA;UACAG,IAAA;UACAqD,MAAA;UACA7C,IAAA,EAAAA,IAAA,CAAA8C,YAAA,CAAAK,OAAA;UACAH,SAAA;YACApB,KAAA;UACA;UACAqB,SAAA;YACAC,KAAA;UACA;QACA,GACA;UACA7D,IAAA;UACAG,IAAA;UACAqD,MAAA;UACA7C,IAAA,EAAAA,IAAA,CAAA8C,YAAA,CAAAM,UAAA;UACAJ,SAAA;YACApB,KAAA;UACA;UACAqB,SAAA;YACAC,KAAA;UACA;QACA,GACA;UACA7D,IAAA;UACAG,IAAA;UACAqD,MAAA;UACA7C,IAAA,EAAAA,IAAA,CAAA8C,YAAA,CAAAO,OAAA;UACAL,SAAA;YACApB,KAAA;UACA;UACAqB,SAAA;YACAC,KAAA;UACA;QACA,GACA;UACA7D,IAAA;UACAG,IAAA;UACAqD,MAAA;UACA7C,IAAA,EAAAA,IAAA,CAAA8C,YAAA,CAAAQ,QAAA;UACAN,SAAA;YACApB,KAAA;UACA;UACAqB,SAAA;YACAC,KAAA;UACA;QACA;MAEA;MAEA,KAAAjD,aAAA,CAAAsD,SAAA,CAAApC,MAAA;IACA;IAEA;IACAD,YAAA,WAAAA,aAAA;MACA,UAAA3B,SAAA,SAAAA,SAAA,CAAAiE,MAAA;QACA,YAAAC,cAAA;MACA;;MAEA;MACA,IAAApB,UAAA;;MAEA;MACA,IAAAS,YAAA;QACAC,OAAA,OAAAxD,SAAA,CAAAwD,OAAA;QACAI,OAAA,OAAA5D,SAAA,CAAA4D,OAAA;QACAC,UAAA,OAAA7D,SAAA,CAAA6D,UAAA;QACAC,OAAA,OAAA9D,SAAA,CAAA8D,OAAA;QACAC,QAAA,OAAA/D,SAAA,CAAA+D,QAAA;MACA;MAEA;QACAjB,UAAA,EAAAA,UAAA;QACAS,YAAA,EAAAA;MACA;IACA;IAEA;IACAW,cAAA,WAAAA,eAAA;MACA,IAAApB,UAAA;MAEA,IAAAS,YAAA;QACAC,OAAA;QACAI,OAAA;QACAC,UAAA;QACAC,OAAA;QACAC,QAAA;MACA;MAEA;QACAjB,UAAA,EAAAA,UAAA;QACAS,YAAA,EAAAA;MACA;IACA;IAEA;IACAxC,YAAA,WAAAA,aAAA;MACA,SAAAL,aAAA;QACA,KAAAA,aAAA,CAAAyD,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}