{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue", "mtime": 1758807462383}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\babel.config.js", "mtime": 1758282982460}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_BridgeSelector", "_interopRequireDefault", "require", "name", "components", "BridgeSelector", "props", "value", "type", "Object", "default", "infrastructureType", "String", "validator", "includes", "readonly", "Boolean", "data", "bridgeList", "showBridgeSelector", "watch", "handler", "newVal", "bridges", "Array", "isArray", "JSON", "stringify", "_toConsumableArray2", "immediate", "deep", "visible", "methods", "emitChange", "_this", "$nextTick", "$emit", "handleBridgeSelection", "selected<PERSON><PERSON><PERSON>", "processedBridges", "map", "bridge", "_objectSpread2", "maintenanceContent", "maintenanceProject", "maintenanceStaff", "length", "$message", "success", "concat", "removeBridgeAssociation", "index", "splice", "validate", "error"], "sources": ["src/views/maintenance/projects/create/components/BridgeConfig.vue"], "sourcesContent": ["<template>\n  <div class=\"bridge-config\">\n    <div v-if=\"!readonly\" class=\"config-header\">\n      <el-button\n        type=\"primary\"\n        icon=\"el-icon-connection\"\n        @click=\"showBridgeSelector = true\"\n      >\n        <i class=\"el-icon-s-home\" v-if=\"infrastructureType === 'bridge'\"></i>\n        <i class=\"el-icon-place\" v-else></i>\n        关联{{ infrastructureType === 'bridge' ? '桥梁' : '隧道' }}\n      </el-button>\n    </div>\n    \n    <!-- 已关联桥梁/隧道列表 -->\n    <div class=\"common-table\">\n      <el-table\n        :data=\"bridgeList\"\n        class=\"maintenance-table\"\n        :empty-text=\"`暂无关联${infrastructureType === 'bridge' ? '桥梁' : '隧道'}`\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        \n        <el-table-column \n          prop=\"name\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}名称`\" \n          min-width=\"120\" \n          show-overflow-tooltip \n        />\n        \n        <el-table-column \n          prop=\"code\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}编号`\" \n          width=\"120\" \n          align=\"center\" \n        />\n        \n        <el-table-column prop=\"road\" label=\"所在道路\" width=\"120\" align=\"center\" />\n        \n        <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceContent\" label=\"养护内容\" min-width=\"150\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceStaff\" label=\"养护人员\" width=\"100\" align=\"center\" />\n        \n        <el-table-column v-if=\"!readonly\" label=\"操作\" width=\"80\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              class=\"danger-text\"\n              @click=\"removeBridgeAssociation(scope.$index)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <!-- 桥梁/隧道选择器 -->\n    <bridge-selector\n      :visible.sync=\"showBridgeSelector\"\n      :multiple=\"true\"\n      :selected-data=\"bridgeList\"\n      :infrastructure-type=\"infrastructureType\"\n      @confirm=\"handleBridgeSelection\"\n    />\n  </div>\n</template>\n\n<script>\nimport BridgeSelector from '@/components/Maintenance/BridgeSelector'\n\nexport default {\n  name: 'BridgeConfig',\n  components: {\n    BridgeSelector\n  },\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge', // bridge, tunnel\n      validator: value => ['bridge', 'tunnel'].includes(value)\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      bridgeList: [],\n      showBridgeSelector: false\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && newVal.bridges && Array.isArray(newVal.bridges)) {\n          // 只在数据真正不同时才更新，避免循环\n          if (JSON.stringify(newVal.bridges) !== JSON.stringify(this.bridgeList)) {\n            this.bridgeList = [...newVal.bridges]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    showBridgeSelector(visible) {\n      // 桥梁选择器显示状态变化\n    }\n  },\n  methods: {\n    // 统一的数据更新方法\n    emitChange() {\n      this.$nextTick(() => {\n        this.$emit('input', {\n          bridges: this.bridgeList\n        })\n      })\n    },\n    \n    // 处理桥梁选择\n    handleBridgeSelection(selectedBridges) {\n      // 为新选择的桥梁/隧道设置默认的养护内容和人员\n      const processedBridges = selectedBridges.map(bridge => ({\n        ...bridge,\n        maintenanceContent: bridge.maintenanceProject || '排水系统养护',\n        maintenanceStaff: bridge.maintenanceStaff || '待分配'\n      }))\n\n      this.bridgeList = processedBridges\n\n      if (processedBridges.length > 0) {\n        this.$message.success(`成功关联 ${processedBridges.length} 个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        this.emitChange()\n      }\n    },\n\n    // 移除桥梁/隧道关联\n    removeBridgeAssociation(index) {\n      this.bridgeList.splice(index, 1)\n      this.$message.success(`已移除${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}关联`)\n      this.emitChange()\n    },\n    \n    // 表单验证\n    validate() {\n      if (this.bridgeList.length === 0) {\n        this.$message.error(`请至少关联一个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        return false\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/styles/maintenance-theme.scss';\n\n.bridge-config {\n  .config-header {\n    margin-bottom: 24px;\n    \n    .el-button {\n      i {\n        margin-right: 4px;\n      }\n    }\n  }\n  \n  .common-table {\n    .maintenance-table {\n      min-height: 200px;\n    }\n  }\n  \n  .danger-text {\n    color: #ef4444 !important;\n    \n    &:hover {\n      color: #dc2626 !important;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;AAwEA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,kBAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;MAAA;MACAG,SAAA,WAAAA,UAAAN,KAAA;QAAA,4BAAAO,QAAA,CAAAP,KAAA;MAAA;IACA;IACAQ,QAAA;MACAP,IAAA,EAAAQ,OAAA;MACAN,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,kBAAA;IACA;EACA;EACAC,KAAA;IACA;IACAb,KAAA;MACAc,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,OAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAH,MAAA,CAAAC,OAAA;UACA;UACA,IAAAG,IAAA,CAAAC,SAAA,CAAAL,MAAA,CAAAC,OAAA,MAAAG,IAAA,CAAAC,SAAA,MAAAT,UAAA;YACA,KAAAA,UAAA,OAAAU,mBAAA,CAAAlB,OAAA,EAAAY,MAAA,CAAAC,OAAA;UACA;QACA;MACA;MACAM,SAAA;MACAC,IAAA;IACA;IAEAX,kBAAA,WAAAA,mBAAAY,OAAA;MACA;IAAA;EAEA;EACAC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAAE,KAAA;UACAb,OAAA,EAAAW,KAAA,CAAAhB;QACA;MACA;IACA;IAEA;IACAmB,qBAAA,WAAAA,sBAAAC,eAAA;MACA;MACA,IAAAC,gBAAA,GAAAD,eAAA,CAAAE,GAAA,WAAAC,MAAA;QAAA,WAAAC,cAAA,CAAAhC,OAAA,MAAAgC,cAAA,CAAAhC,OAAA,MACA+B,MAAA;UACAE,kBAAA,EAAAF,MAAA,CAAAG,kBAAA;UACAC,gBAAA,EAAAJ,MAAA,CAAAI,gBAAA;QAAA;MAAA,CACA;MAEA,KAAA3B,UAAA,GAAAqB,gBAAA;MAEA,IAAAA,gBAAA,CAAAO,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA,6BAAAC,MAAA,CAAAV,gBAAA,CAAAO,MAAA,aAAAG,MAAA,MAAAtC,kBAAA;QACA,KAAAsB,UAAA;MACA;IACA;IAEA;IACAiB,uBAAA,WAAAA,wBAAAC,KAAA;MACA,KAAAjC,UAAA,CAAAkC,MAAA,CAAAD,KAAA;MACA,KAAAJ,QAAA,CAAAC,OAAA,sBAAAC,MAAA,MAAAtC,kBAAA;MACA,KAAAsB,UAAA;IACA;IAEA;IACAoB,QAAA,WAAAA,SAAA;MACA,SAAAnC,UAAA,CAAA4B,MAAA;QACA,KAAAC,QAAA,CAAAO,KAAA,8CAAAL,MAAA,MAAAtC,kBAAA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}