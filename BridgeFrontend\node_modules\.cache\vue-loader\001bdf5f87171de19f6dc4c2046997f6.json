{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUHJvamVjdERldGFpbERpYWxvZyBmcm9tICcuL1Byb2plY3REZXRhaWxEaWFsb2cudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQcm9qZWN0c1ZpZXcnLA0KICBjb21wb25lbnRzOiB7DQogICAgUHJvamVjdERldGFpbERpYWxvZw0KICB9LA0KICBwcm9wczogew0KICAgIHJlcGFpckRhdGE6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOetm+mAieWZqOaVsOaNrg0KICAgICAgZmlsdGVyczogew0KICAgICAgICBicmlkZ2VOYW1lOiAnJywNCiAgICAgICAgcHJvamVjdFR5cGU6ICcnLA0KICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICByZXNwb25zaWJsZTogJycNCiAgICAgIH0sDQogICAgICANCiAgICAgIC8vIOetm+mAieWZqOmAiemhuQ0KICAgICAgYnJpZGdlT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAnWFhYWFhY5aSn5qGlJywgdmFsdWU6ICdicmlkZ2UxJyB9LA0KICAgICAgICB7IGxhYmVsOiAnWVlZWVlZ5aSn5qGlJywgdmFsdWU6ICdicmlkZ2UyJyB9LA0KICAgICAgICB7IGxhYmVsOiAnWlpaWlpa5aSn5qGlJywgdmFsdWU6ICdicmlkZ2UzJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICBwcm9qZWN0T3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5o6S5rC057O757uf5YW75oqkJywgdmFsdWU6ICdkcmFpbmFnZScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S4iumDqOe7k+aehOWFu+aKpCcsIHZhbHVlOiAnc3VwZXJzdHJ1Y3R1cmUnIH0sDQogICAgICAgIHsgbGFiZWw6ICfkuIvpg6jnu5PmnoTlhbvmiqQnLCB2YWx1ZTogJ3N1YnN0cnVjdHVyZScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+ahpemdouezu+WFu+aKpCcsIHZhbHVlOiAnZGVjaycgfQ0KICAgICAgXSwNCiAgICAgIA0KICAgICAgc3RhdHVzT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5pyq5a6M5oiQJywgdmFsdWU6ICdwZW5kaW5nJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5a6h5qC45LitJywgdmFsdWU6ICdyZXZpZXdpbmcnIH0sDQogICAgICAgIHsgbGFiZWw6ICflpI3moLjkuK0nLCB2YWx1ZTogJ3JlY2hlY2tpbmcnIH0sDQogICAgICAgIHsgbGFiZWw6ICfpgIDlm54nLCB2YWx1ZTogJ3JlamVjdGVkJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5bey5a6M5oiQJywgdmFsdWU6ICdjb21wbGV0ZWQnIH0NCiAgICAgIF0sDQogICAgICANCiAgICAgIHJlc3BvbnNpYmxlT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn6buE5pit6KiAJywgdmFsdWU6ICdodWFuZycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WImOmbqOahkCcsIHZhbHVlOiAnbGl1JyB9LA0KICAgICAgICB7IGxhYmVsOiAn572X6aKW56eLJywgdmFsdWU6ICdsdW8nIH0sDQogICAgICAgIHsgbGFiZWw6ICfmnpfmlofpvpknLCB2YWx1ZTogJ2xpbicgfSwNCiAgICAgICAgeyBsYWJlbDogJ+mrmOaeleS5picsIHZhbHVlOiAnZ2FvJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5b6Q5qGn5qGQJywgdmFsdWU6ICd4dScgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S9leWPlOW3nScsIHZhbHVlOiAnaGUnIH0sDQogICAgICAgIHsgbGFiZWw6ICfpg63kupHoiJ8nLCB2YWx1ZTogJ2d1bycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+m7hOaik+iIqicsIHZhbHVlOiAnaHVhbmcyJyB9LA0KICAgICAgICB7IGxhYmVsOiAn6LW15pmv5rexJywgdmFsdWU6ICd6aGFvJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDooajmoLzmlbDmja4NCiAgICAgIHRhYmxlRGF0YTogWw0KICAgICAgICB7DQogICAgICAgICAgc2VyaWFsTnVtYmVyOiAnMScsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFhYWOWkp+ahpScsDQogICAgICAgICAgcHJvamVjdFR5cGU6ICfmjpLmsLTns7vnu5/lhbvmiqQnLA0KICAgICAgICAgIHN0YXR1czogJ+acquWujOaIkCcsDQogICAgICAgICAgcmVzcG9uc2libGU6ICfpu4TmmK3oqIAnLA0KICAgICAgICAgIGNvbnRhY3RQaG9uZTogJzE1ODIwMDA3Mzk0JywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBzZXJpYWxOdW1iZXI6ICcyJywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBwcm9qZWN0VHlwZTogJ+aOkuawtOezu+e7n+WFu+aKpCcsDQogICAgICAgICAgc3RhdHVzOiAn5a6h5qC45LitJywNCiAgICAgICAgICByZXNwb25zaWJsZTogJ+WImOmbqOahkCcsDQogICAgICAgICAgY29udGFjdFBob25lOiAnMTMxMjIyMzg1NzknLA0KICAgICAgICAgIGNvbXBsZXRpb25UaW1lOiAnMjAyNS0wOS0xOCAxMDo0MycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHNlcmlhbE51bWJlcjogJzMnLA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhYWFjlpKfmoaUnLA0KICAgICAgICAgIHByb2plY3RUeXBlOiAn5o6S5rC057O757uf5YW75oqkJywNCiAgICAgICAgICBzdGF0dXM6ICfpgIDlm54nLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn572X6aKW56eLJywNCiAgICAgICAgICBjb250YWN0UGhvbmU6ICcxOTYyMDA1OTQ4MycsDQogICAgICAgICAgY29tcGxldGlvblRpbWU6ICcyMDI1LTA5LTE4IDEwOjQzJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgc2VyaWFsTnVtYmVyOiAnNCcsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFhYWOWkp+ahpScsDQogICAgICAgICAgcHJvamVjdFR5cGU6ICfkuIrpg6jnu5PmnoTlhbvmiqQnLA0KICAgICAgICAgIHN0YXR1czogJ+WkjeaguOS4rScsDQogICAgICAgICAgcmVzcG9uc2libGU6ICfmnpfmlofpvpknLA0KICAgICAgICAgIGNvbnRhY3RQaG9uZTogJzE5NjA3NTU5NDgzJywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBzZXJpYWxOdW1iZXI6ICc1JywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBwcm9qZWN0VHlwZTogJ+S4iumDqOe7k+aehOWFu+aKpCcsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJywNCiAgICAgICAgICByZXNwb25zaWJsZTogJ+mrmOaeleS5picsDQogICAgICAgICAgY29udGFjdFBob25lOiAnMTg1NTcxODk0ODMnLA0KICAgICAgICAgIGNvbXBsZXRpb25UaW1lOiAnMjAyNS0wOS0xOCAxMDo0MycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHNlcmlhbE51bWJlcjogJzYnLA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhYWFjlpKfmoaUnLA0KICAgICAgICAgIHByb2plY3RUeXBlOiAn5LiK6YOo57uT5p6E5YW75oqkJywNCiAgICAgICAgICBzdGF0dXM6ICflt7LlrozmiJAnLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn5b6Q5qGn5qGQJywNCiAgICAgICAgICBjb250YWN0UGhvbmU6ICcxOTAyMDAxNzQ5NScsDQogICAgICAgICAgY29tcGxldGlvblRpbWU6ICcyMDI1LTA5LTE4IDEwOjQzJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgc2VyaWFsTnVtYmVyOiAnMDA3JywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBwcm9qZWN0VHlwZTogJ+S4iumDqOe7k+aehOWFu+aKpCcsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJywNCiAgICAgICAgICByZXNwb25zaWJsZTogJ+S9leWPlOW3nScsDQogICAgICAgICAgY29udGFjdFBob25lOiAnMTkwMjAwMTc0OTUnLA0KICAgICAgICAgIGNvbXBsZXRpb25UaW1lOiAnMjAyNS0wOS0xOCAxMDo0MycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHNlcmlhbE51bWJlcjogJzAwOCcsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFhYWOWkp+ahpScsDQogICAgICAgICAgcHJvamVjdFR5cGU6ICfkuIrpg6jnu5PmnoTlhbvmiqQnLA0KICAgICAgICAgIHN0YXR1czogJ+W3suWujOaIkCcsDQogICAgICAgICAgcmVzcG9uc2libGU6ICfpg63kupHoiJ8nLA0KICAgICAgICAgIGNvbnRhY3RQaG9uZTogJzE5MDIwMDE3NDk1JywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBzZXJpYWxOdW1iZXI6ICcwMDknLA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhYWFjlpKfmoaUnLA0KICAgICAgICAgIHByb2plY3RUeXBlOiAn5LiK6YOo57uT5p6E5YW75oqkJywNCiAgICAgICAgICBzdGF0dXM6ICflt7LlrozmiJAnLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn6buE5qKT6IiqJywNCiAgICAgICAgICBjb250YWN0UGhvbmU6ICcxOTAyMDAxNzQ5NScsDQogICAgICAgICAgY29tcGxldGlvblRpbWU6ICcyMDI1LTA5LTE4IDEwOjQzJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgc2VyaWFsTnVtYmVyOiAnMDEwJywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBwcm9qZWN0VHlwZTogJ+S4iumDqOe7k+aehOWFu+aKpCcsDQogICAgICAgICAgc3RhdHVzOiAn5bey5a6M5oiQJywNCiAgICAgICAgICByZXNwb25zaWJsZTogJ+i1teaZr+a3sScsDQogICAgICAgICAgY29udGFjdFBob25lOiAnMTkwMjAwMTc0OTUnLA0KICAgICAgICAgIGNvbXBsZXRpb25UaW1lOiAnMjAyNS0wOS0xOCAxMDo0MycNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIA0KICAgICAgLy8g6K+m5oOF5by556qX55u45YWzDQogICAgICBkZXRhaWxEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNlbGVjdGVkUHJvamVjdDoge30NCiAgICB9DQogIH0sDQogIA0KICBjb21wdXRlZDogew0KICAgIC8vIOi/h+a7pOWQjueahOihqOagvOaVsOaNrg0KICAgIGZpbHRlcmVkVGFibGVEYXRhKCkgew0KICAgICAgbGV0IGRhdGEgPSBbLi4udGhpcy50YWJsZURhdGFdDQogICAgICANCiAgICAgIGlmICh0aGlzLmZpbHRlcnMuYnJpZGdlTmFtZSkgew0KICAgICAgICBkYXRhID0gZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLmJyaWRnZU5hbWUuaW5jbHVkZXModGhpcy5maWx0ZXJzLmJyaWRnZU5hbWUpKQ0KICAgICAgfQ0KICAgICAgDQogICAgICBpZiAodGhpcy5maWx0ZXJzLnByb2plY3RUeXBlKSB7DQogICAgICAgIGNvbnN0IHByb2plY3RMYWJlbCA9IHRoaXMucHJvamVjdE9wdGlvbnMuZmluZChwID0+IHAudmFsdWUgPT09IHRoaXMuZmlsdGVycy5wcm9qZWN0VHlwZSk/LmxhYmVsDQogICAgICAgIGlmIChwcm9qZWN0TGFiZWwpIHsNCiAgICAgICAgICBkYXRhID0gZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLnByb2plY3RUeXBlID09PSBwcm9qZWN0TGFiZWwpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgaWYgKHRoaXMuZmlsdGVycy5zdGF0dXMpIHsNCiAgICAgICAgY29uc3Qgc3RhdHVzTGFiZWwgPSB0aGlzLnN0YXR1c09wdGlvbnMuZmluZChzID0+IHMudmFsdWUgPT09IHRoaXMuZmlsdGVycy5zdGF0dXMpPy5sYWJlbA0KICAgICAgICBpZiAoc3RhdHVzTGFiZWwpIHsNCiAgICAgICAgICBkYXRhID0gZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLnN0YXR1cyA9PT0gc3RhdHVzTGFiZWwpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgaWYgKHRoaXMuZmlsdGVycy5yZXNwb25zaWJsZSkgew0KICAgICAgICBjb25zdCByZXNwb25zaWJsZUxhYmVsID0gdGhpcy5yZXNwb25zaWJsZU9wdGlvbnMuZmluZChyID0+IHIudmFsdWUgPT09IHRoaXMuZmlsdGVycy5yZXNwb25zaWJsZSk/LmxhYmVsDQogICAgICAgIGlmIChyZXNwb25zaWJsZUxhYmVsKSB7DQogICAgICAgICAgZGF0YSA9IGRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yZXNwb25zaWJsZSA9PT0gcmVzcG9uc2libGVMYWJlbCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gZGF0YQ0KICAgIH0sDQogICAgDQogICAgLy8g5oC75pWw6YePDQogICAgdG90YWxDb3VudCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnRhYmxlRGF0YS5sZW5ndGgNCiAgICB9LA0KICAgIA0KICAgIC8vIOW3suWujOaIkOaVsOmHjw0KICAgIGNvbXBsZXRlZENvdW50KCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFibGVEYXRhLmZpbHRlcihpdGVtID0+IGl0ZW0uc3RhdHVzID09PSAn5bey5a6M5oiQJykubGVuZ3RoDQogICAgfQ0KICB9LA0KICANCiAgbWV0aG9kczogew0KICAgIC8vIOiOt+WPlueKtuaAgeagh+etvuexu+Weiw0KICAgIGdldFN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICfmnKrlrozmiJAnOiAnaW5mbycsDQogICAgICAgICflrqHmoLjkuK0nOiAnd2FybmluZycsDQogICAgICAgICflpI3moLjkuK0nOiAnd2FybmluZycsDQogICAgICAgICfpgIDlm54nOiAnZGFuZ2VyJywNCiAgICAgICAgJ+W3suWujOaIkCc6ICdzdWNjZXNzJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJw0KICAgIH0sDQogICAgDQogICAgLy8g5p+l6K+iDQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5pCc57Si6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5pCc57Si5p2h5Lu2OicsIHRoaXMuZmlsdGVycykNCiAgICB9LA0KICAgIA0KICAgIC8vIOmHjee9rg0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5maWx0ZXJzID0gew0KICAgICAgICBicmlkZ2VOYW1lOiAnJywNCiAgICAgICAgcHJvamVjdFR5cGU6ICcnLA0KICAgICAgICBzdGF0dXM6ICcnLA0KICAgICAgICByZXNwb25zaWJsZTogJycNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOafpeeci+ivpuaDhQ0KICAgIGhhbmRsZVZpZXdEZXRhaWwocm93KSB7DQogICAgICBjb25zb2xlLmxvZygn5p+l55yL6K+m5oOFOicsIHJvdykNCiAgICAgIHRoaXMuc2VsZWN0ZWRQcm9qZWN0ID0geyAuLi5yb3cgfQ0KICAgICAgdGhpcy5kZXRhaWxEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgDQogICAgLy8g5YWz6Zet6K+m5oOF5by556qXDQogICAgaGFuZGxlRGV0YWlsRGlhbG9nQ2xvc2UoKSB7DQogICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5zZWxlY3RlZFByb2plY3QgPSB7fQ0KICAgICAgLy8g56Gu5L+d56e76Zmk6YGu572p5bGCDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IG1vZGFsID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLnYtbW9kYWwnKQ0KICAgICAgICBpZiAobW9kYWwpIHsNCiAgICAgICAgICBtb2RhbC5yZW1vdmUoKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["ProjectsView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectsView.vue", "sourceRoot": "src/views/maintenance/repairs/components/detail", "sourcesContent": ["<template>\r\n  <div class=\"projects-view maintenance-theme\">\r\n    <!-- 筛选器区域 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-row\">\r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.bridgeName\" \r\n            placeholder=\"桥梁名称\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"bridge in bridgeOptions\" \r\n              :key=\"bridge.value\" \r\n              :label=\"bridge.label\" \r\n              :value=\"bridge.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.projectType\" \r\n            placeholder=\"养护项目\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"project in projectOptions\" \r\n              :key=\"project.value\" \r\n              :label=\"project.label\" \r\n              :value=\"project.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.status\" \r\n            placeholder=\"状态\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"status in statusOptions\" \r\n              :key=\"status.value\" \r\n              :label=\"status.label\" \r\n              :value=\"status.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.responsible\" \r\n            placeholder=\"负责人\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"person in responsibleOptions\" \r\n              :key=\"person.value\" \r\n              :label=\"person.label\" \r\n              :value=\"person.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-actions\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 完成量统计 -->\r\n    <div class=\"completion-stats\">\r\n      <span class=\"stats-text\">完成量 {{ completedCount }}/{{ totalCount }}</span>\r\n    </div>\r\n    \r\n    <!-- 数据表格 -->\r\n    <div class=\"table-section\">\r\n      <el-table \r\n        :data=\"filteredTableData\" \r\n        class=\"projects-table\"\r\n        header-row-class-name=\"table-header\"\r\n        row-class-name=\"table-row\"\r\n      >\r\n        <el-table-column prop=\"serialNumber\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"140\" />\r\n        <el-table-column prop=\"projectType\" label=\"养护项目\" min-width=\"140\" />\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-tag \r\n              :type=\"getStatusType(scope.row.status)\" \r\n              size=\"small\"\r\n              class=\"status-tag\"\r\n            >\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"responsible\" label=\"负责人\" width=\"100\" align=\"center\" />\r\n        <el-table-column prop=\"contactPhone\" label=\"联系方式\" width=\"130\" />\r\n        <el-table-column prop=\"completionTime\" label=\"完成时间\" width=\"160\" />\r\n        <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\" \r\n              @click=\"handleViewDetail(scope.row)\"\r\n              class=\"action-btn\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    \r\n    <!-- 项目详情弹窗 -->\r\n    <ProjectDetailDialog \r\n      :visible.sync=\"detailDialogVisible\"\r\n      :project-info=\"selectedProject\"\r\n      @close=\"handleDetailDialogClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectDetailDialog from './ProjectDetailDialog.vue'\r\n\r\nexport default {\r\n  name: 'ProjectsView',\r\n  components: {\r\n    ProjectDetailDialog\r\n  },\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 筛选器数据\r\n      filters: {\r\n        bridgeName: '',\r\n        projectType: '',\r\n        status: '',\r\n        responsible: ''\r\n      },\r\n      \r\n      // 筛选器选项\r\n      bridgeOptions: [\r\n        { label: 'XXXXXX大桥', value: 'bridge1' },\r\n        { label: 'YYYYYY大桥', value: 'bridge2' },\r\n        { label: 'ZZZZZZ大桥', value: 'bridge3' }\r\n      ],\r\n      \r\n      projectOptions: [\r\n        { label: '排水系统养护', value: 'drainage' },\r\n        { label: '上部结构养护', value: 'superstructure' },\r\n        { label: '下部结构养护', value: 'substructure' },\r\n        { label: '桥面系养护', value: 'deck' }\r\n      ],\r\n      \r\n      statusOptions: [\r\n        { label: '未完成', value: 'pending' },\r\n        { label: '审核中', value: 'reviewing' },\r\n        { label: '复核中', value: 'rechecking' },\r\n        { label: '退回', value: 'rejected' },\r\n        { label: '已完成', value: 'completed' }\r\n      ],\r\n      \r\n      responsibleOptions: [\r\n        { label: '黄昭言', value: 'huang' },\r\n        { label: '刘雨桐', value: 'liu' },\r\n        { label: '罗颖秋', value: 'luo' },\r\n        { label: '林文龙', value: 'lin' },\r\n        { label: '高枕书', value: 'gao' },\r\n        { label: '徐桧桐', value: 'xu' },\r\n        { label: '何叔川', value: 'he' },\r\n        { label: '郭云舟', value: 'guo' },\r\n        { label: '黄梓航', value: 'huang2' },\r\n        { label: '赵景深', value: 'zhao' }\r\n      ],\r\n      \r\n      // 表格数据\r\n      tableData: [\r\n        {\r\n          serialNumber: '1',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '未完成',\r\n          responsible: '黄昭言',\r\n          contactPhone: '15820007394',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '2',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '审核中',\r\n          responsible: '刘雨桐',\r\n          contactPhone: '13122238579',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '3',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '退回',\r\n          responsible: '罗颖秋',\r\n          contactPhone: '19620059483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '4',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '复核中',\r\n          responsible: '林文龙',\r\n          contactPhone: '19607559483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '5',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '高枕书',\r\n          contactPhone: '18557189483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '6',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '徐桧桐',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '007',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '何叔川',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '008',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '郭云舟',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '009',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '黄梓航',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '010',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '赵景深',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        }\r\n      ],\r\n      \r\n      // 详情弹窗相关\r\n      detailDialogVisible: false,\r\n      selectedProject: {}\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 过滤后的表格数据\r\n    filteredTableData() {\r\n      let data = [...this.tableData]\r\n      \r\n      if (this.filters.bridgeName) {\r\n        data = data.filter(item => item.bridgeName.includes(this.filters.bridgeName))\r\n      }\r\n      \r\n      if (this.filters.projectType) {\r\n        const projectLabel = this.projectOptions.find(p => p.value === this.filters.projectType)?.label\r\n        if (projectLabel) {\r\n          data = data.filter(item => item.projectType === projectLabel)\r\n        }\r\n      }\r\n      \r\n      if (this.filters.status) {\r\n        const statusLabel = this.statusOptions.find(s => s.value === this.filters.status)?.label\r\n        if (statusLabel) {\r\n          data = data.filter(item => item.status === statusLabel)\r\n        }\r\n      }\r\n      \r\n      if (this.filters.responsible) {\r\n        const responsibleLabel = this.responsibleOptions.find(r => r.value === this.filters.responsible)?.label\r\n        if (responsibleLabel) {\r\n          data = data.filter(item => item.responsible === responsibleLabel)\r\n        }\r\n      }\r\n      \r\n      return data\r\n    },\r\n    \r\n    // 总数量\r\n    totalCount() {\r\n      return this.tableData.length\r\n    },\r\n    \r\n    // 已完成数量\r\n    completedCount() {\r\n      return this.tableData.filter(item => item.status === '已完成').length\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 获取状态标签类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        '未完成': 'info',\r\n        '审核中': 'warning',\r\n        '复核中': 'warning',\r\n        '退回': 'danger',\r\n        '已完成': 'success'\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n    \r\n    // 查询\r\n    handleSearch() {\r\n      // 这里可以添加搜索逻辑\r\n      console.log('搜索条件:', this.filters)\r\n    },\r\n    \r\n    // 重置\r\n    handleReset() {\r\n      this.filters = {\r\n        bridgeName: '',\r\n        projectType: '',\r\n        status: '',\r\n        responsible: ''\r\n      }\r\n    },\r\n    \r\n    // 查看详情\r\n    handleViewDetail(row) {\r\n      console.log('查看详情:', row)\r\n      this.selectedProject = { ...row }\r\n      this.detailDialogVisible = true\r\n    },\r\n    \r\n    // 关闭详情弹窗\r\n    handleDetailDialogClose() {\r\n      this.detailDialogVisible = false\r\n      this.selectedProject = {}\r\n      // 确保移除遮罩层\r\n      this.$nextTick(() => {\r\n        const modal = document.querySelector('.v-modal')\r\n        if (modal) {\r\n          modal.remove()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.projects-view {\r\n  // 筛选器区域\r\n  .filter-section {\r\n    background: #1e3a8a;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    margin-bottom: 16px;\r\n    \r\n    .filter-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n      \r\n      .filter-item {\r\n        flex: 1;\r\n        min-width: 160px;\r\n        \r\n        .filter-select {\r\n          width: 100%;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #1a2332;\r\n            border: 1px solid #374151;\r\n            color: #e5e7eb;\r\n            \r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n            \r\n            &:hover {\r\n              border-color: #4f46e5;\r\n            }\r\n            \r\n            &:focus {\r\n              border-color: #4f46e5;\r\n            }\r\n          }\r\n          \r\n          :deep(.el-input__suffix) {\r\n            .el-input__suffix-inner {\r\n              color: #9ca3af;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .filter-actions {\r\n        display: flex;\r\n        gap: 12px;\r\n        \r\n        .el-button {\r\n          &--primary {\r\n            background: #3b82f6;\r\n            border-color: #3b82f6;\r\n            \r\n            &:hover {\r\n              background: #2563eb;\r\n              border-color: #2563eb;\r\n            }\r\n          }\r\n          \r\n          &:not(.el-button--primary) {\r\n            background: #374151;\r\n            border-color: #374151;\r\n            color: #e5e7eb;\r\n            \r\n            &:hover {\r\n              background: #4b5563;\r\n              border-color: #4b5563;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 完成量统计\r\n  .completion-stats {\r\n    margin-bottom: 16px;\r\n    \r\n    .stats-text {\r\n      color: #e5e7eb;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  // 表格区域\r\n  .table-section {\r\n    .projects-table {\r\n      @extend .common-table;\r\n    }\r\n  }\r\n}\r\n\r\n// 下拉框选项样式\r\n:deep(.el-select-dropdown) {\r\n  background: #1a2332 !important;\r\n  border: 1px solid #374151 !important;\r\n  \r\n  .el-select-dropdown__item {\r\n    background: #1a2332 !important;\r\n    color: #e5e7eb !important;\r\n    \r\n    &:hover {\r\n      background: #1e3a8a !important;\r\n    }\r\n    \r\n    &.selected {\r\n      background: #1e3a8a !important;\r\n      color: #3b82f6 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}