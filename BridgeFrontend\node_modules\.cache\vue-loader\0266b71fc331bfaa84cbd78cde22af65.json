{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue?vue&type=style&index=0&id=7b81f5d4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue", "mtime": 1758811136136}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProjectConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyNA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\r\n  <div class=\"project-config\">\r\n    <!-- 添加项目按钮 - 按原型图结构 -->\r\n    <div v-if=\"!readonly\" class=\"add-project-row\">\r\n      <el-button \r\n        type=\"primary\" \r\n        icon=\"el-icon-plus\"\r\n        class=\"add-project-btn\"\r\n        @click=\"addProject\"\r\n      >\r\n        添加项目\r\n      </el-button>\r\n    </div>\r\n    \r\n    <!-- 项目列表 - 按原型图每行一个项目的结构 -->\r\n    <div class=\"project-list\">\r\n      <div \r\n        v-for=\"(project, index) in projectList\" \r\n        :key=\"index\"\r\n        class=\"project-row\"\r\n      >\r\n        <div class=\"project-input-container\">\r\n          <el-input\r\n            v-model=\"project.name\"\r\n            :placeholder=\"getPlaceholder()\"\r\n            class=\"project-name-input\"\r\n            :disabled=\"readonly\"\r\n            @blur=\"validateProject(index)\"\r\n            @input=\"emitChange\"\r\n          />\r\n          \r\n          <!-- 保洁项目的频次配置 - 按原型图布局 -->\r\n          <template v-if=\"projectType === 'cleaning'\">\r\n            <el-input\r\n              v-model=\"project.frequency\"\r\n              type=\"number\"\r\n              min=\"1\"\r\n              max=\"365\"\r\n              class=\"frequency-input\"\r\n              :disabled=\"readonly\"\r\n              @input=\"emitChange\"\r\n            />\r\n            <span class=\"frequency-unit\">天/1次</span>\r\n          </template>\r\n        </div>\r\n        \r\n        <el-button\r\n          v-if=\"!readonly\"\r\n          type=\"text\"\r\n          class=\"cancel-btn\"\r\n          @click=\"removeProject(index)\"\r\n        >\r\n          取消\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProjectConfig',\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectType: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      projectList: []\r\n    }\r\n  },\r\n  watch: {\r\n    // 只监听外部传入的value，单向数据流\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && newVal.projects && Array.isArray(newVal.projects) && newVal.projects.length > 0) {\r\n          // 只在数据真正不同时才更新，避免循环\r\n          if (JSON.stringify(newVal.projects) !== JSON.stringify(this.projectList)) {\r\n            this.projectList = [...newVal.projects]\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    // 监听项目类型变化，重新初始化\r\n    projectType: {\r\n      handler(newType, oldType) {\r\n        if (newType !== oldType && this.projectList.length > 0) {\r\n          this.projectList = []\r\n          this.$nextTick(() => {\r\n            this.initializeProject()\r\n            this.emitChange()\r\n          })\r\n        }\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时确保至少有一个空项目\r\n    if (this.projectList.length === 0) {\r\n      this.initializeProject()\r\n      this.emitChange()\r\n    }\r\n  },\r\n  methods: {\r\n    // 统一的数据更新方法\r\n    emitChange() {\r\n      this.$nextTick(() => {\r\n        this.$emit('input', {\r\n          projects: this.projectList\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 初始化项目（仅在组件初始化时使用）\r\n    initializeProject() {\r\n      if (this.projectList.length === 0) {\r\n        const newProject = {\r\n          name: '',\r\n          frequency: this.projectType === 'cleaning' ? 7 : null\r\n        }\r\n        this.projectList = [newProject]\r\n      }\r\n    },\r\n    \r\n    // 添加项目（用户点击按钮时使用）\r\n    addProject() {\r\n      const newProject = {\r\n        name: '',\r\n        frequency: this.projectType === 'cleaning' ? 7 : null\r\n      }\r\n      this.projectList.push(newProject)\r\n      this.emitChange()\r\n    },\r\n    \r\n    // 移除项目\r\n    removeProject(index) {\r\n      if (this.projectList.length > 1) {\r\n        this.projectList.splice(index, 1)\r\n        this.emitChange()\r\n      } else {\r\n        this.$message.warning('至少需要保留一个项目')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证项目\r\n    validateProject(index) {\r\n      const project = this.projectList[index]\r\n      if (!project.name) {\r\n        return\r\n      }\r\n      \r\n      // 检查重复\r\n      const duplicateIndex = this.projectList.findIndex((p, i) => \r\n        i !== index && p.name === project.name\r\n      )\r\n      \r\n      if (duplicateIndex !== -1) {\r\n        this.$message.warning('项目名称不能重复')\r\n        project.name = ''\r\n      }\r\n    },\r\n    \r\n    // 获取输入框占位符\r\n    getPlaceholder() {\r\n      const placeholders = {\r\n        monthly: '请输入养护项目名称',\r\n        cleaning: '请输入保洁项目名称',\r\n        emergency: '请输入应急项目名称',\r\n        preventive: '请输入预防养护项目名称'\r\n      }\r\n      return placeholders[this.projectType] || '请输入项目名称'\r\n    },\r\n    \r\n    \r\n    // 表单验证\r\n    validate() {\r\n      // 检查是否有有效项目\r\n      const validProjects = this.projectList.filter(project => project.name.trim())\r\n      \r\n      if (validProjects.length === 0) {\r\n        this.$message.error('请至少添加一个项目')\r\n        return false\r\n      }\r\n      \r\n      // 检查保洁项目的频次\r\n      if (this.projectType === 'cleaning') {\r\n        const invalidFrequency = validProjects.some(project => \r\n          !project.frequency || project.frequency < 1 || project.frequency > 365\r\n        )\r\n        \r\n        if (invalidFrequency) {\r\n          this.$message.error('请设置正确的清洁频次（1-365天）')\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n\r\n.project-config {\r\n  @extend .common-project-config;\r\n  padding: 20px;\r\n  .add-project-row {\r\n    margin-bottom: 24px; // 增加与下方内容的间距\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    \r\n    // 按钮样式已通过公共样式提供\r\n  }\r\n  \r\n  // 项目列表区域\r\n  .project-list {\r\n    .project-row {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      gap: 12px; // 设置各元素之间的间距\r\n      \r\n      .project-input-container {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        \r\n        .project-name-input {\r\n          flex: 1;\r\n          min-width: 200px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            \r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 保洁项目的频次输入框\r\n        .frequency-input {\r\n          width: 80px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            text-align: center;\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        .frequency-unit {\r\n          color: #e5e7eb;\r\n          font-size: 14px;\r\n          white-space: nowrap;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n      \r\n      .cancel-btn {\r\n        color: #ef4444;\r\n        font-size: 14px;\r\n        min-width: 60px;\r\n        height: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        \r\n        &:hover {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n        \r\n        &:focus {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 确保最后一行不会有多余的margin\r\n    .project-row:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  // 只读模式下的样式调整\r\n  &.readonly {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n      \r\n      .frequency-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 确保在深色主题下有良好的视觉效果\r\n.maintenance-theme .project-config,\r\n.inspection-theme .project-config {\r\n  .add-project-btn {\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  }\r\n  \r\n  .project-row {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner),\r\n      .frequency-input :deep(.el-input__inner) {\r\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}