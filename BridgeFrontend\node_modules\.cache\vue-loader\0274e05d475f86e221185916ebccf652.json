{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\DamageTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\DamageTypeChart.vue", "mtime": 1758804563528}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0RhbWFnZVR5cGVDaGFydCcsDQogIHByb3BzOiB7DQogICAgY2hhcnREYXRhOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBsb2FkaW5nOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGhlaWdodDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJzQwMHB4Jw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY2hhcnQ6IG51bGwNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0Q2hhcnQoKQ0KICB9LA0KICBiZWZvcmVVbm1vdW50KCkgew0KICAgIGlmICh0aGlzLmNoYXJ0KSB7DQogICAgICB0aGlzLmNoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBjaGFydERhdGE6IHsNCiAgICAgIGhhbmRsZXIoKSB7DQogICAgICAgIHRoaXMudXBkYXRlQ2hhcnQoKQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0Q2hhcnQoKSB7DQogICAgICB0aGlzLmNoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuY2hhcnRDb250YWluZXIpDQogICAgICB0aGlzLnVwZGF0ZUNoYXJ0KCkNCiAgICAgIA0KICAgICAgLy8g5ZON5bqU5byP5aSE55CGDQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgfSwNCiAgICANCiAgICBoYW5kbGVSZXNpemUoKSB7DQogICAgICBpZiAodGhpcy5jaGFydCkgew0KICAgICAgICB0aGlzLmNoYXJ0LnJlc2l6ZSgpDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICB1cGRhdGVDaGFydCgpIHsNCiAgICAgIGlmICghdGhpcy5jaGFydCkgcmV0dXJuDQoNCiAgICAgIGNvbnN0IGRhdGEgPSB0aGlzLmZvcm1hdENoYXJ0RGF0YSgpDQogICAgICBjb25zdCB0b3RhbENvdW50ID0gZGF0YS5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgaXRlbS52YWx1ZSwgMCkNCiAgICAgIA0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLA0KICAgICAgICAgIGZvcm1hdHRlcjogJ3thfSA8YnIvPntifToge2N9ICh7ZH0lKScNCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICBvcmllbnQ6ICd2ZXJ0aWNhbCcsDQogICAgICAgICAgcmlnaHQ6ICc1JScsDQogICAgICAgICAgdG9wOiAnbWlkZGxlJywNCiAgICAgICAgICBpdGVtV2lkdGg6IDE4LA0KICAgICAgICAgIGl0ZW1IZWlnaHQ6IDE0LA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgY29sb3I6ICcjZmZmZmZmJywNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0sDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihuYW1lKSB7DQogICAgICAgICAgICBjb25zdCBpdGVtID0gZGF0YS5maW5kKGQgPT4gZC5uYW1lID09PSBuYW1lKQ0KICAgICAgICAgICAgcmV0dXJuIG5hbWUgKyAnICAnICsgKGl0ZW0gPyBpdGVtLnZhbHVlIDogJycpDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBncmFwaGljOiB7DQogICAgICAgICAgdHlwZTogJ3RleHQnLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLA0KICAgICAgICAgIHRvcDogJ21pZGRsZScsDQogICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgIHRleHQ6IHRvdGFsQ291bnQudG9TdHJpbmcoKSwNCiAgICAgICAgICAgIGZvbnRTaXplOiAyOCwNCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywNCiAgICAgICAgICAgIGZpbGw6ICcjZmZmZmZmJw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+eXheWus+exu+WeiycsDQogICAgICAgICAgICB0eXBlOiAncGllJywNCiAgICAgICAgICAgIHJhZGl1czogWyc0NSUnLCAnNjUlJ10sDQogICAgICAgICAgICBjZW50ZXI6IFsnMzUlJywgJzUwJSddLA0KICAgICAgICAgICAgYXZvaWRMYWJlbE92ZXJsYXA6IGZhbHNlLA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsYWJlbExpbmU6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgZm9udFNpemU6IDE0LA0KICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJywNCiAgICAgICAgICAgICAgICBjb2xvcjogJyNmZmZmZmYnDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBkYXRhDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBjb2xvcjogWycjNDBFMEQwJywgJyNGRkQ3MDAnLCAnI0ZGOEMwMCcsICcjRkY2QjZCJywgJyM5MzcwREInXQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmNoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpDQogICAgfSwNCiAgICANCiAgICBmb3JtYXRDaGFydERhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuY2hhcnREYXRhIHx8IHRoaXMuY2hhcnREYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gWw0KICAgICAgICAgIHsgdmFsdWU6IDU4MSwgbmFtZTogJ+aKl+a1ricgfSwNCiAgICAgICAgICB7IHZhbHVlOiAxNDAsIG5hbWU6ICfkuIvmsoknIH0sDQogICAgICAgICAgeyB2YWx1ZTogOTUsIG5hbWU6ICdUT1AnIH0sDQogICAgICAgICAgeyB2YWx1ZTogODAsIG5hbWU6ICfmjpLljIUnIH0sDQogICAgICAgICAgeyB2YWx1ZTogNjUsIG5hbWU6ICdhNTgxJyB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgcmV0dXJuIHRoaXMuY2hhcnREYXRhLm1hcChpdGVtID0+ICh7DQogICAgICAgIHZhbHVlOiBpdGVtLmNvdW50IHx8IGl0ZW0udmFsdWUsDQogICAgICAgIG5hbWU6IGl0ZW0udHlwZSB8fCBpdGVtLm5hbWUNCiAgICAgIH0pKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["DamageTypeChart.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DamageTypeChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"damage-type-chart\">\r\n    <div \r\n      ref=\"chartContainer\" \r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'DamageType<PERSON><PERSON>',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n  },\r\n  beforeUnmount() {\r\n    if (this.chart) {\r\n      this.chart.dispose()\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chartContainer)\r\n      this.updateChart()\r\n      \r\n      // 响应式处理\r\n      window.addEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    handleResize() {\r\n      if (this.chart) {\r\n        this.chart.resize()\r\n      }\r\n    },\r\n    \r\n    updateChart() {\r\n      if (!this.chart) return\r\n\r\n      const data = this.formatChartData()\r\n      const totalCount = data.reduce((sum, item) => sum + item.value, 0)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          show: true,\r\n          orient: 'vertical',\r\n          right: '5%',\r\n          top: 'middle',\r\n          itemWidth: 18,\r\n          itemHeight: 14,\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          },\r\n          formatter: function(name) {\r\n            const item = data.find(d => d.name === name)\r\n            return name + '  ' + (item ? item.value : '')\r\n          }\r\n        },\r\n        graphic: {\r\n          type: 'text',\r\n          left: 'center',\r\n          top: 'middle',\r\n          style: {\r\n            text: totalCount.toString(),\r\n            fontSize: 28,\r\n            fontWeight: 'bold',\r\n            fill: '#ffffff'\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '病害类型',\r\n            type: 'pie',\r\n            radius: ['45%', '65%'],\r\n            center: ['35%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: 14,\r\n                fontWeight: 'bold',\r\n                color: '#ffffff'\r\n              }\r\n            },\r\n            data: data\r\n          }\r\n        ],\r\n        color: ['#40E0D0', '#FFD700', '#FF8C00', '#FF6B6B', '#9370DB']\r\n      }\r\n\r\n      this.chart.setOption(option, true)\r\n    },\r\n    \r\n    formatChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return [\r\n          { value: 581, name: '抗浮' },\r\n          { value: 140, name: '下沉' },\r\n          { value: 95, name: 'TOP' },\r\n          { value: 80, name: '排包' },\r\n          { value: 65, name: 'a581' }\r\n        ]\r\n      }\r\n      \r\n      return this.chartData.map(item => ({\r\n        value: item.count || item.value,\r\n        name: item.type || item.name\r\n      }))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.damage-type-chart {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与右侧容器设置保持一致\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n\r\n  // 图表内容容器样式\r\n  div[ref=\"chartContainer\"] {\r\n    position: relative;\r\n    z-index: 3; // 确保图表在伪元素之上\r\n    width: 100% !important;\r\n    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间\r\n  }\r\n}\r\n</style>\r\n"]}]}