{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue?vue&type=style&index=0&id=b3f555b6&lang=scss", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue", "mtime": 1758806998018}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovLyDlr7zlhaXlt6Hmo4DkuLvpopjmoLflvI/lkoxtYWludGVuYW5jZeS4u+mimOagt+W8jw0KQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsNCkBpbXBvcnQgJ0AvYXNzZXRzL3N0eWxlcy9tYWludGVuYW5jZS10aGVtZS5zY3NzJzsNCkBpbXBvcnQgJ0Avc3R5bGVzL2NvbXBvbmVudHMvZGlhbG9nLnNjc3MnOw0KDQovLyDpobnnm67lvLnmoYbkvb/nlKjlhazlhbHmoLflvI/vvIzml6DpnIDoh6rlrprkuYnmoLflvI8NCi8vIOmAmui/hyBjb21tb24tZGlhbG9n44CBaW5zcGVjdGlvbi10YWJz44CBaW5zcGVjdGlvbi10YWJsZSDnrYnnsbvnu6fmib/lhazlhbHmoLflvI8NCg0KLy8g6aG555uu5by55qGG5Zu65a6a5bC65a+45qC35byPIC0g5by65Yi26KaG55uW5omA5pyJ5Y+v6IO955qE5qC35byPDQoucHJvamVjdC1kaWFsb2ctZml4ZWQtc2l6ZSB7DQogIC8vIOacgOmrmOS8mOWFiOe6p+agt+W8j+iuvue9rg0KICA6ZGVlcCguZWwtZGlhbG9nKSB7DQogICAgd2lkdGg6IDcwJSAhaW1wb3J0YW50Ow0KICAgIG1pbi13aWR0aDogODAwcHggIWltcG9ydGFudDsNCiAgICBoZWlnaHQ6IDg1dmggIWltcG9ydGFudDsNCiAgICBtYXgtaGVpZ2h0OiA4NXZoICFpbXBvcnRhbnQ7DQogICAgbWFyZ2luLXRvcDogNy41dmggIWltcG9ydGFudDsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDsNCiAgICANCiAgICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICAgIGhlaWdodDogY2FsYyg4NXZoIC0gMTQwcHgpICFpbXBvcnRhbnQ7DQogICAgICBtYXgtaGVpZ2h0OiBjYWxjKDg1dmggLSAxNDBweCkgIWltcG9ydGFudDsNCiAgICAgIG92ZXJmbG93LXk6IGF1dG8gIWltcG9ydGFudDsNCiAgICAgIHBhZGRpbmc6IDAgIWltcG9ydGFudDsNCiAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3ggIWltcG9ydGFudDsNCiAgICB9DQogIH0NCiAgDQogIC8vIOWTjeW6lOW8j+iuvuiuoQ0KICBAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogICAgOmRlZXAoLmVsLWRpYWxvZykgew0KICAgICAgd2lkdGg6IDgwJSAhaW1wb3J0YW50Ow0KICAgICAgbWluLXdpZHRoOiA3MDBweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KICANCiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogICAgOmRlZXAoLmVsLWRpYWxvZykgew0KICAgICAgd2lkdGg6IDk1JSAhaW1wb3J0YW50Ow0KICAgICAgbWluLXdpZHRoOiBhdXRvICFpbXBvcnRhbnQ7DQogICAgICBoZWlnaHQ6IDkwdmggIWltcG9ydGFudDsNCiAgICAgIG1heC1oZWlnaHQ6IDkwdmggIWltcG9ydGFudDsNCiAgICAgIG1hcmdpbi10b3A6IDV2aCAhaW1wb3J0YW50Ow0KICAgICAgDQogICAgICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICAgICAgaGVpZ2h0OiBjYWxjKDkwdmggLSAxMjBweCkgIWltcG9ydGFudDsNCiAgICAgICAgbWF4LWhlaWdodDogY2FsYyg5MHZoIC0gMTIwcHgpICFpbXBvcnRhbnQ7DQogICAgICB9DQogICAgfQ0KICB9DQogIA0KICBAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsNCiAgICA6ZGVlcCguZWwtZGlhbG9nKSB7DQogICAgICB3aWR0aDogOTglICFpbXBvcnRhbnQ7DQogICAgICBoZWlnaHQ6IDk1dmggIWltcG9ydGFudDsNCiAgICAgIG1heC1oZWlnaHQ6IDk1dmggIWltcG9ydGFudDsNCiAgICAgIG1hcmdpbi10b3A6IDIuNXZoICFpbXBvcnRhbnQ7DQogICAgICANCiAgICAgIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgICAgICBoZWlnaHQ6IGNhbGMoOTV2aCAtIDEwMHB4KSAhaW1wb3J0YW50Ow0KICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDk1dmggLSAxMDBweCkgIWltcG9ydGFudDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLy8g6ZKI5a+554m55a6a5qC35byP57G755qE5by65Yi26KaG55uWIC0g56Gu5L+d5Y2z5L2/5pyJ5YWs5YWx5qC35byP5Lmf6IO955Sf5pWIDQoucHJvamVjdC1kaWFsb2ctZml4ZWQtc2l6ZS5jb21tb24tZGlhbG9nLXdpZGUgew0KICA6ZGVlcCguZWwtZGlhbG9nKSB7DQogICAgaGVpZ2h0OiA4NXZoICFpbXBvcnRhbnQ7DQogICAgbWF4LWhlaWdodDogODV2aCAhaW1wb3J0YW50Ow0KICAgIA0KICAgIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgICAgaGVpZ2h0OiBjYWxjKDg1dmggLSAxNDBweCkgIWltcG9ydGFudDsNCiAgICAgIG1heC1oZWlnaHQ6IGNhbGMoODV2aCAtIDE0MHB4KSAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KfQ0KDQovLyDlvLrliLblupTnlKjliLDmiYDmnInlj6/og73nmoRFbGVtZW50IFVJ5by55qGG57G75ZCNDQouZWwtZGlhbG9nLnByb2plY3QtZGlhbG9nLWZpeGVkLXNpemUsDQouZWwtZGlhbG9nX193cmFwcGVyIC5wcm9qZWN0LWRpYWxvZy1maXhlZC1zaXplLA0KLnByb2plY3QtZGlhbG9nLWZpeGVkLXNpemUgLmVsLWRpYWxvZyB7DQogIGhlaWdodDogODV2aCAhaW1wb3J0YW50Ow0KICBtYXgtaGVpZ2h0OiA4NXZoICFpbXBvcnRhbnQ7DQogIA0KICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICBoZWlnaHQ6IGNhbGMoODV2aCAtIDE0MHB4KSAhaW1wb3J0YW50Ow0KICAgIG1heC1oZWlnaHQ6IGNhbGMoODV2aCAtIDE0MHB4KSAhaW1wb3J0YW50Ow0KICAgIG92ZXJmbG93LXk6IGF1dG8gIWltcG9ydGFudDsNCiAgfQ0KfQ0KDQoucHJvamVjdC1kaWFsb2cgew0KICAuc3RlcC1jb250ZW50IHsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KICAgIG1pbi1oZWlnaHQ6IDQwMHB4Ow0KICAgIGJhY2tncm91bmQ6IHZhcigtLWluc3BlY3Rpb24tYmctcHJpbWFyeSwgIzA5MUE0Qik7DQogICAgY29sb3I6ICNmMWY1Zjk7DQogICAgLy8g5YaF5a655Yy65Z+f5Y+v5Lul5rua5Yqo77yM56e76ZmkbWluLWhlaWdodOmZkOWItg0KICAgIGhlaWdodDogYXV0bzsNCiAgICBvdmVyZmxvdy15OiBhdXRvOw0KICB9DQogIA0KICAvLyDnoa7kv53lhbPpl63mjInpkq7moLflvI/mraPnoa7lupTnlKgNCiAgLmN1c3RvbS1jbG9zZS1idG4gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZSAhaW1wb3J0YW50Ow0KICAgIHRvcDogMjBweCAhaW1wb3J0YW50Ow0KICAgIHJpZ2h0OiAyNHB4ICFpbXBvcnRhbnQ7DQogICAgd2lkdGg6IDM2cHggIWltcG9ydGFudDsNCiAgICBoZWlnaHQ6IDM2cHggIWltcG9ydGFudDsNCiAgICBjdXJzb3I6IHBvaW50ZXIgIWltcG9ydGFudDsNCiAgICB6LWluZGV4OiAxMDAgIWltcG9ydGFudDsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlICFpbXBvcnRhbnQ7DQoNCiAgICBzdmcgew0KICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50Ow0KICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50Ow0KICAgIH0NCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpICFpbXBvcnRhbnQ7DQogICAgICBvcGFjaXR5OiAwLjggIWltcG9ydGFudDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["ProjectDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy0BA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectDialog.vue", "sourceRoot": "src/components/Maintenance", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    :title=\"modalTitle\"\r\n    :visible.sync=\"dialogVisible\"\r\n    :before-close=\"handleClose\"\r\n    :append-to-body=\"true\"\r\n    :modal-append-to-body=\"true\"\r\n    :close-on-click-modal=\"false\"\r\n    :show-close=\"false\"\r\n    custom-class=\"project-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\r\n    top=\"5vh\"\r\n  >\r\n    <!-- 自定义关闭按钮 -->\r\n    <div class=\"custom-close-btn\" @click=\"handleClose\">\r\n      <svg width=\"36\" height=\"36\" viewBox=\"0 0 36 36\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z\" fill=\"white\"/>\r\n      </svg>\r\n    </div>\r\n\r\n    <!-- 步骤导航 -->\r\n    <step-navigation\r\n      :steps=\"steps\"\r\n      :current-step=\"currentStep\"\r\n      :allow-click=\"allowStepClick\"\r\n      :clickable-steps=\"clickableSteps\"\r\n      @step-click=\"handleStepClick\"\r\n    />\r\n    \r\n    <!-- 步骤内容 -->\r\n    <div class=\"step-content\">\r\n      <!-- 基本信息 -->\r\n      <basic-info\r\n        v-if=\"currentStep === 0\"\r\n        ref=\"basicInfo\"\r\n        v-model=\"formData.basicInfo\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n        @project-type-change=\"handleProjectTypeChange\"\r\n      />\r\n      \r\n      <!-- 养护项目配置 -->\r\n      <project-config\r\n        v-if=\"currentStep === 1 && showProjectConfig\"\r\n        ref=\"projectConfig\"\r\n        v-model=\"formData.projectConfig\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 病害养护配置 -->\r\n      <disease-config\r\n        v-if=\"(currentStep === 2 && projectType === 'monthly') || (currentStep === 1 && projectType === 'preventive')\"\r\n        ref=\"diseaseConfig\"\r\n        v-model=\"formData.diseaseConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 实施信息配置 - 预防养护第三步 -->\r\n      <implementation-config\r\n        v-if=\"currentStep === 2 && projectType === 'preventive'\"\r\n        ref=\"implementationConfig\"\r\n        v-model=\"formData.implementationConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 竣工信息配置 - 预防养护第四步 -->\r\n      <completion-info\r\n        v-if=\"currentStep === 3 && projectType === 'preventive'\"\r\n        ref=\"completionInfo\"\r\n        v-model=\"formData.completionInfo\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 养护桥梁配置 - 只在非审批/查看/修改模式下显示 -->\r\n      <bridge-config\r\n        v-if=\"currentStep === getLastStepIndex() && projectType !== 'preventive' && !isApprovalMode && !isViewMode && !isEditMode\"\r\n        ref=\"bridgeConfig\"\r\n        v-model=\"formData.bridgeConfig\"\r\n        :infrastructure-type=\"infrastructureType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 审批信息 - 审批模式、查看模式、修改模式的最后一步显示 -->\r\n      <approval-info\r\n        v-if=\"currentStep === getLastStepIndex() && (isApprovalMode || isViewMode || isEditMode)\"\r\n        ref=\"approvalInfo\"\r\n        v-model=\"formData.approvalInfo\"\r\n        :project-id=\"currentProjectId\"\r\n        :show-approval-form=\"canApprove\"\r\n        @approval-submitted=\"handleApprovalSubmitted\"\r\n        @cancel=\"handleApprovalCancel\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 弹框底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <template v-if=\"isReadonlyMode && !isApprovalMode\">\r\n        <!-- 只读模式只显示关闭按钮 -->\r\n        <el-button @click=\"handleClose\">关闭</el-button>\r\n        <el-button v-if=\"isViewMode\" type=\"primary\" @click=\"handleEdit\">编辑</el-button>\r\n      </template>\r\n      <template v-else-if=\"isApprovalMode\">\r\n        <!-- 审批模式显示审批按钮 -->\r\n        <el-button @click=\"handleReject\" class=\"reject-btn\">退回</el-button>\r\n        <el-button type=\"primary\" @click=\"handleApprove\" class=\"approve-btn\">通过</el-button>\r\n      </template>\r\n      <template v-else-if=\"isEditMode && currentStep === getLastStepIndex()\">\r\n        <!-- 修改模式：在审批信息页只显示保存/提交，不显示上一步和更新 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\">提交</el-button>\r\n      </template>\r\n      <template v-else>\r\n        <!-- 非修改模式或非最后一步：显示正常的步骤按钮 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep > 0\"\r\n          @click=\"handlePrevStep\"\r\n        >\r\n          上一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep < getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleNextStep\"\r\n        >\r\n          下一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep === getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleSubmit\"\r\n        >\r\n          {{ isEditMode ? '更新' : '提交' }}\r\n        </el-button>\r\n      </template>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport StepNavigation from '@/components/Maintenance/StepNavigation'\r\nimport BasicInfo from '@/views/maintenance/projects/create/components/BasicInfo'\r\nimport ProjectConfig from '@/views/maintenance/projects/create/components/ProjectConfig'\r\nimport DiseaseConfig from '@/views/maintenance/projects/create/components/DiseaseConfig'\r\nimport BridgeConfig from '@/views/maintenance/projects/create/components/BridgeConfig'\r\nimport ImplementationConfig from '@/views/maintenance/projects/create/components/ImplementationConfig'\r\nimport CompletionInfo from '@/views/maintenance/projects/create/components/CompletionInfo'\r\nimport ApprovalInfo from '@/views/maintenance/projects/create/components/ApprovalInfo'\r\nimport { getProjectDetail, createProject, updateProject } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ProjectDialog',\r\n  components: {\r\n    StepNavigation,\r\n    BasicInfo,\r\n    ProjectConfig,\r\n    DiseaseConfig,\r\n    BridgeConfig,\r\n    ImplementationConfig,\r\n    CompletionInfo,\r\n    ApprovalInfo\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    mode: {\r\n      type: String,\r\n      required: true,\r\n      validator: value => ['create', 'edit', 'view', 'approve'].includes(value)\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    infrastructureType: {\r\n      type: String,\r\n      default: 'bridge'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      currentStep: 0,\r\n      projectType: 'monthly', // 默认为月度养护\r\n      \r\n      // 表单数据\r\n      formData: {\r\n        basicInfo: {},\r\n        projectConfig: {},\r\n        diseaseConfig: {},\r\n        implementationConfig: {},\r\n        bridgeConfig: {},\r\n        completionInfo: {},\r\n        approvalInfo: {}\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    dialogVisible: {\r\n      get() {\r\n        return this.visible\r\n      },\r\n      set(value) {\r\n        this.$emit('update:visible', value)\r\n      }\r\n    },\r\n\r\n    modalTitle() {\r\n      const titles = {\r\n        create: '新增养护项目',\r\n        edit: '编辑养护项目',\r\n        view: '查看养护项目',\r\n        approve: '审批养护项目'\r\n      }\r\n      return titles[this.mode] || '养护项目'\r\n    },\r\n\r\n    currentProjectId() {\r\n      return this.projectId\r\n    },\r\n    \r\n    isEditMode() {\r\n      return this.mode === 'edit'\r\n    },\r\n    \r\n    isViewMode() {\r\n      return this.mode === 'view'\r\n    },\r\n    \r\n    isApprovalMode() {\r\n      return this.mode === 'approve'\r\n    },\r\n    \r\n    // 是否为只读模式（查看或审批）\r\n    isReadonlyMode() {\r\n      return this.isViewMode || this.isApprovalMode\r\n    },\r\n    \r\n    // 是否允许步骤点击\r\n    allowStepClick() {\r\n      // 查看和审批模式始终允许点击\r\n      if (this.isViewMode || this.isApprovalMode) {\r\n        return true\r\n      }\r\n      // 新增和修改模式允许有条件的点击（由clickableSteps控制具体哪些步骤可点击）\r\n      return true\r\n    },\r\n    \r\n    // 可点击的步骤列表\r\n    clickableSteps() {\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        // 查看、审批和修改模式所有步骤都可点击\r\n        return Array.from({ length: this.steps.length }, (_, i) => i)\r\n      }\r\n      \r\n      // 新增模式：当前步骤及之前的步骤可点击\r\n      return Array.from({ length: this.currentStep + 1 }, (_, i) => i)\r\n    },\r\n    \r\n    // 根据项目类型确定步骤\r\n    steps() {\r\n      const baseSteps = [\r\n        { title: '基本信息' }\r\n      ]\r\n      \r\n      let projectSteps = []\r\n      \r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息 → 养护项目 → 病害养护 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '养护项目' },\r\n          { title: '病害养护' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'cleaning') {\r\n        // 保洁项目：基本信息 → 保洁项目 → 保洁桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '保洁项目' },\r\n          { title: '保洁桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'emergency') {\r\n        // 应急养护：基本信息 → 应急项目 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '应急项目' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息 → 关联病害 → 实施信息 → 竣工信息\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '关联病害' },\r\n          { title: '实施信息' },\r\n          { title: '竣工信息' }\r\n        ]\r\n      } else {\r\n        // 默认步骤\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '项目配置' },\r\n          { title: '关联对象' }\r\n        ]\r\n      }\r\n      \r\n      // 审批模式、查看模式、修改模式下添加审批信息步骤\r\n      if (this.isApprovalMode || this.isViewMode || this.isEditMode) {\r\n        projectSteps.push({ title: '审批信息' })\r\n      }\r\n      \r\n      return projectSteps\r\n    },\r\n    \r\n    // 是否显示项目配置步骤\r\n    showProjectConfig() {\r\n      return ['monthly', 'cleaning', 'emergency'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否显示病害配置步骤\r\n    showDiseaseConfig() {\r\n      return ['monthly', 'preventive'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否可以进行审批操作\r\n    canApprove() {\r\n      // 在实际项目中，这里应该根据当前用户权限和项目状态来判断\r\n      // 目前简化为审批模式下就可以审批\r\n      return this.isApprovalMode\r\n    }\r\n  },\r\n  watch: {\r\n    visible(newVal) {\r\n      if (newVal) {\r\n        // 立即应用样式，避免白色闪烁\r\n        this.$nextTick(() => {\r\n          this.forceApplyDialogStyles()\r\n        })\r\n        \r\n        // 延迟再次应用，确保Element UI后续的样式修改被覆盖\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 200)\r\n        \r\n        // 额外延迟应用表格样式，确保表格渲染完成后样式正确\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 500)\r\n        \r\n        // 最终确保样式正确应用\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 1000)\r\n      }\r\n    },\r\n    \r\n    // 监听Tab切换，确保每个Tab的样式都正确\r\n    currentStep(newVal) {\r\n      this.$nextTick(() => {\r\n        this.forceApplyDialogStyles()\r\n      })\r\n      \r\n      // 延迟应用，确保Tab内容渲染完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 100)\r\n      \r\n      // 额外延迟，确保表格数据加载完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 300)\r\n    }\r\n  },\r\n  async created() {\r\n    if (this.isEditMode || this.isViewMode || this.isApprovalMode) {\r\n      await this.loadProjectData()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载项目数据（编辑模式和查看模式）\r\n    async loadProjectData() {\r\n      try {\r\n        this.loading = true\r\n        \r\n        // 临时使用静态数据，因为后端接口未公布\r\n        // 实际项目中应该使用：const response = await getProjectDetail(this.currentProjectId)\r\n        const mockProject = this.getMockProjectData()\r\n        \r\n        this.projectType = mockProject.projectType\r\n        this.formData = {\r\n          basicInfo: mockProject.basicInfo || {},\r\n          projectConfig: mockProject.projectConfig || {},\r\n          diseaseConfig: mockProject.diseaseConfig || {},\r\n          implementationConfig: mockProject.implementationConfig || {},\r\n          completionInfo: mockProject.completionInfo || {},\r\n          bridgeConfig: mockProject.bridgeConfig || {},\r\n          approvalInfo: mockProject.approvalInfo || {}\r\n        }\r\n        \r\n        // 审批模式下默认跳转到审批信息页面（最后一步）\r\n        if (this.isApprovalMode) {\r\n          this.$nextTick(() => {\r\n            this.currentStep = this.getLastStepIndex()\r\n          })\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载项目数据失败')\r\n        this.handleClose()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 获取模拟项目数据\r\n    getMockProjectData() {\r\n      return {\r\n        projectType: 'monthly',\r\n        basicInfo: {\r\n          projectName: '2024年度桥梁月度养护项目',\r\n          projectType: 'monthly',\r\n          startDate: '2024-01-01',\r\n          endDate: '2024-12-31',\r\n          managementUnit: '市桥梁管理处',\r\n          supervisionUnit: '市交通局',\r\n          maintenanceUnit: '桥梁养护有限公司',\r\n          manager: '张三',\r\n          contactPhone: '13800138000',\r\n          workload: '100万元',\r\n          projectContent: '对辖区内桥梁进行月度常规养护，包括清洁、检查、小修等工作',\r\n          attachments: []\r\n        },\r\n        projectConfig: {\r\n          projects: [\r\n            { name: '桥面清洁', frequency: 30 },\r\n            { name: '栏杆维护', frequency: 60 },\r\n            { name: '伸缩缝检查', frequency: 90 }\r\n          ]\r\n        },\r\n        diseaseConfig: {\r\n          diseases: [\r\n            { id: 1, name: '桥面破损', level: '轻微' },\r\n            { id: 2, name: '栏杆松动', level: '一般' }\r\n          ]\r\n        },\r\n        implementationConfig: {\r\n          works: [\r\n            { name: '施工准备' },\r\n            { name: '现场作业' },\r\n            { name: '质量检查' }\r\n          ]\r\n        },\r\n        completionInfo: {\r\n          completionDate: '2024-12-31',\r\n          acceptanceDate: '2025-01-15',\r\n          qualityGrade: '合格',\r\n          remark: '项目按期完成，质量达标'\r\n        },\r\n        bridgeConfig: {\r\n          bridges: [\r\n            { id: 1, name: '人民桥', location: '人民路', span: '50m' },\r\n            { id: 2, name: '胜利桥', location: '胜利路', span: '30m' }\r\n          ]\r\n        },\r\n        approvalInfo: {\r\n          approvalHistory: [\r\n            {\r\n              id: 1,\r\n              stepName: '开始申请',\r\n              approver: '黄昭言',\r\n              status: '通过',\r\n              comment: '无异议',\r\n              department: '养护公司',\r\n              receiveTime: '2025-09-18\\n10:43',\r\n              processTime: '2025-09-18\\n10:43'\r\n            },\r\n            {\r\n              id: 2,\r\n              stepName: '养护项目审批(一级)',\r\n              approver: '刘雨桐',\r\n              status: '通过',\r\n              comment: '项目方案合理，同意开展',\r\n              department: '技术部门',\r\n              receiveTime: '2025-09-18\\n11:00',\r\n              processTime: '2025-09-18\\n11:30'\r\n            },\r\n            {\r\n              id: 3,\r\n              stepName: '养护项目审批(二级)',\r\n              approver: '罗砚秋',\r\n              status: '通过',\r\n              comment: '预算合理，批准执行',\r\n              department: '财务部门',\r\n              receiveTime: '2025-09-18\\n14:00',\r\n              processTime: '2025-09-18\\n15:20'\r\n            }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取最后一步的索引\r\n    getLastStepIndex() {\r\n      return this.steps.length - 1\r\n    },\r\n    \r\n    // 项目类型变化\r\n    handleProjectTypeChange(type) {\r\n      if (this.projectType === type) {\r\n        return // 防止重复触发\r\n      }\r\n      \r\n      this.projectType = type\r\n      \r\n      // 使用nextTick确保步骤计算完成后再检查当前步骤\r\n      this.$nextTick(() => {\r\n        // 重置后续步骤的数据\r\n        this.formData.projectConfig = {}\r\n        this.formData.diseaseConfig = {}\r\n        this.formData.bridgeConfig = {}\r\n        \r\n        // 如果当前步骤超出新的步骤范围，回到第一步\r\n        if (this.currentStep >= this.steps.length) {\r\n          this.currentStep = 0\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 步骤点击\r\n    async handleStepClick(stepIndex) {\r\n      // 查看、审批和修改模式可以自由切换\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 新增模式的逻辑\r\n      // 如果点击的是当前步骤或之前的步骤，允许直接切换\r\n      if (stepIndex <= this.currentStep) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 如果跳转到后面的步骤，需要验证从当前步骤到目标步骤的所有步骤\r\n      for (let i = this.currentStep; i < stepIndex; i++) {\r\n        const isValid = await this.validateStepByIndex(i)\r\n        if (!isValid) {\r\n          this.$message.warning(`请先完成第${i + 1}步的必填信息`)\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 所有验证通过，跳转到目标步骤\r\n      this.currentStep = stepIndex\r\n    },\r\n    \r\n    // 上一步\r\n    handlePrevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--\r\n      }\r\n    },\r\n    \r\n    // 下一步\r\n    async handleNextStep() {\r\n      const isValid = await this.validateCurrentStep()\r\n      if (!isValid) {\r\n        return\r\n      }\r\n      \r\n      if (this.currentStep < this.getLastStepIndex()) {\r\n        this.currentStep++\r\n      }\r\n    },\r\n    \r\n    // 验证当前步骤\r\n    async validateCurrentStep() {\r\n      return await this.validateStepByIndex(this.currentStep)\r\n    },\r\n    \r\n    // 验证指定步骤\r\n    async validateStepByIndex(stepIndex) {\r\n      const componentName = this.getStepComponentByIndex(stepIndex)\r\n      \r\n      if (componentName && this.$refs[componentName]) {\r\n        try {\r\n          const result = this.$refs[componentName].validate()\r\n          // 如果返回Promise，等待结果\r\n          if (result && typeof result.then === 'function') {\r\n            return await result\r\n          }\r\n          // 如果返回boolean，直接返回\r\n          return result\r\n        } catch (error) {\r\n          console.error('验证失败:', error)\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    // 根据步骤索引获取组件名\r\n    getStepComponentByIndex(stepIndex) {\r\n      if (stepIndex === 0) {\r\n        return 'basicInfo'\r\n      }\r\n      \r\n      // 根据项目类型和步骤索引确定组件\r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息(0) → 养护项目(1) → 病害养护(2) → 养护桥梁(3)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'diseaseConfig'\r\n        if (stepIndex === 3) return 'bridgeConfig'\r\n      } else if (this.projectType === 'cleaning' || this.projectType === 'emergency') {\r\n        // 保洁/应急：基本信息(0) → 项目配置(1) → 桥梁配置(2)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'bridgeConfig'\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息(0) → 关联病害(1) → 实施信息(2) → 竣工信息(3)\r\n        if (stepIndex === 1) return 'diseaseConfig'\r\n        if (stepIndex === 2) return 'implementationConfig'\r\n        if (stepIndex === 3) return 'completionInfo' // 竣工信息\r\n        return null // 预防养护项目没有第4步\r\n      }\r\n      \r\n      return null\r\n    },\r\n    \r\n    // 切换到编辑模式\r\n    handleEdit() {\r\n      this.$emit('edit', this.currentProjectId)\r\n    },\r\n    \r\n    // 保存草稿\r\n    async handleSave() {\r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'draft',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('保存成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('保存成功')\r\n        }\r\n        \r\n        this.$emit('save', data)\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 提交项目\r\n    async handleSubmit() {\r\n      // 验证所有步骤\r\n      if (!this.validateAllSteps()) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'pending',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('更新成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('提交成功')\r\n        }\r\n        \r\n        this.$emit('submit', data)\r\n        this.handleClose()\r\n      } catch (error) {\r\n        this.$message.error(this.isEditMode ? '更新失败' : '提交失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 验证所有步骤\r\n    validateAllSteps() {\r\n      // 这里应该验证所有步骤的数据\r\n      return true\r\n    },\r\n    \r\n    // 处理审批提交结果\r\n    handleApprovalSubmitted(data) {\r\n      const { result } = data\r\n      \r\n      if (result === 'approved') {\r\n        this.$message.success('审批通过成功')\r\n      } else if (result === 'rejected') {\r\n        this.$message.success('已驳回申请')\r\n      } else if (result === 'returned') {\r\n        this.$message.success('已退回修改')\r\n      }\r\n      \r\n      this.$emit('approval-completed', data)\r\n      \r\n      // 审批完成后关闭页面\r\n      setTimeout(() => {\r\n        this.handleClose()\r\n      }, 1500)\r\n    },\r\n    \r\n    // 处理审批取消\r\n    handleApprovalCancel() {\r\n      // 可以选择关闭页面或回到上一步\r\n      this.handleClose()\r\n    },\r\n    \r\n    // 处理审批通过\r\n    async handleApprove() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.approveProject()\r\n        if (result) {\r\n          // 审批成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理审批退回\r\n    async handleReject() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.rejectProject()\r\n        if (result) {\r\n          // 退回成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 应用深色主题样式\r\n    forceApplyDialogStyles() {\r\n      // 参考巡检弹框的实现，强制应用深色主题样式\r\n      setTimeout(() => {\r\n        const dialog = document.querySelector('.el-dialog.project-dialog') ||\r\n                       document.querySelector('.el-dialog.inspection-dialog-base') ||\r\n                       document.querySelector('.el-dialog')\r\n        \r\n        if (dialog) {\r\n          // 应用深色主题到各个区域\r\n          this.applyDarkThemeToAllAreas(dialog)\r\n        } else {\r\n          // 如果还没找到，再次尝试\r\n          setTimeout(() => this.forceApplyDialogStyles(), 200)\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 应用深色主题到弹框的所有区域 - 参考巡检弹框实现\r\n    applyDarkThemeToAllAreas(dialogElement = null) {\r\n      // 如果没有传入dialog元素，尝试查找\r\n      const dialog = dialogElement || document.querySelector('.el-dialog')\r\n      \r\n      if (!dialog) {\r\n        return\r\n      }\r\n      \r\n      // 首先强制设置弹框本身的尺寸\r\n      const dialogStyles = {\r\n        'width': '70%',\r\n        'min-width': '800px',\r\n        'height': '85vh',\r\n        'max-height': '85vh',\r\n        'margin-top': '7.5vh',\r\n        'position': 'relative'\r\n      }\r\n      Object.keys(dialogStyles).forEach(property => {\r\n        dialog.style.setProperty(property, dialogStyles[property], 'important')\r\n      })\r\n      \r\n      // Header区域 - 使用dialog元素作为基础查找\r\n      const header = dialog.querySelector('.el-dialog__header')\r\n      \r\n      if (header) {\r\n        const headerStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'border-bottom': '1px solid #ffffff', // 白色分割线，与巡检日志一致\r\n          'padding': '20px 24px'\r\n        }\r\n        Object.keys(headerStyles).forEach(property => {\r\n          header.style.setProperty(property, headerStyles[property], 'important')\r\n        })\r\n        \r\n        const title = header.querySelector('.el-dialog__title')\r\n        if (title) {\r\n          title.style.setProperty('color', '#ffffff', 'important')\r\n          title.style.setProperty('font-weight', '600', 'important')\r\n          title.style.setProperty('font-size', '18px', 'important')\r\n        }\r\n      }\r\n      \r\n      // Body区域 - 强制设置固定高度和滚动\r\n      const body = dialog.querySelector('.el-dialog__body')\r\n      if (body) {\r\n        const bodyStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'height': 'calc(85vh - 140px)',\r\n          'max-height': 'calc(85vh - 140px)',\r\n          'overflow-y': 'auto',\r\n          'padding': '0',\r\n          'box-sizing': 'border-box'\r\n        }\r\n        Object.keys(bodyStyles).forEach(property => {\r\n          body.style.setProperty(property, bodyStyles[property], 'important')\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 关闭弹框\r\n    handleClose() {\r\n      this.currentStep = 0\r\n      this.$emit('update:visible', false)\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 导入巡检主题样式和maintenance主题样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n@import '@/styles/components/dialog.scss';\r\n\r\n// 项目弹框使用公共样式，无需自定义样式\r\n// 通过 common-dialog、inspection-tabs、inspection-table 等类继承公共样式\r\n\r\n// 项目弹框固定尺寸样式 - 强制覆盖所有可能的样式\r\n.project-dialog-fixed-size {\r\n  // 最高优先级样式设置\r\n  :deep(.el-dialog) {\r\n    width: 70% !important;\r\n    min-width: 800px !important;\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    margin-top: 7.5vh !important;\r\n    position: relative !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n      overflow-y: auto !important;\r\n      padding: 0 !important;\r\n      box-sizing: border-box !important;\r\n    }\r\n  }\r\n  \r\n  // 响应式设计\r\n  @media (max-width: 1200px) {\r\n    :deep(.el-dialog) {\r\n      width: 80% !important;\r\n      min-width: 700px !important;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    :deep(.el-dialog) {\r\n      width: 95% !important;\r\n      min-width: auto !important;\r\n      height: 90vh !important;\r\n      max-height: 90vh !important;\r\n      margin-top: 5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(90vh - 120px) !important;\r\n        max-height: calc(90vh - 120px) !important;\r\n      }\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 480px) {\r\n    :deep(.el-dialog) {\r\n      width: 98% !important;\r\n      height: 95vh !important;\r\n      max-height: 95vh !important;\r\n      margin-top: 2.5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(95vh - 100px) !important;\r\n        max-height: calc(95vh - 100px) !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 针对特定样式类的强制覆盖 - 确保即使有公共样式也能生效\r\n.project-dialog-fixed-size.common-dialog-wide {\r\n  :deep(.el-dialog) {\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 强制应用到所有可能的Element UI弹框类名\r\n.el-dialog.project-dialog-fixed-size,\r\n.el-dialog__wrapper .project-dialog-fixed-size,\r\n.project-dialog-fixed-size .el-dialog {\r\n  height: 85vh !important;\r\n  max-height: 85vh !important;\r\n  \r\n  .el-dialog__body {\r\n    height: calc(85vh - 140px) !important;\r\n    max-height: calc(85vh - 140px) !important;\r\n    overflow-y: auto !important;\r\n  }\r\n}\r\n\r\n.project-dialog {\r\n  .step-content {\r\n    padding: 24px;\r\n    min-height: 400px;\r\n    background: var(--inspection-bg-primary, #091A4B);\r\n    color: #f1f5f9;\r\n    // 内容区域可以滚动，移除min-height限制\r\n    height: auto;\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  // 确保关闭按钮样式正确应用\r\n  .custom-close-btn {\r\n    position: absolute !important;\r\n    top: 20px !important;\r\n    right: 24px !important;\r\n    width: 36px !important;\r\n    height: 36px !important;\r\n    cursor: pointer !important;\r\n    z-index: 100 !important;\r\n    transition: all 0.3s ease !important;\r\n\r\n    svg {\r\n      width: 100% !important;\r\n      height: 100% !important;\r\n      transition: all 0.3s ease !important;\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1) !important;\r\n      opacity: 0.8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}