{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue?vue&type=style&index=0&id=0f55505c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue", "mtime": 1758804563531}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RegionChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RegionChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"regionChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'RegionChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      const element = this.$refs.regionChart\r\n      if (element) {\r\n        this.chartInstance = echarts.init(element)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let result = `${params[0].name}<br/>`\r\n            params.forEach(param => {\r\n              result += `${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['计划巡检', '实际巡检'],\r\n          right: '10%',\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '4%',\r\n          bottom: '15%', // 🔧 减少底部边距，给图表更多空间\r\n          top: '15%', // 🔧 适度增加顶部边距，为图例留出空间\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: data.regions,\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          },\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 🔧 不旋转标签，保持水平显示\r\n            color: '#ffffff',\r\n            fontSize: 11,\r\n            margin: 8, // 🔧 减少标签与轴的距离\r\n            textStyle: {\r\n              baseline: 'top'\r\n            }\r\n          },\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数量',\r\n          min: 0,\r\n          max: 200,\r\n          interval: 20,\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          },\r\n          axisLabel: {\r\n            formatter: '{value}',\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '计划巡检',\r\n            type: 'bar',\r\n            data: data.plannedData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#87CEEB'\r\n                }, {\r\n                  offset: 1, color: '#B0E0E6'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          },\r\n          {\r\n            name: '实际巡检',\r\n            type: 'bar',\r\n            data: data.actualData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#4682B4'\r\n                }, {\r\n                  offset: 1, color: '#5F9EA0'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n      \r\n      // 强制重新渲染图表\r\n      setTimeout(() => {\r\n        if (this.chartInstance) {\r\n          this.chartInstance.resize()\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      const regions = this.chartData.map(item => item.region)\r\n      const plannedData = this.chartData.map(item => item.planned || 0)\r\n      const actualData = this.chartData.map(item => item.actual || 0)\r\n      \r\n      return { regions, plannedData, actualData }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      return {\r\n        regions: ['岳麓区', '开福区', '天心区', '芙蓉区', '雨花区'],\r\n        plannedData: [140, 180, 120, 120, 180],\r\n        actualData: [37, 128, 94, 60, 18]\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 200px !important; // 🔧 减少最小高度，适应flex布局\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"regionChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 200px; // 🔧 减少最小高度，适应更紧凑的布局\r\n}\r\n</style>\r\n"]}]}