{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue", "mtime": 1758804563534}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TrendChart.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TrendChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"trendChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'Trend<PERSON><PERSON>',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    chartType: {\r\n      type: String,\r\n      default: 'line' // line | bar\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    },\r\n    chartType() {\r\n      this.updateChart()\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      if (this.$refs.trendChart) {\r\n        this.chartInstance = echarts.init(this.$refs.trendChart)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['市府区', '美湖区', '龙泉区', '开福区', '芙蓉区'],\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '12%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            boundaryGap: this.chartType === 'bar',\r\n            data: data.categories,\r\n            axisLabel: {\r\n              color: '#ffffff'\r\n            },\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '次数',\r\n            max: 900,\r\n            interval: 100,\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            },\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '市府区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.shifuqu,\r\n            itemStyle: {\r\n              color: '#40E0D0'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '美湖区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.meihuqu,\r\n            itemStyle: {\r\n              color: '#FFD700'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '龙泉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.longquanqu,\r\n            itemStyle: {\r\n              color: '#FFFFFF'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '开福区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.kaifuqu,\r\n            itemStyle: {\r\n              color: '#FF6B6B'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '芙蓉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.furongqu,\r\n            itemStyle: {\r\n              color: '#FF8C00'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      // 如果有传入数据，处理为区域数据格式\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      // 如果chartData有区域数据，使用传入的数据\r\n      const districtData = {\r\n        shifuqu: this.chartData.shifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: this.chartData.meihuqu || [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: this.chartData.longquanqu || [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: this.chartData.kaifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: this.chartData.furongqu || [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      const districtData = {\r\n        shifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与DamageTypeChart保持一致的最小高度\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"trendChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 280px; // 🔧 与DamageTypeChart内部图表保持一致的最小高度\r\n}\r\n</style>\r\n"]}]}