{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue?vue&type=style&index=0&id=fd5921f4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue", "mtime": 1758809365043}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ApprovalInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8RA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "ApprovalInfo.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\r\n  <div class=\"approval-info\">\r\n    <!-- 审批记录 -->\r\n    <div class=\"approval-records\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        审批记录\r\n      </div>\r\n      \r\n      <el-table\r\n        :data=\"approvalHistory\"\r\n        class=\"approval-table\"\r\n        border\r\n      >\r\n        <el-table-column\r\n          prop=\"id\"\r\n          label=\"序号\"\r\n          min-width=\"60\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"stepName\"\r\n          label=\"审批环节\"\r\n          min-width=\"140\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"approver\"\r\n          label=\"处理人\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"审批状态\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"comment\"\r\n          label=\"审批意见\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"department\"\r\n          label=\"处理人部门\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"receiveTime\"\r\n          label=\"接收时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"processTime\"\r\n          label=\"办理时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n      </el-table>\r\n    </div>\r\n    \r\n    <!-- 无审批记录时显示提示 -->\r\n    <div v-if=\"!hasApprovalHistory\" class=\"no-data\">\r\n      <p>暂无审批记录</p>\r\n    </div>\r\n    \r\n    <!-- 审批处理（仅在审批模式下显示） -->\r\n    <div v-if=\"showApprovalForm\" class=\"approval-process\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-edit\"></i>\r\n        审批处理\r\n      </div>\r\n      \r\n      <div class=\"process-form\">\r\n        <div class=\"comment-label\">*处理意见:</div>\r\n        <el-input\r\n          v-model=\"approvalFormData.comment\"\r\n          type=\"textarea\"\r\n          :rows=\"6\"\r\n          placeholder=\"请输入\"\r\n          class=\"comment-input\"\r\n        />\r\n      </div>\r\n      \r\n    </div>\r\n    \r\n    <!-- 注意：审批信息组件内不显示任何操作按钮，所有按钮都统一在父组件中控制 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { approveProject, getProjectApprovalHistory } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ApprovalInfo',\r\n  components: {\r\n  },\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    // 是否显示审批表单（审批模式 vs 查看模式）\r\n    showApprovalForm: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      \r\n      // 审批历史记录\r\n      approvalHistory: [],\r\n      \r\n      // 审批表单数据\r\n      approvalFormData: {\r\n        comment: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否有审批历史记录\r\n    hasApprovalHistory() {\r\n      return this.approvalHistory && this.approvalHistory.length > 0\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          this.initializeData(newVal)\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    projectId: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.loadApprovalHistory()\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    initializeData(data) {\r\n      if (data && data.approvalHistory) {\r\n        this.approvalHistory = data.approvalHistory\r\n      }\r\n    },\r\n    \r\n    // 加载审批历史\r\n    async loadApprovalHistory() {\r\n      if (!this.projectId) return\r\n      \r\n      try {\r\n        this.loading = true\r\n        const response = await getProjectApprovalHistory(this.projectId)\r\n        if (response.code === 200) {\r\n          this.approvalHistory = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载审批历史失败:', error)\r\n        this.$message.error('加载审批历史失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批通过（供父组件调用）\r\n    async approveProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认通过该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'approved',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('审批通过成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'approved',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('审批失败:', error)\r\n          this.$message.error(error.message || '审批失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批退回（供父组件调用）\r\n    async rejectProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认退回该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'returned',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('退回成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'returned',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('退回失败:', error)\r\n          this.$message.error(error.message || '退回失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证方法（为了保持接口一致性）\r\n    validate() {\r\n      return Promise.resolve(true)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.approval-info {\r\n  color: #ffffff;\r\n  \r\n  \r\n  // 无数据提示\r\n  .no-data {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n    color: #9ca3af;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .section-title {\r\n    color: #ffffff;\r\n    font-size: 16px;\r\n    font-weight: normal;\r\n    margin-bottom: 20px;\r\n    text-align: left;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      color: #409eff;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  \r\n  // 审批记录表格\r\n  .approval-records {\r\n    margin-bottom: 32px;\r\n    \r\n    .approval-table {\r\n      width: 100%;\r\n      \r\n      :deep(.el-table) {\r\n        background: transparent;\r\n        color: #ffffff;\r\n        width: 100%;\r\n        \r\n        th {\r\n          background: rgba(255, 255, 255, 0.05);\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          font-weight: normal;\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        td {\r\n          background: transparent;\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          white-space: pre-line; // 支持换行显示\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        &::before {\r\n          background: rgba(255, 255, 255, 0.1);\r\n        }\r\n        \r\n        .el-table__empty-block {\r\n          background: transparent;\r\n          color: #9ca3af;\r\n        }\r\n        \r\n        // 确保表格占满容器宽度\r\n        .el-table__header-wrapper,\r\n        .el-table__body-wrapper {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 审批处理区域\r\n  .approval-process {\r\n    margin-bottom: 32px;\r\n    \r\n    .process-form {\r\n      margin-bottom: 24px;\r\n      \r\n      .comment-label {\r\n        color: #ffffff;\r\n        margin-bottom: 8px;\r\n        font-size: 14px;\r\n      }\r\n      \r\n      .comment-input {\r\n        :deep(.el-textarea__inner) {\r\n          background: #374151;\r\n          border: 1px solid #9ca3af;\r\n          color: #ffffff;\r\n          \r\n          &::placeholder {\r\n            color: #9ca3af;\r\n          }\r\n          \r\n          &:focus {\r\n            border-color: #409eff;\r\n            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n</style>"]}]}