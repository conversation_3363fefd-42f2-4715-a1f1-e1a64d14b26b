{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBBcHByb3ZhbERpYWxvZyBmcm9tICcuL2NvbXBvbmVudHMvQXBwcm92YWxEaWFsb2cudnVlJwppbXBvcnQgVGFiU3dpdGNoIGZyb20gJ0AvY29tcG9uZW50cy9JbnNwZWN0aW9uL1RhYlN3aXRjaCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWFpbnRlbmFuY2VBdWRpdCcsCiAgY29tcG9uZW50czogewogICAgQXBwcm92YWxEaWFsb2csCiAgICBUYWJTd2l0Y2gKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgaW5mcmFzdHJ1Y3R1cmVUeXBlOiAnYnJpZGdlJywgLy8gYnJpZGdlLCB0dW5uZWwKICAgICAgYXVkaXRTdGF0dXM6ICdwZW5kaW5nJywgLy8gcGVuZGluZywgY29tcGxldGVkCiAgICAgIGF1ZGl0TGlzdDogW10sCiAgICAgIHRvdGFsOiAwLAogICAgICAKICAgICAgLy8gVEFC6YCJ6aG5CiAgICAgIHRhYk9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICBuYW1lOiAnYnJpZGdlJywKICAgICAgICAgIGxhYmVsOiAn5qGl5qKB5YW75oqk57u05L+u5a6h5qC4JywKICAgICAgICAgIGljb246ICdicmlkZ2UtaWNvbicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIG5hbWU6ICd0dW5uZWwnLAogICAgICAgICAgbGFiZWw6ICfpmqfpgZPlhbvmiqTnu7Tkv67lrqHmoLgnLAogICAgICAgICAgaWNvbjogJ3R1bm5lbC1pY29uJwogICAgICAgIH0KICAgICAgXSwKICAgICAgCiAgICAgIC8vIOWuoeaJueW8ueahhuebuOWFswogICAgICBhcHByb3ZhbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjdXJyZW50QXBwcm92YWxEYXRhOiB7fSwKICAgICAgCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDIsIC8vIOiuvue9ruWwj+eahOWIhumhteWkp+Wwj+S7peS+v+eci+WIsOWIhumhtee7hOS7tgogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIGF1ZGl0SXRlbTogJycsCiAgICAgICAgaW5pdGlhdG9yOiAnJywKICAgICAgICBpbml0aWF0ZVRpbWU6ICcnLAogICAgICAgIHVuaXQ6ICcnCiAgICAgIH0sCiAgICAgIAogICAgICAvLyDlrqHmoLjpobnpgInpobkKICAgICAgYXVkaXRJdGVtczogWwogICAgICAgIHsgbGFiZWw6ICdYWFhYWFjpobnnm64nLCB2YWx1ZTogJ3Byb2plY3RfMScgfSwKICAgICAgICB7IGxhYmVsOiAnWVlZWVlZ6aG555uuJywgdmFsdWU6ICdwcm9qZWN0XzInIH0sCiAgICAgICAgeyBsYWJlbDogJ1paWlpaWumhueebricsIHZhbHVlOiAncHJvamVjdF8zJyB9CiAgICAgIF0sCiAgICAgIAogICAgICAvLyDlj5HotbfkurrpgInpobkKICAgICAgaW5pdGlhdG9yczogWwogICAgICAgIHsgbGFiZWw6ICflkajmlofpnaknLCB2YWx1ZTogJ3pob3V3ZW5nZScgfSwKICAgICAgICB7IGxhYmVsOiAn572X5a2Q5a6JJywgdmFsdWU6ICdsdW96aWFuJyB9LAogICAgICAgIHsgbGFiZWw6ICfmsZ/ovp7oiJ8nLCB2YWx1ZTogJ2ppYW5nY2l6aG91JyB9LAogICAgICAgIHsgbGFiZWw6ICfmnLHlu7rlhpsnLCB2YWx1ZTogJ3podWppYW5qdW4nIH0sCiAgICAgICAgeyBsYWJlbDogJ+eOi+iLpeWzsCcsIHZhbHVlOiAnd2FuZ3J1b2ZlbmcnIH0sCiAgICAgICAgeyBsYWJlbDogJ+ael+mbqOasoycsIHZhbHVlOiAnbGlueXV4aW4nIH0sCiAgICAgICAgeyBsYWJlbDogJ+mDkei+sOmAuCcsIHZhbHVlOiAnemhlbmdjaGVueWknIH0sCiAgICAgICAgeyBsYWJlbDogJ+W8oOagvOeEticsIHZhbHVlOiAnemhhbmdnZXJhbicgfSwKICAgICAgICB7IGxhYmVsOiAn5p2O5bu65YabJywgdmFsdWU6ICdsaWppYW5qdW4nIH0sCiAgICAgICAgeyBsYWJlbDogJ+i1teaZk+e6oicsIHZhbHVlOiAnemhhb3hpYW9ob25nJyB9LAogICAgICAgIHsgbGFiZWw6ICfpu4Tmt5HoiqwnLCB2YWx1ZTogJ2h1YW5nc2h1ZmVuJyB9LAogICAgICAgIHsgbGFiZWw6ICfpmYjkuL3ljY4nLCB2YWx1ZTogJ2NoZW5saWh1YScgfQogICAgICBdLAogICAgICAKICAgICAgLy8g5Y2V5L2N6YCJ6aG5CiAgICAgIHVuaXRzOiBbCiAgICAgICAgeyBsYWJlbDogJ+mVv+aymeW4guahpeaigeeuoeeQhuWkhCcsIHZhbHVlOiAnY2hhbmdzaGFfYnJpZGdlX21nbXQnIH0sCiAgICAgICAgeyBsYWJlbDogJ+mVv+aymeW4gumap+mBk+euoeeQhuWkhCcsIHZhbHVlOiAnY2hhbmdzaGFfdHVubmVsX21nbXQnIH0sCiAgICAgICAgeyBsYWJlbDogJ+a5luWNl+ecgeS6pOmAmuWOhScsIHZhbHVlOiAnaHVuYW5fdHJhbnNwb3J0X2RlcHQnIH0KICAgICAgXSwKCiAgICAgIC8vIOmdmeaAgea1i+ivleaVsOaNriAtIOWHj+WwkeaVsOaNrumHj+S7peS+v+eci+WIsOWIhumhtee7hOS7tgogICAgICBtb2NrQXVkaXREYXRhOiB7CiAgICAgICAgYnJpZGdlOiB7CiAgICAgICAgICBwZW5kaW5nOiBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBpZDogJzAwMScsCiAgICAgICAgICAgICAgc2VyaWFsTnVtYmVyOiAnMDAxJywKICAgICAgICAgICAgICBhdWRpdEl0ZW06ICdYWFhYWFjpobnnm64nLAogICAgICAgICAgICAgIGluaXRpYXRvcjogJ+WRqOaWh+mdqScsCiAgICAgICAgICAgICAgaW5pdGlhdGVUaW1lOiAnMjAyNS8wOS8wMSAxNjoxNicsCiAgICAgICAgICAgICAgdW5pdDogJ+mVv+aymeW4guahpeaigeeuoeeQhuWkhCcKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIGlkOiAnMDAyJywKICAgICAgICAgICAgICBzZXJpYWxOdW1iZXI6ICcwMDInLAogICAgICAgICAgICAgIGF1ZGl0SXRlbTogJ1lZWVlZWemhueebricsCiAgICAgICAgICAgICAgaW5pdGlhdG9yOiAn572X5a2Q5a6JJywKICAgICAgICAgICAgICBpbml0aWF0ZVRpbWU6ICcyMDI1LzA5LzAxIDE1OjMwJywKICAgICAgICAgICAgICB1bml0OiAn6ZW/5rKZ5biC5qGl5qKB566h55CG5aSEJwogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICcwMDMnLAogICAgICAgICAgICAgIHNlcmlhbE51bWJlcjogJzAwMycsCiAgICAgICAgICAgICAgYXVkaXRJdGVtOiAnWlpaWlpa6aG555uuJywKICAgICAgICAgICAgICBpbml0aWF0b3I6ICfmsZ/ovp7oiJ8nLAogICAgICAgICAgICAgIGluaXRpYXRlVGltZTogJzIwMjUvMDkvMDEgMTQ6NDUnLAogICAgICAgICAgICAgIHVuaXQ6ICfplb/mspnluILmoaXmooHnrqHnkIblpIQnCiAgICAgICAgICAgIH0KICAgICAgICAgIF0sCiAgICAgICAgICBjb21wbGV0ZWQ6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIGlkOiAnMDA0JywKICAgICAgICAgICAgICBzZXJpYWxOdW1iZXI6ICcwMDQnLAogICAgICAgICAgICAgIGF1ZGl0SXRlbTogJ0FBQUFBQemhueebricsCiAgICAgICAgICAgICAgaW5pdGlhdG9yOiAn5pyx5bu65YabJywKICAgICAgICAgICAgICBpbml0aWF0ZVRpbWU6ICcyMDI1LzA4LzI4IDE0OjMwJywKICAgICAgICAgICAgICB1bml0OiAn6ZW/5rKZ5biC5qGl5qKB566h55CG5aSEJwogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICcwMDUnLAogICAgICAgICAgICAgIHNlcmlhbE51bWJlcjogJzAwNScsCiAgICAgICAgICAgICAgYXVkaXRJdGVtOiAnQkJCQkJC6aG555uuJywKICAgICAgICAgICAgICBpbml0aWF0b3I6ICfnjovoi6Xls7AnLAogICAgICAgICAgICAgIGluaXRpYXRlVGltZTogJzIwMjUvMDgvMjUgMTA6MTUnLAogICAgICAgICAgICAgIHVuaXQ6ICfplb/mspnluILmoaXmooHnrqHnkIblpIQnCiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9LAogICAgICAgIHR1bm5lbDogewogICAgICAgICAgcGVuZGluZzogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICcxMDEnLAogICAgICAgICAgICAgIHNlcmlhbE51bWJlcjogJzAwMScsCiAgICAgICAgICAgICAgYXVkaXRJdGVtOiAnQ0NDQ0ND6Zqn6YGT6aG555uuJywKICAgICAgICAgICAgICBpbml0aWF0b3I6ICfmnpfpm6jmrKMnLAogICAgICAgICAgICAgIGluaXRpYXRlVGltZTogJzIwMjUvMDkvMDEgMTU6MzAnLAogICAgICAgICAgICAgIHVuaXQ6ICfplb/mspnluILpmqfpgZPnrqHnkIblpIQnCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBpZDogJzEwMicsCiAgICAgICAgICAgICAgc2VyaWFsTnVtYmVyOiAnMDAyJywKICAgICAgICAgICAgICBhdWRpdEl0ZW06ICdERERERETpmqfpgZPpobnnm64nLAogICAgICAgICAgICAgIGluaXRpYXRvcjogJ+mDkei+sOmAuCcsCiAgICAgICAgICAgICAgaW5pdGlhdGVUaW1lOiAnMjAyNS8wOS8wMSAxNDoyMCcsCiAgICAgICAgICAgICAgdW5pdDogJ+mVv+aymeW4gumap+mBk+euoeeQhuWkhCcKICAgICAgICAgICAgfQogICAgICAgICAgXSwKICAgICAgICAgIGNvbXBsZXRlZDogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICcxMDMnLAogICAgICAgICAgICAgIHNlcmlhbE51bWJlcjogJzAwMycsCiAgICAgICAgICAgICAgYXVkaXRJdGVtOiAnRUVFRUVF6Zqn6YGT6aG555uuJywKICAgICAgICAgICAgICBpbml0aWF0b3I6ICflvKDmoLznhLYnLAogICAgICAgICAgICAgIGluaXRpYXRlVGltZTogJzIwMjUvMDgvMzAgMTY6NDUnLAogICAgICAgICAgICAgIHVuaXQ6ICfplb/mspnluILpmqfpgZPnrqHnkIblpIQnCiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmxvYWRBdWRpdExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgLy8g6I635Y+W5a6h5qC45YiX6KGoCiAgICBsb2FkQXVkaXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlCiAgICAgIAogICAgICAvLyDojrflj5blvZPliY3mlbDmja7mupAKICAgICAgY29uc3QgY3VycmVudERhdGEgPSB0aGlzLm1vY2tBdWRpdERhdGFbdGhpcy5pbmZyYXN0cnVjdHVyZVR5cGVdW3RoaXMuYXVkaXRTdGF0dXNdCiAgICAgIAogICAgICAvLyDlupTnlKjnrZvpgInmnaHku7YKICAgICAgbGV0IGZpbHRlcmVkRGF0YSA9IFsuLi5jdXJyZW50RGF0YV0KICAgICAgCiAgICAgIC8vIOWFs+mUruivjeaQnOe0ogogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5rZXl3b3JkKSB7CiAgICAgICAgY29uc3Qga2V5d29yZCA9IHRoaXMucXVlcnlQYXJhbXMua2V5d29yZC50b0xvd2VyQ2FzZSgpCiAgICAgICAgZmlsdGVyZWREYXRhID0gZmlsdGVyZWREYXRhLmZpbHRlcihpdGVtID0+IAogICAgICAgICAgaXRlbS5hdWRpdEl0ZW0udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSB8fAogICAgICAgICAgaXRlbS5pbml0aWF0b3IudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhrZXl3b3JkKSB8fAogICAgICAgICAgaXRlbS51bml0LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoa2V5d29yZCkKICAgICAgICApCiAgICAgIH0KICAgICAgCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmF1ZGl0SXRlbSkgewogICAgICAgIGZpbHRlcmVkRGF0YSA9IGZpbHRlcmVkRGF0YS5maWx0ZXIoaXRlbSA9PiAKICAgICAgICAgIGl0ZW0uYXVkaXRJdGVtLmluY2x1ZGVzKHRoaXMucXVlcnlQYXJhbXMuYXVkaXRJdGVtKSB8fAogICAgICAgICAgdGhpcy5hdWRpdEl0ZW1zLmZpbmQob3B0ID0+IG9wdC52YWx1ZSA9PT0gdGhpcy5xdWVyeVBhcmFtcy5hdWRpdEl0ZW0pPy5sYWJlbCA9PT0gaXRlbS5hdWRpdEl0ZW0KICAgICAgICApCiAgICAgIH0KICAgICAgCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmluaXRpYXRvcikgewogICAgICAgIGZpbHRlcmVkRGF0YSA9IGZpbHRlcmVkRGF0YS5maWx0ZXIoaXRlbSA9PiAKICAgICAgICAgIGl0ZW0uaW5pdGlhdG9yID09PSB0aGlzLnF1ZXJ5UGFyYW1zLmluaXRpYXRvciB8fAogICAgICAgICAgdGhpcy5pbml0aWF0b3JzLmZpbmQob3B0ID0+IG9wdC52YWx1ZSA9PT0gdGhpcy5xdWVyeVBhcmFtcy5pbml0aWF0b3IpPy5sYWJlbCA9PT0gaXRlbS5pbml0aWF0b3IKICAgICAgICApCiAgICAgIH0KICAgICAgCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLnVuaXQpIHsKICAgICAgICBmaWx0ZXJlZERhdGEgPSBmaWx0ZXJlZERhdGEuZmlsdGVyKGl0ZW0gPT4gCiAgICAgICAgICBpdGVtLnVuaXQuaW5jbHVkZXModGhpcy5xdWVyeVBhcmFtcy51bml0KSB8fAogICAgICAgICAgdGhpcy51bml0cy5maW5kKG9wdCA9PiBvcHQudmFsdWUgPT09IHRoaXMucXVlcnlQYXJhbXMudW5pdCk/LmxhYmVsID09PSBpdGVtLnVuaXQKICAgICAgICApCiAgICAgIH0KICAgICAgCiAgICAgIC8vIOWIhumhteWkhOeQhgogICAgICB0aGlzLnRvdGFsID0gZmlsdGVyZWREYXRhLmxlbmd0aAogICAgICBjb25zdCBzdGFydEluZGV4ID0gKHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSAtIDEpICogdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZQogICAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplCiAgICAgIAogICAgICAvLyDmqKHmi5/lvILmraXliqDovb0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5hdWRpdExpc3QgPSBmaWx0ZXJlZERhdGEuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfSwgMzAwKQogICAgfSwKICAgIAogICAgLy8gVEFC5YiH5o2i5aSE55CGCiAgICBoYW5kbGVUYWJDbGljayh0YWIpIHsKICAgICAgdGhpcy5pbmZyYXN0cnVjdHVyZVR5cGUgPSB0YWIubmFtZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMubG9hZEF1ZGl0TGlzdCgpCiAgICB9LAogICAgCiAgICAvLyDliIfmjaLln7rnoYDorr7mlr3nsbvlnosKICAgIHN3aXRjaEluZnJhc3RydWN0dXJlVHlwZSh0eXBlKSB7CiAgICAgIHRoaXMuaW5mcmFzdHJ1Y3R1cmVUeXBlID0gdHlwZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMubG9hZEF1ZGl0TGlzdCgpCiAgICB9LAogICAgCiAgICAvLyDliIfmjaLlrqHmoLjnirbmgIEKICAgIHN3aXRjaEF1ZGl0U3RhdHVzKHN0YXR1cykgewogICAgICB0aGlzLmF1ZGl0U3RhdHVzID0gc3RhdHVzCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5sb2FkQXVkaXRMaXN0KCkKICAgIH0sCiAgICAKICAgIC8vIOafpeivogogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5sb2FkQXVkaXRMaXN0KCkKICAgIH0sCiAgICAKICAgIC8vIOmHjee9ruafpeivogogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAyLCAvLyDkv53mjIHkuI7liJ3lp4vorr7nva7kuIDoh7QKICAgICAgICBrZXl3b3JkOiAnJywKICAgICAgICBhdWRpdEl0ZW06ICcnLAogICAgICAgIGluaXRpYXRvcjogJycsCiAgICAgICAgaW5pdGlhdGVUaW1lOiAnJywKICAgICAgICB1bml0OiAnJwogICAgICB9CiAgICAgIHRoaXMubG9hZEF1ZGl0TGlzdCgpCiAgICB9LAogICAgCiAgICAvLyDlrqHmoLjpobnnm64KICAgIGhhbmRsZUF1ZGl0KHJvdykgewogICAgICB0aGlzLmN1cnJlbnRBcHByb3ZhbERhdGEgPSByb3cKICAgICAgdGhpcy5hcHByb3ZhbERpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgCiAgICAvLyDmn6XnnIvpobnnm67vvIjlt7LlrqHmoLjnirbmgIHvvIkKICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIC8vIOWvueS6juW3suWuoeaguOeahOmhueebru+8jOS7peWPquivu+aooeW8j+aJk+W8gOWuoeaJueW8ueahhgogICAgICB0aGlzLmN1cnJlbnRBcHByb3ZhbERhdGEgPSB7IC4uLnJvdywgcmVhZG9ubHk6IHRydWUgfQogICAgICB0aGlzLmFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICAKICAgIC8vIOWuoeaJueaTjeS9nOaIkOWKnwogICAgaGFuZGxlQXBwcm92YWxTdWNjZXNzKGRhdGEpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlrqHmibnmk43kvZzmiJDlip9gKQogICAgICAvLyDliLfmlrDliJfooagKICAgICAgdGhpcy5sb2FkQXVkaXRMaXN0KCkKICAgIH0sCiAgICAKICAgIC8vIOWIhumhteWkp+Wwj+WPmOWMlgogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSA9IHZhbAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMubG9hZEF1ZGl0TGlzdCgpCiAgICB9LAogICAgCiAgICAvLyDlvZPliY3pobXlj5jljJYKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IHZhbAogICAgICB0aGlzLmxvYWRBdWRpdExpc3QoKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/audit", "sourcesContent": ["<template>\n  <div class=\"maintenance-audit-page maintenance-theme inspection-container\">\n    <div class=\"page-container\">\n      <!-- TAB切换 -->\n      <div class=\"inspection-tabs\">\n        <TabSwitch\n          v-model=\"infrastructureType\"\n          :tabs=\"tabOptions\"\n          @tab-click=\"handleTabClick\"\n        />\n      </div>\n\n      <!-- 审核状态标签 -->\n      <div class=\"secondary-tab-navigation\">\n        <div class=\"secondary-tabs\">\n          <div \n            class=\"tab-item\"\n            :class=\"{ 'is-active': auditStatus === 'pending' }\"\n            @click=\"switchAuditStatus('pending')\"\n          >\n            <i class=\"el-icon-clock\"></i>\n            待审核\n          </div>\n          <div \n            class=\"tab-item\"\n            :class=\"{ 'is-active': auditStatus === 'completed' }\"\n            @click=\"switchAuditStatus('completed')\"\n          >\n            <i class=\"el-icon-check\"></i>\n            已审核\n          </div>\n        </div>\n      </div>\n\n      <!-- 筛选表单 -->\n      <div class=\"filter-section\">\n        <div class=\"filter-content\">\n          <el-input\n            v-model=\"queryParams.keyword\"\n            placeholder=\"搜索审核项目、发起人\"\n            clearable\n            class=\"filter-select\"\n          />\n\n          <el-select\n            v-model=\"queryParams.auditItem\"\n            placeholder=\"审核项\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部审核项\" value=\"\" />\n            <el-option \n              v-for=\"item in auditItems\" \n              :key=\"item.value\" \n              :label=\"item.label\" \n              :value=\"item.value\" \n            />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.initiator\"\n            placeholder=\"发起人\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部发起人\" value=\"\" />\n            <el-option \n              v-for=\"user in initiators\" \n              :key=\"user.value\" \n              :label=\"user.label\" \n              :value=\"user.value\" \n            />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.initiateTime\"\n            placeholder=\"发起时间\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部时间\" value=\"\" />\n            <el-option label=\"今天\" value=\"today\" />\n            <el-option label=\"本周\" value=\"week\" />\n            <el-option label=\"本月\" value=\"month\" />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.unit\"\n            placeholder=\"单位\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部单位\" value=\"\" />\n            <el-option \n              v-for=\"unit in units\" \n              :key=\"unit.value\" \n              :label=\"unit.label\" \n              :value=\"unit.value\" \n            />\n          </el-select>\n          \n          <div class=\"filter-actions\">\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </div>\n        </div>\n      </div>\n        \n      <!-- 数据表格 -->\n      <div class=\"inspection-table common-table\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"auditList\"\n          class=\"audit-table\"\n          stripe\n        >\n          <el-table-column \n            prop=\"serialNumber\" \n            label=\"序号\" \n            width=\"80\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"auditItem\" \n            label=\"审核项\" \n            min-width=\"200\"\n            show-overflow-tooltip \n          />\n          \n          <el-table-column \n            prop=\"initiator\" \n            label=\"发起人\" \n            width=\"120\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"initiateTime\" \n            label=\"发起时间\" \n            width=\"180\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"unit\" \n            label=\"单位\" \n            min-width=\"180\"\n            show-overflow-tooltip \n          />\n          \n          <el-table-column \n            label=\"操作\" \n            width=\"100\" \n            align=\"center\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"auditStatus === 'pending' ? handleAudit(scope.row) : handleView(scope.row)\"\n              >\n                {{ auditStatus === 'pending' ? '审核' : '查看' }}\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n        \n      <!-- 分页 -->\n      <div class=\"inspection-pagination\">\n        <el-pagination\n          :current-page=\"queryParams.pageNum\"\n          :page-sizes=\"[2, 5, 10, 20]\"\n          :page-size=\"queryParams.pageSize\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          small\n        />\n      </div>\n    </div>\n\n    <!-- 审批弹框 -->\n    <ApprovalDialog\n      :visible.sync=\"approvalDialogVisible\"\n      :approval-data=\"currentApprovalData\"\n      :readonly=\"currentApprovalData.readonly || false\"\n      @approve=\"handleApprovalSuccess\"\n      @reject=\"handleApprovalSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport ApprovalDialog from './components/ApprovalDialog.vue'\nimport TabSwitch from '@/components/Inspection/TabSwitch'\n\nexport default {\n  name: 'MaintenanceAudit',\n  components: {\n    ApprovalDialog,\n    TabSwitch\n  },\n  data() {\n    return {\n      loading: false,\n      infrastructureType: 'bridge', // bridge, tunnel\n      auditStatus: 'pending', // pending, completed\n      auditList: [],\n      total: 0,\n      \n      // TAB选项\n      tabOptions: [\n        {\n          name: 'bridge',\n          label: '桥梁养护维修审核',\n          icon: 'bridge-icon'\n        },\n        {\n          name: 'tunnel',\n          label: '隧道养护维修审核',\n          icon: 'tunnel-icon'\n        }\n      ],\n      \n      // 审批弹框相关\n      approvalDialogVisible: false,\n      currentApprovalData: {},\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 2, // 设置小的分页大小以便看到分页组件\n        keyword: '',\n        auditItem: '',\n        initiator: '',\n        initiateTime: '',\n        unit: ''\n      },\n      \n      // 审核项选项\n      auditItems: [\n        { label: 'XXXXXX项目', value: 'project_1' },\n        { label: 'YYYYYY项目', value: 'project_2' },\n        { label: 'ZZZZZZ项目', value: 'project_3' }\n      ],\n      \n      // 发起人选项\n      initiators: [\n        { label: '周文革', value: 'zhouwenge' },\n        { label: '罗子安', value: 'luozian' },\n        { label: '江辞舟', value: 'jiangcizhou' },\n        { label: '朱建军', value: 'zhujianjun' },\n        { label: '王若峰', value: 'wangruofeng' },\n        { label: '林雨欣', value: 'linyuxin' },\n        { label: '郑辰逸', value: 'zhengchenyi' },\n        { label: '张格然', value: 'zhanggeran' },\n        { label: '李建军', value: 'lijianjun' },\n        { label: '赵晓红', value: 'zhaoxiaohong' },\n        { label: '黄淑芬', value: 'huangshufen' },\n        { label: '陈丽华', value: 'chenlihua' }\n      ],\n      \n      // 单位选项\n      units: [\n        { label: '长沙市桥梁管理处', value: 'changsha_bridge_mgmt' },\n        { label: '长沙市隧道管理处', value: 'changsha_tunnel_mgmt' },\n        { label: '湖南省交通厅', value: 'hunan_transport_dept' }\n      ],\n\n      // 静态测试数据 - 减少数据量以便看到分页组件\n      mockAuditData: {\n        bridge: {\n          pending: [\n            {\n              id: '001',\n              serialNumber: '001',\n              auditItem: 'XXXXXX项目',\n              initiator: '周文革',\n              initiateTime: '2025/09/01 16:16',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '002',\n              serialNumber: '002',\n              auditItem: 'YYYYYY项目',\n              initiator: '罗子安',\n              initiateTime: '2025/09/01 15:30',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '003',\n              serialNumber: '003',\n              auditItem: 'ZZZZZZ项目',\n              initiator: '江辞舟',\n              initiateTime: '2025/09/01 14:45',\n              unit: '长沙市桥梁管理处'\n            }\n          ],\n          completed: [\n            {\n              id: '004',\n              serialNumber: '004',\n              auditItem: 'AAAAAA项目',\n              initiator: '朱建军',\n              initiateTime: '2025/08/28 14:30',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '005',\n              serialNumber: '005',\n              auditItem: 'BBBBBB项目',\n              initiator: '王若峰',\n              initiateTime: '2025/08/25 10:15',\n              unit: '长沙市桥梁管理处'\n            }\n          ]\n        },\n        tunnel: {\n          pending: [\n            {\n              id: '101',\n              serialNumber: '001',\n              auditItem: 'CCCCCC隧道项目',\n              initiator: '林雨欣',\n              initiateTime: '2025/09/01 15:30',\n              unit: '长沙市隧道管理处'\n            },\n            {\n              id: '102',\n              serialNumber: '002',\n              auditItem: 'DDDDDD隧道项目',\n              initiator: '郑辰逸',\n              initiateTime: '2025/09/01 14:20',\n              unit: '长沙市隧道管理处'\n            }\n          ],\n          completed: [\n            {\n              id: '103',\n              serialNumber: '003',\n              auditItem: 'EEEEEE隧道项目',\n              initiator: '张格然',\n              initiateTime: '2025/08/30 16:45',\n              unit: '长沙市隧道管理处'\n            }\n          ]\n        }\n      }\n    }\n  },\n  created() {\n    this.loadAuditList()\n  },\n  methods: {\n    // 获取审核列表\n    loadAuditList() {\n      this.loading = true\n      \n      // 获取当前数据源\n      const currentData = this.mockAuditData[this.infrastructureType][this.auditStatus]\n      \n      // 应用筛选条件\n      let filteredData = [...currentData]\n      \n      // 关键词搜索\n      if (this.queryParams.keyword) {\n        const keyword = this.queryParams.keyword.toLowerCase()\n        filteredData = filteredData.filter(item => \n          item.auditItem.toLowerCase().includes(keyword) ||\n          item.initiator.toLowerCase().includes(keyword) ||\n          item.unit.toLowerCase().includes(keyword)\n        )\n      }\n      \n      if (this.queryParams.auditItem) {\n        filteredData = filteredData.filter(item => \n          item.auditItem.includes(this.queryParams.auditItem) ||\n          this.auditItems.find(opt => opt.value === this.queryParams.auditItem)?.label === item.auditItem\n        )\n      }\n      \n      if (this.queryParams.initiator) {\n        filteredData = filteredData.filter(item => \n          item.initiator === this.queryParams.initiator ||\n          this.initiators.find(opt => opt.value === this.queryParams.initiator)?.label === item.initiator\n        )\n      }\n      \n      if (this.queryParams.unit) {\n        filteredData = filteredData.filter(item => \n          item.unit.includes(this.queryParams.unit) ||\n          this.units.find(opt => opt.value === this.queryParams.unit)?.label === item.unit\n        )\n      }\n      \n      // 分页处理\n      this.total = filteredData.length\n      const startIndex = (this.queryParams.pageNum - 1) * this.queryParams.pageSize\n      const endIndex = startIndex + this.queryParams.pageSize\n      \n      // 模拟异步加载\n      setTimeout(() => {\n        this.auditList = filteredData.slice(startIndex, endIndex)\n        this.loading = false\n      }, 300)\n    },\n    \n    // TAB切换处理\n    handleTabClick(tab) {\n      this.infrastructureType = tab.name\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 切换基础设施类型\n    switchInfrastructureType(type) {\n      this.infrastructureType = type\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 切换审核状态\n    switchAuditStatus(status) {\n      this.auditStatus = status\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 2, // 保持与初始设置一致\n        keyword: '',\n        auditItem: '',\n        initiator: '',\n        initiateTime: '',\n        unit: ''\n      }\n      this.loadAuditList()\n    },\n    \n    // 审核项目\n    handleAudit(row) {\n      this.currentApprovalData = row\n      this.approvalDialogVisible = true\n    },\n    \n    // 查看项目（已审核状态）\n    handleView(row) {\n      // 对于已审核的项目，以只读模式打开审批弹框\n      this.currentApprovalData = { ...row, readonly: true }\n      this.approvalDialogVisible = true\n    },\n    \n    // 审批操作成功\n    handleApprovalSuccess(data) {\n      this.$message.success(`审批操作成功`)\n      // 刷新列表\n      this.loadAuditList()\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.loadAuditList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.maintenance-audit-page {\n  // 样式已通过主题文件提供\n  \n  // 使用公共状态Tab导航样式\n  .secondary-tab-navigation {\n    @extend .common-status-tab-navigation;\n    \n    .secondary-tabs {\n      @extend .status-tabs;\n    }\n  }\n}\n\n// Element UI下拉选项样式覆盖\n:deep(.el-select-dropdown) {\n  background: #374151;\n  border: 1px solid #4b5563;\n  \n  .el-select-dropdown__item {\n    color: #ffffff;\n    \n    &:hover {\n      background: #4b5563;\n    }\n    \n    &.selected {\n      background: #3b82f6;\n      color: #ffffff;\n    }\n  }\n}\n</style>\n"]}]}