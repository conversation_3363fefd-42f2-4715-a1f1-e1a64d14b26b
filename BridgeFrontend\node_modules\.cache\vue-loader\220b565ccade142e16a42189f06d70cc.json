{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\RepairDetailView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\RepairDetailView.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQmFzaWNJbmZvVmlldyBmcm9tICcuL2RldGFpbC9CYXNpY0luZm9WaWV3LnZ1ZScNCmltcG9ydCBQcm9qZWN0c1ZpZXcgZnJvbSAnLi9kZXRhaWwvUHJvamVjdHNWaWV3LnZ1ZScNCmltcG9ydCBEaXNlYXNlc1ZpZXcgZnJvbSAnLi9kZXRhaWwvRGlzZWFzZXNWaWV3LnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUmVwYWlyRGV0YWlsVmlldycsDQogIGNvbXBvbmVudHM6IHsNCiAgICBCYXNpY0luZm9WaWV3LA0KICAgIFByb2plY3RzVmlldywNCiAgICBEaXNlYXNlc1ZpZXcNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICByZXBhaXJEYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhY3RpdmVUYWI6ICdwcm9qZWN0cycgLy8g6buY6K6k5pi+56S65YW75oqk6aG555uudGFi77yM5L6/5LqO5rWL6K+VDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["RepairDetailView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RepairDetailView.vue", "sourceRoot": "src/views/maintenance/repairs/components", "sourcesContent": ["<template>\r\n  <div class=\"repair-detail-view maintenance-theme\">\r\n    <div class=\"page-container\">\r\n      <div class=\"card-container\">\r\n        <!-- 导航标签页 -->\r\n        <el-tabs v-model=\"activeTab\" class=\"detail-tabs\">\r\n          <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n            <basic-info-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"养护项目\" name=\"projects\">\r\n            <projects-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"病害养护\" name=\"diseases\">\r\n            <diseases-view :repair-data=\"repairData\" />\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BasicInfoView from './detail/BasicInfoView.vue'\r\nimport ProjectsView from './detail/ProjectsView.vue'\r\nimport DiseasesView from './detail/DiseasesView.vue'\r\n\r\nexport default {\r\n  name: 'RepairDetailView',\r\n  components: {\r\n    BasicInfoView,\r\n    ProjectsView,\r\n    DiseasesView\r\n  },\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      activeTab: 'projects' // 默认显示养护项目tab，便于测试\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.repair-detail-view {\r\n  .page-container {\r\n    padding: 0;\r\n  }\r\n  \r\n  .card-container {\r\n    background: #1a2332;\r\n    border: 1px solid #374151;\r\n    border-radius: 8px;\r\n    padding: 24px;\r\n  }\r\n  \r\n  .detail-tabs {\r\n    @extend .common-element-tabs;\r\n  }\r\n}\r\n</style>\r\n"]}]}