{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\DamageTypeChart.vue?vue&type=style&index=0&id=07687aa7&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\DamageTypeChart.vue", "mtime": 1758804563528}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovLyDlm77ooajlpJbmoYblrrnlmajmoLflvI8gLSDkuI7nrZvpgInljLrln5/moLflvI/kuIDoh7QNCi5kYW1hZ2UtdHlwZS1jaGFydCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxQjJBNTYgMCUsICMyQTNCNkIgMTAwJSkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDEwcHggIWltcG9ydGFudDsNCiAgcGFkZGluZzogMCAyMHB4IDE0cHggMjBweCAhaW1wb3J0YW50OyAvLyDwn5SnIOenu+mZpOmhtumDqHBhZGRpbmfvvIznlLHlpJbpg6hjaGFydC1oZWFkZXLlpITnkIYNCiAgbWluLWhlaWdodDogMzIwcHggIWltcG9ydGFudDsgLy8g8J+UpyDkuI7lj7Pkvqflrrnlmajorr7nva7kv53mjIHkuIDoh7QNCiAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7IC8vIPCflKcg5L2/55SoMTAwJemrmOW6pumAguW6lOeItuWuueWZqA0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47IC8vIPCflKcg5pS55Li65YiX5pa55ZCR77yM5Li65Zu+6KGo5o+Q5L6b5pu05aW955qE5biD5bGADQogIG92ZXJmbG93OiBoaWRkZW47IC8vIPCflKcg56Gu5L+d5YaF5a655LiN5Lya5rqi5Ye66L655qGGDQogIA0KICAvLyDkvb/nlKjkvKrlhYPntKDlrp7njrDlt6bkuIrop5Llkozlj7PkuIvop5LnmoTkuq7ovrnmoYbmlYjmnpzvvIzkuI7nrZvpgInljLrln5/po47moLzkuIDoh7QNCiAgJjo6YmVmb3JlIHsNCiAgICBjb250ZW50OiAnJzsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgdG9wOiAtMXB4Ow0KICAgIHJpZ2h0OiAtMXB4Ow0KICAgIHdpZHRoOiAxMnB4Ow0KICAgIGhlaWdodDogMTJweDsNCiAgICBiYWNrZ3JvdW5kOiAjMkEzQjZCOw0KICAgIGJvcmRlci10b3AtcmlnaHQtcmFkaXVzOiAxMHB4Ow0KICAgIHotaW5kZXg6IDE7DQogIH0NCg0KICAmOjphZnRlciB7DQogICAgY29udGVudDogJyc7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIHRvcDogMDsNCiAgICBsZWZ0OiAwOw0KICAgIHJpZ2h0OiAwOw0KICAgIGJvdHRvbTogMDsNCiAgICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICAgIHBvaW50ZXItZXZlbnRzOiBub25lOw0KICAgIHotaW5kZXg6IDI7DQogICAgLy8g5Y+q5Zyo5bem5LiK6KeS5ZKM5Y+z5LiL6KeS5re75Yqg5Lqu6L655qGG77yM5LiO562b6YCJ5Yy65Z+f5L+d5oyB5LiA6Ie0DQogICAgYmFja2dyb3VuZDoNCiAgICAgIC8vIOW3puS4iuinkuS6rui+ueahhg0KICAgICAgbGluZWFyLWdyYWRpZW50KDkwZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSAxcHgsIHRyYW5zcGFyZW50IDFweCksDQogICAgICBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSAxcHgsIHRyYW5zcGFyZW50IDFweCksDQogICAgICAvLyDlj7PkuIvop5Lkuq7ovrnmoYYNCiAgICAgIGxpbmVhci1ncmFkaWVudCgyNzBkZWcsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpIDFweCwgdHJhbnNwYXJlbnQgMXB4KSwNCiAgICAgIGxpbmVhci1ncmFkaWVudCgwZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMCUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KSAxcHgsIHRyYW5zcGFyZW50IDFweCk7DQogICAgYmFja2dyb3VuZC1zaXplOiAzMHB4IDFweCwgMXB4IDMwcHgsIDMwcHggMXB4LCAxcHggMzBweDsNCiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiB0b3AgbGVmdCwgdG9wIGxlZnQsIGJvdHRvbSByaWdodCwgYm90dG9tIHJpZ2h0Ow0KICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7DQogIH0NCg0KICAvLyDlm77ooajlhoXlrrnlrrnlmajmoLflvI8NCiAgZGl2W3JlZj0iY2hhcnRDb250YWluZXIiXSB7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIHotaW5kZXg6IDM7IC8vIOehruS/neWbvuihqOWcqOS8quWFg+e0oOS5i+S4ig0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgZmxleDogMTsgLy8g8J+UpyDkvb/nlKhmbGV45Y2g5ruh5Ymp5L2Z56m66Ze077yM5pu/5LujaGVpZ2h0OiAxMDAlDQogICAgbWluLWhlaWdodDogMjgwcHg7IC8vIPCflKcg5LiO5a655Zmo6K6+572u5Y2P6LCD77yM5YeP5Y67aGVhZGVy5ZKMcGFkZGluZ+epuumXtA0KICB9DQp9DQo="}, {"version": 3, "sources": ["DamageTypeChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DamageTypeChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"damage-type-chart\">\r\n    <div \r\n      ref=\"chartContainer\" \r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'DamageType<PERSON><PERSON>',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n  },\r\n  beforeUnmount() {\r\n    if (this.chart) {\r\n      this.chart.dispose()\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$refs.chartContainer)\r\n      this.updateChart()\r\n      \r\n      // 响应式处理\r\n      window.addEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    handleResize() {\r\n      if (this.chart) {\r\n        this.chart.resize()\r\n      }\r\n    },\r\n    \r\n    updateChart() {\r\n      if (!this.chart) return\r\n\r\n      const data = this.formatChartData()\r\n      const totalCount = data.reduce((sum, item) => sum + item.value, 0)\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          show: true,\r\n          orient: 'vertical',\r\n          right: '5%',\r\n          top: 'middle',\r\n          itemWidth: 18,\r\n          itemHeight: 14,\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          },\r\n          formatter: function(name) {\r\n            const item = data.find(d => d.name === name)\r\n            return name + '  ' + (item ? item.value : '')\r\n          }\r\n        },\r\n        graphic: {\r\n          type: 'text',\r\n          left: 'center',\r\n          top: 'middle',\r\n          style: {\r\n            text: totalCount.toString(),\r\n            fontSize: 28,\r\n            fontWeight: 'bold',\r\n            fill: '#ffffff'\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '病害类型',\r\n            type: 'pie',\r\n            radius: ['45%', '65%'],\r\n            center: ['35%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: 14,\r\n                fontWeight: 'bold',\r\n                color: '#ffffff'\r\n              }\r\n            },\r\n            data: data\r\n          }\r\n        ],\r\n        color: ['#40E0D0', '#FFD700', '#FF8C00', '#FF6B6B', '#9370DB']\r\n      }\r\n\r\n      this.chart.setOption(option, true)\r\n    },\r\n    \r\n    formatChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return [\r\n          { value: 581, name: '抗浮' },\r\n          { value: 140, name: '下沉' },\r\n          { value: 95, name: 'TOP' },\r\n          { value: 80, name: '排包' },\r\n          { value: 65, name: 'a581' }\r\n        ]\r\n      }\r\n      \r\n      return this.chartData.map(item => ({\r\n        value: item.count || item.value,\r\n        name: item.type || item.name\r\n      }))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.damage-type-chart {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与右侧容器设置保持一致\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n\r\n  // 图表内容容器样式\r\n  div[ref=\"chartContainer\"] {\r\n    position: relative;\r\n    z-index: 3; // 确保图表在伪元素之上\r\n    width: 100% !important;\r\n    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间\r\n  }\r\n}\r\n</style>\r\n"]}]}