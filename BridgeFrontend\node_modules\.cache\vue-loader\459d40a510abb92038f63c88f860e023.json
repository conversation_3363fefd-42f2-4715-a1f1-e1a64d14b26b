{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue", "mtime": 1758810696266}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEVtZXJnZW5jeVJlcGFpckxpc3QgfSBmcm9tICdAL2FwaS9tYWludGVuYW5jZS9yZXBhaXJzJwppbXBvcnQgU3RhdHVzVGFnIGZyb20gJ0AvY29tcG9uZW50cy9NYWludGVuYW5jZS9TdGF0dXNUYWcnCmltcG9ydCBFbWVyZ2VuY3lEZXRhaWxEaWFsb2cgZnJvbSAnLi9jb21wb25lbnRzL0VtZXJnZW5jeURldGFpbERpYWxvZycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTWFpbnRlbmFuY2VFbWVyZ2VuY3lSZXBhaXJzJywKICBjb21wb25lbnRzOiB7CiAgICBTdGF0dXNUYWcsCiAgICBFbWVyZ2VuY3lEZXRhaWxEaWFsb2cKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgYWN0aXZlVGFiOiAnYnJpZGdlJywgLy8gYnJpZGdlLCB0dW5uZWwKICAgICAgZW1lcmdlbmN5TGlzdDogW10sCiAgICAgIHNlbGVjdGVkRW1lcmdlbmN5OiB7fSwKICAgICAgc2hvd0RldGFpbERpYWxvZzogZmFsc2UsCiAgICAgIHRvdGFsOiAwLAogICAgICAKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgYnJpZGdlTmFtZTogJycsCiAgICAgICAgc3RhdHVzOiAnJywKICAgICAgICBkaXNlYXNlVHlwZTogJycsCiAgICAgICAgbWFuYWdlcjogJycsCiAgICAgICAgbWFpbnRlbmFuY2VVbml0OiAnJywKICAgICAgICBpbmZyYXN0cnVjdHVyZVR5cGU6ICdicmlkZ2UnCiAgICAgIH0sCiAgICAgIAogICAgICAvLyDpgInpobnmlbDmja4KICAgICAgYnJpZGdlT3B0aW9uczogW10sCiAgICAgIG1hbmFnZXJPcHRpb25zOiBbXSwKICAgICAgdW5pdE9wdGlvbnM6IFtdLAogICAgICAKICAgICAgLy8g54q25oCB6YCJ6aG5CiAgICAgIHN0YXR1c09wdGlvbnM6IFsKICAgICAgICB7IGxhYmVsOiAn5b6F5aSE55CGJywgdmFsdWU6ICdwZW5kaW5nJyB9LAogICAgICAgIHsgbGFiZWw6ICflpITnkIblrqHmibnkuK0nLCB2YWx1ZTogJ2luX3JldmlldycgfSwKICAgICAgICB7IGxhYmVsOiAn6YCA5ZueJywgdmFsdWU6ICdyZWplY3RlZCcgfSwKICAgICAgICB7IGxhYmVsOiAn5bey5aSE55CGJywgdmFsdWU6ICdjb21wbGV0ZWQnIH0KICAgICAgXSwKICAgICAgCiAgICAgIC8vIOeXheWus+exu+Wei+mAiemhuQogICAgICBkaXNlYXNlVHlwZXM6IFsKICAgICAgICB7IGxhYmVsOiAn5Ly457yp57yd57y65aSxJywgdmFsdWU6ICdleHBhbnNpb25fam9pbnRfbWlzc2luZycgfSwKICAgICAgICB7IGxhYmVsOiAn54Wn5piO6K6+5pa957y65aSxJywgdmFsdWU6ICdsaWdodGluZ19taXNzaW5nJyB9LAogICAgICAgIHsgbGFiZWw6ICfmiqTmoI/mjZ/lnY8nLCB2YWx1ZTogJ2d1YXJkcmFpbF9kYW1hZ2UnIH0sCiAgICAgICAgeyBsYWJlbDogJ+ahpemdouegtOaNnycsIHZhbHVlOiAnZGVja19kYW1hZ2UnIH0sCiAgICAgICAgeyBsYWJlbDogJ+aOkuawtOS4jeeVhScsIHZhbHVlOiAnZHJhaW5hZ2VfcG9vcicgfQogICAgICBdCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOiOt+WPluW6lOaApee7tOS/ruWIl+ihqAogICAgYXN5bmMgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuaW5mcmFzdHJ1Y3R1cmVUeXBlID0gdGhpcy5hY3RpdmVUYWIKICAgICAgICAKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEVtZXJnZW5jeVJlcGFpckxpc3QodGhpcy5xdWVyeVBhcmFtcykKICAgICAgICB0aGlzLmVtZXJnZW5jeUxpc3QgPSByZXNwb25zZS5kYXRhLmxpc3QgfHwgW10KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbCB8fCAwCiAgICAgICAgdGhpcy5icmlkZ2VPcHRpb25zID0gcmVzcG9uc2UuZGF0YS5icmlkZ2VPcHRpb25zIHx8IFtdCiAgICAgICAgdGhpcy5tYW5hZ2VyT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGEubWFuYWdlck9wdGlvbnMgfHwgW10KICAgICAgICB0aGlzLnVuaXRPcHRpb25zID0gcmVzcG9uc2UuZGF0YS51bml0T3B0aW9ucyB8fCBbXQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluW6lOaApee7tOS/ruWIl+ihqOWksei0pScpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5YiH5o2i5qCH562+CiAgICBzd2l0Y2hUYWIodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICAKICAgIC8vIOafpeivogogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICAKICAgIC8vIOmHjee9ruafpeivogogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBicmlkZ2VOYW1lOiAnJywKICAgICAgICBzdGF0dXM6ICcnLAogICAgICAgIGRpc2Vhc2VUeXBlOiAnJywKICAgICAgICBtYW5hZ2VyOiAnJywKICAgICAgICBtYWludGVuYW5jZVVuaXQ6ICcnLAogICAgICAgIGluZnJhc3RydWN0dXJlVHlwZTogdGhpcy5hY3RpdmVUYWIKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIAogICAgLy8g5p+l55yL6K+m5oOFCiAgICBoYW5kbGVWaWV3KHJvdykgewogICAgICB0aGlzLnNlbGVjdGVkRW1lcmdlbmN5ID0gcm93CiAgICAgIHRoaXMuc2hvd0RldGFpbERpYWxvZyA9IHRydWUKICAgIH0sCiAgICAKICAgIC8vIOWIhumhteWkp+Wwj+WPmOWMlgogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSA9IHZhbAogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIAogICAgLy8g5b2T5YmN6aG15Y+Y5YyWCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSB2YWwKICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/repairs/emergency", "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题 -->\n        <div class=\"page-header\">\n          <h2>应急维修管理</h2>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'bridge' }\"\n              @click=\"switchTab('bridge')\"\n            >\n              <i class=\"el-icon-s-home\"></i>\n              桥梁养护维修\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tunnel' }\"\n              @click=\"switchTab('tunnel')\"\n            >\n              <i class=\"el-icon-place\"></i>\n              隧道养护维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级导航 -->\n        <div class=\"sub-navigation\">\n          <div class=\"sub-tab-container\">\n            <div \n              class=\"sub-tab-item\"\n              @click=\"$router.push('/maintenance/repairs')\"\n            >\n              养护/保洁项目\n            </div>\n            <div \n              class=\"sub-tab-item is-active\"\n            >\n              应急维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 筛选表单 -->\n        <div class=\"filter-form\">\n          <el-form :model=\"queryParams\" inline>\n            <el-form-item label=\"桥梁名称\">\n              <el-select\n                v-model=\"queryParams.bridgeName\"\n                placeholder=\"请选择桥梁\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"bridge in bridgeOptions\"\n                  :key=\"bridge.value\"\n                  :label=\"bridge.label\"\n                  :value=\"bridge.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"状态\">\n              <el-select\n                v-model=\"queryParams.status\"\n                placeholder=\"请选择状态\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"status in statusOptions\"\n                  :key=\"status.value\"\n                  :label=\"status.label\"\n                  :value=\"status.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"病害类型\">\n              <el-select\n                v-model=\"queryParams.diseaseType\"\n                placeholder=\"请选择类型\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"type in diseaseTypes\"\n                  :key=\"type.value\"\n                  :label=\"type.label\"\n                  :value=\"type.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"负责人\">\n              <el-select\n                v-model=\"queryParams.manager\"\n                placeholder=\"请选择负责人\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"manager in managerOptions\"\n                  :key=\"manager.value\"\n                  :label=\"manager.label\"\n                  :value=\"manager.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"养护单位\">\n              <el-select\n                v-model=\"queryParams.maintenanceUnit\"\n                placeholder=\"请选择单位\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"unit in unitOptions\"\n                  :key=\"unit.value\"\n                  :label=\"unit.label\"\n                  :value=\"unit.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item>\n              <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n              <el-button @click=\"resetQuery\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        \n        <!-- 数据表格 -->\n        <div class=\"table-container\">\n          <el-table\n            v-loading=\"loading\"\n            :data=\"emergencyList\"\n            class=\"maintenance-table\"\n          >\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n            \n            <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n            \n            <el-table-column prop=\"reporter\" label=\"上报人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"reportTime\" label=\"上报时间\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"contactPhone\" label=\"联系方式\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <status-tag :status=\"scope.row.status\" type=\"task\" />\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <span class=\"disease-count\">{{ scope.row.diseaseCount }}</span>\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"manager\" label=\"负责人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"maintenanceUnit\" label=\"养护单位\" min-width=\"150\" show-overflow-tooltip />\n            \n            <el-table-column label=\"操作\" width=\"80\" align=\"center\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"handleView(scope.row)\"\n                >\n                  查看\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <!-- 应急维修详情弹窗 -->\n    <emergency-detail-dialog\n      :visible.sync=\"showDetailDialog\"\n      :emergency-data=\"selectedEmergency\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getEmergencyRepairList } from '@/api/maintenance/repairs'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport EmergencyDetailDialog from './components/EmergencyDetailDialog'\n\nexport default {\n  name: 'MaintenanceEmergencyRepairs',\n  components: {\n    StatusTag,\n    EmergencyDetailDialog\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'bridge', // bridge, tunnel\n      emergencyList: [],\n      selectedEmergency: {},\n      showDetailDialog: false,\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: 'bridge'\n      },\n      \n      // 选项数据\n      bridgeOptions: [],\n      managerOptions: [],\n      unitOptions: [],\n      \n      // 状态选项\n      statusOptions: [\n        { label: '待处理', value: 'pending' },\n        { label: '处理审批中', value: 'in_review' },\n        { label: '退回', value: 'rejected' },\n        { label: '已处理', value: 'completed' }\n      ],\n      \n      // 病害类型选项\n      diseaseTypes: [\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\n        { label: '照明设施缺失', value: 'lighting_missing' },\n        { label: '护栏损坏', value: 'guardrail_damage' },\n        { label: '桥面破损', value: 'deck_damage' },\n        { label: '排水不畅', value: 'drainage_poor' }\n      ]\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 获取应急维修列表\n    async getList() {\n      this.loading = true\n      try {\n        this.queryParams.infrastructureType = this.activeTab\n        \n        const response = await getEmergencyRepairList(this.queryParams)\n        this.emergencyList = response.data.list || []\n        this.total = response.data.total || 0\n        this.bridgeOptions = response.data.bridgeOptions || []\n        this.managerOptions = response.data.managerOptions || []\n        this.unitOptions = response.data.unitOptions || []\n      } catch (error) {\n        this.$message.error('获取应急维修列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: this.activeTab\n      }\n      this.getList()\n    },\n    \n    // 查看详情\n    handleView(row) {\n      this.selectedEmergency = row\n      this.showDetailDialog = true\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.sub-navigation {\n  @extend .common-secondary-navigation;\n  \n  .sub-tab-container {\n    @extend .sub-tab-container;\n  }\n}\n\n.filter-form {\n  padding: 24px;\n  background: #1e3a8a;\n  border-bottom: 1px solid #4b5563;\n}\n\n.table-container {\n  padding: 0 24px;\n  \n  .disease-count {\n    color: #3b82f6;\n    font-weight: bold;\n  }\n}\n\n.pagination-container {\n  padding: 16px 24px;\n  text-align: center;\n}\n</style>\n"]}]}