{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\index.vue", "mtime": 1758804563537}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBBY3Rpb25zLCBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycNCmltcG9ydCBTdGF0aXN0aWNzQ2FyZHMgZnJvbSAnLi9jb21wb25lbnRzL1N0YXRpc3RpY3NDYXJkcycNCmltcG9ydCBUcmVuZENoYXJ0IGZyb20gJy4vY29tcG9uZW50cy9UcmVuZENoYXJ0Jw0KaW1wb3J0IFJlZ2lvbkNoYXJ0IGZyb20gJy4vY29tcG9uZW50cy9SZWdpb25DaGFydCcNCmltcG9ydCBEYW1hZ2VUeXBlQ2hhcnQgZnJvbSAnLi9jb21wb25lbnRzL0RhbWFnZVR5cGVDaGFydCcNCmltcG9ydCBUb3AxMFJhbmtpbmdDaGFydCBmcm9tICcuL2NvbXBvbmVudHMvVG9wMTBSYW5raW5nQ2hhcnQnDQppbXBvcnQgeyBGaWx0ZXJTZWN0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL0luc3BlY3Rpb24nDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0luc3BlY3Rpb25TdGF0aXN0aWNzJywNCiAgY29tcG9uZW50czogew0KICAgIFN0YXRpc3RpY3NDYXJkcywNCiAgICBUcmVuZENoYXJ0LA0KICAgIFJlZ2lvbkNoYXJ0LA0KICAgIERhbWFnZVR5cGVDaGFydCwNCiAgICBUb3AxMFJhbmtpbmdDaGFydCwNCiAgICBGaWx0ZXJTZWN0aW9uDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOW9k+WJjea/gOa0u+eahHRhYg0KICAgICAgYWN0aXZlVGFiOiAnYnJpZGdlJywNCg0KDQogICAgICAvLyDnrZvpgInooajljZUNCiAgICAgIGZpbHRlckZvcm06IHsNCiAgICAgICAgYnJpZGdlTmFtZTogJycsDQogICAgICAgIHJlcG9ydFRpbWVSYW5nZTogW10sDQogICAgICAgIHRpbWVSYW5nZTogJ21vbnRoJywNCiAgICAgICAgcmVnaW9uOiAnJywNCiAgICAgICAgcHJvamVjdERlcHQ6ICcnDQogICAgICB9LA0KDQoNCiAgICAgIC8vIOacgOi/keW3oeajgOiusOW9lQ0KICAgICAgcmVjZW50UmVjb3JkczogW10sDQoNCg0KICAgICAgLy8g5pel5pyf6YCJ5oup5Zmo54Sm54K554q25oCB77yI5LiO55eF5a6z5YiX6KGo6aG15LiA6Ie077yJDQogICAgICBpc0RhdGVQaWNrZXJGb2N1c2VkOiBmYWxzZSwNCg0KICAgICAgLy8g562b6YCJ6YWN572uDQogICAgICBmaWx0ZXJDb25maWdzOiB7fQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBHZXR0ZXJzKCdpbnNwZWN0aW9uJywgWw0KICAgICAgJ3N0YXRpc3RpY3NEYXRhJywNCiAgICAgICdzZWxlY3RPcHRpb25zJywNCiAgICAgICdsb2FkaW5nU3RhdGVzJw0KICAgIF0pLA0KDQogICAgbG9hZGluZygpIHsNCiAgICAgIHJldHVybiB0aGlzLmxvYWRpbmdTdGF0ZXMuaW5zcGVjdGlvblJlY29yZHMNCiAgICB9LA0KDQogICAgYnJpZGdlT3B0aW9ucygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdE9wdGlvbnMuYnJpZGdlT3B0aW9ucyB8fCBbXQ0KICAgIH0sDQogICAgLy8g5Yqo5oCB55qE5qGl5qKBL+map+mBk+WQjeensOaWh+acrA0KICAgIGJyaWRnZU5hbWVUZXh0KCkgew0KICAgICAgcmV0dXJuIHRoaXMuYWN0aXZlVGFiID09PSAndHVubmVsJyA/ICfpmqfpgZPlkI3np7AnIDogJ+ahpeaigeWQjeensCcNCiAgICB9LA0KDQoNCiAgICAvLyDml6XmnJ/ojIPlm7TmmL7npLrmlofmnKzvvIjkuI7nl4XlrrPliJfooajpobXkuIDoh7TvvIkNCiAgICBkYXRlUmFuZ2VEaXNwbGF5VGV4dCgpIHsNCiAgICAgIGlmICh0aGlzLmZpbHRlckZvcm0ucmVwb3J0VGltZVJhbmdlICYmIHRoaXMuZmlsdGVyRm9ybS5yZXBvcnRUaW1lUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHJldHVybiBgJHt0aGlzLmZpbHRlckZvcm0ucmVwb3J0VGltZVJhbmdlWzBdfSDoh7MgJHt0aGlzLmZpbHRlckZvcm0ucmVwb3J0VGltZVJhbmdlWzFdfWANCiAgICAgIH0NCiAgICAgIHJldHVybiAn5pel5pyf6IyD5Zu0Jw0KICAgIH0NCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICBhd2FpdCB0aGlzLmluaXRQYWdlRGF0YSgpDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy8g6aG16Z2i5oyC6L295ZCO55qE5Yid5aeL5YyW5pON5L2cDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBBY3Rpb25zKCdpbnNwZWN0aW9uJywgWw0KICAgICAgJ2ZldGNoU3RhdGlzdGljc0RhdGEnLA0KICAgICAgJ2ZldGNoUmVjZW50SW5zcGVjdGlvblJlY29yZHMnLA0KICAgICAgJ2luaXRTZWxlY3RPcHRpb25zJywNCiAgICAgICd1cGRhdGVGaWx0ZXJzJw0KICAgIF0pLA0KDQogICAgLy8g5pCc57SiDQogICAgYXN5bmMgaGFuZGxlU2VhcmNoKGZvcm1EYXRhKSB7DQogICAgICB0aGlzLnVwZGF0ZUZpbHRlcnMoew0KICAgICAgICBpbnNwZWN0aW9uVHlwZTogdGhpcy5hY3RpdmVUYWIsDQogICAgICAgIGJyaWRnZU5hbWU6IGZvcm1EYXRhLmJyaWRnZU5hbWUsDQogICAgICAgIHJlcG9ydFRpbWVSYW5nZTogZm9ybURhdGEucmVwb3J0VGltZVJhbmdlLA0KICAgICAgICB0aW1lUmFuZ2U6IGZvcm1EYXRhLnRpbWVSYW5nZSwNCiAgICAgICAgcmVnaW9uOiBmb3JtRGF0YS5yZWdpb24sDQogICAgICAgIHByb2plY3REZXB0OiBmb3JtRGF0YS5wcm9qZWN0RGVwdA0KICAgICAgfSkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hTdGF0aXN0aWNzRGF0YSgpDQogICAgICBhd2FpdCB0aGlzLmxvYWRSZWNlbnRSZWNvcmRzKCkNCiAgICB9LA0KDQogICAgLy8g6YeN572uDQogICAgYXN5bmMgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLmZpbHRlckZvcm0gPSB7DQogICAgICAgIGJyaWRnZU5hbWU6ICcnLA0KICAgICAgICByZXBvcnRUaW1lUmFuZ2U6IFtdLA0KICAgICAgICB0aW1lUmFuZ2U6ICdtb250aCcsDQogICAgICAgIHJlZ2lvbjogJycsDQogICAgICAgIHByb2plY3REZXB0OiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy51cGRhdGVGaWx0ZXJzKHsNCiAgICAgICAgaW5zcGVjdGlvblR5cGU6IHRoaXMuYWN0aXZlVGFiLA0KICAgICAgICBicmlkZ2VOYW1lOiAnJywNCiAgICAgICAgcmVwb3J0VGltZVJhbmdlOiBbXSwNCiAgICAgICAgdGltZVJhbmdlOiB0aGlzLmZpbHRlckZvcm0udGltZVJhbmdlLA0KICAgICAgICByZWdpb246ICcnLA0KICAgICAgICBwcm9qZWN0RGVwdDogJycNCiAgICAgIH0pDQogICAgICBhd2FpdCB0aGlzLmZldGNoU3RhdGlzdGljc0RhdGEoKQ0KICAgICAgYXdhaXQgdGhpcy5sb2FkUmVjZW50UmVjb3JkcygpDQogICAgfSwNCg0KICAgIC8vIOWIneWni+WMlumhtemdouaVsOaNrg0KICAgIGFzeW5jIGluaXRQYWdlRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWIneWni+WMluS4i+aLiemAiemhuQ0KICAgICAgICBhd2FpdCB0aGlzLmluaXRTZWxlY3RPcHRpb25zKCkNCg0KICAgICAgICAvLyDmm7TmlrDnrZvpgInmnaHku7YNCiAgICAgICAgdGhpcy51cGRhdGVGaWx0ZXJzKHsNCiAgICAgICAgICBpbnNwZWN0aW9uVHlwZTogdGhpcy5hY3RpdmVUYWIsDQogICAgICAgICAgdGltZVJhbmdlOiB0aGlzLmZpbHRlckZvcm0udGltZVJhbmdlDQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g6I635Y+W57uf6K6h5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMuZmV0Y2hTdGF0aXN0aWNzRGF0YSgpDQoNCiAgICAgICAgLy8g6I635Y+W5pyA6L+R5beh5qOA6K6w5b2VDQogICAgICAgIGF3YWl0IHRoaXMubG9hZFJlY2VudFJlY29yZHMoKQ0KDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliJ3lp4vljJbpobXpnaLmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaVsOaNruWksei0pScpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veacgOi/keW3oeajgOiusOW9lQ0KICAgIGFzeW5jIGxvYWRSZWNlbnRSZWNvcmRzKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaFJlY2VudEluc3BlY3Rpb25SZWNvcmRzKHsgbGltaXQ6IDggfSkNCg0KICAgICAgICAvLyDku45zdG9yZeiOt+WPluaVsOaNruaIluS9v+eUqOm7mOiupOaVsOaNrg0KICAgICAgICB0aGlzLnJlY2VudFJlY29yZHMgPSB0aGlzLmdldERlZmF1bHRSZWNlbnRSZWNvcmRzKCkNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295pyA6L+R5beh5qOA6K6w5b2V5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnJlY2VudFJlY29yZHMgPSB0aGlzLmdldERlZmF1bHRSZWNlbnRSZWNvcmRzKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W6buY6K6k5pyA6L+R5beh5qOA6K6w5b2VDQogICAgZ2V0RGVmYXVsdFJlY2VudFJlY29yZHMoKSB7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkNCiAgICAgIGNvbnN0IGRhdGVzID0gW10NCg0KICAgICAgLy8g55Sf5oiQ5pyA6L+ROOWkqeeahOaXpeacnw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA4OyBpKyspIHsNCiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRvZGF5KQ0KICAgICAgICBkYXRlLnNldERhdGUodG9kYXkuZ2V0RGF0ZSgpIC0gaSkNCiAgICAgICAgZGF0ZXMucHVzaChkYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSkNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhY5aSn5qGlJywNCiAgICAgICAgICBpbnNwZWN0aW9uRGF0ZTogZGF0ZXNbMF0sDQogICAgICAgICAgaW5zcGVjdG9yOiAn5ZC05Lqu5ZCJJywNCiAgICAgICAgICBjb250YWN0TnVtYmVyOiAnMTM1ODAwMzc0OTInLA0KICAgICAgICAgIG1vbnRobHlGaW5lOiAyMg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFjlpKfmoaUnLA0KICAgICAgICAgIGluc3BlY3Rpb25EYXRlOiBkYXRlc1sxXSwNCiAgICAgICAgICBpbnNwZWN0b3I6ICfpmYjnp4Doi7EnLA0KICAgICAgICAgIGNvbnRhY3ROdW1iZXI6ICcxNTIxMDA4NzM5NScsDQogICAgICAgICAgbW9udGhseUZpbmU6IDI2DQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWOWkp+ahpScsDQogICAgICAgICAgaW5zcGVjdGlvbkRhdGU6IGRhdGVzWzFdLA0KICAgICAgICAgIGluc3BlY3RvcjogJ+mZiOaYreWQiScsDQogICAgICAgICAgY29udGFjdE51bWJlcjogJzE1OTEwMDE4NDk1JywNCiAgICAgICAgICBtb250aGx5RmluZTogNg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDQsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFjlpKfmoaUnLA0KICAgICAgICAgIGluc3BlY3Rpb25EYXRlOiBkYXRlc1syXSwNCiAgICAgICAgICBpbnNwZWN0b3I6ICfnjovlu7rlhpsnLA0KICAgICAgICAgIGNvbnRhY3ROdW1iZXI6ICcxMzEyMjIzODU3OScsDQogICAgICAgICAgbW9udGhseUZpbmU6IDkNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA1LA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhY5aSn5qGlJywNCiAgICAgICAgICBpbnNwZWN0aW9uRGF0ZTogZGF0ZXNbM10sDQogICAgICAgICAgaW5zcGVjdG9yOiAn5ZC06LaF5qCLJywNCiAgICAgICAgICBjb250YWN0TnVtYmVyOiAnMTM3MjAwODk2ODUnLA0KICAgICAgICAgIG1vbnRobHlGaW5lOiAzMw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDYsDQogICAgICAgICAgYnJpZGdlTmFtZTogJ1hYWFjlpKfmoaUnLA0KICAgICAgICAgIGluc3BlY3Rpb25EYXRlOiBkYXRlc1s0XSwNCiAgICAgICAgICBpbnNwZWN0b3I6ICfmsZ/ono3ooYwnLA0KICAgICAgICAgIGNvbnRhY3ROdW1iZXI6ICcxODIwMjAzODU3OScsDQogICAgICAgICAgbW9udGhseUZpbmU6IDExDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogNywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWOWkp+ahpScsDQogICAgICAgICAgaW5zcGVjdGlvbkRhdGU6IGRhdGVzWzVdLA0KICAgICAgICAgIGluc3BlY3RvcjogJ+WImOWQm+WQmycsDQogICAgICAgICAgY29udGFjdE51bWJlcjogJzE4MzEwMDQ5NjgzJywNCiAgICAgICAgICBtb250aGx5RmluZTogNjINCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiA4LA0KICAgICAgICAgIGJyaWRnZU5hbWU6ICdYWFhY5aSn5qGlJywNCiAgICAgICAgICBpbnNwZWN0aW9uRGF0ZTogZGF0ZXNbNl0sDQogICAgICAgICAgaW5zcGVjdG9yOiAn6KKB5aaC6LCmJywNCiAgICAgICAgICBjb250YWN0TnVtYmVyOiAnMTc4MjEyMjk1ODMnLA0KICAgICAgICAgIG1vbnRobHlGaW5lOiAxOA0KICAgICAgICB9DQogICAgICBdDQogICAgfSwNCg0KICAgIC8vIFRBQuWIh+aNog0KICAgIGFzeW5jIGhhbmRsZVRhYkNsaWNrKHRhYikgew0KICAgICAgdGhpcy5hY3RpdmVUYWIgPSB0YWIubmFtZQ0KICAgICAgdGhpcy51cGRhdGVGaWx0ZXJzKHsgaW5zcGVjdGlvblR5cGU6IHRoaXMuYWN0aXZlVGFiIH0pDQogICAgICBhd2FpdCB0aGlzLmZldGNoU3RhdGlzdGljc0RhdGEoKQ0KICAgICAgYXdhaXQgdGhpcy5sb2FkUmVjZW50UmVjb3JkcygpDQogICAgfSwNCg0KDQogICAgLy8g5p+l55yL5omA5pyJ6K6w5b2VDQogICAgdmlld0FsbFJlY29yZHMoKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IG5hbWU6ICdJbnNwZWN0aW9uUmVjb3JkcycgfSkNCiAgICB9LA0KDQogICAgLy8g5p+l55yL6K6w5b2V6K+m5oOFDQogICAgdmlld1JlY29yZERldGFpbChyZWNvcmQpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfmn6XnnIvorrDlvZXor6bmg4U6JywgcmVjb3JkKQ0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5omT5byA6K+m5oOF5by556qX5oiW6Lez6L2s5Yiw6K+m5oOF6aG16Z2iDQogICAgfSwNCg0KDQogICAgLy8g5pel5pyf6IyD5Zu06YCJ5oup5Zmo55u45YWz5pa55rOV77yI5LiO55eF5a6z5YiX6KGo6aG15LiA6Ie077yJDQogICAgLy8g5YiH5o2i5pel5pyf6YCJ5oup5Zmo5pi+56S6DQogICAgdG9nZ2xlRGF0ZVBpY2tlcigpIHsNCiAgICAgIHRoaXMuaXNEYXRlUGlja2VyRm9jdXNlZCA9IHRydWUNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5oaWRkZW5EYXRlUGlja2VyLmZvY3VzKCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaXpeacn+mAieaLqeWZqOWkseeEpg0KICAgIGhhbmRsZURhdGVQaWNrZXJCbHVyKCkgew0KICAgICAgLy8g5bu26L+f5omn6KGM77yM56Gu5L+d54K55Ye75pON5L2c6IO95q2j5bi45a6M5oiQDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy5pc0RhdGVQaWNrZXJGb2N1c2VkID0gZmFsc2UNCiAgICAgIH0sIDIwMCkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5pel5pyf6IyD5Zu05Y+Y5YyWDQogICAgaGFuZGxlRGF0ZVJhbmdlQ2hhbmdlKHZhbHVlKSB7DQogICAgICB0aGlzLmZpbHRlckZvcm0ucmVwb3J0VGltZVJhbmdlID0gdmFsdWUNCiAgICAgIC8vIOaXpeacn+mAieaLqeWujOaIkOWQjuenu+mZpOeEpueCueeKtuaAgQ0KICAgICAgdGhpcy5pc0RhdGVQaWNrZXJGb2N1c2VkID0gZmFsc2UNCiAgICB9LA0KDQogICAgLy8g5riF56m65pel5pyf6IyD5Zu0DQogICAgY2xlYXJEYXRlUmFuZ2UoKSB7DQogICAgICB0aGlzLmZpbHRlckZvcm0ucmVwb3J0VGltZVJhbmdlID0gW10NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/statistics", "sourcesContent": ["<template>\r\n  <div class=\"inspection-statistics inspection-container\">\r\n    <div class=\"page-container\">\r\n      <!-- TAB切换 -->\r\n      <el-tabs\r\n        v-model=\"activeTab\"\r\n        @tab-click=\"handleTabClick\"\r\n        class=\"statistics-tabs inspection-tabs\"\r\n      >\r\n        <el-tab-pane name=\"bridge\">\r\n          <span slot=\"label\">\r\n            <svg-icon icon-class=\"bridge\" />\r\n            桥梁统计\r\n          </span>\r\n        </el-tab-pane>\r\n        <el-tab-pane name=\"tunnel\">\r\n          <span slot=\"label\">\r\n            <svg-icon icon-class=\"tunnel\" />\r\n            隧道统计\r\n          </span>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <!-- 筛选条件（采用通用筛选组件） -->\r\n      <FilterSection\r\n        v-model=\"filterForm\"\r\n        :configs=\"filterConfigs\"\r\n        :options=\"selectOptions\"\r\n        @search=\"handleSearch\"\r\n        @reset=\"handleReset\"\r\n        class=\"filter-area\"\r\n      >\r\n        <template #filters=\"{ formData, options }\">\r\n          <!-- 桥梁/隧道名称 -->\r\n          <el-select\r\n            v-model=\"formData.bridgeName\"\r\n            :placeholder=\"bridgeNameText\"\r\n            clearable\r\n            filterable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option\r\n              v-for=\"option in options.bridgeOptions\"\r\n              :key=\"option.value\"\r\n              :label=\"option.label\"\r\n              :value=\"option.value\"\r\n            />\r\n          </el-select>\r\n\r\n\r\n          <!-- 日期范围（与病害列表页一致的自定义样式） -->\r\n          <div class=\"custom-date-range-selector filter-select\">\r\n            <div\r\n              class=\"date-range-display\"\r\n              :class=\"{ 'is-focused': isDatePickerFocused }\"\r\n              @click=\"toggleDatePicker\"\r\n              @blur=\"handleDatePickerBlur\"\r\n              @keydown.enter=\"toggleDatePicker\"\r\n              @keydown.space.prevent=\"toggleDatePicker\"\r\n              tabindex=\"0\"\r\n            >\r\n              <span class=\"date-range-text\">\r\n                {{ dateRangeDisplayText }}\r\n              </span>\r\n              <span class=\"el-input__suffix\">\r\n                <i\r\n                  v-if=\"filterForm.reportTimeRange && filterForm.reportTimeRange.length === 2\"\r\n                  class=\"el-icon-circle-close el-input__icon clear-icon\"\r\n                  @click.stop=\"clearDateRange\"\r\n                ></i>\r\n                <i\r\n                  v-else\r\n                  class=\"el-icon-arrow-down el-input__icon dropdown-icon\"\r\n                ></i>\r\n              </span>\r\n            </div>\r\n\r\n            <!-- 隐藏的日期选择器 -->\r\n            <el-date-picker\r\n              ref=\"hiddenDatePicker\"\r\n              v-model=\"filterForm.reportTimeRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              style=\"position: absolute; opacity: 0; pointer-events: none; z-index: -1;\"\r\n              @change=\"handleDateRangeChange\"\r\n            />\r\n          </div>\r\n\r\n          <!-- 区域 -->\r\n          <el-select\r\n            v-model=\"formData.region\"\r\n            placeholder=\"区域\"\r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option label=\"开福区\" value=\"kaifu\" />\r\n            <el-option label=\"雨花区\" value=\"yuhua\" />\r\n            <el-option label=\"芙蓉区\" value=\"furong\" />\r\n            <el-option label=\"天心区\" value=\"tianxin\" />\r\n            <el-option label=\"岳麓区\" value=\"yuelu\" />\r\n          </el-select>\r\n\r\n          <!-- 项目部 -->\r\n          <el-select\r\n            v-model=\"formData.projectDept\"\r\n            placeholder=\"项目部\"\r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option label=\"第一项目部\" value=\"dept1\" />\r\n            <el-option label=\"第二项目部\" value=\"dept2\" />\r\n            <el-option label=\"第三项目部\" value=\"dept3\" />\r\n          </el-select>\r\n        </template>\r\n      </FilterSection>\r\n\r\n      <!-- 统计卡片 -->\r\n      <StatisticsCards\r\n        :statistics-data=\"statisticsData\"\r\n        :loading=\"loading\"\r\n        class=\"cards-area\"\r\n      />\r\n\r\n      <!-- 图表区域 -->\r\n      <el-row :gutter=\"20\" class=\"charts-row\">\r\n        <!-- 巡检趋势分析 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"chart-card\" shadow=\"never\">\r\n            <div class=\"chart-header\">\r\n              <h4>巡检趋势分析</h4>\r\n            </div>\r\n            <TrendChart\r\n              :chart-data=\"statisticsData.trendData\"\r\n              :chart-type=\"'line'\"\r\n              :loading=\"loading\"\r\n              height=\"300px\"\r\n            />\r\n          </el-card>\r\n        </el-col>\r\n\r\n        <!-- 各区巡检完成情况 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"chart-card\" shadow=\"never\">\r\n            <div class=\"chart-header\">\r\n              <h4>各区巡检完成情况</h4>\r\n            </div>\r\n            <RegionChart\r\n              :chart-data=\"statisticsData.regionData\"\r\n              :loading=\"loading\"\r\n              height=\"300px\"\r\n            />\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行：左侧最近巡检记录 + 右侧病害分析 -->\r\n      <el-row :gutter=\"20\" class=\"data-row\">\r\n        <!-- 左侧：最近巡检记录 -->\r\n        <el-col :span=\"12\">\r\n          <!-- 🔧 标题移到卡片外部 -->\r\n          <div class=\"chart-title\">\r\n            <h4>最近巡检记录</h4>\r\n          </div>\r\n\r\n          <!-- 🔧 外框只包围表格内容 -->\r\n          <div class=\"table-container\">\r\n            <div class=\"common-table\">\r\n              <el-table\r\n                v-loading=\"loading\"\r\n                :data=\"recentRecords\"\r\n                size=\"small\"\r\n                style=\"width: 100%;\"\r\n              >\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"50\" align=\"center\" />\r\n              <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"100\" show-overflow-tooltip />\r\n              <el-table-column prop=\"inspectionDate\" label=\"检测日期\" width=\"100\" align=\"center\" />\r\n              <el-table-column prop=\"inspector\" label=\"巡检人员\" width=\"80\" align=\"center\" />\r\n              <el-table-column prop=\"contactNumber\" label=\"联系方式\" width=\"120\" align=\"center\" />\r\n              <el-table-column prop=\"monthlyFine\" label=\"发现问题\" width=\"80\" align=\"center\" />\r\n              <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"viewRecordDetail(scope.row)\"\r\n                  >\r\n                    详情\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n\r\n        <!-- 右侧：病害分析区域 -->\r\n        <el-col :span=\"12\">\r\n\r\n              <div class=\"chart-title\">\r\n                <h4>病害类型分布</h4>\r\n              </div>\r\n              <div class=\"table-container-right\">\r\n                  <DamageTypeChart\r\n                    :chart-data=\"statisticsData.damageTypeData\"\r\n                    :loading=\"loading\"\r\n                    height=\"100%\"\r\n                  />\r\n              </div>\r\n\r\n            <!-- 下半部分：桥梁病害数量TOP10 -->\r\n\r\n              <div class=\"chart-title\">\r\n                <h4>病害数量top10</h4>\r\n              </div>\r\n            <div class=\"table-container-right\">\r\n                <Top10RankingChart\r\n                  :chart-data=\"statisticsData.bridgeRanking\"\r\n                  :loading=\"loading\"\r\n                  height=\"100%\"\r\n                />\r\n            </div>\r\n\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport StatisticsCards from './components/StatisticsCards'\r\nimport TrendChart from './components/TrendChart'\r\nimport RegionChart from './components/RegionChart'\r\nimport DamageTypeChart from './components/DamageTypeChart'\r\nimport Top10RankingChart from './components/Top10RankingChart'\r\nimport { FilterSection } from '@/components/Inspection'\r\n\r\nexport default {\r\n  name: 'InspectionStatistics',\r\n  components: {\r\n    StatisticsCards,\r\n    TrendChart,\r\n    RegionChart,\r\n    DamageTypeChart,\r\n    Top10RankingChart,\r\n    FilterSection\r\n  },\r\n  data() {\r\n    return {\r\n      // 当前激活的tab\r\n      activeTab: 'bridge',\r\n\r\n\r\n      // 筛选表单\r\n      filterForm: {\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: 'month',\r\n        region: '',\r\n        projectDept: ''\r\n      },\r\n\r\n\r\n      // 最近巡检记录\r\n      recentRecords: [],\r\n\r\n\r\n      // 日期选择器焦点状态（与病害列表页一致）\r\n      isDatePickerFocused: false,\r\n\r\n      // 筛选配置\r\n      filterConfigs: {}\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('inspection', [\r\n      'statisticsData',\r\n      'selectOptions',\r\n      'loadingStates'\r\n    ]),\r\n\r\n    loading() {\r\n      return this.loadingStates.inspectionRecords\r\n    },\r\n\r\n    bridgeOptions() {\r\n      return this.selectOptions.bridgeOptions || []\r\n    },\r\n    // 动态的桥梁/隧道名称文本\r\n    bridgeNameText() {\r\n      return this.activeTab === 'tunnel' ? '隧道名称' : '桥梁名称'\r\n    },\r\n\r\n\r\n    // 日期范围显示文本（与病害列表页一致）\r\n    dateRangeDisplayText() {\r\n      if (this.filterForm.reportTimeRange && this.filterForm.reportTimeRange.length === 2) {\r\n        return `${this.filterForm.reportTimeRange[0]} 至 ${this.filterForm.reportTimeRange[1]}`\r\n      }\r\n      return '日期范围'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.initPageData()\r\n  },\r\n  mounted() {\r\n    // 页面挂载后的初始化操作\r\n  },\r\n  methods: {\r\n    ...mapActions('inspection', [\r\n      'fetchStatisticsData',\r\n      'fetchRecentInspectionRecords',\r\n      'initSelectOptions',\r\n      'updateFilters'\r\n    ]),\r\n\r\n    // 搜索\r\n    async handleSearch(formData) {\r\n      this.updateFilters({\r\n        inspectionType: this.activeTab,\r\n        bridgeName: formData.bridgeName,\r\n        reportTimeRange: formData.reportTimeRange,\r\n        timeRange: formData.timeRange,\r\n        region: formData.region,\r\n        projectDept: formData.projectDept\r\n      })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n    // 重置\r\n    async handleReset() {\r\n      this.filterForm = {\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: 'month',\r\n        region: '',\r\n        projectDept: ''\r\n      }\r\n      this.updateFilters({\r\n        inspectionType: this.activeTab,\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: this.filterForm.timeRange,\r\n        region: '',\r\n        projectDept: ''\r\n      })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n    // 初始化页面数据\r\n    async initPageData() {\r\n      try {\r\n        // 初始化下拉选项\r\n        await this.initSelectOptions()\r\n\r\n        // 更新筛选条件\r\n        this.updateFilters({\r\n          inspectionType: this.activeTab,\r\n          timeRange: this.filterForm.timeRange\r\n        })\r\n\r\n        // 获取统计数据\r\n        await this.fetchStatisticsData()\r\n\r\n        // 获取最近巡检记录\r\n        await this.loadRecentRecords()\r\n\r\n      } catch (error) {\r\n        console.error('初始化页面数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      }\r\n    },\r\n\r\n    // 加载最近巡检记录\r\n    async loadRecentRecords() {\r\n      try {\r\n        await this.fetchRecentInspectionRecords({ limit: 8 })\r\n\r\n        // 从store获取数据或使用默认数据\r\n        this.recentRecords = this.getDefaultRecentRecords()\r\n\r\n      } catch (error) {\r\n        console.error('加载最近巡检记录失败:', error)\r\n        this.recentRecords = this.getDefaultRecentRecords()\r\n      }\r\n    },\r\n\r\n    // 获取默认最近巡检记录\r\n    getDefaultRecentRecords() {\r\n      const today = new Date()\r\n      const dates = []\r\n\r\n      // 生成最近8天的日期\r\n      for (let i = 0; i < 8; i++) {\r\n        const date = new Date(today)\r\n        date.setDate(today.getDate() - i)\r\n        dates.push(date.toISOString().split('T')[0])\r\n      }\r\n\r\n      return [\r\n        {\r\n          id: 1,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[0],\r\n          inspector: '吴亮吉',\r\n          contactNumber: '13580037492',\r\n          monthlyFine: 22\r\n        },\r\n        {\r\n          id: 2,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[1],\r\n          inspector: '陈秀英',\r\n          contactNumber: '15210087395',\r\n          monthlyFine: 26\r\n        },\r\n        {\r\n          id: 3,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[1],\r\n          inspector: '陈昭吉',\r\n          contactNumber: '15910018495',\r\n          monthlyFine: 6\r\n        },\r\n        {\r\n          id: 4,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[2],\r\n          inspector: '王建军',\r\n          contactNumber: '13122238579',\r\n          monthlyFine: 9\r\n        },\r\n        {\r\n          id: 5,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[3],\r\n          inspector: '吴超栋',\r\n          contactNumber: '13720089685',\r\n          monthlyFine: 33\r\n        },\r\n        {\r\n          id: 6,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[4],\r\n          inspector: '江融行',\r\n          contactNumber: '18202038579',\r\n          monthlyFine: 11\r\n        },\r\n        {\r\n          id: 7,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[5],\r\n          inspector: '刘君君',\r\n          contactNumber: '18310049683',\r\n          monthlyFine: 62\r\n        },\r\n        {\r\n          id: 8,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[6],\r\n          inspector: '袁如谦',\r\n          contactNumber: '17821229583',\r\n          monthlyFine: 18\r\n        }\r\n      ]\r\n    },\r\n\r\n    // TAB切换\r\n    async handleTabClick(tab) {\r\n      this.activeTab = tab.name\r\n      this.updateFilters({ inspectionType: this.activeTab })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n\r\n    // 查看所有记录\r\n    viewAllRecords() {\r\n      this.$router.push({ name: 'InspectionRecords' })\r\n    },\r\n\r\n    // 查看记录详情\r\n    viewRecordDetail(record) {\r\n      console.log('查看记录详情:', record)\r\n      // 这里可以打开详情弹窗或跳转到详情页面\r\n    },\r\n\r\n\r\n    // 日期范围选择器相关方法（与病害列表页一致）\r\n    // 切换日期选择器显示\r\n    toggleDatePicker() {\r\n      this.isDatePickerFocused = true\r\n      this.$nextTick(() => {\r\n        this.$refs.hiddenDatePicker.focus()\r\n      })\r\n    },\r\n\r\n    // 处理日期选择器失焦\r\n    handleDatePickerBlur() {\r\n      // 延迟执行，确保点击操作能正常完成\r\n      setTimeout(() => {\r\n        this.isDatePickerFocused = false\r\n      }, 200)\r\n    },\r\n\r\n    // 处理日期范围变化\r\n    handleDateRangeChange(value) {\r\n      this.filterForm.reportTimeRange = value\r\n      // 日期选择完成后移除焦点状态\r\n      this.isDatePickerFocused = false\r\n    },\r\n\r\n    // 清空日期范围\r\n    clearDateRange() {\r\n      this.filterForm.reportTimeRange = []\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 导入主题样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/components/table.scss';\r\n\r\n.inspection-statistics {\r\n\r\n  // 自定义日期范围选择器样式，匹配病害列表页\r\n  .custom-date-range-selector {\r\n    position: relative;\r\n    width: 100%;\r\n    min-width: 180px;\r\n    flex: 1;\r\n    display: flex; // 确保与其他filter-select对齐\r\n    align-items: center; // 垂直居中对齐\r\n\r\n    .date-range-display {\r\n      position: relative;\r\n      box-sizing: border-box;\r\n      display: inline-flex; // 改为inline-flex确保更好的对齐\r\n      align-items: center; // 垂直居中对齐\r\n      width: 100%;\r\n      height: 40px !important;\r\n      padding: 0 30px 0 15px;\r\n      // 使用与filter-select完全相同的样式\r\n      background: rgba(255, 255, 255, 0.1) !important;\r\n      border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n      border-radius: 8px !important;\r\n      color: #f8fafc !important;\r\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n      font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n      line-height: 140% !important;\r\n      cursor: pointer;\r\n      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n\r\n      &::placeholder {\r\n        color: rgba(255, 255, 255, 0.5) !important;\r\n      }\r\n\r\n      &:hover {\r\n        border-color: rgba(255, 255, 255, 0.4) !important;\r\n      }\r\n\r\n      &:focus,\r\n      &.is-focused {\r\n        outline: none;\r\n        border-color: #409EFF !important;\r\n        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;\r\n      }\r\n\r\n      .date-range-text {\r\n        display: block;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        // 移除line-height，使用父容器的flex对齐\r\n        color: #f8fafc !important; // 直接使用与病害类型相同的颜色值\r\n        font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n        font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n\r\n        &:empty::before {\r\n          content: '日期范围';\r\n          color: rgba(255, 255, 255, 0.5) !important;\r\n          font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n          font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n        }\r\n      }\r\n\r\n      .el-input__suffix {\r\n        position: absolute;\r\n        top: 0;\r\n        right: 15px;\r\n        height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        pointer-events: none;\r\n\r\n        .el-input__icon {\r\n          color: rgba(255, 255, 255, 0.7) !important;\r\n          font-size: 14px !important;\r\n          transition: color 0.3s ease !important;\r\n\r\n          &:hover {\r\n            color: rgba(255, 255, 255, 0.9) !important;\r\n          }\r\n\r\n          &.clear-icon {\r\n            pointer-events: auto;\r\n            cursor: pointer;\r\n\r\n            &:hover {\r\n              color: #f56c6c !important;\r\n            }\r\n          }\r\n\r\n          &.dropdown-icon {\r\n            pointer-events: none;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 确保隐藏的日期选择器完全不可见\r\n    .el-date-editor {\r\n      position: absolute !important;\r\n      opacity: 0 !important;\r\n      pointer-events: none !important;\r\n      z-index: -1 !important;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .page-container {\r\n    .statistics-tabs {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .filter-area {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .cards-area {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n\r\n    .charts-row {\r\n      margin-bottom: 20px;\r\n\r\n      .chart-card {\r\n        height: 420px;\r\n        overflow: hidden;\r\n        .chart-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 20px;\r\n          padding: 14px 20px 0 20px;\r\n          flex-shrink: 0;\r\n\r\n          h4 {\r\n            margin: 0;\r\n            font-size: 16px;\r\n            font-weight: 600;\r\n            color: #ffffff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .data-row {\r\n      .chart-title {\r\n        margin-bottom: 20px;\r\n        margin-left: 40px;\r\n\r\n        h4 {\r\n          margin: 0;\r\n          font-size: 16px;\r\n          color: #fff;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n\r\n      .table-container-right {\r\n        height: 340px;\r\n        margin: 20px;\r\n      }\r\n\r\n      .table-container {\r\n        height: 735px;\r\n        margin: 20px;\r\n        background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n        border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n        border-radius: 10px !important;\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        .common-table {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          padding: 14px 20px;\r\n          position: relative;\r\n          z-index: 3;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .inspection-statistics {\r\n    .charts-row,\r\n    .data-row {\r\n      .el-col {\r\n        margin-bottom: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .inspection-statistics {\r\n    .data-row {\r\n      .el-col {\r\n        margin-bottom: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}