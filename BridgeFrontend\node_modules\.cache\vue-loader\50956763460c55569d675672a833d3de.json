{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgRGlzZWFzZURldGFpbERpYWxvZyBmcm9tICcuL0Rpc2Vhc2VEZXRhaWxEaWFsb2cudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdEaXNlYXNlc1ZpZXcnLA0KICBjb21wb25lbnRzOiB7DQogICAgRGlzZWFzZURldGFpbERpYWxvZw0KICB9LA0KICBwcm9wczogew0KICAgIHByb2plY3REYXRhOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNob3dEZXRhaWxEaWFsb2c6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWREaXNlYXNlOiBudWxsLA0KICAgICAgZmlsdGVyRm9ybTogew0KICAgICAgICBicmlkZ2VOYW1lOiAnJywNCiAgICAgICAgZGlzZWFzZVR5cGU6ICcnLA0KICAgICAgICBzdGF0dXM6ICcnDQogICAgICB9LA0KICAgICAgdGFibGVEYXRhOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICBpbmRleDogMSwNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4OScsDQogICAgICAgICAgZGlzZWFzZVBhcnQ6ICfkvLjnvKnnvJ0nLA0KICAgICAgICAgIGRpc2Vhc2VUeXBlOiAn5Ly457yp57yd57y65aSxJywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn546L5pmv5rexJywNCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMiwNCiAgICAgICAgICBpbmRleDogMiwNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4OCcsDQogICAgICAgICAgZGlzZWFzZVBhcnQ6ICfkvLjnvKnnvJ0nLA0KICAgICAgICAgIGRpc2Vhc2VUeXBlOiAn5Ly457yp57yd57y65aSxJywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn5YiY5b+X5by6JywNCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMywNCiAgICAgICAgICBpbmRleDogMywNCiAgICAgICAgICBicmlkZ2VOYW1lOiAnWFhYWFhY5aSn5qGlJywNCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4NycsDQogICAgICAgICAgZGlzZWFzZVBhcnQ6ICfnhafmmI7orr7mlr0nLA0KICAgICAgICAgIGRpc2Vhc2VUeXBlOiAn54Wn5piO6K6+5pa957y65aSxJywNCiAgICAgICAgICBjb21wbGV0aW9uVGltZTogJzIwMjUtMDktMTggMTA6NDMnLA0KICAgICAgICAgIHJlc3BvbnNpYmxlOiAn6LW15Li05rSyJywNCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBicmlkZ2VPcHRpb25zOiBbDQogICAgICAgIHsgbGFiZWw6ICdYWFhYWFjlpKfmoaUnLCB2YWx1ZTogJ2JyaWRnZTEnIH0sDQogICAgICAgIHsgbGFiZWw6ICdZWVlZWVnlpKfmoaUnLCB2YWx1ZTogJ2JyaWRnZTInIH0sDQogICAgICAgIHsgbGFiZWw6ICdaWlpaWlrlpKfmoaUnLCB2YWx1ZTogJ2JyaWRnZTMnIH0NCiAgICAgIF0sDQogICAgICBkaXNlYXNlVHlwZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+S8uOe8qee8nee8uuWksScsIHZhbHVlOiAnZXhwYW5zaW9uX2pvaW50X21pc3NpbmcnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnhafmmI7orr7mlr3nvLrlpLEnLCB2YWx1ZTogJ2xpZ2h0aW5nX21pc3NpbmcnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmiqTmoI/mjZ/lnY8nLCB2YWx1ZTogJ2d1YXJkcmFpbF9kYW1hZ2UnIH0sDQogICAgICAgIHsgbGFiZWw6ICfot6/pnaLnoLTmjZ8nLCB2YWx1ZTogJ3BhdmVtZW50X2RhbWFnZScgfQ0KICAgICAgXSwNCiAgICAgIHN0YXR1c09wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+W3suWujOaIkCcsIHZhbHVlOiAnY29tcGxldGVkJyB9LA0KICAgICAgICB7IGxhYmVsOiAn6L+b6KGM5LitJywgdmFsdWU6ICdpbl9wcm9ncmVzcycgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W+heWkhOeQhicsIHZhbHVlOiAncGVuZGluZycgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB0b3RhbENvdW50KCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFibGVEYXRhLmxlbmd0aA0KICAgIH0sDQogICAgY29tcGxldGVkQ291bnQoKSB7DQogICAgICByZXR1cm4gdGhpcy50YWJsZURhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKS5sZW5ndGgNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5sb2FkVGFibGVEYXRhKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVNlYXJjaCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIC8vIOaooeaLn+aQnOe0ouW7tui/nw0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIC8vIOi/memHjOW6lOivpeiwg+eUqEFQSei/m+ihjOaQnOe0og0KICAgICAgICBjb25zb2xlLmxvZygn5pCc57Si5p2h5Lu2OicsIHRoaXMuZmlsdGVyRm9ybSkNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmn6Xor6LlrozmiJAnKQ0KICAgICAgfSwgMTAwMCkNCiAgICB9LA0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5maWx0ZXJGb3JtLmJyaWRnZU5hbWUgPSAnJw0KICAgICAgdGhpcy5maWx0ZXJGb3JtLmRpc2Vhc2VUeXBlID0gJycNCiAgICAgIHRoaXMuZmlsdGVyRm9ybS5zdGF0dXMgPSAnJw0KICAgICAgdGhpcy5sb2FkVGFibGVEYXRhKCkNCiAgICB9LA0KICAgIGhhbmRsZVZpZXdEZXRhaWwocm93KSB7DQogICAgICB0aGlzLnNlbGVjdGVkRGlzZWFzZSA9IHJvdw0KICAgICAgdGhpcy5zaG93RGV0YWlsRGlhbG9nID0gdHJ1ZQ0KICAgIH0sDQogICAgbG9hZFRhYmxlRGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIC8vIOaooeaLn0FQSeiwg+eUqA0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIC8vIOi/memHjOW6lOivpeiwg+eUqEFQSeiOt+WPluaVsOaNrg0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSwgNTAwKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["DiseasesView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DiseasesView.vue", "sourceRoot": "src/views/maintenance/repairs/components/detail", "sourcesContent": ["<template>\r\n  <div class=\"diseases-view\">\r\n    <!-- 筛选条件 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-row\">\r\n        <el-select\r\n          v-model=\"filterForm.bridgeName\"\r\n          placeholder=\"桥梁名称\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"bridge in bridgeOptions\"\r\n            :key=\"bridge.value\"\r\n            :label=\"bridge.label\"\r\n            :value=\"bridge.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.diseaseType\"\r\n          placeholder=\"病害类型\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"type in diseaseTypeOptions\"\r\n            :key=\"type.value\"\r\n            :label=\"type.label\"\r\n            :value=\"type.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.status\"\r\n          placeholder=\"状态\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"status in statusOptions\"\r\n            :key=\"status.value\"\r\n            :label=\"status.label\"\r\n            :value=\"status.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <div class=\"filter-actions\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 完成量统计 -->\r\n    <div class=\"completion-stats\">\r\n      完成量 {{ completedCount }}/{{ totalCount }}\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        class=\"diseases-table\"\r\n        header-row-class-name=\"table-header\"\r\n        row-class-name=\"table-row\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column prop=\"index\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" />\r\n        <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\r\n        <el-table-column prop=\"diseasePart\" label=\"病害部位\" min-width=\"100\" />\r\n        <el-table-column prop=\"diseaseType\" label=\"病害类型\" min-width=\"120\" />\r\n        <el-table-column prop=\"completionTime\" label=\"完成时间\" width=\"160\" align=\"center\" />\r\n        <el-table-column prop=\"responsible\" label=\"负责人\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              link\r\n              @click=\"handleViewDetail(scope.row)\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 病害详情弹窗 -->\r\n    <DiseaseDetailDialog\r\n      v-model=\"showDetailDialog\"\r\n      :disease-data=\"selectedDisease\"\r\n      @refresh=\"loadTableData\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DiseaseDetailDialog from './DiseaseDetailDialog.vue'\r\n\r\nexport default {\r\n  name: 'DiseasesView',\r\n  components: {\r\n    DiseaseDetailDialog\r\n  },\r\n  props: {\r\n    projectData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      showDetailDialog: false,\r\n      selectedDisease: null,\r\n      filterForm: {\r\n        bridgeName: '',\r\n        diseaseType: '',\r\n        status: ''\r\n      },\r\n      tableData: [\r\n        {\r\n          id: 1,\r\n          index: 1,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '989',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '王景深',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 2,\r\n          index: 2,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '988',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '刘志强',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 3,\r\n          index: 3,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '987',\r\n          diseasePart: '照明设施',\r\n          diseaseType: '照明设施缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '赵临洲',\r\n          status: 'completed'\r\n        }\r\n      ],\r\n      bridgeOptions: [\r\n        { label: 'XXXXXX大桥', value: 'bridge1' },\r\n        { label: 'YYYYYY大桥', value: 'bridge2' },\r\n        { label: 'ZZZZZZ大桥', value: 'bridge3' }\r\n      ],\r\n      diseaseTypeOptions: [\r\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\r\n        { label: '照明设施缺失', value: 'lighting_missing' },\r\n        { label: '护栏损坏', value: 'guardrail_damage' },\r\n        { label: '路面破损', value: 'pavement_damage' }\r\n      ],\r\n      statusOptions: [\r\n        { label: '已完成', value: 'completed' },\r\n        { label: '进行中', value: 'in_progress' },\r\n        { label: '待处理', value: 'pending' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    totalCount() {\r\n      return this.tableData.length\r\n    },\r\n    completedCount() {\r\n      return this.tableData.filter(item => item.status === 'completed').length\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTableData()\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.loading = true\r\n      // 模拟搜索延迟\r\n      setTimeout(() => {\r\n        // 这里应该调用API进行搜索\r\n        console.log('搜索条件:', this.filterForm)\r\n        this.loading = false\r\n        this.$message.success('查询完成')\r\n      }, 1000)\r\n    },\r\n    handleReset() {\r\n      this.filterForm.bridgeName = ''\r\n      this.filterForm.diseaseType = ''\r\n      this.filterForm.status = ''\r\n      this.loadTableData()\r\n    },\r\n    handleViewDetail(row) {\r\n      this.selectedDisease = row\r\n      this.showDetailDialog = true\r\n    },\r\n    loadTableData() {\r\n      this.loading = true\r\n      // 模拟API调用\r\n      setTimeout(() => {\r\n        // 这里应该调用API获取数据\r\n        this.loading = false\r\n      }, 500)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.diseases-view {\r\n  padding: 20px;\r\n  color: #e5e7eb;\r\n\r\n  .filter-section {\r\n    margin-bottom: 20px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n\r\n      .filter-select {\r\n        width: 180px;\r\n\r\n        :deep(.el-input__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          .el-input__inner {\r\n            color: #e5e7eb;\r\n            background: transparent;\r\n\r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n          }\r\n        }\r\n\r\n        :deep(.el-select__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          &.is-focused {\r\n            border-color: #3b82f6;\r\n          }\r\n\r\n          .el-select__placeholder {\r\n            color: #9ca3af;\r\n          }\r\n\r\n          .el-select__selected-item {\r\n            color: #e5e7eb;\r\n          }\r\n\r\n          .el-select__caret {\r\n            color: #9ca3af;\r\n          }\r\n        }\r\n      }\r\n\r\n      .filter-actions {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .el-button {\r\n          &.el-button--primary {\r\n            background: #3b82f6;\r\n            border-color: #3b82f6;\r\n            color: #ffffff;\r\n\r\n            &:hover {\r\n              background: #2563eb;\r\n              border-color: #2563eb;\r\n            }\r\n          }\r\n\r\n          &:not(.el-button--primary) {\r\n            background: #374151;\r\n            border-color: #4b5563;\r\n            color: #e5e7eb;\r\n\r\n            &:hover {\r\n              background: #4b5563;\r\n              border-color: #6b7280;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .completion-stats {\r\n    margin-bottom: 16px;\r\n    font-size: 14px;\r\n    color: #d1d5db;\r\n  }\r\n\r\n  .table-section {\r\n    .diseases-table {\r\n      @extend .common-table;\r\n    }\r\n  }\r\n}\r\n\r\n// 下拉选项样式\r\n:deep(.el-select-dropdown) {\r\n  background: #374151;\r\n  border: 1px solid #4b5563;\r\n\r\n  .el-select-dropdown__item {\r\n    color: #e5e7eb;\r\n\r\n    &:hover {\r\n      background: #4b5563;\r\n    }\r\n\r\n    &.is-selected {\r\n      background: #3b82f6;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}