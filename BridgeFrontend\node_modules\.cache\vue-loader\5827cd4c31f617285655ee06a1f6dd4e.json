{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue", "mtime": 1758804563532}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdUb3AxMFJhbmtpbmdDaGFydCcsDQogIHByb3BzOiB7DQogICAgY2hhcnREYXRhOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBsb2FkaW5nOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGhlaWdodDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJzQwMHB4Jw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBkaXNwbGF5RGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy5jaGFydERhdGEgfHwgdGhpcy5jaGFydERhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBbDQogICAgICAgICAgeyBicmlkZ2VOYW1lOiAnWFhYWOeXheWusycsIGNvdW50OiA5MzIgfSwNCiAgICAgICAgICB7IGJyaWRnZU5hbWU6ICdYWFhY55eF5a6zJywgY291bnQ6IDg5OSB9LA0KICAgICAgICAgIHsgYnJpZGdlTmFtZTogJ1hYWFjnl4XlrrMnLCBjb3VudDogNzAyIH0sDQogICAgICAgICAgeyBicmlkZ2VOYW1lOiAnWFhYWOeXheWusycsIGNvdW50OiA1NDMgfSwNCiAgICAgICAgICB7IGJyaWRnZU5hbWU6ICdYWFhY55eF5a6zJywgY291bnQ6IDIwOCB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0aGlzLmNoYXJ0RGF0YS5zbGljZSgwLCA1KQ0KICAgIH0sDQogICAgbWF4Q291bnQoKSB7DQogICAgICBpZiAodGhpcy5kaXNwbGF5RGF0YS5sZW5ndGggPT09IDApIHJldHVybiAxDQogICAgICByZXR1cm4gTWF0aC5tYXgoLi4udGhpcy5kaXNwbGF5RGF0YS5tYXAoaXRlbSA9PiBpdGVtLmNvdW50IHx8IGl0ZW0udmFsdWUgfHwgMCkpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0UHJvZ3Jlc3NXaWR0aChjb3VudCkgew0KICAgICAgaWYgKHRoaXMubWF4Q291bnQgPT09IDApIHJldHVybiAwDQogICAgICByZXR1cm4gTWF0aC5tYXgoKGNvdW50IC8gdGhpcy5tYXhDb3VudCkgKiAxMDAsIDUpIC8vIOacgOWwj+WuveW6pjUlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["Top10RankingChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Top10RankingChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"top10-ranking-chart\" v-loading=\"loading\">\r\n    <div class=\"ranking-list\">\r\n      <div\r\n        v-for=\"(item, index) in displayData\"\r\n        :key=\"index\"\r\n        class=\"ranking-item\"\r\n        :class=\"{ 'top-three': index < 3 }\"\r\n      >\r\n        <div class=\"rank-badge\" :class=\"`rank-${index + 1}`\">\r\n          TOP{{ index + 1 }}\r\n        </div>\r\n        <div class=\"bridge-info\">\r\n          <span class=\"bridge-name\">{{ item.bridgeName || item.name }}</span>\r\n          <div class=\"progress-container\">\r\n            <div \r\n              class=\"progress-bar\"\r\n              :style=\"{ width: `${getProgressWidth(item.count || item.value)}%` }\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <span class=\"count\">{{ item.count || item.value }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Top10RankingChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    loading: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    }\r\n  },\r\n  computed: {\r\n    displayData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return [\r\n          { bridgeName: 'XXXX病害', count: 932 },\r\n          { bridgeName: 'XXXX病害', count: 899 },\r\n          { bridgeName: 'XXXX病害', count: 702 },\r\n          { bridgeName: 'XXXX病害', count: 543 },\r\n          { bridgeName: 'XXXX病害', count: 208 }\r\n        ]\r\n      }\r\n      return this.chartData.slice(0, 5)\r\n    },\r\n    maxCount() {\r\n      if (this.displayData.length === 0) return 1\r\n      return Math.max(...this.displayData.map(item => item.count || item.value || 0))\r\n    }\r\n  },\r\n  methods: {\r\n    getProgressWidth(count) {\r\n      if (this.maxCount === 0) return 0\r\n      return Math.max((count / this.maxCount) * 100, 5) // 最小宽度5%\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.top10-ranking-chart {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与右侧容器设置保持一致\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n  \r\n  .ranking-list {\r\n    position: relative;\r\n    z-index: 3; // 确保内容在伪元素之上\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    width: 100%;\r\n    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间\r\n    justify-content: center;\r\n    \r\n    .ranking-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 6px 0;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover {\r\n        background-color: rgba(64, 158, 255, 0.05);\r\n        border-radius: 4px;\r\n        transform: translateX(2px);\r\n      }\r\n      \r\n      &.top-three {\r\n        .rank-badge {\r\n          background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\r\n          color: #fff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      \r\n      .rank-badge {\r\n        width: 50px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #40E0D0;\r\n        color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 10px;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        \r\n        &.rank-1 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-2 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-3 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-4 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-5 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n      }\r\n      \r\n      .bridge-info {\r\n        flex: 1;\r\n        min-width: 0;\r\n        \r\n        .bridge-name {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #ffffff;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          margin-bottom: 2px;\r\n        }\r\n        \r\n        .progress-container {\r\n          width: 100%;\r\n          height: 6px;\r\n          background-color: #f5f7fa;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n          \r\n          .progress-bar {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);\r\n            border-radius: 3px;\r\n            transition: width 0.6s ease;\r\n            animation: progressAnimation 1s ease-out;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .count {\r\n        font-size: 11px;\r\n        color: #ffffff;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        min-width: 20px;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes progressAnimation {\r\n  from {\r\n    width: 0%;\r\n  }\r\n  to {\r\n    width: var(--target-width);\r\n  }\r\n}\r\n\r\n// 响应式优化\r\n@media (max-width: 768px) {\r\n  .top10-ranking-chart {\r\n    .ranking-list {\r\n      .ranking-item {\r\n        .bridge-info {\r\n          .bridge-name {\r\n            font-size: 11px;\r\n          }\r\n        }\r\n        \r\n        .count {\r\n          font-size: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}