{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue", "mtime": 1758807462383}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BridgeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "BridgeConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"bridge-config\">\n    <div v-if=\"!readonly\" class=\"config-header\">\n      <el-button\n        type=\"primary\"\n        icon=\"el-icon-connection\"\n        @click=\"showBridgeSelector = true\"\n      >\n        <i class=\"el-icon-s-home\" v-if=\"infrastructureType === 'bridge'\"></i>\n        <i class=\"el-icon-place\" v-else></i>\n        关联{{ infrastructureType === 'bridge' ? '桥梁' : '隧道' }}\n      </el-button>\n    </div>\n    \n    <!-- 已关联桥梁/隧道列表 -->\n    <div class=\"common-table\">\n      <el-table\n        :data=\"bridgeList\"\n        class=\"maintenance-table\"\n        :empty-text=\"`暂无关联${infrastructureType === 'bridge' ? '桥梁' : '隧道'}`\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        \n        <el-table-column \n          prop=\"name\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}名称`\" \n          min-width=\"120\" \n          show-overflow-tooltip \n        />\n        \n        <el-table-column \n          prop=\"code\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}编号`\" \n          width=\"120\" \n          align=\"center\" \n        />\n        \n        <el-table-column prop=\"road\" label=\"所在道路\" width=\"120\" align=\"center\" />\n        \n        <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceContent\" label=\"养护内容\" min-width=\"150\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceStaff\" label=\"养护人员\" width=\"100\" align=\"center\" />\n        \n        <el-table-column v-if=\"!readonly\" label=\"操作\" width=\"80\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              class=\"danger-text\"\n              @click=\"removeBridgeAssociation(scope.$index)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <!-- 桥梁/隧道选择器 -->\n    <bridge-selector\n      :visible.sync=\"showBridgeSelector\"\n      :multiple=\"true\"\n      :selected-data=\"bridgeList\"\n      :infrastructure-type=\"infrastructureType\"\n      @confirm=\"handleBridgeSelection\"\n    />\n  </div>\n</template>\n\n<script>\nimport BridgeSelector from '@/components/Maintenance/BridgeSelector'\n\nexport default {\n  name: 'BridgeConfig',\n  components: {\n    BridgeSelector\n  },\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge', // bridge, tunnel\n      validator: value => ['bridge', 'tunnel'].includes(value)\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      bridgeList: [],\n      showBridgeSelector: false\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && newVal.bridges && Array.isArray(newVal.bridges)) {\n          // 只在数据真正不同时才更新，避免循环\n          if (JSON.stringify(newVal.bridges) !== JSON.stringify(this.bridgeList)) {\n            this.bridgeList = [...newVal.bridges]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    showBridgeSelector(visible) {\n      // 桥梁选择器显示状态变化\n    }\n  },\n  methods: {\n    // 统一的数据更新方法\n    emitChange() {\n      this.$nextTick(() => {\n        this.$emit('input', {\n          bridges: this.bridgeList\n        })\n      })\n    },\n    \n    // 处理桥梁选择\n    handleBridgeSelection(selectedBridges) {\n      // 为新选择的桥梁/隧道设置默认的养护内容和人员\n      const processedBridges = selectedBridges.map(bridge => ({\n        ...bridge,\n        maintenanceContent: bridge.maintenanceProject || '排水系统养护',\n        maintenanceStaff: bridge.maintenanceStaff || '待分配'\n      }))\n\n      this.bridgeList = processedBridges\n\n      if (processedBridges.length > 0) {\n        this.$message.success(`成功关联 ${processedBridges.length} 个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        this.emitChange()\n      }\n    },\n\n    // 移除桥梁/隧道关联\n    removeBridgeAssociation(index) {\n      this.bridgeList.splice(index, 1)\n      this.$message.success(`已移除${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}关联`)\n      this.emitChange()\n    },\n    \n    // 表单验证\n    validate() {\n      if (this.bridgeList.length === 0) {\n        this.$message.error(`请至少关联一个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        return false\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/styles/maintenance-theme.scss';\n\n.bridge-config {\n  .config-header {\n    margin-bottom: 24px;\n    \n    .el-button {\n      i {\n        margin-right: 4px;\n      }\n    }\n  }\n  \n  .common-table {\n    .maintenance-table {\n      min-height: 200px;\n    }\n  }\n  \n  .danger-text {\n    color: #ef4444 !important;\n    \n    &:hover {\n      color: #dc2626 !important;\n    }\n  }\n}\n</style>\n"]}]}