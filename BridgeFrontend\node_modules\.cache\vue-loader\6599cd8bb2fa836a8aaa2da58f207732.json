{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\DiseaseConfig.vue?vue&type=style&index=0&id=11e84bb9&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\DiseaseConfig.vue", "mtime": 1758807113725}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgJ0Avc3R5bGVzL2luc3BlY3Rpb24tdGhlbWUuc2Nzcyc7CkBpbXBvcnQgJ0AvYXNzZXRzL3N0eWxlcy9tYWludGVuYW5jZS10aGVtZS5zY3NzJzsKCi5kaXNlYXNlLWNvbmZpZyB7CiAgLy8g5aSN55So6YCa55So5qC35byP77yM5peg6ZyA6Ieq5a6a5LmJ5qC35byPCn0K"}, {"version": 3, "sources": ["DiseaseConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0eA;AACA;;AAEA;AACA;AACA", "file": "DiseaseConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"disease-config\">\n    <!-- 添加病害按钮 - 复用ProjectConfig的样式 -->\n    <div v-if=\"!readonly\" class=\"add-project-row\" style=\"margin-bottom: 24px;\">\n      <el-button \n        type=\"primary\" \n        icon=\"el-icon-link\"\n        class=\"add-project-btn\"\n        @click=\"showDiseaseDialog = true\"\n      >\n        关联病害\n      </el-button>\n    </div>\n    \n    <!-- 已关联病害列表 - 复用通用表格样式 -->\n    <div class=\"common-table\">\n      <el-table\n        :data=\"diseaseList\"\n        class=\"maintenance-table\"\n        empty-text=\"暂无关联病害\"\n        style=\"width: 100%\"\n        :row-style=\"{ height: '32px' }\"\n        size=\"small\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        \n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseDescription\" label=\"病害描述\" min-width=\"150\" show-overflow-tooltip />\n        \n        <el-table-column v-if=\"!readonly\" label=\"操作\" width=\"80\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              class=\"maintenance-danger-text\"\n              @click=\"removeDiseaseAssociation(scope.$index)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <!-- 病害选择弹窗 -->\n    <el-dialog\n      title=\"关联病害\"\n      :visible.sync=\"showDiseaseDialog\"\n      custom-class=\"disease-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\n      :close-on-click-modal=\"false\"\n      :before-close=\"handleDialogClose\"\n      :modal-append-to-body=\"true\"\n      :append-to-body=\"true\"\n      top=\"5vh\"\n      destroy-on-close\n    >\n      <div class=\"dialog-content\">\n        <!-- 搜索表单 - 复用通用搜索表单样式 -->\n        <div class=\"search-form\">\n          <el-form :model=\"searchParams\" inline>\n            <el-form-item label=\"桥梁名称\">\n              <el-input\n                v-model=\"searchParams.bridgeName\"\n                placeholder=\"请输入桥梁名称\"\n                clearable\n                style=\"width: 200px\"\n              />\n            </el-form-item>\n            \n            <el-form-item label=\"病害类型\">\n              <el-select\n                v-model=\"searchParams.type\"\n                placeholder=\"请选择病害类型\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"type in diseaseTypes\"\n                  :key=\"type.value\"\n                  :label=\"type.label\"\n                  :value=\"type.label\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item>\n              <el-button type=\"primary\" @click=\"searchDiseases\">查询</el-button>\n              <el-button @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        \n        <!-- 病害列表 - 复用通用表格样式 -->\n        <div class=\"common-table\">\n          <el-table\n            ref=\"diseaseSelectionTable\"\n            v-loading=\"diseaseLoading\"\n            :data=\"availableDiseases\"\n            class=\"maintenance-table\"\n            style=\"width: 100%\"\n            :row-style=\"{ height: '32px' }\"\n            size=\"small\"\n            @selection-change=\"handleDiseaseSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\" />\n            \n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n            \n            <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n            \n            <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseDescription\" label=\"病害描述\" min-width=\"150\" show-overflow-tooltip />\n          </el-table>\n        </div>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"searchParams.pageNum\"\n            :page-sizes=\"[10, 20, 50]\"\n            :page-size=\"searchParams.pageSize\"\n            :total=\"diseaseTotal\"\n            layout=\"total, sizes, prev, pager, next\"\n            @size-change=\"handleDiseaseSizeChange\"\n            @current-change=\"handleDiseaseCurrentChange\"\n          />\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleDialogClose\">取消</el-button>\n        <el-button \n          type=\"primary\" \n          @click=\"confirmDiseaseSelection\"\n          :disabled=\"selectedDiseases.length === 0\"\n        >\n          确定 {{ selectedDiseases.length > 0 ? `(已选择 ${selectedDiseases.length} 项)` : '' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getDiseaseList } from '@/api/maintenance/projects'\n\nexport default {\n  name: 'DiseaseConfig',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      diseaseList: [],\n      showDiseaseDialog: false,\n      diseaseLoading: false,\n      availableDiseases: [],\n      selectedDiseases: [],\n      diseaseTotal: 0,\n      \n      // 搜索参数\n      searchParams: {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      },\n      \n      // 病害类型选项\n      diseaseTypes: [\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\n        { label: '照明设施缺失', value: 'lighting_missing' },\n        { label: '护栏损坏', value: 'guardrail_damage' },\n        { label: '桥面破损', value: 'deck_damage' },\n        { label: '排水不畅', value: 'drainage_poor' }\n      ]\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && newVal.diseases && Array.isArray(newVal.diseases)) {\n          // 只在数据真正不同时才更新，避免循环\n          if (JSON.stringify(newVal.diseases) !== JSON.stringify(this.diseaseList)) {\n            this.diseaseList = [...newVal.diseases]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    showDiseaseDialog(visible) {\n      if (visible) {\n        this.loadAvailableDiseases()\n      }\n    }\n  },\n  methods: {\n    // 统一的数据更新方法\n    emitChange() {\n      this.$nextTick(() => {\n        this.$emit('input', {\n          diseases: this.diseaseList\n        })\n      })\n    },\n    \n    // 加载可用病害列表\n    async loadAvailableDiseases() {\n      this.diseaseLoading = true\n      try {\n        const response = await getDiseaseList(this.searchParams)\n        console.log('病害列表API响应:', response)\n        \n        // 适配不同的API响应格式\n        let diseases = []\n        let total = 0\n        \n        console.log('API响应结构:', response)\n        \n        if (response.data) {\n          // 优先使用 data.rows (模拟API格式)\n          if (response.data.rows) {\n            diseases = response.data.rows\n            total = response.data.total || 0\n          }\n          // 其次使用 data.list (标准格式)\n          else if (response.data.list) {\n            diseases = response.data.list\n            total = response.data.total || 0\n          }\n          // 最后直接使用 data (简单格式)\n          else if (Array.isArray(response.data)) {\n            diseases = response.data\n            total = response.data.length\n          }\n        }\n        \n        console.log('解析后的病害数据:', diseases, '总数:', total)\n        \n        // 数据字段映射和标准化\n        this.availableDiseases = diseases.map(disease => ({\n          id: disease.id,\n          bridgeName: disease.bridgeName || '未知桥梁',\n          diseaseCode: disease.diseaseCode || disease.code || '-',\n          diseasePart: disease.location || disease.diseasePart || '-', // 修正：使用 location 字段\n          diseaseType: disease.type || disease.diseaseType || '-', // 修正：使用 type 字段\n          diseaseCount: disease.quantity || disease.diseaseCount || 0, // 修正：使用 quantity 字段\n          diseaseDescription: disease.description || disease.diseaseDescription || '-', // 修正：使用 description 字段\n          diseaseLevel: disease.level || disease.diseaseLevel || 1, // 修正：使用 level 字段\n          reporter: disease.reportPerson || disease.reporter || '-', // 修正：使用 reportPerson 字段\n          reportTime: disease.reportTime || new Date().toISOString()\n        }))\n        \n        this.diseaseTotal = total\n        \n        // 设置已选中的病害\n        this.$nextTick(() => {\n          if (this.$refs.diseaseSelectionTable) {\n            this.availableDiseases.forEach(disease => {\n              const isSelected = this.diseaseList.some(selected => selected.id === disease.id)\n              if (isSelected) {\n                this.$refs.diseaseSelectionTable.toggleRowSelection(disease, true)\n              }\n            })\n          }\n        })\n        \n        // 如果没有数据，显示提示信息\n        if (this.availableDiseases.length === 0) {\n          this.$message.info('暂无可关联的病害数据')\n        }\n        \n      } catch (error) {\n        console.error('加载病害列表失败:', error)\n        this.$message.error('加载病害列表失败，请稍后重试')\n        \n        // 提供默认的示例数据\n        this.availableDiseases = this.getDefaultDiseaseData()\n        this.diseaseTotal = this.availableDiseases.length\n      } finally {\n        this.diseaseLoading = false\n      }\n    },\n    \n    // 搜索病害\n    searchDiseases() {\n      this.searchParams.pageNum = 1\n      this.loadAvailableDiseases()\n    },\n    \n    // 重置搜索\n    resetSearch() {\n      this.searchParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      }\n      this.loadAvailableDiseases()\n    },\n    \n    // 病害选择变化\n    handleDiseaseSelectionChange(selection) {\n      this.selectedDiseases = selection\n    },\n    \n    // 处理弹窗关闭\n    handleDialogClose(done) {\n      // 重置选择状态\n      this.selectedDiseases = []\n      // 重置搜索条件\n      this.searchParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      }\n      \n      // 如果有done回调，调用它；否则直接关闭\n      if (typeof done === 'function') {\n        done()\n      } else {\n        this.showDiseaseDialog = false\n      }\n    },\n\n    // 确认病害选择\n    confirmDiseaseSelection() {\n      if (this.selectedDiseases.length === 0) {\n        this.$message.warning('请先选择要关联的病害')\n        return\n      }\n\n      // 合并已有病害和新选择的病害\n      const existingIds = this.diseaseList.map(disease => disease.id)\n      const newDiseases = this.selectedDiseases.filter(disease => \n        !existingIds.includes(disease.id)\n      )\n      \n      // 检查重复关联\n      const duplicateCount = this.selectedDiseases.length - newDiseases.length\n      if (duplicateCount > 0) {\n        this.$message.warning(`已过滤 ${duplicateCount} 个重复的病害`)\n      }\n      \n      if (newDiseases.length > 0) {\n        this.diseaseList = [...this.diseaseList, ...newDiseases]\n        this.$message.success(`成功关联 ${newDiseases.length} 个病害`)\n        this.emitChange()\n      } else if (duplicateCount === 0) {\n        this.$message.info('未选择新的病害')\n      }\n      \n      // 关闭弹窗\n      this.handleDialogClose()\n    },\n    \n    // 移除病害关联\n    removeDiseaseAssociation(index) {\n      this.diseaseList.splice(index, 1)\n      this.$message.success('已移除病害关联')\n      this.emitChange()\n    },\n    \n    // 病害分页大小变化\n    handleDiseaseSizeChange(val) {\n      this.searchParams.pageSize = val\n      this.loadAvailableDiseases()\n    },\n    \n    // 病害当前页变化\n    handleDiseaseCurrentChange(val) {\n      this.searchParams.pageNum = val\n      this.loadAvailableDiseases()\n    },\n    \n    // 获取默认病害数据（用于API失败时的降级处理）\n    getDefaultDiseaseData() {\n      return [\n        {\n          id: 989,\n          bridgeName: '湘江大桥',\n          diseaseCode: '989',\n          diseasePart: '伸缩缝',\n          diseaseType: '伸缩缝缺失',\n          diseaseCount: 7,\n          diseaseDescription: '桥梁东侧伸缩缝存在缺失，影响行车安全',\n          diseaseLevel: 3,\n          reporter: '张三',\n          reportTime: '2025-09-01 09:30:00'\n        },\n        {\n          id: 988,\n          bridgeName: '湘江大桥',\n          diseaseCode: '988',\n          diseasePart: '伸缩缝',\n          diseaseType: '伸缩缝缺失',\n          diseaseCount: 47,\n          diseaseDescription: '桥梁西侧多处伸缩缝存在缺失现象',\n          diseaseLevel: 2,\n          reporter: '李四',\n          reportTime: '2025-09-02 14:20:00'\n        },\n        {\n          id: 987,\n          bridgeName: '浏阳河大桥',\n          diseaseCode: '987',\n          diseasePart: '照明设施',\n          diseaseType: '照明设施缺失',\n          diseaseCount: 42,\n          diseaseDescription: '桥梁照明设施老化，部分路段照明不足',\n          diseaseLevel: 1,\n          reporter: '王五',\n          reportTime: '2025-09-03 16:45:00'\n        },\n        {\n          id: 986,\n          bridgeName: '橘子洲大桥',\n          diseaseCode: '986',\n          diseasePart: '护栏',\n          diseaseType: '护栏损坏',\n          diseaseCount: 15,\n          diseaseDescription: '桥梁护栏部分段落存在损坏，需要及时修复',\n          diseaseLevel: 2,\n          reporter: '赵六',\n          reportTime: '2025-09-04 10:15:00'\n        },\n        {\n          id: 985,\n          bridgeName: '银盆岭大桥',\n          diseaseCode: '985',\n          diseasePart: '桥面',\n          diseaseType: '桥面破损',\n          diseaseCount: 23,\n          diseaseDescription: '桥面沥青出现裂缝和坑洞，影响行车舒适性',\n          diseaseLevel: 3,\n          reporter: '孙七',\n          reportTime: '2025-09-05 15:30:00'\n        },\n        {\n          id: 984,\n          bridgeName: '猴子石大桥',\n          diseaseCode: '984',\n          diseasePart: '排水系统',\n          diseaseType: '排水不畅',\n          diseaseCount: 8,\n          diseaseDescription: '桥梁排水系统堵塞，雨季积水严重',\n          diseaseLevel: 2,\n          reporter: '周八',\n          reportTime: '2025-09-06 08:45:00'\n        }\n      ]\n    },\n\n    // 表单验证\n    validate() {\n      // 病害关联是可选的，所以总是返回true\n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.disease-config {\n  // 复用通用样式，无需自定义样式\n}\n</style>\n"]}]}