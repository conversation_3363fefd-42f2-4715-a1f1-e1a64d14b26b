{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\diseases\\detail.vue?vue&type=style&index=0&id=5fe382e8&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\diseases\\detail.vue", "mtime": 1758804563526}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovLyDlr7zlhaXkuLvpopjmoLflvI/lkozpgJrnlKjmt7flhaUNCkBpbXBvcnQgJ0Avc3R5bGVzL2luc3BlY3Rpb24tdGhlbWUuc2Nzcyc7DQpAaW1wb3J0ICdAL3N0eWxlcy9taXhpbnMvaW5zcGVjdGlvbi1jb21tb24uc2Nzcyc7DQoNCi5kaXNlYXNlLWRldGFpbCB7DQogIEBpbmNsdWRlIGluc3BlY3Rpb24tcGFnZS1jb250YWluZXI7DQogIGJhY2tncm91bmQ6IHZhcigtLWluc3BlY3Rpb24tYmctcHJpbWFyeSk7DQogIG1pbi1oZWlnaHQ6IDEwMCU7IC8vIOmAguW6lOeItuWuueWZqOmrmOW6pg0KICBvdmVyZmxvdy15OiB2aXNpYmxlOw0KICANCiAgLnBhZ2UtY29udGFpbmVyIHsNCiAgICBtYXgtd2lkdGg6IDEyMDBweDsNCiAgICBtYXJnaW46IDAgYXV0bzsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KICAgIHBhZGRpbmctYm90dG9tOiA1MHB4OyAvLyDnoa7kv53lupXpg6jlhoXlrrnkuI3ooqvpga7mjKENCiAgICANCiAgICAud29ya2Zsb3ctY2FyZCwNCiAgICAuaW5mby1jYXJkLA0KICAgIC5qdWRnZS1jYXJkLA0KICAgIC5kaXNwb3NlLWNhcmQsDQogICAgLnJldmlldy1jYXJkLA0KICAgIC5hcmNoaXZlLWNhcmQgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoOSwgMjYsIDc1LCAwLjk1KSAwJSwgcmdiYSgzMCwgNTgsIDEzOCwgMC45KSAxMDAlKTsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoOTIsIDE1NywgMjU1LCAwLjMpOw0KICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsNCiAgICAgIGNvbG9yOiAjZjFmNWY5Ow0KICAgICAgYm94LXNoYWRvdzogMCAwIDAgMXB4IHJnYmEoOTIsIDE1NywgMjU1LCAwLjIpLCAwIDRweCA2cHggLTFweCByZ2JhKDAsIDAsIDAsIDAuMyk7DQogICAgICANCiAgICAgICY6aG92ZXIgew0KICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAxcHggcmdiYSg5MiwgMTU3LCAyNTUsIDAuMyksIDAgOHB4IDI1cHggcmdiYSg5MiwgMTU3LCAyNTUsIDAuMik7DQogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgICAgIH0NCiAgICB9DQogICAgDQogICAgLy8g54q25oCB5rWB56iL5Y2h54mH5qC35byPIC0g5L2/55So5rex6Imy5Li76aKYDQogICAgLndvcmtmbG93LWNhcmQgew0KICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzA5MUE0QiAwJSwgIzFlM2E4YSAxMDAlKSAhaW1wb3J0YW50Ow0KICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg3OSwgNzAsIDIyOSwgMC4zKSAhaW1wb3J0YW50Ow0KICAgICAgYm9yZGVyLXJhZGl1czogMTZweCAhaW1wb3J0YW50Ow0KICAgICAgYm94LXNoYWRvdzogMCAwIDAgMXB4IHJnYmEoNzksIDcwLCAyMjksIDAuMiksIDAgNHB4IDZweCAtMXB4IHJnYmEoMCwgMCwgMCwgMC4zKSwgMCAycHggNHB4IC0xcHggcmdiYSgwLCAwLCAwLCAwLjIpICFpbXBvcnRhbnQ7DQogICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCkgIWltcG9ydGFudDsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICBjb2xvcjogI2YxZjVmOSAhaW1wb3J0YW50Ow0KICAgICAgDQogICAgICA6ZGVlcCguZWwtY2FyZF9fYm9keSkgew0KICAgICAgICBwYWRkaW5nOiAyNHB4ICFpbXBvcnRhbnQ7DQogICAgICB9DQogICAgICANCiAgICAgICAgLndvcmtmbG93LWhlYWRlciB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxNnB4Ow0KICAgICAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogICAgICAgIA0KICAgICAgICAuaGVhZGVyLWxlZnQgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICANCiAgICAgICAgICAud29ya2Zsb3ctaWNvbiB7DQogICAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgICBjb2xvcjogIzVDOURGRjsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgICAgICAgIHBhZGRpbmc6IDhweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoOTIsIDE1NywgMjU1LCAwLjIpOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICBoMyB7DQogICAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICAgICAgY29sb3I6ICNmMWY1Zjk7DQogICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogLTAuMDI1ZW07DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICAuY3VycmVudC1oYW5kbGVyIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7DQogICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgcmdiYSg5MiwgMTU3LCAyNTUsIDAuMTUpLCByZ2JhKDExNiwgMTY3LCAyNDUsIDAuMSkpOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjZTJlOGYwOw0KICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoOTIsIDE1NywgMjU1LCAwLjMpOw0KICAgICAgICAgIA0KICAgICAgICAgIGkgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogICAgICAgICAgICBjb2xvcjogIzVDOURGRjsNCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgLmhhbmRsZS10aW1lIHsNCiAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7DQogICAgICAgICAgICBwYWRkaW5nOiAycHggOHB4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgY29sb3I6ICNjYmQ1ZTE7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC53b3JrZmxvdy1jb250YWluZXIgew0KICAgICAgICAud29ya2Zsb3ctc3RlcHMgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogICAgICAgICAgZ2FwOiAyMHB4Ow0KICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgICANCiAgICAgICAgICAvLyDmqKrlkJHov57mjqXnur8NCiAgICAgICAgICAmOjpiZWZvcmUgew0KICAgICAgICAgICAgY29udGVudDogJyc7DQogICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgICAgICB0b3A6IDI0cHg7DQogICAgICAgICAgICBsZWZ0OiA2MHB4Ow0KICAgICAgICAgICAgcmlnaHQ6IDYwcHg7DQogICAgICAgICAgICBoZWlnaHQ6IDJweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgICAgICAgICAgIHotaW5kZXg6IDE7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC53b3JrZmxvdy1zdGVwIHsNCiAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAgICAgICB6LWluZGV4OiAyOw0KICAgICAgICAgICAgDQogICAgICAgICAgICAuc3RlcC1jaXJjbGUgew0KICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgLnN0ZXAtaWNvbiB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDQ4cHg7DQogICAgICAgICAgICAgICAgaGVpZ2h0OiA0OHB4Ow0KICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTsNCiAgICAgICAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7DQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgaSB7DQogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIHNwYW4gew0KICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLnN0ZXAtY29udGVudCB7DQogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIC5zdGVwLXRpdGxlIHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7DQogICAgICAgICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjAyNWVtOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAuc3RlcC1kZXNjIHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICAgICAgY29sb3I6ICM2NDc0OEI7DQogICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA2cHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIC5zdGVwLXRpbWUgew0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTFweDsNCiAgICAgICAgICAgICAgICBjb2xvcjogIzk0QTNCODsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICAgIHBhZGRpbmc6IDJweCA2cHg7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxNDgsIDE2MywgMTg0LCAwLjEpOw0KICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5pyq5byA5aeL54q25oCBIC0g54Gw6ImyDQogICAgICAgICAgICAmLnBlbmRpbmcgew0KICAgICAgICAgICAgICAuc3RlcC1jaXJjbGUgLnN0ZXAtaWNvbiB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxNDgsIDE2MywgMTg0LCAwLjIpOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjOTRBM0I4Ow0KICAgICAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHJnYmEoMTQ4LCAxNjMsIDE4NCwgMC4zKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgLnN0ZXAtY29udGVudCB7DQogICAgICAgICAgICAgICAgLnN0ZXAtdGl0bGUgew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICM5NEEzQjg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIC5zdGVwLWRlc2Mgew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NDc0OEI7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICANCiAgICAgICAgICAgIC8vIOW9k+WJjemYtuautSAtIOiTneiJsu+8iOmhueebruS4u+mimOiJsu+8iQ0KICAgICAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgICAgICAuc3RlcC1jaXJjbGUgLnN0ZXAtaWNvbiB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzVDOURGRiAwJSwgIzc0YTdmNSAxMDAlKTsNCiAgICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICAgICAgICAgICAgYm9yZGVyOiAzcHggc29saWQgcmdiYSg5MiwgMTU3LCAyNTUsIDAuMyk7DQogICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgNHB4IHJnYmEoOTIsIDE1NywgMjU1LCAwLjE1KSwgMCA4cHggMjBweCByZ2JhKDkyLCAxNTcsIDI1NSwgMC4yNSk7DQogICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgICAgICAgICAgICAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAuc3RlcC1jb250ZW50IHsNCiAgICAgICAgICAgICAgICAuc3RlcC10aXRsZSB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogI2YxZjVmOTsNCiAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIC5zdGVwLWRlc2Mgew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICNlMmU4ZjA7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIC5zdGVwLXRpbWUgew0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSg5MiwgMTU3LCAyNTUsIDAuMik7DQogICAgICAgICAgICAgICAgICBjb2xvcjogIzVDOURGRjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgLy8g5bey5a6M5oiQ54q25oCBIC0g57u/6ImyDQogICAgICAgICAgICAmLmNvbXBsZXRlZCB7DQogICAgICAgICAgICAgIC5zdGVwLWNpcmNsZSAuc3RlcC1pY29uIHsNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzBCMDhGIDAlLCAjMTBiOTgxIDEwMCUpOw0KICAgICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCByZ2JhKDQ4LCAxNzYsIDE0MywgMC4zKTsNCiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoNDgsIDE3NiwgMTQzLCAwLjIpOw0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIGkgew0KICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAuc3RlcC1jb250ZW50IHsNCiAgICAgICAgICAgICAgICAuc3RlcC10aXRsZSB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogI2YxZjVmOTsNCiAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIC5zdGVwLWRlc2Mgew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICNlMmU4ZjA7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIC5zdGVwLXRpbWUgew0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSg0OCwgMTc2LCAxNDMsIDAuMik7DQogICAgICAgICAgICAgICAgICBjb2xvcjogIzMwQjA4RjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIC5zdGF0dXMtbGVnZW5kIHsNCiAgICAgICAgICBtYXJnaW4tdG9wOiAzMnB4Ow0KICAgICAgICAgIHBhZGRpbmctdG9wOiAyMHB4Ow0KICAgICAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICBnYXA6IDMycHg7DQogICAgICAgICAgDQogICAgICAgICAgLmxlZ2VuZC1pdGVtIHsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4Ow0KICAgICAgICAgICAgY29sb3I6ICNjYmQ1ZTE7DQogICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgDQogICAgICAgICAgICAubGVnZW5kLWRvdCB7DQogICAgICAgICAgICAgIHdpZHRoOiAxMnB4Ow0KICAgICAgICAgICAgICBoZWlnaHQ6IDEycHg7DQogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAmLmNvbXBsZXRlZCB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzMwQjA4RiAwJSwgIzEwYjk4MSAxMDAlKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM1QzlERkYgMCUsICM3NGE3ZjUgMTAwJSk7DQogICAgICAgICAgICAgICAgYW5pbWF0aW9uOiBwdWxzZS1kb3QgMnMgaW5maW5pdGU7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICYucGVuZGluZyB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxNDgsIDE2MywgMTg0LCAwLjMpOw0KICAgICAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkIHJnYmEoMTQ4LCAxNjMsIDE4NCwgMC4yKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgICANCiAgICAuY2FyZC1oZWFkZXIgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIA0KICAgICAgaDMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBtYXJnaW46IDA7DQogICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgY29sb3I6ICNmMWY1Zjk7DQogICAgICAgIA0KICAgICAgICBpIHsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgICAgICBjb2xvcjogIzVDOURGRjsNCiAgICAgICAgICBwYWRkaW5nOiA2cHg7DQogICAgICAgICAgYmFja2dyb3VuZDogcmdiYSg5MiwgMTU3LCAyNTUsIDAuMik7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICAgIA0KICAgIC5pbmZvLXNlY3Rpb24gew0KICAgICAgLmluZm8taXRlbSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgICAgICBwYWRkaW5nOiAxMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzVDOURGRjsNCiAgICAgICAgDQogICAgICAgIC5pbmZvLWxhYmVsIHsNCiAgICAgICAgICB3aWR0aDogMTIwcHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgICBmbGV4LXNocmluazogMDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgLmluZm8tdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjZTJlOGYwOw0KICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgICAgICAgICANCiAgICAgICAgICAmLmRlc2NyaXB0aW9uIHsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7DQogICAgICAgICAgICBjb2xvcjogI2UyZThmMDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIC8vIOWFvOWuueaXp+eahGxhYmVs5ZKMc3Bhbue7k+aehA0KICAgICAgICBsYWJlbCB7DQogICAgICAgICAgd2lkdGg6IDEyMHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIHNwYW4gew0KICAgICAgICAgIGNvbG9yOiAjZTJlOGYwOw0KICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgLmRlc2NyaXB0aW9uIHsNCiAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjsNCiAgICAgICAgICBjb2xvcjogI2UyZThmMDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICAuZGlzZWFzZS1pbWFnZXMgew0KICAgICAgICBtYXJnaW4tdG9wOiAyNHB4Ow0KICAgICAgICANCiAgICAgICAgaDQgew0KICAgICAgICAgIG1hcmdpbjogMCAwIDE2cHggMDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogI2YxZjVmOTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgICANCiAgICAuYXJjaGl2ZS1pbmZvIHsNCiAgICAgIC5pbmZvLWl0ZW0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICANCiAgICAgICAgbGFiZWwgew0KICAgICAgICAgIHdpZHRoOiAxMjBweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgIGNvbG9yOiAjY2JkNWUxOw0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICBzcGFuIHsNCiAgICAgICAgICBjb2xvcjogI2YxZjVmOTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgICANCiAgICAuYWN0aW9uLWJ1dHRvbnMgew0KICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgcGFkZGluZzogMjBweCAwOw0KICAgICAgDQogICAgICAuZWwtYnV0dG9uIHsNCiAgICAgICAgbWFyZ2luOiAwIDhweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLy8g5Yqo55S75a6a5LmJDQpAa2V5ZnJhbWVzIHB1bHNlIHsNCiAgMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDRweCByZ2JhKDkyLCAxNTcsIDI1NSwgMC4xNSksIDAgOHB4IDIwcHggcmdiYSg5MiwgMTU3LCAyNTUsIDAuMjUpOw0KICB9DQogIDUwJSB7DQogICAgYm94LXNoYWRvdzogMCAwIDAgOHB4IHJnYmEoOTIsIDE1NywgMjU1LCAwLjIpLCAwIDhweCAyMHB4IHJnYmEoOTIsIDE1NywgMjU1LCAwLjMpOw0KICB9DQogIDEwMCUgew0KICAgIGJveC1zaGFkb3c6IDAgMCAwIDRweCByZ2JhKDkyLCAxNTcsIDI1NSwgMC4xNSksIDAgOHB4IDIwcHggcmdiYSg5MiwgMTU3LCAyNTUsIDAuMjUpOw0KICB9DQp9DQoNCkBrZXlmcmFtZXMgcHVsc2UtZG90IHsNCiAgMCUgew0KICAgIG9wYWNpdHk6IDE7DQogICAgdHJhbnNmb3JtOiBzY2FsZSgxKTsNCiAgfQ0KICA1MCUgew0KICAgIG9wYWNpdHk6IDAuNzsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7DQogIH0NCiAgMTAwJSB7DQogICAgb3BhY2l0eTogMTsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpOw0KICB9DQp9DQoNCi8vIOa3seiJsuS4u+mimOe7n+S4gOagt+W8jyAtIOS4juajgOafpeaooeWdl+aVtOS9k+mjjuagvOS/neaMgeS4gOiHtA0KDQovLyDlk43lupTlvI/orr7orqENCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAuZGlzZWFzZS1kZXRhaWwgew0KICAgIC5wYWdlLWNvbnRhaW5lciB7DQogICAgICBwYWRkaW5nOiAxNnB4Ow0KICAgICAgDQogICAgICAud29ya2Zsb3ctY2FyZCB7DQogICAgICAgIC53b3JrZmxvdy1oZWFkZXIgew0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogICAgICAgICAgZ2FwOiAxMnB4Ow0KICAgICAgICAgIA0KICAgICAgICAgIC5oZWFkZXItbGVmdCBoMyB7DQogICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgfQ0KICAgICAgICAgIA0KICAgICAgICAgIC5jdXJyZW50LWhhbmRsZXIgew0KICAgICAgICAgICAgYWxpZ24tc2VsZjogc3RyZXRjaDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIC53b3JrZmxvdy1jb250YWluZXIgew0KICAgICAgICAgIC53b3JrZmxvdy1zdGVwcyB7DQogICAgICAgICAgICAud29ya2Zsb3ctc3RlcCB7DQogICAgICAgICAgICAgIC5zdGVwLWNpcmNsZSAuc3RlcC1pY29uIHsNCiAgICAgICAgICAgICAgICB3aWR0aDogNDBweDsNCiAgICAgICAgICAgICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgaSB7DQogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgIHNwYW4gew0KICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgLnN0ZXAtY29udGVudCB7DQogICAgICAgICAgICAgICAgLnN0ZXAtdGl0bGUgew0KICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAuc3RlcC1kZXNjIHsNCiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgDQogICAgICAgICAgICAgIC5zdGVwLWNvbm5lY3RvciB7DQogICAgICAgICAgICAgICAgbGVmdDogMTlweDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICANCiAgICAgICAgICAuc3RhdHVzLWxlZ2VuZCB7DQogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgICAgZ2FwOiAxNnB4Ow0KICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogICAgICAgICAgICANCiAgICAgICAgICAgIC5sZWdlbmQtaXRlbSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLmNhcmQtaGVhZGVyIGgzIHsNCiAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICANCiAgICAgICAgaSB7DQogICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgIHBhZGRpbmc6IDZweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICAuaW5mby1zZWN0aW9uIHsNCiAgICAgICAgLmluZm8taXRlbSB7DQogICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICAgICAgICBwYWRkaW5nOiAxMnB4Ow0KICAgICAgICAgIA0KICAgICAgICAgIC5pbmZvLWxhYmVsLA0KICAgICAgICAgIGxhYmVsIHsNCiAgICAgICAgICAgIHdpZHRoOiBhdXRvOw0KICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgICAgICAgICAgDQogICAgICAgICAgICAmOjphZnRlciB7DQogICAgICAgICAgICAgIGRpc3BsYXk6IG5vbmU7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgICAgIHBhZGRpbmc6IDI0cHggMDsNCiAgICAgICAgDQogICAgICAgIC5lbC1idXR0b24gew0KICAgICAgICAgIHdpZHRoOiAxMjBweDsNCiAgICAgICAgICBtYXJnaW46IDZweCA0cHg7DQogICAgICAgICAgcGFkZGluZzogMTBweCAxNnB4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsNCiAgLmRpc2Vhc2UtZGV0YWlsIHsNCiAgICAucGFnZS1jb250YWluZXIgew0KICAgICAgcGFkZGluZzogMTJweDsNCiAgICAgIA0KICAgICAgLndvcmtmbG93LWNvbnRhaW5lciB7DQogICAgICAgIC5zdGF0dXMtbGVnZW5kIHsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICBnYXA6IDIwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLmFjdGlvbi1idXR0b25zIHsNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgbWFyZ2luOiA2cHggMDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgo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file": "detail.vue", "sourceRoot": "src/views/inspection/diseases", "sourcesContent": ["<template>\r\n  <div class=\"disease-detail\">\r\n    <!-- 使用传统的卡片布局结构，保持现有功能 -->\r\n    <div class=\"page-container\">\r\n      <!-- 状态流程卡片 -->\r\n      <el-card class=\"workflow-card\" shadow=\"never\">\r\n        <div class=\"workflow-header\">\r\n          <div class=\"header-left\">\r\n            <i class=\"el-icon-s-grid workflow-icon\"></i>\r\n            <h3>病害处理流程</h3>\r\n          </div>\r\n          <div class=\"current-handler\" v-if=\"currentHandler\">\r\n            <i class=\"el-icon-user\"></i>\r\n            <span>当前处理人：{{ currentHandler }}</span>\r\n            <span v-if=\"currentHandleTime\" class=\"handle-time\">{{ formatTime(currentHandleTime) }}</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"workflow-container\">\r\n          <div class=\"workflow-steps\">\r\n            <div \r\n              v-for=\"(step, index) in workflowSteps\" \r\n              :key=\"step.key\"\r\n              class=\"workflow-step\"\r\n              :class=\"{ \r\n                'active': index === currentWorkflowStep,\r\n                'completed': index < currentWorkflowStep,\r\n                'pending': index > currentWorkflowStep\r\n              }\"\r\n            >\r\n              <div class=\"step-circle\">\r\n                <div class=\"step-icon\">\r\n                  <i v-if=\"index < currentWorkflowStep\" class=\"el-icon-check\"></i>\r\n                  <i v-else-if=\"index === currentWorkflowStep\" :class=\"getStepIcon(step.key)\"></i>\r\n                  <span v-else>{{ index + 1 }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"step-content\">\r\n                <div class=\"step-title\">{{ step.title }}</div>\r\n                <div class=\"step-desc\">{{ getStepDescription(step.key) }}</div>\r\n                <div class=\"step-time\" v-if=\"getStepTime(step.key)\">{{ formatTime(getStepTime(step.key)) }}</div>\r\n              </div>\r\n              <div \r\n                v-if=\"index < workflowSteps.length - 1\" \r\n                class=\"step-connector\"\r\n                :class=\"{ 'active': index < currentWorkflowStep }\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 状态说明 -->\r\n          <div class=\"status-legend\">\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot completed\"></div>\r\n              <span>已完成</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot active\"></div>\r\n              <span>当前阶段</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot pending\"></div>\r\n              <span>未开始</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 病害基本信息卡片 -->\r\n      <el-card class=\"info-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-info\"></i> 病害基本信息</h3>\r\n        </div>\r\n        \r\n        <div class=\"info-section\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">巡检人员</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.inspector || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">巡检单位</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.inspectionUnit || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">上报属性</div>\r\n                <div class=\"info-value\">\r\n                  <StatusTag \r\n                    v-if=\"diseaseDetail.reportAttribute\"\r\n                    :status=\"diseaseDetail.reportAttribute\"\r\n                    type=\"inspection\"\r\n                  />\r\n                  <span v-else>-</span>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">所属桥梁/隧道</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.bridgeName || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害类型</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseType || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害数量</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseCount || 0 }}处</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害位置</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseLocation || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害描述</div>\r\n                <div class=\"info-value description\">{{ diseaseDetail.diseaseDescription || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          \r\n          <!-- 病害照片 -->\r\n          <div v-if=\"diseaseDetail.diseaseImages && diseaseDetail.diseaseImages.length > 0\" class=\"disease-images\">\r\n            <h4>病害照片</h4>\r\n            <ImageViewer\r\n              :images=\"diseaseDetail.diseaseImages\"\r\n              :grid-type=\"4\"\r\n              :show-upload=\"false\"\r\n              :show-delete=\"false\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 判定信息卡片 -->\r\n      <el-card v-if=\"showJudgeInfo\" class=\"judge-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-edit\"></i> 判定信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseJudgeForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isJudgeEditable\"\r\n          @save=\"handleSaveJudge\"\r\n          @submit=\"handleSubmitJudge\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 处置信息卡片 -->\r\n      <el-card v-if=\"showDisposeInfo\" class=\"dispose-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 处置信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseDisposeForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isDisposeEditable\"\r\n          @save=\"handleSaveDispose\"\r\n          @submit=\"handleSubmitDispose\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 复核信息卡片 -->\r\n      <el-card v-if=\"showReviewInfo\" class=\"review-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-check\"></i> 复核信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseReviewForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isReviewEditable\"\r\n          @save=\"handleSaveReview\"\r\n          @submit=\"handleSubmitReview\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 归档信息卡片 -->\r\n      <el-card v-if=\"showArchiveInfo\" class=\"archive-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-folder\"></i> 归档信息</h3>\r\n        </div>\r\n        \r\n        <div class=\"archive-info\">\r\n          <div class=\"info-item\">\r\n            <label>归档时间</label>\r\n            <span>{{ diseaseDetail.archiveTime || '-' }}</span>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 操作按钮区 -->\r\n      <div class=\"action-buttons\">\r\n        <el-button @click=\"handleGoBack\">返回</el-button>\r\n        \r\n        <!-- 判定提交按钮（判定中状态显示） -->\r\n        <el-button \r\n          v-if=\"showJudgeSubmitButton\"\r\n          type=\"primary\" \r\n          @click=\"triggerJudgeSubmit\"\r\n        >\r\n          判定提交\r\n        </el-button>\r\n        \r\n        <!-- 复核提交按钮（复核中状态显示） -->\r\n        <el-button \r\n          v-if=\"showReviewSubmitButton\"\r\n          type=\"primary\" \r\n          @click=\"triggerReviewSubmit\"\r\n        >\r\n          复核提交\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { StatusTag, ImageViewer } from '@/components/Inspection'\r\nimport DiseaseJudgeForm from './components/DiseaseJudgeForm'\r\nimport DiseaseDisposeForm from './components/DiseaseDisposeForm'\r\nimport DiseaseReviewForm from './components/DiseaseReviewForm'\r\n\r\nexport default {\r\n  name: 'DiseaseDetail',\r\n  components: {\r\n    StatusTag,\r\n    ImageViewer,\r\n    DiseaseJudgeForm,\r\n    DiseaseDisposeForm,\r\n    DiseaseReviewForm\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      diseaseDetail: {},\r\n      \r\n      // 工作流步骤配置\r\n      workflowSteps: [\r\n        { key: 'report', title: '上报' },\r\n        { key: 'judge', title: '判定' },\r\n        { key: 'dispose', title: '处置' },\r\n        { key: 'review', title: '复核' },\r\n        { key: 'archive', title: '归档' }\r\n      ],\r\n      \r\n      // 编辑状态\r\n      isEditingJudge: false,\r\n      isEditingDispose: false,\r\n      isEditingReview: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('inspection', ['currentDiseaseDetail']),\r\n    \r\n    diseaseId() {\r\n      return this.$route.params.id\r\n    },\r\n    \r\n    currentAction() {\r\n      return this.$route.query.action || 'view'\r\n    },\r\n    \r\n    // 从路由参数获取病害数据\r\n    routeDiseaseData() {\r\n      return this.$route.params.diseaseData || null\r\n    },\r\n    \r\n    // 当前工作流步骤\r\n    currentWorkflowStep() {\r\n      const statusMap = {\r\n        'judging': 1,\r\n        'planning': 2,\r\n        'disposing': 2,\r\n        'reviewing': 3,\r\n        'archived': 4\r\n      }\r\n      return statusMap[this.diseaseDetail.diseaseStatus] || 0\r\n    },\r\n    \r\n    // 当前处理人信息\r\n    currentHandler() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      if (status === 'judging') {\r\n        return this.diseaseDetail.currentJudger || ''\r\n      } else if (status === 'disposing') {\r\n        return this.diseaseDetail.currentProcessor || ''\r\n      } else if (status === 'reviewing') {\r\n        return this.diseaseDetail.currentReviewer || ''\r\n      }\r\n      return ''\r\n    },\r\n    \r\n    // 当前处理时间\r\n    currentHandleTime() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      if (status === 'judging') {\r\n        return this.diseaseDetail.judgeStartTime || ''\r\n      } else if (status === 'disposing') {\r\n        return this.diseaseDetail.disposeStartTime || ''\r\n      } else if (status === 'reviewing') {\r\n        return this.diseaseDetail.reviewStartTime || ''\r\n      }\r\n      return ''\r\n    },\r\n    \r\n    // 是否显示判定信息\r\n    showJudgeInfo() {\r\n      return ['judging', 'planning', 'disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示处置信息\r\n    showDisposeInfo() {\r\n      return ['disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示复核信息\r\n    showReviewInfo() {\r\n      return ['reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示归档信息\r\n    showArchiveInfo() {\r\n      return this.diseaseDetail.diseaseStatus === 'archived'\r\n    },\r\n    \r\n    // 是否显示提交按钮\r\n    showSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 判定区域是否可编辑（只有判定中状态可编辑）\r\n    isJudgeEditable() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 处置区域是否可编辑（只有处置中状态可编辑）\r\n    isDisposeEditable() {\r\n      return ['planning', 'disposing'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 复核区域是否可编辑（只有复核中状态可编辑）\r\n    isReviewEditable() {\r\n      return this.diseaseDetail.diseaseStatus === 'reviewing'\r\n    },\r\n    \r\n    // 是否显示判定提交按钮\r\n    showJudgeSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 是否显示复核提交按钮\r\n    showReviewSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'reviewing'\r\n    },\r\n    \r\n  },\r\n  async created() {\r\n    await this.initPageData()\r\n  },\r\n  methods: {\r\n    ...mapActions('inspection', ['fetchDiseaseDetail']),\r\n    \r\n    // 初始化页面数据\r\n    async initPageData() {\r\n      this.loading = true\r\n      \r\n      try {\r\n        // 优先使用路由传递的数据，如果没有则从API获取\r\n        if (this.routeDiseaseData) {\r\n          console.log('使用路由传递的病害数据:', this.routeDiseaseData)\r\n          this.diseaseDetail = { ...this.routeDiseaseData }\r\n        } else {\r\n          await this.fetchDiseaseDetail(this.diseaseId)\r\n          this.diseaseDetail = this.currentDiseaseDetail || this.getDefaultDiseaseDetail()\r\n        }\r\n        \r\n        // 根据action设置编辑状态\r\n        this.setEditingState()\r\n        \r\n        // 初始化编辑状态 - 确保在对应阶段默认可编辑\r\n        this.initEditingState()\r\n        \r\n      } catch (error) {\r\n        console.error('加载病害详情失败:', error)\r\n        this.$message.error('加载病害详情失败')\r\n        \r\n        // 使用默认数据\r\n        this.diseaseDetail = this.getDefaultDiseaseDetail()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 设置编辑状态\r\n    setEditingState() {\r\n      this.isEditingJudge = this.currentAction === 'judge'\r\n      this.isEditingDispose = this.currentAction === 'dispose'\r\n      this.isEditingReview = this.currentAction === 'review'\r\n    },\r\n    \r\n    // 初始化编辑状态\r\n    initEditingState() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      \r\n      // 如果没有指定action，则根据状态默认设置编辑状态\r\n      if (!this.currentAction || this.currentAction === 'view') {\r\n        if (status === 'judging') {\r\n          this.isEditingJudge = true\r\n        } else if (status === 'planning' || status === 'disposing') {\r\n          this.isEditingDispose = true\r\n        } else if (status === 'reviewing') {\r\n          this.isEditingReview = true\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取默认病害详情数据\r\n    getDefaultDiseaseDetail() {\r\n      return {\r\n        id: this.diseaseId,\r\n        bridgeName: '橘子洲大桥',\r\n        inspector: '张三',\r\n        inspectionUnit: '长沙市政养护单位',\r\n        reportAttribute: 'daily',\r\n        diseaseType: '裂缝',\r\n        diseaseCount: 3,\r\n        diseaseLocation: '承载桁+200m，距止测所约53米，共3处桥面的裂缝',\r\n        diseaseDescription: '桥面板出现3处的裂缝，最大宽度30.3mm，长度1.2m，可能影响结构耐久性，建议及时处理。',\r\n        diseaseStatus: 'judging', // 默认为判定中状态，这样可以测试编辑功能\r\n        reportTime: '2024-01-08 09:30:00',\r\n        // 添加判定信息示例数据\r\n        judgeType: 'daily_maintenance',\r\n        judgeComment: '经判定，该病害属于日常养护范围，建议在一周内完成修复工作。裂缝位置不影响结构安全，但需及时处理防止进一步扩大。',\r\n        requiredFinishTime: '2024-01-15 18:00:00',\r\n        judger: '李工程师',\r\n        judgeTime: '2024-01-08 14:30:00',\r\n        diseaseImages: [\r\n          { url: require('@/assets/images/test/disease1.png'), alt: '病害图片1' },\r\n          { url: require('@/assets/images/test/disease2.png'), alt: '病害图片2' },\r\n          { url: require('@/assets/images/test/disease3.png'), alt: '病害图片3' },\r\n          { url: require('@/assets/images/test/disease4.png'), alt: '病害图片4' }\r\n        ]\r\n      }\r\n    },\r\n    \r\n    // 保存判定信息\r\n    async handleSaveJudge(judgeData) {\r\n      try {\r\n        // 这里调用保存判定API\r\n        // await saveDiseaseJudge(this.diseaseId, judgeData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存判定信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    // 提交判定信息\r\n    async handleSubmitJudge(judgeData) {\r\n      try {\r\n        // 这里调用提交判定API\r\n        // await submitDiseaseJudge(this.diseaseId, judgeData)\r\n        \r\n        this.$message.success('判定信息提交成功')\r\n        // 提交成功后刷新页面数据或跳转\r\n        await this.initPageData()\r\n      } catch (error) {\r\n        console.error('提交判定信息失败:', error)\r\n        this.$message.error('提交失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 保存处置信息\r\n    async handleSaveDispose(disposeData) {\r\n      try {\r\n        // 这里调用保存处置API\r\n        // await saveDiseaseDispose(this.diseaseId, disposeData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存处置信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 保存复核信息\r\n    async handleSaveReview(reviewData) {\r\n      try {\r\n        // 这里调用保存复核API\r\n        // await saveDiseaseReview(this.diseaseId, reviewData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存复核信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    // 提交复核信息\r\n    async handleSubmitReview(reviewData) {\r\n      try {\r\n        // 这里调用提交复核API\r\n        // await submitDiseaseReview(this.diseaseId, reviewData)\r\n        \r\n        this.$message.success('复核信息提交成功')\r\n        // 提交成功后刷新页面数据或跳转\r\n        await this.initPageData()\r\n      } catch (error) {\r\n        console.error('提交复核信息失败:', error)\r\n        this.$message.error('提交失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 取消编辑\r\n    handleCancelEdit() {\r\n      this.isEditingJudge = false\r\n      this.isEditingDispose = false\r\n      this.isEditingReview = false\r\n      \r\n      // 移除action参数\r\n      this.$router.replace({\r\n        name: 'DiseaseDetail',\r\n        params: { id: this.diseaseId }\r\n      })\r\n    },\r\n    \r\n    \r\n    // 返回列表\r\n    handleGoBack() {\r\n      this.$router.go(-1)\r\n    },\r\n    \r\n    // 触发判定提交（通过$refs调用子组件方法）\r\n    triggerJudgeSubmit() {\r\n      // 这里可以调用判定表单组件的提交方法，或者触发提交事件\r\n      // 如果判定表单组件有ref，可以直接调用: this.$refs.judgeForm.submit()\r\n      // 目前模拟表单数据并调用提交方法\r\n      const mockJudgeData = {\r\n        judgeType: 'daily_maintenance',\r\n        judgeComment: '测试判定信息',\r\n        requiredFinishTime: '2024-01-15 18:00:00'\r\n      }\r\n      this.handleSubmitJudge(mockJudgeData)\r\n    },\r\n    \r\n    // 触发复核提交（通过$refs调用子组件方法）\r\n    triggerReviewSubmit() {\r\n      // 这里可以调用复核表单组件的提交方法，或者触发提交事件\r\n      // 如果复核表单组件有ref，可以直接调用: this.$refs.reviewForm.submit()\r\n      // 目前模拟表单数据并调用提交方法\r\n      const mockReviewData = {\r\n        reviewResult: 'approved',\r\n        reviewComment: '测试复核信息'\r\n      }\r\n      this.handleSubmitReview(mockReviewData)\r\n    },\r\n    \r\n    // 获取步骤图标\r\n    getStepIcon(stepKey) {\r\n      const iconMap = {\r\n        'report': 'el-icon-edit',\r\n        'judge': 'el-icon-s-claim',\r\n        'dispose': 'el-icon-s-tools',\r\n        'review': 'el-icon-view',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[stepKey] || 'el-icon-circle-check'\r\n    },\r\n    \r\n    // 获取步骤描述\r\n    getStepDescription(stepKey) {\r\n      const descMap = {\r\n        'report': '病害信息已上报',\r\n        'judge': '专家进行病害判定',\r\n        'dispose': '制定处置方案并执行',\r\n        'review': '验收处置效果',\r\n        'archive': '病害处理完成归档'\r\n      }\r\n      return descMap[stepKey] || ''\r\n    },\r\n    \r\n    // 获取步骤完成时间\r\n    getStepTime(stepKey) {\r\n      const timeMap = {\r\n        'report': this.diseaseDetail.reportTime,\r\n        'judge': this.diseaseDetail.judgeTime,\r\n        'dispose': this.diseaseDetail.disposeTime,\r\n        'review': this.diseaseDetail.reviewTime,\r\n        'archive': this.diseaseDetail.archiveTime\r\n      }\r\n      return timeMap[stepKey] || ''\r\n    },\r\n    \r\n    // 格式化时间显示\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      \r\n      // 如果是日期字符串，转换为更友好的格式\r\n      const date = new Date(time)\r\n      if (isNaN(date.getTime())) return time\r\n      \r\n      const now = new Date()\r\n      const diff = now.getTime() - date.getTime()\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      \r\n      if (days === 0) {\r\n        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })\r\n      } else if (days === 1) {\r\n        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })\r\n      } else if (days < 7) {\r\n        return `${days}天前`\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 导入主题样式和通用混入\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/mixins/inspection-common.scss';\r\n\r\n.disease-detail {\r\n  @include inspection-page-container;\r\n  background: var(--inspection-bg-primary);\r\n  min-height: 100%; // 适应父容器高度\r\n  overflow-y: visible;\r\n  \r\n  .page-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 24px;\r\n    padding-bottom: 50px; // 确保底部内容不被遮挡\r\n    \r\n    .workflow-card,\r\n    .info-card,\r\n    .judge-card,\r\n    .dispose-card,\r\n    .review-card,\r\n    .archive-card {\r\n      margin-bottom: 20px;\r\n      background: linear-gradient(135deg, rgba(9, 26, 75, 0.95) 0%, rgba(30, 58, 138, 0.9) 100%);\r\n      border: 1px solid rgba(92, 157, 255, 0.3);\r\n      border-radius: 16px;\r\n      color: #f1f5f9;\r\n      box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3);\r\n      \r\n      &:hover {\r\n        box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.3), 0 8px 25px rgba(92, 157, 255, 0.2);\r\n        transform: translateY(-2px);\r\n      }\r\n    }\r\n    \r\n    // 状态流程卡片样式 - 使用深色主题\r\n    .workflow-card {\r\n      background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;\r\n      border: 1px solid rgba(79, 70, 229, 0.3) !important;\r\n      border-radius: 16px !important;\r\n      box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;\r\n      backdrop-filter: blur(10px) !important;\r\n      overflow: hidden;\r\n      color: #f1f5f9 !important;\r\n      \r\n      :deep(.el-card__body) {\r\n        padding: 24px !important;\r\n      }\r\n      \r\n        .workflow-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 32px;\r\n          padding-bottom: 16px;\r\n          border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n        \r\n        .header-left {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .workflow-icon {\r\n            font-size: 20px;\r\n            color: #5C9DFF;\r\n            margin-right: 12px;\r\n            padding: 8px;\r\n            background: rgba(92, 157, 255, 0.2);\r\n            border-radius: 8px;\r\n          }\r\n          \r\n          h3 {\r\n            margin: 0;\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: #f1f5f9;\r\n            letter-spacing: -0.025em;\r\n          }\r\n        }\r\n        \r\n        .current-handler {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 8px 16px;\r\n          background: linear-gradient(135deg, rgba(92, 157, 255, 0.15), rgba(116, 167, 245, 0.1));\r\n          border-radius: 12px;\r\n          font-size: 14px;\r\n          color: #e2e8f0;\r\n          border: 1px solid rgba(92, 157, 255, 0.3);\r\n          \r\n          i {\r\n            margin-right: 6px;\r\n            color: #5C9DFF;\r\n          }\r\n          \r\n          .handle-time {\r\n            margin-left: 8px;\r\n            padding: 2px 8px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border-radius: 6px;\r\n            font-size: 12px;\r\n            color: #cbd5e1;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .workflow-container {\r\n        .workflow-steps {\r\n          display: flex;\r\n          flex-direction: row;\r\n          justify-content: space-between;\r\n          align-items: flex-start;\r\n          gap: 20px;\r\n          position: relative;\r\n          \r\n          // 横向连接线\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 24px;\r\n            left: 60px;\r\n            right: 60px;\r\n            height: 2px;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            z-index: 1;\r\n          }\r\n          \r\n          .workflow-step {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            text-align: center;\r\n            position: relative;\r\n            z-index: 2;\r\n            \r\n            .step-circle {\r\n              margin-bottom: 12px;\r\n              \r\n              .step-icon {\r\n                width: 48px;\r\n                height: 48px;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-weight: 600;\r\n                font-size: 16px;\r\n                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n                position: relative;\r\n                background: white;\r\n                \r\n                i {\r\n                  font-size: 18px;\r\n                }\r\n                \r\n                span {\r\n                  font-size: 16px;\r\n                  font-weight: 700;\r\n                }\r\n              }\r\n            }\r\n            \r\n            .step-content {\r\n              text-align: center;\r\n              \r\n              .step-title {\r\n                font-size: 14px;\r\n                font-weight: 600;\r\n                margin-bottom: 4px;\r\n                letter-spacing: -0.025em;\r\n              }\r\n              \r\n              .step-desc {\r\n                font-size: 12px;\r\n                color: #64748B;\r\n                line-height: 1.4;\r\n                margin-bottom: 6px;\r\n              }\r\n              \r\n              .step-time {\r\n                font-size: 11px;\r\n                color: #94A3B8;\r\n                font-weight: 500;\r\n                padding: 2px 6px;\r\n                background: rgba(148, 163, 184, 0.1);\r\n                border-radius: 4px;\r\n                display: inline-block;\r\n              }\r\n            }\r\n            \r\n            // 未开始状态 - 灰色\r\n            &.pending {\r\n              .step-circle .step-icon {\r\n                background: rgba(148, 163, 184, 0.2);\r\n                color: #94A3B8;\r\n                border: 2px solid rgba(148, 163, 184, 0.3);\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #94A3B8;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #64748B;\r\n                }\r\n              }\r\n            }\r\n            \r\n            // 当前阶段 - 蓝色（项目主题色）\r\n            &.active {\r\n              .step-circle .step-icon {\r\n                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);\r\n                color: white;\r\n                border: 3px solid rgba(92, 157, 255, 0.3);\r\n                box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n                transform: scale(1.05);\r\n                animation: pulse 2s infinite;\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #f1f5f9;\r\n                  font-weight: 700;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #e2e8f0;\r\n                }\r\n                \r\n                .step-time {\r\n                  background: rgba(92, 157, 255, 0.2);\r\n                  color: #5C9DFF;\r\n                }\r\n              }\r\n            }\r\n            \r\n            // 已完成状态 - 绿色\r\n            &.completed {\r\n              .step-circle .step-icon {\r\n                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);\r\n                color: white;\r\n                border: 2px solid rgba(48, 176, 143, 0.3);\r\n                box-shadow: 0 4px 12px rgba(48, 176, 143, 0.2);\r\n                \r\n                i {\r\n                  font-size: 20px;\r\n                  font-weight: bold;\r\n                }\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #f1f5f9;\r\n                  font-weight: 600;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #e2e8f0;\r\n                }\r\n                \r\n                .step-time {\r\n                  background: rgba(48, 176, 143, 0.2);\r\n                  color: #30B08F;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        .status-legend {\r\n          margin-top: 32px;\r\n          padding-top: 20px;\r\n          border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n          display: flex;\r\n          justify-content: center;\r\n          gap: 32px;\r\n          \r\n          .legend-item {\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 13px;\r\n            color: #cbd5e1;\r\n            font-weight: 500;\r\n            \r\n            .legend-dot {\r\n              width: 12px;\r\n              height: 12px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              \r\n              &.completed {\r\n                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);\r\n              }\r\n              \r\n              &.active {\r\n                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);\r\n                animation: pulse-dot 2s infinite;\r\n              }\r\n              \r\n              &.pending {\r\n                background: rgba(148, 163, 184, 0.3);\r\n                border: 2px solid rgba(148, 163, 184, 0.2);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .card-header {\r\n      margin-bottom: 20px;\r\n      \r\n      h3 {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #f1f5f9;\r\n        \r\n        i {\r\n          margin-right: 8px;\r\n          color: #5C9DFF;\r\n          padding: 6px;\r\n          background: rgba(92, 157, 255, 0.2);\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .info-section {\r\n      .info-item {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        margin-bottom: 16px;\r\n        padding: 12px;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 8px;\r\n        border-left: 3px solid #5C9DFF;\r\n        \r\n        .info-label {\r\n          width: 120px;\r\n          font-weight: 600;\r\n          color: #ffffff;\r\n          flex-shrink: 0;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        .info-value {\r\n          color: #e2e8f0;\r\n          word-break: break-all;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n          \r\n          &.description {\r\n            line-height: 1.6;\r\n            color: #e2e8f0;\r\n          }\r\n        }\r\n        \r\n        // 兼容旧的label和span结构\r\n        label {\r\n          width: 120px;\r\n          font-weight: 600;\r\n          color: #ffffff;\r\n          flex-shrink: 0;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        span {\r\n          color: #e2e8f0;\r\n          word-break: break-all;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        .description {\r\n          margin: 0;\r\n          line-height: 1.6;\r\n          color: #e2e8f0;\r\n        }\r\n      }\r\n      \r\n      .disease-images {\r\n        margin-top: 24px;\r\n        \r\n        h4 {\r\n          margin: 0 0 16px 0;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #f1f5f9;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .archive-info {\r\n      .info-item {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        label {\r\n          width: 120px;\r\n          font-weight: 500;\r\n          color: #cbd5e1;\r\n        }\r\n        \r\n        span {\r\n          color: #f1f5f9;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .action-buttons {\r\n      text-align: center;\r\n      padding: 20px 0;\r\n      \r\n      .el-button {\r\n        margin: 0 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画定义\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n  }\r\n  50% {\r\n    box-shadow: 0 0 0 8px rgba(92, 157, 255, 0.2), 0 8px 20px rgba(92, 157, 255, 0.3);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n  }\r\n}\r\n\r\n@keyframes pulse-dot {\r\n  0% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 深色主题统一样式 - 与检查模块整体风格保持一致\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .disease-detail {\r\n    .page-container {\r\n      padding: 16px;\r\n      \r\n      .workflow-card {\r\n        .workflow-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          gap: 12px;\r\n          \r\n          .header-left h3 {\r\n            font-size: 18px;\r\n          }\r\n          \r\n          .current-handler {\r\n            align-self: stretch;\r\n          }\r\n        }\r\n        \r\n        .workflow-container {\r\n          .workflow-steps {\r\n            .workflow-step {\r\n              .step-circle .step-icon {\r\n                width: 40px;\r\n                height: 40px;\r\n                \r\n                i {\r\n                  font-size: 16px;\r\n                }\r\n                \r\n                span {\r\n                  font-size: 14px;\r\n                }\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  font-size: 14px;\r\n                }\r\n                \r\n                .step-desc {\r\n                  font-size: 13px;\r\n                }\r\n              }\r\n              \r\n              .step-connector {\r\n                left: 19px;\r\n              }\r\n            }\r\n          }\r\n          \r\n          .status-legend {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            align-items: flex-start;\r\n            \r\n            .legend-item {\r\n              font-size: 12px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .card-header h3 {\r\n        font-size: 16px;\r\n        \r\n        i {\r\n          font-size: 18px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n      \r\n      .info-section {\r\n        .info-item {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          padding: 12px;\r\n          \r\n          .info-label,\r\n          label {\r\n            width: auto;\r\n            margin-bottom: 8px;\r\n            \r\n            &::after {\r\n              display: none;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        padding: 24px 0;\r\n        \r\n        .el-button {\r\n          width: 120px;\r\n          margin: 6px 4px;\r\n          padding: 10px 16px;\r\n          font-size: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .disease-detail {\r\n    .page-container {\r\n      padding: 12px;\r\n      \r\n      .workflow-container {\r\n        .status-legend {\r\n          flex-direction: row;\r\n          flex-wrap: wrap;\r\n          justify-content: center;\r\n          gap: 20px;\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        .el-button {\r\n          width: 100%;\r\n          margin: 6px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}</style>\r\n"]}]}