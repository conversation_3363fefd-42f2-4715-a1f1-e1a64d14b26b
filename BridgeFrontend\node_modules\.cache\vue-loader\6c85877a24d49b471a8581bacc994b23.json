{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue?vue&type=style&index=0&id=6313b3c0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\DiseasesView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DiseasesView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4NA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DiseasesView.vue", "sourceRoot": "src/views/maintenance/repairs/components/detail", "sourcesContent": ["<template>\r\n  <div class=\"diseases-view\">\r\n    <!-- 筛选条件 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-row\">\r\n        <el-select\r\n          v-model=\"filterForm.bridgeName\"\r\n          placeholder=\"桥梁名称\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"bridge in bridgeOptions\"\r\n            :key=\"bridge.value\"\r\n            :label=\"bridge.label\"\r\n            :value=\"bridge.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.diseaseType\"\r\n          placeholder=\"病害类型\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"type in diseaseTypeOptions\"\r\n            :key=\"type.value\"\r\n            :label=\"type.label\"\r\n            :value=\"type.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <el-select\r\n          v-model=\"filterForm.status\"\r\n          placeholder=\"状态\"\r\n          class=\"filter-select\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"status in statusOptions\"\r\n            :key=\"status.value\"\r\n            :label=\"status.label\"\r\n            :value=\"status.value\"\r\n          />\r\n        </el-select>\r\n\r\n        <div class=\"filter-actions\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 完成量统计 -->\r\n    <div class=\"completion-stats\">\r\n      完成量 {{ completedCount }}/{{ totalCount }}\r\n    </div>\r\n\r\n    <!-- 数据表格 -->\r\n    <div class=\"table-section\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        class=\"diseases-table\"\r\n        header-row-class-name=\"table-header\"\r\n        row-class-name=\"table-row\"\r\n        v-loading=\"loading\"\r\n      >\r\n        <el-table-column prop=\"index\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" />\r\n        <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\r\n        <el-table-column prop=\"diseasePart\" label=\"病害部位\" min-width=\"100\" />\r\n        <el-table-column prop=\"diseaseType\" label=\"病害类型\" min-width=\"120\" />\r\n        <el-table-column prop=\"completionTime\" label=\"完成时间\" width=\"160\" align=\"center\" />\r\n        <el-table-column prop=\"responsible\" label=\"负责人\" width=\"100\" align=\"center\" />\r\n        <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              link\r\n              @click=\"handleViewDetail(scope.row)\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <!-- 病害详情弹窗 -->\r\n    <DiseaseDetailDialog\r\n      v-model=\"showDetailDialog\"\r\n      :disease-data=\"selectedDisease\"\r\n      @refresh=\"loadTableData\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DiseaseDetailDialog from './DiseaseDetailDialog.vue'\r\n\r\nexport default {\r\n  name: 'DiseasesView',\r\n  components: {\r\n    DiseaseDetailDialog\r\n  },\r\n  props: {\r\n    projectData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      showDetailDialog: false,\r\n      selectedDisease: null,\r\n      filterForm: {\r\n        bridgeName: '',\r\n        diseaseType: '',\r\n        status: ''\r\n      },\r\n      tableData: [\r\n        {\r\n          id: 1,\r\n          index: 1,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '989',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '王景深',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 2,\r\n          index: 2,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '988',\r\n          diseasePart: '伸缩缝',\r\n          diseaseType: '伸缩缝缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '刘志强',\r\n          status: 'completed'\r\n        },\r\n        {\r\n          id: 3,\r\n          index: 3,\r\n          bridgeName: 'XXXXXX大桥',\r\n          diseaseCode: '987',\r\n          diseasePart: '照明设施',\r\n          diseaseType: '照明设施缺失',\r\n          completionTime: '2025-09-18 10:43',\r\n          responsible: '赵临洲',\r\n          status: 'completed'\r\n        }\r\n      ],\r\n      bridgeOptions: [\r\n        { label: 'XXXXXX大桥', value: 'bridge1' },\r\n        { label: 'YYYYYY大桥', value: 'bridge2' },\r\n        { label: 'ZZZZZZ大桥', value: 'bridge3' }\r\n      ],\r\n      diseaseTypeOptions: [\r\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\r\n        { label: '照明设施缺失', value: 'lighting_missing' },\r\n        { label: '护栏损坏', value: 'guardrail_damage' },\r\n        { label: '路面破损', value: 'pavement_damage' }\r\n      ],\r\n      statusOptions: [\r\n        { label: '已完成', value: 'completed' },\r\n        { label: '进行中', value: 'in_progress' },\r\n        { label: '待处理', value: 'pending' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    totalCount() {\r\n      return this.tableData.length\r\n    },\r\n    completedCount() {\r\n      return this.tableData.filter(item => item.status === 'completed').length\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTableData()\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.loading = true\r\n      // 模拟搜索延迟\r\n      setTimeout(() => {\r\n        // 这里应该调用API进行搜索\r\n        console.log('搜索条件:', this.filterForm)\r\n        this.loading = false\r\n        this.$message.success('查询完成')\r\n      }, 1000)\r\n    },\r\n    handleReset() {\r\n      this.filterForm.bridgeName = ''\r\n      this.filterForm.diseaseType = ''\r\n      this.filterForm.status = ''\r\n      this.loadTableData()\r\n    },\r\n    handleViewDetail(row) {\r\n      this.selectedDisease = row\r\n      this.showDetailDialog = true\r\n    },\r\n    loadTableData() {\r\n      this.loading = true\r\n      // 模拟API调用\r\n      setTimeout(() => {\r\n        // 这里应该调用API获取数据\r\n        this.loading = false\r\n      }, 500)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.diseases-view {\r\n  padding: 20px;\r\n  color: #e5e7eb;\r\n\r\n  .filter-section {\r\n    margin-bottom: 20px;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n\r\n      .filter-select {\r\n        width: 180px;\r\n\r\n        :deep(.el-input__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          .el-input__inner {\r\n            color: #e5e7eb;\r\n            background: transparent;\r\n\r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n          }\r\n        }\r\n\r\n        :deep(.el-select__wrapper) {\r\n          background: #374151;\r\n          border: 1px solid #4b5563;\r\n          box-shadow: none;\r\n\r\n          &.is-focused {\r\n            border-color: #3b82f6;\r\n          }\r\n\r\n          .el-select__placeholder {\r\n            color: #9ca3af;\r\n          }\r\n\r\n          .el-select__selected-item {\r\n            color: #e5e7eb;\r\n          }\r\n\r\n          .el-select__caret {\r\n            color: #9ca3af;\r\n          }\r\n        }\r\n      }\r\n\r\n      .filter-actions {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .el-button {\r\n          &.el-button--primary {\r\n            background: #3b82f6;\r\n            border-color: #3b82f6;\r\n            color: #ffffff;\r\n\r\n            &:hover {\r\n              background: #2563eb;\r\n              border-color: #2563eb;\r\n            }\r\n          }\r\n\r\n          &:not(.el-button--primary) {\r\n            background: #374151;\r\n            border-color: #4b5563;\r\n            color: #e5e7eb;\r\n\r\n            &:hover {\r\n              background: #4b5563;\r\n              border-color: #6b7280;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .completion-stats {\r\n    margin-bottom: 16px;\r\n    font-size: 14px;\r\n    color: #d1d5db;\r\n  }\r\n\r\n  .table-section {\r\n    .diseases-table {\r\n      @extend .common-table;\r\n    }\r\n  }\r\n}\r\n\r\n// 下拉选项样式\r\n:deep(.el-select-dropdown) {\r\n  background: #374151;\r\n  border: 1px solid #4b5563;\r\n\r\n  .el-select-dropdown__item {\r\n    color: #e5e7eb;\r\n\r\n    &:hover {\r\n      background: #4b5563;\r\n    }\r\n\r\n    &.is-selected {\r\n      background: #3b82f6;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}