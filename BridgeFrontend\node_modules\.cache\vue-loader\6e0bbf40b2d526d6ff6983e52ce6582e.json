{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\index.vue?vue&type=style&index=0&id=7d79ef77&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\index.vue", "mtime": 1758804563537}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIOWvvOWFpeS4u+mimOagt+W8jw0KQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsNCkBpbXBvcnQgJ0Avc3R5bGVzL2NvbXBvbmVudHMvdGFibGUuc2Nzcyc7DQoNCi5pbnNwZWN0aW9uLXN0YXRpc3RpY3Mgew0KDQogIC8vIOiHquWumuS5ieaXpeacn+iMg+WbtOmAieaLqeWZqOagt+W8j++8jOWMuemFjeeXheWus+WIl+ihqOmhtQ0KICAuY3VzdG9tLWRhdGUtcmFuZ2Utc2VsZWN0b3Igew0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtaW4td2lkdGg6IDE4MHB4Ow0KICAgIGZsZXg6IDE7DQogICAgZGlzcGxheTogZmxleDsgLy8g56Gu5L+d5LiO5YW25LuWZmlsdGVyLXNlbGVjdOWvuem9kA0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7IC8vIOWeguebtOWxheS4reWvuem9kA0KDQogICAgLmRhdGUtcmFuZ2UtZGlzcGxheSB7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7IC8vIOaUueS4umlubGluZS1mbGV456Gu5L+d5pu05aW955qE5a+56b2QDQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOyAvLyDlnoLnm7TlsYXkuK3lr7npvZANCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiA0MHB4ICFpbXBvcnRhbnQ7DQogICAgICBwYWRkaW5nOiAwIDMwcHggMCAxNXB4Ow0KICAgICAgLy8g5L2/55So5LiOZmlsdGVyLXNlbGVjdOWujOWFqOebuOWQjOeahOagt+W8jw0KICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMikgIWltcG9ydGFudDsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweCAhaW1wb3J0YW50Ow0KICAgICAgY29sb3I6ICNmOGZhZmMgIWltcG9ydGFudDsNCiAgICAgIGZvbnQtZmFtaWx5OiAiUGluZ0ZhbmcgU0MiLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICJTZWdvZSBVSSIsIFJvYm90bywgc2Fucy1zZXJpZiAhaW1wb3J0YW50Ow0KICAgICAgZm9udC1zaXplOiB2YXIoLS1zZHMtdHlwb2dyYXBoeS1ib2R5LXNpemUtbWVkaXVtLCAxNnB4KSAhaW1wb3J0YW50Ow0KICAgICAgZm9udC13ZWlnaHQ6IHZhcigtLXNkcy10eXBvZ3JhcGh5LWJvZHktZm9udC13ZWlnaHQtcmVndWxhciwgNDAwKSAhaW1wb3J0YW50Ow0KICAgICAgbGluZS1oZWlnaHQ6IDE0MCUgIWltcG9ydGFudDsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjJzIGN1YmljLWJlemllcigwLjY0NSwgMC4wNDUsIDAuMzU1LCAxKTsNCg0KICAgICAgJjo6cGxhY2Vob2xkZXIgew0KICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpICFpbXBvcnRhbnQ7DQogICAgICB9DQoNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KSAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAmOmZvY3VzLA0KICAgICAgJi5pcy1mb2N1c2VkIHsNCiAgICAgICAgb3V0bGluZTogbm9uZTsNCiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQogICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4yKSAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAuZGF0ZS1yYW5nZS10ZXh0IHsNCiAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgLy8g56e76ZmkbGluZS1oZWlnaHTvvIzkvb/nlKjniLblrrnlmajnmoRmbGV45a+56b2QDQogICAgICAgIGNvbG9yOiAjZjhmYWZjICFpbXBvcnRhbnQ7IC8vIOebtOaOpeS9v+eUqOS4jueXheWus+exu+Wei+ebuOWQjOeahOminOiJsuWAvA0KICAgICAgICBmb250LWZhbWlseTogIlBpbmdGYW5nIFNDIiwgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8sIHNhbnMtc2VyaWYgIWltcG9ydGFudDsNCiAgICAgICAgZm9udC1zaXplOiB2YXIoLS1zZHMtdHlwb2dyYXBoeS1ib2R5LXNpemUtbWVkaXVtLCAxNnB4KSAhaW1wb3J0YW50Ow0KICAgICAgICBmb250LXdlaWdodDogdmFyKC0tc2RzLXR5cG9ncmFwaHktYm9keS1mb250LXdlaWdodC1yZWd1bGFyLCA0MDApICFpbXBvcnRhbnQ7DQoNCiAgICAgICAgJjplbXB0eTo6YmVmb3JlIHsNCiAgICAgICAgICBjb250ZW50OiAn5pel5pyf6IyD5Zu0JzsNCiAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpICFpbXBvcnRhbnQ7DQogICAgICAgICAgZm9udC1mYW1pbHk6ICJQaW5nRmFuZyBTQyIsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgUm9ib3RvLCBzYW5zLXNlcmlmICFpbXBvcnRhbnQ7DQogICAgICAgICAgZm9udC1zaXplOiB2YXIoLS1zZHMtdHlwb2dyYXBoeS1ib2R5LXNpemUtbWVkaXVtLCAxNnB4KSAhaW1wb3J0YW50Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiB2YXIoLS1zZHMtdHlwb2dyYXBoeS1ib2R5LWZvbnQtd2VpZ2h0LXJlZ3VsYXIsIDQwMCkgIWltcG9ydGFudDsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuZWwtaW5wdXRfX3N1ZmZpeCB7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgdG9wOiAwOw0KICAgICAgICByaWdodDogMTVweDsNCiAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBwb2ludGVyLWV2ZW50czogbm9uZTsNCg0KICAgICAgICAuZWwtaW5wdXRfX2ljb24gew0KICAgICAgICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNykgIWltcG9ydGFudDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDsNCiAgICAgICAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzIGVhc2UgIWltcG9ydGFudDsNCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgICYuY2xlYXItaWNvbiB7DQogICAgICAgICAgICBwb2ludGVyLWV2ZW50czogYXV0bzsNCiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCg0KICAgICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICAgIGNvbG9yOiAjZjU2YzZjICFpbXBvcnRhbnQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgJi5kcm9wZG93bi1pY29uIHsNCiAgICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC8vIOehruS/nemakOiXj+eahOaXpeacn+mAieaLqeWZqOWujOWFqOS4jeWPr+ingQ0KICAgIC5lbC1kYXRlLWVkaXRvciB7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGUgIWltcG9ydGFudDsNCiAgICAgIG9wYWNpdHk6IDAgIWltcG9ydGFudDsNCiAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lICFpbXBvcnRhbnQ7DQogICAgICB6LWluZGV4OiAtMSAhaW1wb3J0YW50Ow0KICAgICAgdG9wOiAwOw0KICAgICAgbGVmdDogMDsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgIH0NCiAgfQ0KDQogIC5wYWdlLWNvbnRhaW5lciB7DQogICAgLnN0YXRpc3RpY3MtdGFicyB7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgIH0NCg0KICAgIC5maWx0ZXItYXJlYSB7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgIH0NCg0KICAgIC5jYXJkcy1hcmVhIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgfQ0KDQoNCiAgICAuY2hhcnRzLXJvdyB7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgICAuY2hhcnQtY2FyZCB7DQogICAgICAgIGhlaWdodDogNDIwcHg7DQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIC5jaGFydC1oZWFkZXIgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgICAgICBwYWRkaW5nOiAxNHB4IDIwcHggMCAyMHB4Ow0KICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KDQogICAgICAgICAgaDQgew0KICAgICAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5kYXRhLXJvdyB7DQogICAgICAuY2hhcnQtdGl0bGUgew0KICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgICAgICBtYXJnaW4tbGVmdDogNDBweDsNCg0KICAgICAgICBoNCB7DQogICAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC50YWJsZS1jb250YWluZXItcmlnaHQgew0KICAgICAgICBoZWlnaHQ6IDM0MHB4Ow0KICAgICAgICBtYXJnaW46IDIwcHg7DQogICAgICB9DQoNCiAgICAgIC50YWJsZS1jb250YWluZXIgew0KICAgICAgICBoZWlnaHQ6IDczNXB4Ow0KICAgICAgICBtYXJnaW46IDIwcHg7DQogICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxQjJBNTYgMCUsICMyQTNCNkIgMTAwJSkgIWltcG9ydGFudDsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHggIWltcG9ydGFudDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICAgICAgIC5jb21tb24tdGFibGUgew0KICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgcGFkZGluZzogMTRweCAyMHB4Ow0KICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgICB6LWluZGV4OiAzOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICB9DQogIH0NCn0NCg0KLy8g5ZON5bqU5byP6K6+6K6hDQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7DQogIC5pbnNwZWN0aW9uLXN0YXRpc3RpY3Mgew0KICAgIC5jaGFydHMtcm93LA0KICAgIC5kYXRhLXJvdyB7DQogICAgICAuZWwtY29sIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5pbnNwZWN0aW9uLXN0YXRpc3RpY3Mgew0KICAgIC5kYXRhLXJvdyB7DQogICAgICAuZWwtY29sIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAghBA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/statistics", "sourcesContent": ["<template>\r\n  <div class=\"inspection-statistics inspection-container\">\r\n    <div class=\"page-container\">\r\n      <!-- TAB切换 -->\r\n      <el-tabs\r\n        v-model=\"activeTab\"\r\n        @tab-click=\"handleTabClick\"\r\n        class=\"statistics-tabs inspection-tabs\"\r\n      >\r\n        <el-tab-pane name=\"bridge\">\r\n          <span slot=\"label\">\r\n            <svg-icon icon-class=\"bridge\" />\r\n            桥梁统计\r\n          </span>\r\n        </el-tab-pane>\r\n        <el-tab-pane name=\"tunnel\">\r\n          <span slot=\"label\">\r\n            <svg-icon icon-class=\"tunnel\" />\r\n            隧道统计\r\n          </span>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <!-- 筛选条件（采用通用筛选组件） -->\r\n      <FilterSection\r\n        v-model=\"filterForm\"\r\n        :configs=\"filterConfigs\"\r\n        :options=\"selectOptions\"\r\n        @search=\"handleSearch\"\r\n        @reset=\"handleReset\"\r\n        class=\"filter-area\"\r\n      >\r\n        <template #filters=\"{ formData, options }\">\r\n          <!-- 桥梁/隧道名称 -->\r\n          <el-select\r\n            v-model=\"formData.bridgeName\"\r\n            :placeholder=\"bridgeNameText\"\r\n            clearable\r\n            filterable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option\r\n              v-for=\"option in options.bridgeOptions\"\r\n              :key=\"option.value\"\r\n              :label=\"option.label\"\r\n              :value=\"option.value\"\r\n            />\r\n          </el-select>\r\n\r\n\r\n          <!-- 日期范围（与病害列表页一致的自定义样式） -->\r\n          <div class=\"custom-date-range-selector filter-select\">\r\n            <div\r\n              class=\"date-range-display\"\r\n              :class=\"{ 'is-focused': isDatePickerFocused }\"\r\n              @click=\"toggleDatePicker\"\r\n              @blur=\"handleDatePickerBlur\"\r\n              @keydown.enter=\"toggleDatePicker\"\r\n              @keydown.space.prevent=\"toggleDatePicker\"\r\n              tabindex=\"0\"\r\n            >\r\n              <span class=\"date-range-text\">\r\n                {{ dateRangeDisplayText }}\r\n              </span>\r\n              <span class=\"el-input__suffix\">\r\n                <i\r\n                  v-if=\"filterForm.reportTimeRange && filterForm.reportTimeRange.length === 2\"\r\n                  class=\"el-icon-circle-close el-input__icon clear-icon\"\r\n                  @click.stop=\"clearDateRange\"\r\n                ></i>\r\n                <i\r\n                  v-else\r\n                  class=\"el-icon-arrow-down el-input__icon dropdown-icon\"\r\n                ></i>\r\n              </span>\r\n            </div>\r\n\r\n            <!-- 隐藏的日期选择器 -->\r\n            <el-date-picker\r\n              ref=\"hiddenDatePicker\"\r\n              v-model=\"filterForm.reportTimeRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              style=\"position: absolute; opacity: 0; pointer-events: none; z-index: -1;\"\r\n              @change=\"handleDateRangeChange\"\r\n            />\r\n          </div>\r\n\r\n          <!-- 区域 -->\r\n          <el-select\r\n            v-model=\"formData.region\"\r\n            placeholder=\"区域\"\r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option label=\"开福区\" value=\"kaifu\" />\r\n            <el-option label=\"雨花区\" value=\"yuhua\" />\r\n            <el-option label=\"芙蓉区\" value=\"furong\" />\r\n            <el-option label=\"天心区\" value=\"tianxin\" />\r\n            <el-option label=\"岳麓区\" value=\"yuelu\" />\r\n          </el-select>\r\n\r\n          <!-- 项目部 -->\r\n          <el-select\r\n            v-model=\"formData.projectDept\"\r\n            placeholder=\"项目部\"\r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option label=\"第一项目部\" value=\"dept1\" />\r\n            <el-option label=\"第二项目部\" value=\"dept2\" />\r\n            <el-option label=\"第三项目部\" value=\"dept3\" />\r\n          </el-select>\r\n        </template>\r\n      </FilterSection>\r\n\r\n      <!-- 统计卡片 -->\r\n      <StatisticsCards\r\n        :statistics-data=\"statisticsData\"\r\n        :loading=\"loading\"\r\n        class=\"cards-area\"\r\n      />\r\n\r\n      <!-- 图表区域 -->\r\n      <el-row :gutter=\"20\" class=\"charts-row\">\r\n        <!-- 巡检趋势分析 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"chart-card\" shadow=\"never\">\r\n            <div class=\"chart-header\">\r\n              <h4>巡检趋势分析</h4>\r\n            </div>\r\n            <TrendChart\r\n              :chart-data=\"statisticsData.trendData\"\r\n              :chart-type=\"'line'\"\r\n              :loading=\"loading\"\r\n              height=\"300px\"\r\n            />\r\n          </el-card>\r\n        </el-col>\r\n\r\n        <!-- 各区巡检完成情况 -->\r\n        <el-col :span=\"12\">\r\n          <el-card class=\"chart-card\" shadow=\"never\">\r\n            <div class=\"chart-header\">\r\n              <h4>各区巡检完成情况</h4>\r\n            </div>\r\n            <RegionChart\r\n              :chart-data=\"statisticsData.regionData\"\r\n              :loading=\"loading\"\r\n              height=\"300px\"\r\n            />\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行：左侧最近巡检记录 + 右侧病害分析 -->\r\n      <el-row :gutter=\"20\" class=\"data-row\">\r\n        <!-- 左侧：最近巡检记录 -->\r\n        <el-col :span=\"12\">\r\n          <!-- 🔧 标题移到卡片外部 -->\r\n          <div class=\"chart-title\">\r\n            <h4>最近巡检记录</h4>\r\n          </div>\r\n\r\n          <!-- 🔧 外框只包围表格内容 -->\r\n          <div class=\"table-container\">\r\n            <div class=\"common-table\">\r\n              <el-table\r\n                v-loading=\"loading\"\r\n                :data=\"recentRecords\"\r\n                size=\"small\"\r\n                style=\"width: 100%;\"\r\n              >\r\n              <el-table-column type=\"index\" label=\"序号\" width=\"50\" align=\"center\" />\r\n              <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"100\" show-overflow-tooltip />\r\n              <el-table-column prop=\"inspectionDate\" label=\"检测日期\" width=\"100\" align=\"center\" />\r\n              <el-table-column prop=\"inspector\" label=\"巡检人员\" width=\"80\" align=\"center\" />\r\n              <el-table-column prop=\"contactNumber\" label=\"联系方式\" width=\"120\" align=\"center\" />\r\n              <el-table-column prop=\"monthlyFine\" label=\"发现问题\" width=\"80\" align=\"center\" />\r\n              <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    type=\"text\"\r\n                    size=\"mini\"\r\n                    @click=\"viewRecordDetail(scope.row)\"\r\n                  >\r\n                    详情\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n\r\n        <!-- 右侧：病害分析区域 -->\r\n        <el-col :span=\"12\">\r\n\r\n              <div class=\"chart-title\">\r\n                <h4>病害类型分布</h4>\r\n              </div>\r\n              <div class=\"table-container-right\">\r\n                  <DamageTypeChart\r\n                    :chart-data=\"statisticsData.damageTypeData\"\r\n                    :loading=\"loading\"\r\n                    height=\"100%\"\r\n                  />\r\n              </div>\r\n\r\n            <!-- 下半部分：桥梁病害数量TOP10 -->\r\n\r\n              <div class=\"chart-title\">\r\n                <h4>病害数量top10</h4>\r\n              </div>\r\n            <div class=\"table-container-right\">\r\n                <Top10RankingChart\r\n                  :chart-data=\"statisticsData.bridgeRanking\"\r\n                  :loading=\"loading\"\r\n                  height=\"100%\"\r\n                />\r\n            </div>\r\n\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport StatisticsCards from './components/StatisticsCards'\r\nimport TrendChart from './components/TrendChart'\r\nimport RegionChart from './components/RegionChart'\r\nimport DamageTypeChart from './components/DamageTypeChart'\r\nimport Top10RankingChart from './components/Top10RankingChart'\r\nimport { FilterSection } from '@/components/Inspection'\r\n\r\nexport default {\r\n  name: 'InspectionStatistics',\r\n  components: {\r\n    StatisticsCards,\r\n    TrendChart,\r\n    RegionChart,\r\n    DamageTypeChart,\r\n    Top10RankingChart,\r\n    FilterSection\r\n  },\r\n  data() {\r\n    return {\r\n      // 当前激活的tab\r\n      activeTab: 'bridge',\r\n\r\n\r\n      // 筛选表单\r\n      filterForm: {\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: 'month',\r\n        region: '',\r\n        projectDept: ''\r\n      },\r\n\r\n\r\n      // 最近巡检记录\r\n      recentRecords: [],\r\n\r\n\r\n      // 日期选择器焦点状态（与病害列表页一致）\r\n      isDatePickerFocused: false,\r\n\r\n      // 筛选配置\r\n      filterConfigs: {}\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('inspection', [\r\n      'statisticsData',\r\n      'selectOptions',\r\n      'loadingStates'\r\n    ]),\r\n\r\n    loading() {\r\n      return this.loadingStates.inspectionRecords\r\n    },\r\n\r\n    bridgeOptions() {\r\n      return this.selectOptions.bridgeOptions || []\r\n    },\r\n    // 动态的桥梁/隧道名称文本\r\n    bridgeNameText() {\r\n      return this.activeTab === 'tunnel' ? '隧道名称' : '桥梁名称'\r\n    },\r\n\r\n\r\n    // 日期范围显示文本（与病害列表页一致）\r\n    dateRangeDisplayText() {\r\n      if (this.filterForm.reportTimeRange && this.filterForm.reportTimeRange.length === 2) {\r\n        return `${this.filterForm.reportTimeRange[0]} 至 ${this.filterForm.reportTimeRange[1]}`\r\n      }\r\n      return '日期范围'\r\n    }\r\n  },\r\n  async created() {\r\n    await this.initPageData()\r\n  },\r\n  mounted() {\r\n    // 页面挂载后的初始化操作\r\n  },\r\n  methods: {\r\n    ...mapActions('inspection', [\r\n      'fetchStatisticsData',\r\n      'fetchRecentInspectionRecords',\r\n      'initSelectOptions',\r\n      'updateFilters'\r\n    ]),\r\n\r\n    // 搜索\r\n    async handleSearch(formData) {\r\n      this.updateFilters({\r\n        inspectionType: this.activeTab,\r\n        bridgeName: formData.bridgeName,\r\n        reportTimeRange: formData.reportTimeRange,\r\n        timeRange: formData.timeRange,\r\n        region: formData.region,\r\n        projectDept: formData.projectDept\r\n      })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n    // 重置\r\n    async handleReset() {\r\n      this.filterForm = {\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: 'month',\r\n        region: '',\r\n        projectDept: ''\r\n      }\r\n      this.updateFilters({\r\n        inspectionType: this.activeTab,\r\n        bridgeName: '',\r\n        reportTimeRange: [],\r\n        timeRange: this.filterForm.timeRange,\r\n        region: '',\r\n        projectDept: ''\r\n      })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n    // 初始化页面数据\r\n    async initPageData() {\r\n      try {\r\n        // 初始化下拉选项\r\n        await this.initSelectOptions()\r\n\r\n        // 更新筛选条件\r\n        this.updateFilters({\r\n          inspectionType: this.activeTab,\r\n          timeRange: this.filterForm.timeRange\r\n        })\r\n\r\n        // 获取统计数据\r\n        await this.fetchStatisticsData()\r\n\r\n        // 获取最近巡检记录\r\n        await this.loadRecentRecords()\r\n\r\n      } catch (error) {\r\n        console.error('初始化页面数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      }\r\n    },\r\n\r\n    // 加载最近巡检记录\r\n    async loadRecentRecords() {\r\n      try {\r\n        await this.fetchRecentInspectionRecords({ limit: 8 })\r\n\r\n        // 从store获取数据或使用默认数据\r\n        this.recentRecords = this.getDefaultRecentRecords()\r\n\r\n      } catch (error) {\r\n        console.error('加载最近巡检记录失败:', error)\r\n        this.recentRecords = this.getDefaultRecentRecords()\r\n      }\r\n    },\r\n\r\n    // 获取默认最近巡检记录\r\n    getDefaultRecentRecords() {\r\n      const today = new Date()\r\n      const dates = []\r\n\r\n      // 生成最近8天的日期\r\n      for (let i = 0; i < 8; i++) {\r\n        const date = new Date(today)\r\n        date.setDate(today.getDate() - i)\r\n        dates.push(date.toISOString().split('T')[0])\r\n      }\r\n\r\n      return [\r\n        {\r\n          id: 1,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[0],\r\n          inspector: '吴亮吉',\r\n          contactNumber: '13580037492',\r\n          monthlyFine: 22\r\n        },\r\n        {\r\n          id: 2,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[1],\r\n          inspector: '陈秀英',\r\n          contactNumber: '15210087395',\r\n          monthlyFine: 26\r\n        },\r\n        {\r\n          id: 3,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[1],\r\n          inspector: '陈昭吉',\r\n          contactNumber: '15910018495',\r\n          monthlyFine: 6\r\n        },\r\n        {\r\n          id: 4,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[2],\r\n          inspector: '王建军',\r\n          contactNumber: '13122238579',\r\n          monthlyFine: 9\r\n        },\r\n        {\r\n          id: 5,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[3],\r\n          inspector: '吴超栋',\r\n          contactNumber: '13720089685',\r\n          monthlyFine: 33\r\n        },\r\n        {\r\n          id: 6,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[4],\r\n          inspector: '江融行',\r\n          contactNumber: '18202038579',\r\n          monthlyFine: 11\r\n        },\r\n        {\r\n          id: 7,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[5],\r\n          inspector: '刘君君',\r\n          contactNumber: '18310049683',\r\n          monthlyFine: 62\r\n        },\r\n        {\r\n          id: 8,\r\n          bridgeName: 'XXXX大桥',\r\n          inspectionDate: dates[6],\r\n          inspector: '袁如谦',\r\n          contactNumber: '17821229583',\r\n          monthlyFine: 18\r\n        }\r\n      ]\r\n    },\r\n\r\n    // TAB切换\r\n    async handleTabClick(tab) {\r\n      this.activeTab = tab.name\r\n      this.updateFilters({ inspectionType: this.activeTab })\r\n      await this.fetchStatisticsData()\r\n      await this.loadRecentRecords()\r\n    },\r\n\r\n\r\n    // 查看所有记录\r\n    viewAllRecords() {\r\n      this.$router.push({ name: 'InspectionRecords' })\r\n    },\r\n\r\n    // 查看记录详情\r\n    viewRecordDetail(record) {\r\n      console.log('查看记录详情:', record)\r\n      // 这里可以打开详情弹窗或跳转到详情页面\r\n    },\r\n\r\n\r\n    // 日期范围选择器相关方法（与病害列表页一致）\r\n    // 切换日期选择器显示\r\n    toggleDatePicker() {\r\n      this.isDatePickerFocused = true\r\n      this.$nextTick(() => {\r\n        this.$refs.hiddenDatePicker.focus()\r\n      })\r\n    },\r\n\r\n    // 处理日期选择器失焦\r\n    handleDatePickerBlur() {\r\n      // 延迟执行，确保点击操作能正常完成\r\n      setTimeout(() => {\r\n        this.isDatePickerFocused = false\r\n      }, 200)\r\n    },\r\n\r\n    // 处理日期范围变化\r\n    handleDateRangeChange(value) {\r\n      this.filterForm.reportTimeRange = value\r\n      // 日期选择完成后移除焦点状态\r\n      this.isDatePickerFocused = false\r\n    },\r\n\r\n    // 清空日期范围\r\n    clearDateRange() {\r\n      this.filterForm.reportTimeRange = []\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 导入主题样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/components/table.scss';\r\n\r\n.inspection-statistics {\r\n\r\n  // 自定义日期范围选择器样式，匹配病害列表页\r\n  .custom-date-range-selector {\r\n    position: relative;\r\n    width: 100%;\r\n    min-width: 180px;\r\n    flex: 1;\r\n    display: flex; // 确保与其他filter-select对齐\r\n    align-items: center; // 垂直居中对齐\r\n\r\n    .date-range-display {\r\n      position: relative;\r\n      box-sizing: border-box;\r\n      display: inline-flex; // 改为inline-flex确保更好的对齐\r\n      align-items: center; // 垂直居中对齐\r\n      width: 100%;\r\n      height: 40px !important;\r\n      padding: 0 30px 0 15px;\r\n      // 使用与filter-select完全相同的样式\r\n      background: rgba(255, 255, 255, 0.1) !important;\r\n      border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n      border-radius: 8px !important;\r\n      color: #f8fafc !important;\r\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n      font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n      line-height: 140% !important;\r\n      cursor: pointer;\r\n      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\r\n\r\n      &::placeholder {\r\n        color: rgba(255, 255, 255, 0.5) !important;\r\n      }\r\n\r\n      &:hover {\r\n        border-color: rgba(255, 255, 255, 0.4) !important;\r\n      }\r\n\r\n      &:focus,\r\n      &.is-focused {\r\n        outline: none;\r\n        border-color: #409EFF !important;\r\n        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;\r\n      }\r\n\r\n      .date-range-text {\r\n        display: block;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        // 移除line-height，使用父容器的flex对齐\r\n        color: #f8fafc !important; // 直接使用与病害类型相同的颜色值\r\n        font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n        font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n\r\n        &:empty::before {\r\n          content: '日期范围';\r\n          color: rgba(255, 255, 255, 0.5) !important;\r\n          font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;\r\n          font-size: var(--sds-typography-body-size-medium, 16px) !important;\r\n          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;\r\n        }\r\n      }\r\n\r\n      .el-input__suffix {\r\n        position: absolute;\r\n        top: 0;\r\n        right: 15px;\r\n        height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        pointer-events: none;\r\n\r\n        .el-input__icon {\r\n          color: rgba(255, 255, 255, 0.7) !important;\r\n          font-size: 14px !important;\r\n          transition: color 0.3s ease !important;\r\n\r\n          &:hover {\r\n            color: rgba(255, 255, 255, 0.9) !important;\r\n          }\r\n\r\n          &.clear-icon {\r\n            pointer-events: auto;\r\n            cursor: pointer;\r\n\r\n            &:hover {\r\n              color: #f56c6c !important;\r\n            }\r\n          }\r\n\r\n          &.dropdown-icon {\r\n            pointer-events: none;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 确保隐藏的日期选择器完全不可见\r\n    .el-date-editor {\r\n      position: absolute !important;\r\n      opacity: 0 !important;\r\n      pointer-events: none !important;\r\n      z-index: -1 !important;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .page-container {\r\n    .statistics-tabs {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .filter-area {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .cards-area {\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n\r\n    .charts-row {\r\n      margin-bottom: 20px;\r\n\r\n      .chart-card {\r\n        height: 420px;\r\n        overflow: hidden;\r\n        .chart-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 20px;\r\n          padding: 14px 20px 0 20px;\r\n          flex-shrink: 0;\r\n\r\n          h4 {\r\n            margin: 0;\r\n            font-size: 16px;\r\n            font-weight: 600;\r\n            color: #ffffff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .data-row {\r\n      .chart-title {\r\n        margin-bottom: 20px;\r\n        margin-left: 40px;\r\n\r\n        h4 {\r\n          margin: 0;\r\n          font-size: 16px;\r\n          color: #fff;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n\r\n      .table-container-right {\r\n        height: 340px;\r\n        margin: 20px;\r\n      }\r\n\r\n      .table-container {\r\n        height: 735px;\r\n        margin: 20px;\r\n        background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n        border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n        border-radius: 10px !important;\r\n        position: relative;\r\n        overflow: hidden;\r\n\r\n        .common-table {\r\n          height: 100%;\r\n          display: flex;\r\n          flex-direction: column;\r\n          padding: 14px 20px;\r\n          position: relative;\r\n          z-index: 3;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .inspection-statistics {\r\n    .charts-row,\r\n    .data-row {\r\n      .el-col {\r\n        margin-bottom: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .inspection-statistics {\r\n    .data-row {\r\n      .el-col {\r\n        margin-bottom: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}