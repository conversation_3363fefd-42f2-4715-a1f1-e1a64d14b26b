{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ProjectConfig.vue", "mtime": 1758811136136}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ProjectConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "ProjectConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\r\n  <div class=\"project-config\">\r\n    <!-- 添加项目按钮 - 按原型图结构 -->\r\n    <div v-if=\"!readonly\" class=\"add-project-row\">\r\n      <el-button \r\n        type=\"primary\" \r\n        icon=\"el-icon-plus\"\r\n        class=\"add-project-btn\"\r\n        @click=\"addProject\"\r\n      >\r\n        添加项目\r\n      </el-button>\r\n    </div>\r\n    \r\n    <!-- 项目列表 - 按原型图每行一个项目的结构 -->\r\n    <div class=\"project-list\">\r\n      <div \r\n        v-for=\"(project, index) in projectList\" \r\n        :key=\"index\"\r\n        class=\"project-row\"\r\n      >\r\n        <div class=\"project-input-container\">\r\n          <el-input\r\n            v-model=\"project.name\"\r\n            :placeholder=\"getPlaceholder()\"\r\n            class=\"project-name-input\"\r\n            :disabled=\"readonly\"\r\n            @blur=\"validateProject(index)\"\r\n            @input=\"emitChange\"\r\n          />\r\n          \r\n          <!-- 保洁项目的频次配置 - 按原型图布局 -->\r\n          <template v-if=\"projectType === 'cleaning'\">\r\n            <el-input\r\n              v-model=\"project.frequency\"\r\n              type=\"number\"\r\n              min=\"1\"\r\n              max=\"365\"\r\n              class=\"frequency-input\"\r\n              :disabled=\"readonly\"\r\n              @input=\"emitChange\"\r\n            />\r\n            <span class=\"frequency-unit\">天/1次</span>\r\n          </template>\r\n        </div>\r\n        \r\n        <el-button\r\n          v-if=\"!readonly\"\r\n          type=\"text\"\r\n          class=\"cancel-btn\"\r\n          @click=\"removeProject(index)\"\r\n        >\r\n          取消\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ProjectConfig',\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectType: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      projectList: []\r\n    }\r\n  },\r\n  watch: {\r\n    // 只监听外部传入的value，单向数据流\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && newVal.projects && Array.isArray(newVal.projects) && newVal.projects.length > 0) {\r\n          // 只在数据真正不同时才更新，避免循环\r\n          if (JSON.stringify(newVal.projects) !== JSON.stringify(this.projectList)) {\r\n            this.projectList = [...newVal.projects]\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    // 监听项目类型变化，重新初始化\r\n    projectType: {\r\n      handler(newType, oldType) {\r\n        if (newType !== oldType && this.projectList.length > 0) {\r\n          this.projectList = []\r\n          this.$nextTick(() => {\r\n            this.initializeProject()\r\n            this.emitChange()\r\n          })\r\n        }\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化时确保至少有一个空项目\r\n    if (this.projectList.length === 0) {\r\n      this.initializeProject()\r\n      this.emitChange()\r\n    }\r\n  },\r\n  methods: {\r\n    // 统一的数据更新方法\r\n    emitChange() {\r\n      this.$nextTick(() => {\r\n        this.$emit('input', {\r\n          projects: this.projectList\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 初始化项目（仅在组件初始化时使用）\r\n    initializeProject() {\r\n      if (this.projectList.length === 0) {\r\n        const newProject = {\r\n          name: '',\r\n          frequency: this.projectType === 'cleaning' ? 7 : null\r\n        }\r\n        this.projectList = [newProject]\r\n      }\r\n    },\r\n    \r\n    // 添加项目（用户点击按钮时使用）\r\n    addProject() {\r\n      const newProject = {\r\n        name: '',\r\n        frequency: this.projectType === 'cleaning' ? 7 : null\r\n      }\r\n      this.projectList.push(newProject)\r\n      this.emitChange()\r\n    },\r\n    \r\n    // 移除项目\r\n    removeProject(index) {\r\n      if (this.projectList.length > 1) {\r\n        this.projectList.splice(index, 1)\r\n        this.emitChange()\r\n      } else {\r\n        this.$message.warning('至少需要保留一个项目')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证项目\r\n    validateProject(index) {\r\n      const project = this.projectList[index]\r\n      if (!project.name) {\r\n        return\r\n      }\r\n      \r\n      // 检查重复\r\n      const duplicateIndex = this.projectList.findIndex((p, i) => \r\n        i !== index && p.name === project.name\r\n      )\r\n      \r\n      if (duplicateIndex !== -1) {\r\n        this.$message.warning('项目名称不能重复')\r\n        project.name = ''\r\n      }\r\n    },\r\n    \r\n    // 获取输入框占位符\r\n    getPlaceholder() {\r\n      const placeholders = {\r\n        monthly: '请输入养护项目名称',\r\n        cleaning: '请输入保洁项目名称',\r\n        emergency: '请输入应急项目名称',\r\n        preventive: '请输入预防养护项目名称'\r\n      }\r\n      return placeholders[this.projectType] || '请输入项目名称'\r\n    },\r\n    \r\n    \r\n    // 表单验证\r\n    validate() {\r\n      // 检查是否有有效项目\r\n      const validProjects = this.projectList.filter(project => project.name.trim())\r\n      \r\n      if (validProjects.length === 0) {\r\n        this.$message.error('请至少添加一个项目')\r\n        return false\r\n      }\r\n      \r\n      // 检查保洁项目的频次\r\n      if (this.projectType === 'cleaning') {\r\n        const invalidFrequency = validProjects.some(project => \r\n          !project.frequency || project.frequency < 1 || project.frequency > 365\r\n        )\r\n        \r\n        if (invalidFrequency) {\r\n          this.$message.error('请设置正确的清洁频次（1-365天）')\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n\r\n.project-config {\r\n  @extend .common-project-config;\r\n  padding: 20px;\r\n  .add-project-row {\r\n    margin-bottom: 24px; // 增加与下方内容的间距\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    \r\n    // 按钮样式已通过公共样式提供\r\n  }\r\n  \r\n  // 项目列表区域\r\n  .project-list {\r\n    .project-row {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 16px;\r\n      gap: 12px; // 设置各元素之间的间距\r\n      \r\n      .project-input-container {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n        \r\n        .project-name-input {\r\n          flex: 1;\r\n          min-width: 200px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            \r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 保洁项目的频次输入框\r\n        .frequency-input {\r\n          width: 80px;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #374151;\r\n            border-color: #6b7280;\r\n            color: #ffffff;\r\n            height: 40px;\r\n            border-radius: 6px;\r\n            text-align: center;\r\n            \r\n            &:focus {\r\n              border-color: #3b82f6;\r\n              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n            }\r\n          }\r\n        }\r\n        \r\n        .frequency-unit {\r\n          color: #e5e7eb;\r\n          font-size: 14px;\r\n          white-space: nowrap;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n      \r\n      .cancel-btn {\r\n        color: #ef4444;\r\n        font-size: 14px;\r\n        min-width: 60px;\r\n        height: 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        \r\n        &:hover {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n        \r\n        &:focus {\r\n          color: #dc2626;\r\n          background-color: rgba(239, 68, 68, 0.1);\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 确保最后一行不会有多余的margin\r\n    .project-row:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  \r\n  // 只读模式下的样式调整\r\n  &.readonly {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n      \r\n      .frequency-input :deep(.el-input__inner) {\r\n        background: #1f2937;\r\n        border-color: #374151;\r\n        color: #d1d5db;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 确保在深色主题下有良好的视觉效果\r\n.maintenance-theme .project-config,\r\n.inspection-theme .project-config {\r\n  .add-project-btn {\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  }\r\n  \r\n  .project-row {\r\n    .project-input-container {\r\n      .project-name-input :deep(.el-input__inner),\r\n      .frequency-input :deep(.el-input__inner) {\r\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}