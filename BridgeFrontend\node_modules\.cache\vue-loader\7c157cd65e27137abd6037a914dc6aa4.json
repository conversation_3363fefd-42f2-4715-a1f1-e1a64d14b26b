{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue?vue&type=template&id=683765ba&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}