{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\DiseaseConfig.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\DiseaseConfig.vue", "mtime": 1758807113725}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldERpc2Vhc2VMaXN0IH0gZnJvbSAnQC9hcGkvbWFpbnRlbmFuY2UvcHJvamVjdHMnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Rpc2Vhc2VDb25maWcnLAogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkKICAgIH0sCiAgICByZWFkb25seTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpc2Vhc2VMaXN0OiBbXSwKICAgICAgc2hvd0Rpc2Vhc2VEaWFsb2c6IGZhbHNlLAogICAgICBkaXNlYXNlTG9hZGluZzogZmFsc2UsCiAgICAgIGF2YWlsYWJsZURpc2Vhc2VzOiBbXSwKICAgICAgc2VsZWN0ZWREaXNlYXNlczogW10sCiAgICAgIGRpc2Vhc2VUb3RhbDogMCwKICAgICAgCiAgICAgIC8vIOaQnOe0ouWPguaVsAogICAgICBzZWFyY2hQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAyMCwKICAgICAgICBicmlkZ2VOYW1lOiAnJywKICAgICAgICB0eXBlOiAnJwogICAgICB9LAogICAgICAKICAgICAgLy8g55eF5a6z57G75Z6L6YCJ6aG5CiAgICAgIGRpc2Vhc2VUeXBlczogWwogICAgICAgIHsgbGFiZWw6ICfkvLjnvKnnvJ3nvLrlpLEnLCB2YWx1ZTogJ2V4cGFuc2lvbl9qb2ludF9taXNzaW5nJyB9LAogICAgICAgIHsgbGFiZWw6ICfnhafmmI7orr7mlr3nvLrlpLEnLCB2YWx1ZTogJ2xpZ2h0aW5nX21pc3NpbmcnIH0sCiAgICAgICAgeyBsYWJlbDogJ+aKpOagj+aNn+WdjycsIHZhbHVlOiAnZ3VhcmRyYWlsX2RhbWFnZScgfSwKICAgICAgICB7IGxhYmVsOiAn5qGl6Z2i56C05o2fJywgdmFsdWU6ICdkZWNrX2RhbWFnZScgfSwKICAgICAgICB7IGxhYmVsOiAn5o6S5rC05LiN55WFJywgdmFsdWU6ICdkcmFpbmFnZV9wb29yJyB9CiAgICAgIF0KICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICAvLyDlj6rnm5HlkKzlpJbpg6jkvKDlhaXnmoR2YWx1Ze+8jOWNleWQkeaVsOaNrua1gQogICAgdmFsdWU6IHsKICAgICAgaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC5kaXNlYXNlcyAmJiBBcnJheS5pc0FycmF5KG5ld1ZhbC5kaXNlYXNlcykpIHsKICAgICAgICAgIC8vIOWPquWcqOaVsOaNruecn+ato+S4jeWQjOaXtuaJjeabtOaWsO+8jOmBv+WFjeW+queOrwogICAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KG5ld1ZhbC5kaXNlYXNlcykgIT09IEpTT04uc3RyaW5naWZ5KHRoaXMuZGlzZWFzZUxpc3QpKSB7CiAgICAgICAgICAgIHRoaXMuZGlzZWFzZUxpc3QgPSBbLi4ubmV3VmFsLmRpc2Vhc2VzXQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlLAogICAgICBkZWVwOiB0cnVlCiAgICB9LAogICAgCiAgICBzaG93RGlzZWFzZURpYWxvZyh2aXNpYmxlKSB7CiAgICAgIGlmICh2aXNpYmxlKSB7CiAgICAgICAgdGhpcy5sb2FkQXZhaWxhYmxlRGlzZWFzZXMoKQogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDnu5/kuIDnmoTmlbDmja7mm7TmlrDmlrnms5UKICAgIGVtaXRDaGFuZ2UoKSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIHsKICAgICAgICAgIGRpc2Vhc2VzOiB0aGlzLmRpc2Vhc2VMaXN0CiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOWKoOi9veWPr+eUqOeXheWus+WIl+ihqAogICAgYXN5bmMgbG9hZEF2YWlsYWJsZURpc2Vhc2VzKCkgewogICAgICB0aGlzLmRpc2Vhc2VMb2FkaW5nID0gdHJ1ZQogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0RGlzZWFzZUxpc3QodGhpcy5zZWFyY2hQYXJhbXMpCiAgICAgICAgY29uc29sZS5sb2coJ+eXheWus+WIl+ihqEFQSeWTjeW6lDonLCByZXNwb25zZSkKICAgICAgICAKICAgICAgICAvLyDpgILphY3kuI3lkIznmoRBUEnlk43lupTmoLzlvI8KICAgICAgICBsZXQgZGlzZWFzZXMgPSBbXQogICAgICAgIGxldCB0b3RhbCA9IDAKICAgICAgICAKICAgICAgICBjb25zb2xlLmxvZygnQVBJ5ZON5bqU57uT5p6EOicsIHJlc3BvbnNlKQogICAgICAgIAogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICAvLyDkvJjlhYjkvb/nlKggZGF0YS5yb3dzICjmqKHmi59BUEnmoLzlvI8pCiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5yb3dzKSB7CiAgICAgICAgICAgIGRpc2Vhc2VzID0gcmVzcG9uc2UuZGF0YS5yb3dzCiAgICAgICAgICAgIHRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbCB8fCAwCiAgICAgICAgICB9CiAgICAgICAgICAvLyDlhbbmrKHkvb/nlKggZGF0YS5saXN0ICjmoIflh4bmoLzlvI8pCiAgICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5kYXRhLmxpc3QpIHsKICAgICAgICAgICAgZGlzZWFzZXMgPSByZXNwb25zZS5kYXRhLmxpc3QKICAgICAgICAgICAgdG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsIHx8IDAKICAgICAgICAgIH0KICAgICAgICAgIC8vIOacgOWQjuebtOaOpeS9v+eUqCBkYXRhICjnroDljZXmoLzlvI8pCiAgICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgIGRpc2Vhc2VzID0gcmVzcG9uc2UuZGF0YQogICAgICAgICAgICB0b3RhbCA9IHJlc3BvbnNlLmRhdGEubGVuZ3RoCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIAogICAgICAgIGNvbnNvbGUubG9nKCfop6PmnpDlkI7nmoTnl4XlrrPmlbDmja46JywgZGlzZWFzZXMsICfmgLvmlbA6JywgdG90YWwpCiAgICAgICAgCiAgICAgICAgLy8g5pWw5o2u5a2X5q615pig5bCE5ZKM5qCH5YeG5YyWCiAgICAgICAgdGhpcy5hdmFpbGFibGVEaXNlYXNlcyA9IGRpc2Vhc2VzLm1hcChkaXNlYXNlID0+ICh7CiAgICAgICAgICBpZDogZGlzZWFzZS5pZCwKICAgICAgICAgIGJyaWRnZU5hbWU6IGRpc2Vhc2UuYnJpZGdlTmFtZSB8fCAn5pyq55+l5qGl5qKBJywKICAgICAgICAgIGRpc2Vhc2VDb2RlOiBkaXNlYXNlLmRpc2Vhc2VDb2RlIHx8IGRpc2Vhc2UuY29kZSB8fCAnLScsCiAgICAgICAgICBkaXNlYXNlUGFydDogZGlzZWFzZS5sb2NhdGlvbiB8fCBkaXNlYXNlLmRpc2Vhc2VQYXJ0IHx8ICctJywgLy8g5L+u5q2j77ya5L2/55SoIGxvY2F0aW9uIOWtl+autQogICAgICAgICAgZGlzZWFzZVR5cGU6IGRpc2Vhc2UudHlwZSB8fCBkaXNlYXNlLmRpc2Vhc2VUeXBlIHx8ICctJywgLy8g5L+u5q2j77ya5L2/55SoIHR5cGUg5a2X5q61CiAgICAgICAgICBkaXNlYXNlQ291bnQ6IGRpc2Vhc2UucXVhbnRpdHkgfHwgZGlzZWFzZS5kaXNlYXNlQ291bnQgfHwgMCwgLy8g5L+u5q2j77ya5L2/55SoIHF1YW50aXR5IOWtl+autQogICAgICAgICAgZGlzZWFzZURlc2NyaXB0aW9uOiBkaXNlYXNlLmRlc2NyaXB0aW9uIHx8IGRpc2Vhc2UuZGlzZWFzZURlc2NyaXB0aW9uIHx8ICctJywgLy8g5L+u5q2j77ya5L2/55SoIGRlc2NyaXB0aW9uIOWtl+autQogICAgICAgICAgZGlzZWFzZUxldmVsOiBkaXNlYXNlLmxldmVsIHx8IGRpc2Vhc2UuZGlzZWFzZUxldmVsIHx8IDEsIC8vIOS/ruato++8muS9v+eUqCBsZXZlbCDlrZfmrrUKICAgICAgICAgIHJlcG9ydGVyOiBkaXNlYXNlLnJlcG9ydFBlcnNvbiB8fCBkaXNlYXNlLnJlcG9ydGVyIHx8ICctJywgLy8g5L+u5q2j77ya5L2/55SoIHJlcG9ydFBlcnNvbiDlrZfmrrUKICAgICAgICAgIHJlcG9ydFRpbWU6IGRpc2Vhc2UucmVwb3J0VGltZSB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkKICAgICAgICB9KSkKICAgICAgICAKICAgICAgICB0aGlzLmRpc2Vhc2VUb3RhbCA9IHRvdGFsCiAgICAgICAgCiAgICAgICAgLy8g6K6+572u5bey6YCJ5Lit55qE55eF5a6zCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgaWYgKHRoaXMuJHJlZnMuZGlzZWFzZVNlbGVjdGlvblRhYmxlKSB7CiAgICAgICAgICAgIHRoaXMuYXZhaWxhYmxlRGlzZWFzZXMuZm9yRWFjaChkaXNlYXNlID0+IHsKICAgICAgICAgICAgICBjb25zdCBpc1NlbGVjdGVkID0gdGhpcy5kaXNlYXNlTGlzdC5zb21lKHNlbGVjdGVkID0+IHNlbGVjdGVkLmlkID09PSBkaXNlYXNlLmlkKQogICAgICAgICAgICAgIGlmIChpc1NlbGVjdGVkKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmRpc2Vhc2VTZWxlY3Rpb25UYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24oZGlzZWFzZSwgdHJ1ZSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAKICAgICAgICAvLyDlpoLmnpzmsqHmnInmlbDmja7vvIzmmL7npLrmj5DnpLrkv6Hmga8KICAgICAgICBpZiAodGhpcy5hdmFpbGFibGVEaXNlYXNlcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5pqC5peg5Y+v5YWz6IGU55qE55eF5a6z5pWw5o2uJykKICAgICAgICB9CiAgICAgICAgCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L2955eF5a6z5YiX6KGo5aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veeXheWus+WIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlScpCiAgICAgICAgCiAgICAgICAgLy8g5o+Q5L6b6buY6K6k55qE56S65L6L5pWw5o2uCiAgICAgICAgdGhpcy5hdmFpbGFibGVEaXNlYXNlcyA9IHRoaXMuZ2V0RGVmYXVsdERpc2Vhc2VEYXRhKCkKICAgICAgICB0aGlzLmRpc2Vhc2VUb3RhbCA9IHRoaXMuYXZhaWxhYmxlRGlzZWFzZXMubGVuZ3RoCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5kaXNlYXNlTG9hZGluZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOaQnOe0oueXheWuswogICAgc2VhcmNoRGlzZWFzZXMoKSB7CiAgICAgIHRoaXMuc2VhcmNoUGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMubG9hZEF2YWlsYWJsZURpc2Vhc2VzKCkKICAgIH0sCiAgICAKICAgIC8vIOmHjee9ruaQnOe0ogogICAgcmVzZXRTZWFyY2goKSB7CiAgICAgIHRoaXMuc2VhcmNoUGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGJyaWRnZU5hbWU6ICcnLAogICAgICAgIHR5cGU6ICcnCiAgICAgIH0KICAgICAgdGhpcy5sb2FkQXZhaWxhYmxlRGlzZWFzZXMoKQogICAgfSwKICAgIAogICAgLy8g55eF5a6z6YCJ5oup5Y+Y5YyWCiAgICBoYW5kbGVEaXNlYXNlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnNlbGVjdGVkRGlzZWFzZXMgPSBzZWxlY3Rpb24KICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhuW8ueeql+WFs+mXrQogICAgaGFuZGxlRGlhbG9nQ2xvc2UoZG9uZSkgewogICAgICAvLyDph43nva7pgInmi6nnirbmgIEKICAgICAgdGhpcy5zZWxlY3RlZERpc2Vhc2VzID0gW10KICAgICAgLy8g6YeN572u5pCc57Si5p2h5Lu2CiAgICAgIHRoaXMuc2VhcmNoUGFyYW1zID0gewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIGJyaWRnZU5hbWU6ICcnLAogICAgICAgIHR5cGU6ICcnCiAgICAgIH0KICAgICAgCiAgICAgIC8vIOWmguaenOaciWRvbmXlm57osIPvvIzosIPnlKjlroPvvJvlkKbliJnnm7TmjqXlhbPpl60KICAgICAgaWYgKHR5cGVvZiBkb25lID09PSAnZnVuY3Rpb24nKSB7CiAgICAgICAgZG9uZSgpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zaG93RGlzZWFzZURpYWxvZyA9IGZhbHNlCiAgICAgIH0KICAgIH0sCgogICAgLy8g56Gu6K6k55eF5a6z6YCJ5oupCiAgICBjb25maXJtRGlzZWFzZVNlbGVjdGlvbigpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWREaXNlYXNlcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWFs+iBlOeahOeXheWusycpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOWQiOW5tuW3suacieeXheWus+WSjOaWsOmAieaLqeeahOeXheWuswogICAgICBjb25zdCBleGlzdGluZ0lkcyA9IHRoaXMuZGlzZWFzZUxpc3QubWFwKGRpc2Vhc2UgPT4gZGlzZWFzZS5pZCkKICAgICAgY29uc3QgbmV3RGlzZWFzZXMgPSB0aGlzLnNlbGVjdGVkRGlzZWFzZXMuZmlsdGVyKGRpc2Vhc2UgPT4gCiAgICAgICAgIWV4aXN0aW5nSWRzLmluY2x1ZGVzKGRpc2Vhc2UuaWQpCiAgICAgICkKICAgICAgCiAgICAgIC8vIOajgOafpemHjeWkjeWFs+iBlAogICAgICBjb25zdCBkdXBsaWNhdGVDb3VudCA9IHRoaXMuc2VsZWN0ZWREaXNlYXNlcy5sZW5ndGggLSBuZXdEaXNlYXNlcy5sZW5ndGgKICAgICAgaWYgKGR1cGxpY2F0ZUNvdW50ID4gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg5bey6L+H5rukICR7ZHVwbGljYXRlQ291bnR9IOS4qumHjeWkjeeahOeXheWus2ApCiAgICAgIH0KICAgICAgCiAgICAgIGlmIChuZXdEaXNlYXNlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5kaXNlYXNlTGlzdCA9IFsuLi50aGlzLmRpc2Vhc2VMaXN0LCAuLi5uZXdEaXNlYXNlc10KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+WFs+iBlCAke25ld0Rpc2Vhc2VzLmxlbmd0aH0g5Liq55eF5a6zYCkKICAgICAgICB0aGlzLmVtaXRDaGFuZ2UoKQogICAgICB9IGVsc2UgaWYgKGR1cGxpY2F0ZUNvdW50ID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmnKrpgInmi6nmlrDnmoTnl4XlrrMnKQogICAgICB9CiAgICAgIAogICAgICAvLyDlhbPpl63lvLnnqpcKICAgICAgdGhpcy5oYW5kbGVEaWFsb2dDbG9zZSgpCiAgICB9LAogICAgCiAgICAvLyDnp7vpmaTnl4XlrrPlhbPogZQKICAgIHJlbW92ZURpc2Vhc2VBc3NvY2lhdGlvbihpbmRleCkgewogICAgICB0aGlzLmRpc2Vhc2VMaXN0LnNwbGljZShpbmRleCwgMSkKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7Lnp7vpmaTnl4XlrrPlhbPogZQnKQogICAgICB0aGlzLmVtaXRDaGFuZ2UoKQogICAgfSwKICAgIAogICAgLy8g55eF5a6z5YiG6aG15aSn5bCP5Y+Y5YyWCiAgICBoYW5kbGVEaXNlYXNlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5zZWFyY2hQYXJhbXMucGFnZVNpemUgPSB2YWwKICAgICAgdGhpcy5sb2FkQXZhaWxhYmxlRGlzZWFzZXMoKQogICAgfSwKICAgIAogICAgLy8g55eF5a6z5b2T5YmN6aG15Y+Y5YyWCiAgICBoYW5kbGVEaXNlYXNlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5zZWFyY2hQYXJhbXMucGFnZU51bSA9IHZhbAogICAgICB0aGlzLmxvYWRBdmFpbGFibGVEaXNlYXNlcygpCiAgICB9LAogICAgCiAgICAvLyDojrflj5bpu5jorqTnl4XlrrPmlbDmja7vvIjnlKjkuo5BUEnlpLHotKXml7bnmoTpmY3nuqflpITnkIbvvIkKICAgIGdldERlZmF1bHREaXNlYXNlRGF0YSgpIHsKICAgICAgcmV0dXJuIFsKICAgICAgICB7CiAgICAgICAgICBpZDogOTg5LAogICAgICAgICAgYnJpZGdlTmFtZTogJ+a5mOaxn+Wkp+ahpScsCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4OScsCiAgICAgICAgICBkaXNlYXNlUGFydDogJ+S8uOe8qee8nScsCiAgICAgICAgICBkaXNlYXNlVHlwZTogJ+S8uOe8qee8nee8uuWksScsCiAgICAgICAgICBkaXNlYXNlQ291bnQ6IDcsCiAgICAgICAgICBkaXNlYXNlRGVzY3JpcHRpb246ICfmoaXmooHkuJzkvqfkvLjnvKnnvJ3lrZjlnKjnvLrlpLHvvIzlvbHlk43ooYzovablronlhagnLAogICAgICAgICAgZGlzZWFzZUxldmVsOiAzLAogICAgICAgICAgcmVwb3J0ZXI6ICflvKDkuIknLAogICAgICAgICAgcmVwb3J0VGltZTogJzIwMjUtMDktMDEgMDk6MzA6MDAnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogOTg4LAogICAgICAgICAgYnJpZGdlTmFtZTogJ+a5mOaxn+Wkp+ahpScsCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4OCcsCiAgICAgICAgICBkaXNlYXNlUGFydDogJ+S8uOe8qee8nScsCiAgICAgICAgICBkaXNlYXNlVHlwZTogJ+S8uOe8qee8nee8uuWksScsCiAgICAgICAgICBkaXNlYXNlQ291bnQ6IDQ3LAogICAgICAgICAgZGlzZWFzZURlc2NyaXB0aW9uOiAn5qGl5qKB6KW/5L6n5aSa5aSE5Ly457yp57yd5a2Y5Zyo57y65aSx546w6LGhJywKICAgICAgICAgIGRpc2Vhc2VMZXZlbDogMiwKICAgICAgICAgIHJlcG9ydGVyOiAn5p2O5ZubJywKICAgICAgICAgIHJlcG9ydFRpbWU6ICcyMDI1LTA5LTAyIDE0OjIwOjAwJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDk4NywKICAgICAgICAgIGJyaWRnZU5hbWU6ICfmtY/pmLPmsrPlpKfmoaUnLAogICAgICAgICAgZGlzZWFzZUNvZGU6ICc5ODcnLAogICAgICAgICAgZGlzZWFzZVBhcnQ6ICfnhafmmI7orr7mlr0nLAogICAgICAgICAgZGlzZWFzZVR5cGU6ICfnhafmmI7orr7mlr3nvLrlpLEnLAogICAgICAgICAgZGlzZWFzZUNvdW50OiA0MiwKICAgICAgICAgIGRpc2Vhc2VEZXNjcmlwdGlvbjogJ+ahpeaigeeFp+aYjuiuvuaWveiAgeWMlu+8jOmDqOWIhui3r+auteeFp+aYjuS4jei2sycsCiAgICAgICAgICBkaXNlYXNlTGV2ZWw6IDEsCiAgICAgICAgICByZXBvcnRlcjogJ+eOi+S6lCcsCiAgICAgICAgICByZXBvcnRUaW1lOiAnMjAyNS0wOS0wMyAxNjo0NTowMCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiA5ODYsCiAgICAgICAgICBicmlkZ2VOYW1lOiAn5qmY5a2Q5rSy5aSn5qGlJywKICAgICAgICAgIGRpc2Vhc2VDb2RlOiAnOTg2JywKICAgICAgICAgIGRpc2Vhc2VQYXJ0OiAn5oqk5qCPJywKICAgICAgICAgIGRpc2Vhc2VUeXBlOiAn5oqk5qCP5o2f5Z2PJywKICAgICAgICAgIGRpc2Vhc2VDb3VudDogMTUsCiAgICAgICAgICBkaXNlYXNlRGVzY3JpcHRpb246ICfmoaXmooHmiqTmoI/pg6jliIbmrrXokL3lrZjlnKjmjZ/lnY/vvIzpnIDopoHlj4rml7bkv67lpI0nLAogICAgICAgICAgZGlzZWFzZUxldmVsOiAyLAogICAgICAgICAgcmVwb3J0ZXI6ICfotbXlha0nLAogICAgICAgICAgcmVwb3J0VGltZTogJzIwMjUtMDktMDQgMTA6MTU6MDAnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogOTg1LAogICAgICAgICAgYnJpZGdlTmFtZTogJ+mTtuebhuWyreWkp+ahpScsCiAgICAgICAgICBkaXNlYXNlQ29kZTogJzk4NScsCiAgICAgICAgICBkaXNlYXNlUGFydDogJ+ahpemdoicsCiAgICAgICAgICBkaXNlYXNlVHlwZTogJ+ahpemdouegtOaNnycsCiAgICAgICAgICBkaXNlYXNlQ291bnQ6IDIzLAogICAgICAgICAgZGlzZWFzZURlc2NyaXB0aW9uOiAn5qGl6Z2i5rKl6Z2S5Ye6546w6KOC57yd5ZKM5Z2R5rSe77yM5b2x5ZON6KGM6L2m6IiS6YCC5oCnJywKICAgICAgICAgIGRpc2Vhc2VMZXZlbDogMywKICAgICAgICAgIHJlcG9ydGVyOiAn5a2Z5LiDJywKICAgICAgICAgIHJlcG9ydFRpbWU6ICcyMDI1LTA5LTA1IDE1OjMwOjAwJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6IDk4NCwKICAgICAgICAgIGJyaWRnZU5hbWU6ICfnjLTlrZDnn7PlpKfmoaUnLAogICAgICAgICAgZGlzZWFzZUNvZGU6ICc5ODQnLAogICAgICAgICAgZGlzZWFzZVBhcnQ6ICfmjpLmsLTns7vnu58nLAogICAgICAgICAgZGlzZWFzZVR5cGU6ICfmjpLmsLTkuI3nlYUnLAogICAgICAgICAgZGlzZWFzZUNvdW50OiA4LAogICAgICAgICAgZGlzZWFzZURlc2NyaXB0aW9uOiAn5qGl5qKB5o6S5rC057O757uf5aC15aGe77yM6Zuo5a2j56ev5rC05Lil6YeNJywKICAgICAgICAgIGRpc2Vhc2VMZXZlbDogMiwKICAgICAgICAgIHJlcG9ydGVyOiAn5ZGo5YWrJywKICAgICAgICAgIHJlcG9ydFRpbWU6ICcyMDI1LTA5LTA2IDA4OjQ1OjAwJwogICAgICAgIH0KICAgICAgXQogICAgfSwKCiAgICAvLyDooajljZXpqozor4EKICAgIHZhbGlkYXRlKCkgewogICAgICAvLyDnl4XlrrPlhbPogZTmmK/lj6/pgInnmoTvvIzmiYDku6XmgLvmmK/ov5Tlm550cnVlCiAgICAgIHJldHVybiB0cnVlCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["DiseaseConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "DiseaseConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"disease-config\">\n    <!-- 添加病害按钮 - 复用ProjectConfig的样式 -->\n    <div v-if=\"!readonly\" class=\"add-project-row\" style=\"margin-bottom: 24px;\">\n      <el-button \n        type=\"primary\" \n        icon=\"el-icon-link\"\n        class=\"add-project-btn\"\n        @click=\"showDiseaseDialog = true\"\n      >\n        关联病害\n      </el-button>\n    </div>\n    \n    <!-- 已关联病害列表 - 复用通用表格样式 -->\n    <div class=\"common-table\">\n      <el-table\n        :data=\"diseaseList\"\n        class=\"maintenance-table\"\n        empty-text=\"暂无关联病害\"\n        style=\"width: 100%\"\n        :row-style=\"{ height: '32px' }\"\n        size=\"small\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        \n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\" />\n        \n        <el-table-column prop=\"diseaseDescription\" label=\"病害描述\" min-width=\"150\" show-overflow-tooltip />\n        \n        <el-table-column v-if=\"!readonly\" label=\"操作\" width=\"80\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              class=\"maintenance-danger-text\"\n              @click=\"removeDiseaseAssociation(scope.$index)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <!-- 病害选择弹窗 -->\n    <el-dialog\n      title=\"关联病害\"\n      :visible.sync=\"showDiseaseDialog\"\n      custom-class=\"disease-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\n      :close-on-click-modal=\"false\"\n      :before-close=\"handleDialogClose\"\n      :modal-append-to-body=\"true\"\n      :append-to-body=\"true\"\n      top=\"5vh\"\n      destroy-on-close\n    >\n      <div class=\"dialog-content\">\n        <!-- 搜索表单 - 复用通用搜索表单样式 -->\n        <div class=\"search-form\">\n          <el-form :model=\"searchParams\" inline>\n            <el-form-item label=\"桥梁名称\">\n              <el-input\n                v-model=\"searchParams.bridgeName\"\n                placeholder=\"请输入桥梁名称\"\n                clearable\n                style=\"width: 200px\"\n              />\n            </el-form-item>\n            \n            <el-form-item label=\"病害类型\">\n              <el-select\n                v-model=\"searchParams.type\"\n                placeholder=\"请选择病害类型\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"type in diseaseTypes\"\n                  :key=\"type.value\"\n                  :label=\"type.label\"\n                  :value=\"type.label\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item>\n              <el-button type=\"primary\" @click=\"searchDiseases\">查询</el-button>\n              <el-button @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        \n        <!-- 病害列表 - 复用通用表格样式 -->\n        <div class=\"common-table\">\n          <el-table\n            ref=\"diseaseSelectionTable\"\n            v-loading=\"diseaseLoading\"\n            :data=\"availableDiseases\"\n            class=\"maintenance-table\"\n            style=\"width: 100%\"\n            :row-style=\"{ height: '32px' }\"\n            size=\"small\"\n            @selection-change=\"handleDiseaseSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\" />\n            \n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n            \n            <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n            \n            <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseDescription\" label=\"病害描述\" min-width=\"150\" show-overflow-tooltip />\n          </el-table>\n        </div>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"searchParams.pageNum\"\n            :page-sizes=\"[10, 20, 50]\"\n            :page-size=\"searchParams.pageSize\"\n            :total=\"diseaseTotal\"\n            layout=\"total, sizes, prev, pager, next\"\n            @size-change=\"handleDiseaseSizeChange\"\n            @current-change=\"handleDiseaseCurrentChange\"\n          />\n        </div>\n      </div>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleDialogClose\">取消</el-button>\n        <el-button \n          type=\"primary\" \n          @click=\"confirmDiseaseSelection\"\n          :disabled=\"selectedDiseases.length === 0\"\n        >\n          确定 {{ selectedDiseases.length > 0 ? `(已选择 ${selectedDiseases.length} 项)` : '' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getDiseaseList } from '@/api/maintenance/projects'\n\nexport default {\n  name: 'DiseaseConfig',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      diseaseList: [],\n      showDiseaseDialog: false,\n      diseaseLoading: false,\n      availableDiseases: [],\n      selectedDiseases: [],\n      diseaseTotal: 0,\n      \n      // 搜索参数\n      searchParams: {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      },\n      \n      // 病害类型选项\n      diseaseTypes: [\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\n        { label: '照明设施缺失', value: 'lighting_missing' },\n        { label: '护栏损坏', value: 'guardrail_damage' },\n        { label: '桥面破损', value: 'deck_damage' },\n        { label: '排水不畅', value: 'drainage_poor' }\n      ]\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && newVal.diseases && Array.isArray(newVal.diseases)) {\n          // 只在数据真正不同时才更新，避免循环\n          if (JSON.stringify(newVal.diseases) !== JSON.stringify(this.diseaseList)) {\n            this.diseaseList = [...newVal.diseases]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    showDiseaseDialog(visible) {\n      if (visible) {\n        this.loadAvailableDiseases()\n      }\n    }\n  },\n  methods: {\n    // 统一的数据更新方法\n    emitChange() {\n      this.$nextTick(() => {\n        this.$emit('input', {\n          diseases: this.diseaseList\n        })\n      })\n    },\n    \n    // 加载可用病害列表\n    async loadAvailableDiseases() {\n      this.diseaseLoading = true\n      try {\n        const response = await getDiseaseList(this.searchParams)\n        console.log('病害列表API响应:', response)\n        \n        // 适配不同的API响应格式\n        let diseases = []\n        let total = 0\n        \n        console.log('API响应结构:', response)\n        \n        if (response.data) {\n          // 优先使用 data.rows (模拟API格式)\n          if (response.data.rows) {\n            diseases = response.data.rows\n            total = response.data.total || 0\n          }\n          // 其次使用 data.list (标准格式)\n          else if (response.data.list) {\n            diseases = response.data.list\n            total = response.data.total || 0\n          }\n          // 最后直接使用 data (简单格式)\n          else if (Array.isArray(response.data)) {\n            diseases = response.data\n            total = response.data.length\n          }\n        }\n        \n        console.log('解析后的病害数据:', diseases, '总数:', total)\n        \n        // 数据字段映射和标准化\n        this.availableDiseases = diseases.map(disease => ({\n          id: disease.id,\n          bridgeName: disease.bridgeName || '未知桥梁',\n          diseaseCode: disease.diseaseCode || disease.code || '-',\n          diseasePart: disease.location || disease.diseasePart || '-', // 修正：使用 location 字段\n          diseaseType: disease.type || disease.diseaseType || '-', // 修正：使用 type 字段\n          diseaseCount: disease.quantity || disease.diseaseCount || 0, // 修正：使用 quantity 字段\n          diseaseDescription: disease.description || disease.diseaseDescription || '-', // 修正：使用 description 字段\n          diseaseLevel: disease.level || disease.diseaseLevel || 1, // 修正：使用 level 字段\n          reporter: disease.reportPerson || disease.reporter || '-', // 修正：使用 reportPerson 字段\n          reportTime: disease.reportTime || new Date().toISOString()\n        }))\n        \n        this.diseaseTotal = total\n        \n        // 设置已选中的病害\n        this.$nextTick(() => {\n          if (this.$refs.diseaseSelectionTable) {\n            this.availableDiseases.forEach(disease => {\n              const isSelected = this.diseaseList.some(selected => selected.id === disease.id)\n              if (isSelected) {\n                this.$refs.diseaseSelectionTable.toggleRowSelection(disease, true)\n              }\n            })\n          }\n        })\n        \n        // 如果没有数据，显示提示信息\n        if (this.availableDiseases.length === 0) {\n          this.$message.info('暂无可关联的病害数据')\n        }\n        \n      } catch (error) {\n        console.error('加载病害列表失败:', error)\n        this.$message.error('加载病害列表失败，请稍后重试')\n        \n        // 提供默认的示例数据\n        this.availableDiseases = this.getDefaultDiseaseData()\n        this.diseaseTotal = this.availableDiseases.length\n      } finally {\n        this.diseaseLoading = false\n      }\n    },\n    \n    // 搜索病害\n    searchDiseases() {\n      this.searchParams.pageNum = 1\n      this.loadAvailableDiseases()\n    },\n    \n    // 重置搜索\n    resetSearch() {\n      this.searchParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      }\n      this.loadAvailableDiseases()\n    },\n    \n    // 病害选择变化\n    handleDiseaseSelectionChange(selection) {\n      this.selectedDiseases = selection\n    },\n    \n    // 处理弹窗关闭\n    handleDialogClose(done) {\n      // 重置选择状态\n      this.selectedDiseases = []\n      // 重置搜索条件\n      this.searchParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        type: ''\n      }\n      \n      // 如果有done回调，调用它；否则直接关闭\n      if (typeof done === 'function') {\n        done()\n      } else {\n        this.showDiseaseDialog = false\n      }\n    },\n\n    // 确认病害选择\n    confirmDiseaseSelection() {\n      if (this.selectedDiseases.length === 0) {\n        this.$message.warning('请先选择要关联的病害')\n        return\n      }\n\n      // 合并已有病害和新选择的病害\n      const existingIds = this.diseaseList.map(disease => disease.id)\n      const newDiseases = this.selectedDiseases.filter(disease => \n        !existingIds.includes(disease.id)\n      )\n      \n      // 检查重复关联\n      const duplicateCount = this.selectedDiseases.length - newDiseases.length\n      if (duplicateCount > 0) {\n        this.$message.warning(`已过滤 ${duplicateCount} 个重复的病害`)\n      }\n      \n      if (newDiseases.length > 0) {\n        this.diseaseList = [...this.diseaseList, ...newDiseases]\n        this.$message.success(`成功关联 ${newDiseases.length} 个病害`)\n        this.emitChange()\n      } else if (duplicateCount === 0) {\n        this.$message.info('未选择新的病害')\n      }\n      \n      // 关闭弹窗\n      this.handleDialogClose()\n    },\n    \n    // 移除病害关联\n    removeDiseaseAssociation(index) {\n      this.diseaseList.splice(index, 1)\n      this.$message.success('已移除病害关联')\n      this.emitChange()\n    },\n    \n    // 病害分页大小变化\n    handleDiseaseSizeChange(val) {\n      this.searchParams.pageSize = val\n      this.loadAvailableDiseases()\n    },\n    \n    // 病害当前页变化\n    handleDiseaseCurrentChange(val) {\n      this.searchParams.pageNum = val\n      this.loadAvailableDiseases()\n    },\n    \n    // 获取默认病害数据（用于API失败时的降级处理）\n    getDefaultDiseaseData() {\n      return [\n        {\n          id: 989,\n          bridgeName: '湘江大桥',\n          diseaseCode: '989',\n          diseasePart: '伸缩缝',\n          diseaseType: '伸缩缝缺失',\n          diseaseCount: 7,\n          diseaseDescription: '桥梁东侧伸缩缝存在缺失，影响行车安全',\n          diseaseLevel: 3,\n          reporter: '张三',\n          reportTime: '2025-09-01 09:30:00'\n        },\n        {\n          id: 988,\n          bridgeName: '湘江大桥',\n          diseaseCode: '988',\n          diseasePart: '伸缩缝',\n          diseaseType: '伸缩缝缺失',\n          diseaseCount: 47,\n          diseaseDescription: '桥梁西侧多处伸缩缝存在缺失现象',\n          diseaseLevel: 2,\n          reporter: '李四',\n          reportTime: '2025-09-02 14:20:00'\n        },\n        {\n          id: 987,\n          bridgeName: '浏阳河大桥',\n          diseaseCode: '987',\n          diseasePart: '照明设施',\n          diseaseType: '照明设施缺失',\n          diseaseCount: 42,\n          diseaseDescription: '桥梁照明设施老化，部分路段照明不足',\n          diseaseLevel: 1,\n          reporter: '王五',\n          reportTime: '2025-09-03 16:45:00'\n        },\n        {\n          id: 986,\n          bridgeName: '橘子洲大桥',\n          diseaseCode: '986',\n          diseasePart: '护栏',\n          diseaseType: '护栏损坏',\n          diseaseCount: 15,\n          diseaseDescription: '桥梁护栏部分段落存在损坏，需要及时修复',\n          diseaseLevel: 2,\n          reporter: '赵六',\n          reportTime: '2025-09-04 10:15:00'\n        },\n        {\n          id: 985,\n          bridgeName: '银盆岭大桥',\n          diseaseCode: '985',\n          diseasePart: '桥面',\n          diseaseType: '桥面破损',\n          diseaseCount: 23,\n          diseaseDescription: '桥面沥青出现裂缝和坑洞，影响行车舒适性',\n          diseaseLevel: 3,\n          reporter: '孙七',\n          reportTime: '2025-09-05 15:30:00'\n        },\n        {\n          id: 984,\n          bridgeName: '猴子石大桥',\n          diseaseCode: '984',\n          diseasePart: '排水系统',\n          diseaseType: '排水不畅',\n          diseaseCount: 8,\n          diseaseDescription: '桥梁排水系统堵塞，雨季积水严重',\n          diseaseLevel: 2,\n          reporter: '周八',\n          reportTime: '2025-09-06 08:45:00'\n        }\n      ]\n    },\n\n    // 表单验证\n    validate() {\n      // 病害关联是可选的，所以总是返回true\n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.disease-config {\n  // 复用通用样式，无需自定义样式\n}\n</style>\n"]}]}