{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue", "mtime": 1758808367674}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BridgeSelector.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BridgeSelector.vue", "sourceRoot": "src/components/Maintenance", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"关联桥梁\"\n    :visible.sync=\"dialogVisible\"\n    custom-class=\"bridge-selector-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\n    :close-on-click-modal=\"false\"\n    :modal-append-to-body=\"true\"\n    :append-to-body=\"true\"\n    top=\"5vh\"\n    destroy-on-close\n  >\n    <div class=\"dialog-content\">\n      <!-- 搜索表单 - 复用通用搜索表单样式 -->\n      <div class=\"search-form\">\n        <el-form :model=\"queryParams\" inline>\n          <el-form-item label=\"桥梁名称\">\n            <el-input\n              v-model=\"queryParams.name\"\n              placeholder=\"输入桥梁名称或编号\"\n              clearable\n              style=\"width: 200px\"\n              @keyup.enter.native=\"handleQuery\"\n              @clear=\"handleQuery\"\n            />\n          </el-form-item>\n          \n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      \n      \n      <!-- 桥梁列表 - 复用通用表格样式 -->\n      <div class=\"common-table\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"bridgeList\"\n          class=\"maintenance-table\"\n          style=\"width: 100%\"\n          :row-style=\"{ height: '32px' }\"\n          size=\"small\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column\n            type=\"selection\"\n            width=\"55\"\n            :selectable=\"isSelectable\"\n          />\n          \n          <el-table-column prop=\"project\" label=\"养护项目\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"maintainer\" label=\"养护人员\" width=\"100\" align=\"center\" />\n          \n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n          \n          <el-table-column prop=\"name\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"code\" label=\"桥梁编号\" width=\"120\" align=\"center\" />\n          \n          <el-table-column prop=\"road\" label=\"所在道路\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        </el-table>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"dialogVisible = false\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\">\n        确定 ({{ selectedBridges.length }})\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getBridgeList } from '@/api/maintenance/projects'\n\nexport default {\n  name: 'BridgeSelector',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    multiple: {\n      type: Boolean,\n      default: true\n    },\n    selectedData: {\n      type: Array,\n      default: () => []\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge' // bridge, tunnel\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      bridgeList: [],\n      selectedBridges: [],\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: 'bridge',\n        name: '',\n        district: '',\n        managementUnit: ''\n      },\n      \n      // 选项数据\n      districtOptions: [\n        { label: '岳麓区', value: 'yuelu' },\n        { label: '芙蓉区', value: 'furong' },\n        { label: '天心区', value: 'tianxin' },\n        { label: '开福区', value: 'kaifu' },\n        { label: '雨花区', value: 'yuhua' },\n        { label: '望城区', value: 'wangcheng' }\n      ],\n      \n      unitOptions: [\n        { label: '长沙市桥梁管理处', value: 'changsha_bridge' },\n        { label: '长沙市隧道管理处', value: 'changsha_tunnel' },\n        { label: '岳麓区市政局', value: 'yuelu_municipal' },\n        { label: '芙蓉区市政局', value: 'furong_municipal' }\n      ]\n    }\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.visible\n      },\n      set(val) {\n        this.$emit('update:visible', val)\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initData()\n        this.getList()\n      }\n    },\n    \n    infrastructureType: {\n      immediate: true,\n      handler(val) {\n        this.queryParams.infrastructureType = val\n      }\n    }\n  },\n  methods: {\n    // 初始化数据\n    initData() {\n      this.selectedBridges = [...this.selectedData]\n      this.queryParams.infrastructureType = this.infrastructureType\n    },\n    \n    // 获取桥梁列表\n    async getList() {\n      try {\n        this.loading = true\n        const response = await getBridgeList(this.queryParams)\n        if (response.data) {\n          let bridgeData = response.data.rows || response.data.list || []\n          \n          // 为每条数据添加结构图所需的字段\n          this.bridgeList = bridgeData.map((bridge, index) => ({\n            ...bridge,\n            project: bridge.project || 'XXXXXX大桥', // 养护项目\n            maintainer: bridge.maintainer || ['黄昭言', '刘雨桐', '罗砚秋'][index % 3], // 养护人员\n            code: bridge.code || `CS-B-${String(index + 1).padStart(3, '0')}`, // 桥梁编号\n            road: bridge.road || '枫林一路', // 所在道路\n            managementUnit: bridge.managementUnit || '桥隧中心' // 管理单位\n          }))\n          this.total = response.data.total || 0\n        } else {\n          // 如果接口失败，使用模拟数据展示结构\n          this.bridgeList = [\n            {\n              id: 1,\n              project: 'XXXXXX大桥',\n              maintainer: '黄昭言',\n              name: 'XXX大桥',\n              code: 'CS-B-001',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 2,\n              project: 'XXXXXX大桥',\n              maintainer: '刘雨桐',\n              name: '刘雨桐',\n              code: 'CS-B-002', \n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 3,\n              project: 'XXXXXX大桥',\n              maintainer: '罗砚秋',\n              name: '罗砚秋',\n              code: 'CS-B-003',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            }\n          ]\n          this.total = 3\n        }\n      } catch (error) {\n        console.error('获取桥梁列表失败:', error)\n        this.$message.error('获取桥梁列表失败')\n        \n        // 错误时也显示模拟数据以展示结构\n        this.bridgeList = [\n          {\n            id: 1,\n            project: 'XXXXXX大桥',\n            maintainer: '黄昭言',\n            name: 'XXX大桥',\n            code: 'CS-B-001',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 2,\n            project: 'XXXXXX大桥',\n            maintainer: '刘雨桐',\n            name: '刘雨桐',\n            code: 'CS-B-002',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 3,\n            project: 'XXXXXX大桥',\n            maintainer: '罗砚秋',\n            name: '罗砚秋',\n            code: 'CS-B-003',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          }\n        ]\n        this.total = 3\n        \n        // 设置已选中的行\n        this.$nextTick(() => {\n          if (this.$refs.bridgeTable && this.selectedData && this.selectedData.length > 0) {\n            this.bridgeList.forEach(bridge => {\n              const isSelected = this.selectedData.some(selected => selected.id === bridge.id)\n              if (isSelected) {\n                this.$refs.bridgeTable.toggleRowSelection(bridge, true)\n              }\n            })\n          }\n        })\n        \n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: this.infrastructureType,\n        name: '',\n        district: '',\n        managementUnit: ''\n      }\n      this.getList()\n    },\n    \n    // 选择桥梁\n    selectBridge(bridge) {\n      if (!this.multiple) {\n        this.selectedBridges = [bridge]\n      } else {\n        if (!this.isSelected(bridge)) {\n          this.selectedBridges.push(bridge)\n        }\n      }\n    },\n    \n    // 移除选择\n    removeSelected(bridge) {\n      const index = this.selectedBridges.findIndex(item => item.id === bridge.id)\n      if (index > -1) {\n        this.selectedBridges.splice(index, 1)\n      }\n    },\n    \n    // 清空选择\n    clearSelected() {\n      this.selectedBridges = []\n    },\n    \n    // 判断是否已选择\n    isSelected(bridge) {\n      return this.selectedBridges.some(item => item.id === bridge.id)\n    },\n    \n    // 判断是否可选择\n    isSelectable(row) {\n      return true // 允许选择和取消选择\n    },\n    \n    // 表格选择变化\n    handleSelectionChange(selection) {\n      if (this.multiple) {\n        // 直接使用当前页面的选择结果\n        this.selectedBridges = selection\n      }\n    },\n    \n    // 确认选择\n    confirmSelection() {\n      this.$emit('confirm', this.selectedBridges)\n      this.dialogVisible = false\n    },\n    \n    // 获取养护等级类型\n    getMaintenanceLevelType(level) {\n      const typeMap = {\n        '一级': 'danger',\n        '二级': 'warning',\n        '三级': 'info',\n        '四级': 'success'\n      }\n      return typeMap[level] || 'info'\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n:deep(.bridge-selector-dialog) {\n  // 弹窗样式覆盖 - 遵循设计文档6.7.7规范\n  .el-dialog {\n    background: #091A4B !important; // 与主要背景色一致\n    border-radius: 12px !important;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;\n    border: none !important;\n    width: 70vw !important;\n    max-width: 1200px !important;\n    min-width: 800px !important;\n  }\n  \n  .el-dialog__header {\n    background: #091A4B !important;\n    border-bottom: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    height: 56px !important; // 按设计文档规范\n    \n    .el-dialog__title {\n      color: #f8fafc !important; // 6.7.1 主要文字颜色\n      font-size: 18px !important;\n      font-weight: normal !important; // 正常字重\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n    }\n    \n    .el-dialog__close {\n      color: #94a3b8 !important;\n      font-size: 18px !important;\n      width: 32px !important;\n      height: 32px !important;\n      line-height: 32px !important;\n      \n      &:hover {\n        color: #ffffff !important;\n        background: #ef4444 !important;\n        border-radius: 50% !important;\n      }\n    }\n  }\n  \n  .el-dialog__body {\n    background: #091A4B !important;\n    padding: 0 !important; // 移除默认padding，让内容区域自行控制\n    color: #f8fafc !important;\n    max-height: calc(90vh - 120px) !important; // 按设计文档规范\n    overflow-y: auto !important;\n  }\n  \n  .el-dialog__footer {\n    background: #091A4B !important;\n    border-top: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    text-align: right !important;\n  }\n  \n  .selector-content {\n    padding: 24px !important; // 内容区域统一内边距\n    \n    // 搜索筛选区域 - 遵循6.7.8输入框组件样式\n    .search-section {\n      padding: 20px !important;\n      background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n      border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n      border-radius: 8px !important;\n      margin-bottom: 20px !important;\n      \n      // 搜索输入框样式\n      .el-input {\n        .el-input__inner {\n          background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n          border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n          border-radius: 8px !important; // 6.7.8 输入框圆角\n          height: 40px !important; // 6.7.8 输入框高度\n          font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          font-size: 16px !important;\n          font-weight: normal !important;\n          \n          &::placeholder {\n            color: rgba(255, 255, 255, 0.5) !important; // 6.7.8 占位符颜色\n          }\n          \n          &:focus {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n        }\n      }\n    }\n    \n    \n    // 桥梁列表表格 - 遵循6.7.4表格组件样式\n    .bridge-list {\n      background: transparent !important;\n      border: none !important;\n      border-radius: 8px !important;\n      \n      // 表格样式覆盖 - 完全遵循maintenance-theme.scss的表格样式\n      .el-table {\n        background: transparent !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n        border: none !important;\n        \n        // 无边框设计\n        &::before {\n          display: none !important;\n        }\n        \n        // 表头样式 - 完全按照设计文档6.7.4规范\n        .el-table__header-wrapper {\n          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n          \n          th {\n            background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n            color: #FFF !important;\n            font-size: 16px !important;\n            font-weight: 400 !important; // 正常字重\n            line-height: 140% !important; // 22.4px\n            height: 44px !important;\n            padding: 14px 12px !important;\n            border: 1px solid rgba(255, 255, 255, 0.1) !important;\n            text-align: center !important;\n            white-space: nowrap !important;\n            \n            &:last-child {\n              border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n            }\n          }\n        }\n        \n        // 数据行样式 - 完全按照设计文档6.7.4规范\n        .el-table__body-wrapper {\n          background: transparent !important;\n          \n          tr {\n            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            \n            // 移除双数行的背景色差异\n            &:nth-child(odd) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            &:nth-child(even) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            // 悬停效果\n            &:hover {\n              background: rgba(255, 255, 255, 0.05) !important;\n              \n              td {\n                background: rgba(255, 255, 255, 0.05) !important;\n              }\n            }\n            \n            // 最后一行无底部边框\n            &:last-child {\n              td {\n                border-bottom: none !important;\n              }\n            }\n            \n            td {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n              color: #FFF !important;\n              font-size: 14px !important;\n              font-weight: 400 !important;\n              line-height: 120% !important; // 16.8px\n              padding: 4px 12px !important;\n              border: 1px solid rgba(255, 255, 255, 0.1) !important;\n              text-align: center !important;\n              height: 32px !important;\n              \n              &:last-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n              }\n            }\n          }\n        }\n        \n        // 空状态样式\n        .el-table__empty-block {\n          background: transparent !important;\n          \n          .el-table__empty-text {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n          }\n        }\n      }\n      \n      // 复选框样式\n      .el-checkbox {\n        .el-checkbox__inner {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.2) !important;\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important;\n          }\n          \n          &.is-checked {\n            background: #5C9DFF !important; // 6.7.1 蓝色强调色\n            border-color: #5C9DFF !important;\n          }\n        }\n        \n        .el-checkbox__label {\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n        }\n      }\n      \n      .bridge-type {\n        display: flex !important;\n        align-items: center !important;\n        gap: 4px !important;\n        \n        i {\n          color: #5C9DFF !important; // 6.7.1 蓝色强调色\n        }\n      }\n      \n      .selected-btn {\n        color: #10b981 !important; // 绿色表示已选择\n        \n        &:hover {\n          color: #34d399 !important;\n        }\n      }\n      \n      // 分页容器样式\n      .pagination-container {\n        padding: 20px 0 !important;\n        text-align: center !important;\n        background: transparent !important;\n        \n        // 分页组件样式 - 遵循设计文档规范\n        .el-pagination {\n          .el-pagination__total,\n          .el-pagination__jump {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n            font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          }\n          \n          .btn-prev,\n          .btn-next,\n          .el-pager li {\n            background: transparent !important;\n            border: 1px solid #4b5563 !important;\n            color: #94a3b8 !important;\n            border-radius: 4px !important;\n            margin: 0 2px !important;\n            \n            &:hover {\n              background: #374151 !important;\n              color: #ffffff !important;\n              border-color: rgba(255, 255, 255, 0.4) !important;\n            }\n            \n            &.active {\n              background: #5C9DFF !important; // 6.7.1 蓝色强调色\n              color: #ffffff !important;\n              border-color: #5C9DFF !important;\n            }\n          }\n          \n          .el-pagination__jump {\n            .el-input__inner {\n              background: rgba(255, 255, 255, 0.1) !important;\n              border-color: rgba(255, 255, 255, 0.2) !important;\n              color: #f8fafc !important;\n              \n              &:focus {\n                border-color: rgba(255, 255, 255, 0.4) !important;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  \n  // 底部按钮样式 - 遵循设计文档规范\n  .dialog-footer {\n    text-align: right !important;\n    \n    .el-button {\n      min-width: 80px !important;\n      height: 36px !important;\n      border-radius: 6px !important;\n      font-size: 14px !important;\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n      font-weight: normal !important;\n      padding: 0 16px !important;\n      margin-left: 12px !important;\n      \n      // 默认按钮样式\n      &.el-button--default {\n        background: rgba(255, 255, 255, 0.1) !important;\n        border: 1px solid rgba(255, 255, 255, 0.3) !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        \n        &:hover {\n          background: rgba(255, 255, 255, 0.15) !important;\n          border-color: rgba(255, 255, 255, 0.4) !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.3) !important;\n          color: #f8fafc !important;\n        }\n      }\n      \n      // 主要按钮样式\n      &.el-button--primary {\n        background: #5C9DFF !important; // 6.7.1 蓝色强调色\n        border-color: #5C9DFF !important;\n        color: #ffffff !important;\n        \n        &:hover {\n          background: #74a7f5 !important; // 6.7.1 浅蓝色\n          border-color: #74a7f5 !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: #5C9DFF !important;\n          border-color: #5C9DFF !important;\n          color: #ffffff !important;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}