{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue?vue&type=style&index=0&id=683765ba&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\ProjectsView.vue", "mtime": 1758810696270}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsNCg0KLnByb2plY3RzLXZpZXcgew0KICAvLyDnrZvpgInlmajljLrln58NCiAgLmZpbHRlci1zZWN0aW9uIHsNCiAgICBiYWNrZ3JvdW5kOiAjMWUzYThhOw0KICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgDQogICAgLmZpbHRlci1yb3cgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBnYXA6IDE2cHg7DQogICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICANCiAgICAgIC5maWx0ZXItaXRlbSB7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgIG1pbi13aWR0aDogMTYwcHg7DQogICAgICAgIA0KICAgICAgICAuZmlsdGVyLXNlbGVjdCB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgDQogICAgICAgICAgOmRlZXAoLmVsLWlucHV0X19pbm5lcikgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzFhMjMzMjsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMzNzQxNTE7DQogICAgICAgICAgICBjb2xvcjogI2U1ZTdlYjsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgJjo6cGxhY2Vob2xkZXIgew0KICAgICAgICAgICAgICBjb2xvcjogIzljYTNhZjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzRmNDZlNTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIA0KICAgICAgICAgICAgJjpmb2N1cyB7DQogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzRmNDZlNTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgOmRlZXAoLmVsLWlucHV0X19zdWZmaXgpIHsNCiAgICAgICAgICAgIC5lbC1pbnB1dF9fc3VmZml4LWlubmVyIHsNCiAgICAgICAgICAgICAgY29sb3I6ICM5Y2EzYWY7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC5maWx0ZXItYWN0aW9ucyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGdhcDogMTJweDsNCiAgICAgICAgDQogICAgICAgIC5lbC1idXR0b24gew0KICAgICAgICAgICYtLXByaW1hcnkgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzNiODJmNjsNCiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzNiODJmNjsNCiAgICAgICAgICAgIA0KICAgICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyNTYzZWI7DQogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzI1NjNlYjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgDQogICAgICAgICAgJjpub3QoLmVsLWJ1dHRvbi0tcHJpbWFyeSkgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzM3NDE1MTsNCiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzM3NDE1MTsNCiAgICAgICAgICAgIGNvbG9yOiAjZTVlN2ViOw0KICAgICAgICAgICAgDQogICAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzRiNTU2MzsNCiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNGI1NTYzOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICANCiAgLy8g5a6M5oiQ6YeP57uf6K6hDQogIC5jb21wbGV0aW9uLXN0YXRzIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICAgIA0KICAgIC5zdGF0cy10ZXh0IHsNCiAgICAgIGNvbG9yOiAjZTVlN2ViOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICB9DQogIH0NCiAgDQogIC8vIOihqOagvOWMuuWfnw0KICAudGFibGUtc2VjdGlvbiB7DQogICAgLnByb2plY3RzLXRhYmxlIHsNCiAgICAgIEBleHRlbmQgLmNvbW1vbi10YWJsZTsNCiAgICB9DQogIH0NCn0NCg0KLy8g5LiL5ouJ5qGG6YCJ6aG55qC35byPDQo6ZGVlcCguZWwtc2VsZWN0LWRyb3Bkb3duKSB7DQogIGJhY2tncm91bmQ6ICMxYTIzMzIgIWltcG9ydGFudDsNCiAgYm9yZGVyOiAxcHggc29saWQgIzM3NDE1MSAhaW1wb3J0YW50Ow0KICANCiAgLmVsLXNlbGVjdC1kcm9wZG93bl9faXRlbSB7DQogICAgYmFja2dyb3VuZDogIzFhMjMzMiAhaW1wb3J0YW50Ow0KICAgIGNvbG9yOiAjZTVlN2ViICFpbXBvcnRhbnQ7DQogICAgDQogICAgJjpob3ZlciB7DQogICAgICBiYWNrZ3JvdW5kOiAjMWUzYThhICFpbXBvcnRhbnQ7DQogICAgfQ0KICAgIA0KICAgICYuc2VsZWN0ZWQgew0KICAgICAgYmFja2dyb3VuZDogIzFlM2E4YSAhaW1wb3J0YW50Ow0KICAgICAgY29sb3I6ICMzYjgyZjYgIWltcG9ydGFudDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["ProjectsView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkYA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectsView.vue", "sourceRoot": "src/views/maintenance/repairs/components/detail", "sourcesContent": ["<template>\r\n  <div class=\"projects-view maintenance-theme\">\r\n    <!-- 筛选器区域 -->\r\n    <div class=\"filter-section\">\r\n      <div class=\"filter-row\">\r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.bridgeName\" \r\n            placeholder=\"桥梁名称\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"bridge in bridgeOptions\" \r\n              :key=\"bridge.value\" \r\n              :label=\"bridge.label\" \r\n              :value=\"bridge.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.projectType\" \r\n            placeholder=\"养护项目\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"project in projectOptions\" \r\n              :key=\"project.value\" \r\n              :label=\"project.label\" \r\n              :value=\"project.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.status\" \r\n            placeholder=\"状态\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"status in statusOptions\" \r\n              :key=\"status.value\" \r\n              :label=\"status.label\" \r\n              :value=\"status.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-item\">\r\n          <el-select \r\n            v-model=\"filters.responsible\" \r\n            placeholder=\"负责人\" \r\n            clearable\r\n            class=\"filter-select\"\r\n          >\r\n            <el-option \r\n              v-for=\"person in responsibleOptions\" \r\n              :key=\"person.value\" \r\n              :label=\"person.label\" \r\n              :value=\"person.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        \r\n        <div class=\"filter-actions\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleReset\">重置</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 完成量统计 -->\r\n    <div class=\"completion-stats\">\r\n      <span class=\"stats-text\">完成量 {{ completedCount }}/{{ totalCount }}</span>\r\n    </div>\r\n    \r\n    <!-- 数据表格 -->\r\n    <div class=\"table-section\">\r\n      <el-table \r\n        :data=\"filteredTableData\" \r\n        class=\"projects-table\"\r\n        header-row-class-name=\"table-header\"\r\n        row-class-name=\"table-row\"\r\n      >\r\n        <el-table-column prop=\"serialNumber\" label=\"序号\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"140\" />\r\n        <el-table-column prop=\"projectType\" label=\"养护项目\" min-width=\"140\" />\r\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-tag \r\n              :type=\"getStatusType(scope.row.status)\" \r\n              size=\"small\"\r\n              class=\"status-tag\"\r\n            >\r\n              {{ scope.row.status }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"responsible\" label=\"负责人\" width=\"100\" align=\"center\" />\r\n        <el-table-column prop=\"contactPhone\" label=\"联系方式\" width=\"130\" />\r\n        <el-table-column prop=\"completionTime\" label=\"完成时间\" width=\"160\" />\r\n        <el-table-column label=\"操作\" width=\"80\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\" \r\n              @click=\"handleViewDetail(scope.row)\"\r\n              class=\"action-btn\"\r\n            >\r\n              详情\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    \r\n    <!-- 项目详情弹窗 -->\r\n    <ProjectDetailDialog \r\n      :visible.sync=\"detailDialogVisible\"\r\n      :project-info=\"selectedProject\"\r\n      @close=\"handleDetailDialogClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ProjectDetailDialog from './ProjectDetailDialog.vue'\r\n\r\nexport default {\r\n  name: 'ProjectsView',\r\n  components: {\r\n    ProjectDetailDialog\r\n  },\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 筛选器数据\r\n      filters: {\r\n        bridgeName: '',\r\n        projectType: '',\r\n        status: '',\r\n        responsible: ''\r\n      },\r\n      \r\n      // 筛选器选项\r\n      bridgeOptions: [\r\n        { label: 'XXXXXX大桥', value: 'bridge1' },\r\n        { label: 'YYYYYY大桥', value: 'bridge2' },\r\n        { label: 'ZZZZZZ大桥', value: 'bridge3' }\r\n      ],\r\n      \r\n      projectOptions: [\r\n        { label: '排水系统养护', value: 'drainage' },\r\n        { label: '上部结构养护', value: 'superstructure' },\r\n        { label: '下部结构养护', value: 'substructure' },\r\n        { label: '桥面系养护', value: 'deck' }\r\n      ],\r\n      \r\n      statusOptions: [\r\n        { label: '未完成', value: 'pending' },\r\n        { label: '审核中', value: 'reviewing' },\r\n        { label: '复核中', value: 'rechecking' },\r\n        { label: '退回', value: 'rejected' },\r\n        { label: '已完成', value: 'completed' }\r\n      ],\r\n      \r\n      responsibleOptions: [\r\n        { label: '黄昭言', value: 'huang' },\r\n        { label: '刘雨桐', value: 'liu' },\r\n        { label: '罗颖秋', value: 'luo' },\r\n        { label: '林文龙', value: 'lin' },\r\n        { label: '高枕书', value: 'gao' },\r\n        { label: '徐桧桐', value: 'xu' },\r\n        { label: '何叔川', value: 'he' },\r\n        { label: '郭云舟', value: 'guo' },\r\n        { label: '黄梓航', value: 'huang2' },\r\n        { label: '赵景深', value: 'zhao' }\r\n      ],\r\n      \r\n      // 表格数据\r\n      tableData: [\r\n        {\r\n          serialNumber: '1',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '未完成',\r\n          responsible: '黄昭言',\r\n          contactPhone: '15820007394',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '2',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '审核中',\r\n          responsible: '刘雨桐',\r\n          contactPhone: '13122238579',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '3',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '排水系统养护',\r\n          status: '退回',\r\n          responsible: '罗颖秋',\r\n          contactPhone: '19620059483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '4',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '复核中',\r\n          responsible: '林文龙',\r\n          contactPhone: '19607559483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '5',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '高枕书',\r\n          contactPhone: '18557189483',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '6',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '徐桧桐',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '007',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '何叔川',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '008',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '郭云舟',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '009',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '黄梓航',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        },\r\n        {\r\n          serialNumber: '010',\r\n          bridgeName: 'XXXXXX大桥',\r\n          projectType: '上部结构养护',\r\n          status: '已完成',\r\n          responsible: '赵景深',\r\n          contactPhone: '19020017495',\r\n          completionTime: '2025-09-18 10:43'\r\n        }\r\n      ],\r\n      \r\n      // 详情弹窗相关\r\n      detailDialogVisible: false,\r\n      selectedProject: {}\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 过滤后的表格数据\r\n    filteredTableData() {\r\n      let data = [...this.tableData]\r\n      \r\n      if (this.filters.bridgeName) {\r\n        data = data.filter(item => item.bridgeName.includes(this.filters.bridgeName))\r\n      }\r\n      \r\n      if (this.filters.projectType) {\r\n        const projectLabel = this.projectOptions.find(p => p.value === this.filters.projectType)?.label\r\n        if (projectLabel) {\r\n          data = data.filter(item => item.projectType === projectLabel)\r\n        }\r\n      }\r\n      \r\n      if (this.filters.status) {\r\n        const statusLabel = this.statusOptions.find(s => s.value === this.filters.status)?.label\r\n        if (statusLabel) {\r\n          data = data.filter(item => item.status === statusLabel)\r\n        }\r\n      }\r\n      \r\n      if (this.filters.responsible) {\r\n        const responsibleLabel = this.responsibleOptions.find(r => r.value === this.filters.responsible)?.label\r\n        if (responsibleLabel) {\r\n          data = data.filter(item => item.responsible === responsibleLabel)\r\n        }\r\n      }\r\n      \r\n      return data\r\n    },\r\n    \r\n    // 总数量\r\n    totalCount() {\r\n      return this.tableData.length\r\n    },\r\n    \r\n    // 已完成数量\r\n    completedCount() {\r\n      return this.tableData.filter(item => item.status === '已完成').length\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 获取状态标签类型\r\n    getStatusType(status) {\r\n      const statusMap = {\r\n        '未完成': 'info',\r\n        '审核中': 'warning',\r\n        '复核中': 'warning',\r\n        '退回': 'danger',\r\n        '已完成': 'success'\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n    \r\n    // 查询\r\n    handleSearch() {\r\n      // 这里可以添加搜索逻辑\r\n      console.log('搜索条件:', this.filters)\r\n    },\r\n    \r\n    // 重置\r\n    handleReset() {\r\n      this.filters = {\r\n        bridgeName: '',\r\n        projectType: '',\r\n        status: '',\r\n        responsible: ''\r\n      }\r\n    },\r\n    \r\n    // 查看详情\r\n    handleViewDetail(row) {\r\n      console.log('查看详情:', row)\r\n      this.selectedProject = { ...row }\r\n      this.detailDialogVisible = true\r\n    },\r\n    \r\n    // 关闭详情弹窗\r\n    handleDetailDialogClose() {\r\n      this.detailDialogVisible = false\r\n      this.selectedProject = {}\r\n      // 确保移除遮罩层\r\n      this.$nextTick(() => {\r\n        const modal = document.querySelector('.v-modal')\r\n        if (modal) {\r\n          modal.remove()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.projects-view {\r\n  // 筛选器区域\r\n  .filter-section {\r\n    background: #1e3a8a;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    margin-bottom: 16px;\r\n    \r\n    .filter-row {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n      flex-wrap: wrap;\r\n      \r\n      .filter-item {\r\n        flex: 1;\r\n        min-width: 160px;\r\n        \r\n        .filter-select {\r\n          width: 100%;\r\n          \r\n          :deep(.el-input__inner) {\r\n            background: #1a2332;\r\n            border: 1px solid #374151;\r\n            color: #e5e7eb;\r\n            \r\n            &::placeholder {\r\n              color: #9ca3af;\r\n            }\r\n            \r\n            &:hover {\r\n              border-color: #4f46e5;\r\n            }\r\n            \r\n            &:focus {\r\n              border-color: #4f46e5;\r\n            }\r\n          }\r\n          \r\n          :deep(.el-input__suffix) {\r\n            .el-input__suffix-inner {\r\n              color: #9ca3af;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .filter-actions {\r\n        display: flex;\r\n        gap: 12px;\r\n        \r\n        .el-button {\r\n          &--primary {\r\n            background: #3b82f6;\r\n            border-color: #3b82f6;\r\n            \r\n            &:hover {\r\n              background: #2563eb;\r\n              border-color: #2563eb;\r\n            }\r\n          }\r\n          \r\n          &:not(.el-button--primary) {\r\n            background: #374151;\r\n            border-color: #374151;\r\n            color: #e5e7eb;\r\n            \r\n            &:hover {\r\n              background: #4b5563;\r\n              border-color: #4b5563;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 完成量统计\r\n  .completion-stats {\r\n    margin-bottom: 16px;\r\n    \r\n    .stats-text {\r\n      color: #e5e7eb;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  // 表格区域\r\n  .table-section {\r\n    .projects-table {\r\n      @extend .common-table;\r\n    }\r\n  }\r\n}\r\n\r\n// 下拉框选项样式\r\n:deep(.el-select-dropdown) {\r\n  background: #1a2332 !important;\r\n  border: 1px solid #374151 !important;\r\n  \r\n  .el-select-dropdown__item {\r\n    background: #1a2332 !important;\r\n    color: #e5e7eb !important;\r\n    \r\n    &:hover {\r\n      background: #1e3a8a !important;\r\n    }\r\n    \r\n    &.selected {\r\n      background: #1e3a8a !important;\r\n      color: #3b82f6 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}