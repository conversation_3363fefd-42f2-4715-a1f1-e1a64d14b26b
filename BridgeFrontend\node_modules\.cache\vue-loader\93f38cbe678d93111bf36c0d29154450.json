{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\diseases\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\diseases\\detail.vue", "mtime": 1758804563526}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBBY3Rpb25zLCBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCB7IFN0YXR1c1RhZywgSW1hZ2VWaWV3ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvSW5zcGVjdGlvbicNCmltcG9ydCBEaXNlYXNlSnVkZ2VGb3JtIGZyb20gJy4vY29tcG9uZW50cy9EaXNlYXNlSnVkZ2VGb3JtJw0KaW1wb3J0IERpc2Vhc2VEaXNwb3NlRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvRGlzZWFzZURpc3Bvc2VGb3JtJw0KaW1wb3J0IERpc2Vhc2VSZXZpZXdGb3JtIGZyb20gJy4vY29tcG9uZW50cy9EaXNlYXNlUmV2aWV3Rm9ybScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnRGlzZWFzZURldGFpbCcsDQogIGNvbXBvbmVudHM6IHsNCiAgICBTdGF0dXNUYWcsDQogICAgSW1hZ2VWaWV3ZXIsDQogICAgRGlzZWFzZUp1ZGdlRm9ybSwNCiAgICBEaXNlYXNlRGlzcG9zZUZvcm0sDQogICAgRGlzZWFzZVJldmlld0Zvcm0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBkaXNlYXNlRGV0YWlsOiB7fSwNCiAgICAgIA0KICAgICAgLy8g5bel5L2c5rWB5q2l6aqk6YWN572uDQogICAgICB3b3JrZmxvd1N0ZXBzOiBbDQogICAgICAgIHsga2V5OiAncmVwb3J0JywgdGl0bGU6ICfkuIrmiqUnIH0sDQogICAgICAgIHsga2V5OiAnanVkZ2UnLCB0aXRsZTogJ+WIpOWumicgfSwNCiAgICAgICAgeyBrZXk6ICdkaXNwb3NlJywgdGl0bGU6ICflpITnva4nIH0sDQogICAgICAgIHsga2V5OiAncmV2aWV3JywgdGl0bGU6ICflpI3moLgnIH0sDQogICAgICAgIHsga2V5OiAnYXJjaGl2ZScsIHRpdGxlOiAn5b2S5qGjJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDnvJbovpHnirbmgIENCiAgICAgIGlzRWRpdGluZ0p1ZGdlOiBmYWxzZSwNCiAgICAgIGlzRWRpdGluZ0Rpc3Bvc2U6IGZhbHNlLA0KICAgICAgaXNFZGl0aW5nUmV2aWV3OiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBHZXR0ZXJzKCdpbnNwZWN0aW9uJywgWydjdXJyZW50RGlzZWFzZURldGFpbCddKSwNCiAgICANCiAgICBkaXNlYXNlSWQoKSB7DQogICAgICByZXR1cm4gdGhpcy4kcm91dGUucGFyYW1zLmlkDQogICAgfSwNCiAgICANCiAgICBjdXJyZW50QWN0aW9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHJvdXRlLnF1ZXJ5LmFjdGlvbiB8fCAndmlldycNCiAgICB9LA0KICAgIA0KICAgIC8vIOS7jui3r+eUseWPguaVsOiOt+WPlueXheWus+aVsOaNrg0KICAgIHJvdXRlRGlzZWFzZURhdGEoKSB7DQogICAgICByZXR1cm4gdGhpcy4kcm91dGUucGFyYW1zLmRpc2Vhc2VEYXRhIHx8IG51bGwNCiAgICB9LA0KICAgIA0KICAgIC8vIOW9k+WJjeW3peS9nOa1geatpemqpA0KICAgIGN1cnJlbnRXb3JrZmxvd1N0ZXAoKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICdqdWRnaW5nJzogMSwNCiAgICAgICAgJ3BsYW5uaW5nJzogMiwNCiAgICAgICAgJ2Rpc3Bvc2luZyc6IDIsDQogICAgICAgICdyZXZpZXdpbmcnOiAzLA0KICAgICAgICAnYXJjaGl2ZWQnOiA0DQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3RoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzXSB8fCAwDQogICAgfSwNCiAgICANCiAgICAvLyDlvZPliY3lpITnkIbkurrkv6Hmga8NCiAgICBjdXJyZW50SGFuZGxlcigpIHsNCiAgICAgIGNvbnN0IHN0YXR1cyA9IHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzDQogICAgICBpZiAoc3RhdHVzID09PSAnanVkZ2luZycpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5jdXJyZW50SnVkZ2VyIHx8ICcnDQogICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gJ2Rpc3Bvc2luZycpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5jdXJyZW50UHJvY2Vzc29yIHx8ICcnDQogICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gJ3Jldmlld2luZycpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5jdXJyZW50UmV2aWV3ZXIgfHwgJycNCiAgICAgIH0NCiAgICAgIHJldHVybiAnJw0KICAgIH0sDQogICAgDQogICAgLy8g5b2T5YmN5aSE55CG5pe26Ze0DQogICAgY3VycmVudEhhbmRsZVRpbWUoKSB7DQogICAgICBjb25zdCBzdGF0dXMgPSB0aGlzLmRpc2Vhc2VEZXRhaWwuZGlzZWFzZVN0YXR1cw0KICAgICAgaWYgKHN0YXR1cyA9PT0gJ2p1ZGdpbmcnKSB7DQogICAgICAgIHJldHVybiB0aGlzLmRpc2Vhc2VEZXRhaWwuanVkZ2VTdGFydFRpbWUgfHwgJycNCiAgICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAnZGlzcG9zaW5nJykgew0KICAgICAgICByZXR1cm4gdGhpcy5kaXNlYXNlRGV0YWlsLmRpc3Bvc2VTdGFydFRpbWUgfHwgJycNCiAgICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAncmV2aWV3aW5nJykgew0KICAgICAgICByZXR1cm4gdGhpcy5kaXNlYXNlRGV0YWlsLnJldmlld1N0YXJ0VGltZSB8fCAnJw0KICAgICAgfQ0KICAgICAgcmV0dXJuICcnDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrliKTlrprkv6Hmga8NCiAgICBzaG93SnVkZ2VJbmZvKCkgew0KICAgICAgcmV0dXJuIFsnanVkZ2luZycsICdwbGFubmluZycsICdkaXNwb3NpbmcnLCAncmV2aWV3aW5nJywgJ2FyY2hpdmVkJ10uaW5jbHVkZXModGhpcy5kaXNlYXNlRGV0YWlsLmRpc2Vhc2VTdGF0dXMpDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrlpITnva7kv6Hmga8NCiAgICBzaG93RGlzcG9zZUluZm8oKSB7DQogICAgICByZXR1cm4gWydkaXNwb3NpbmcnLCAncmV2aWV3aW5nJywgJ2FyY2hpdmVkJ10uaW5jbHVkZXModGhpcy5kaXNlYXNlRGV0YWlsLmRpc2Vhc2VTdGF0dXMpDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrlpI3moLjkv6Hmga8NCiAgICBzaG93UmV2aWV3SW5mbygpIHsNCiAgICAgIHJldHVybiBbJ3Jldmlld2luZycsICdhcmNoaXZlZCddLmluY2x1ZGVzKHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzKQ0KICAgIH0sDQogICAgDQogICAgLy8g5piv5ZCm5pi+56S65b2S5qGj5L+h5oGvDQogICAgc2hvd0FyY2hpdmVJbmZvKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzID09PSAnYXJjaGl2ZWQnDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrmj5DkuqTmjInpkq4NCiAgICBzaG93U3VibWl0QnV0dG9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzID09PSAnanVkZ2luZycNCiAgICB9LA0KICAgIA0KICAgIC8vIOWIpOWumuWMuuWfn+aYr+WQpuWPr+e8lui+ke+8iOWPquacieWIpOWumuS4reeKtuaAgeWPr+e8lui+ke+8iQ0KICAgIGlzSnVkZ2VFZGl0YWJsZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmRpc2Vhc2VEZXRhaWwuZGlzZWFzZVN0YXR1cyA9PT0gJ2p1ZGdpbmcnDQogICAgfSwNCiAgICANCiAgICAvLyDlpITnva7ljLrln5/mmK/lkKblj6/nvJbovpHvvIjlj6rmnInlpITnva7kuK3nirbmgIHlj6/nvJbovpHvvIkNCiAgICBpc0Rpc3Bvc2VFZGl0YWJsZSgpIHsNCiAgICAgIHJldHVybiBbJ3BsYW5uaW5nJywgJ2Rpc3Bvc2luZyddLmluY2x1ZGVzKHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzKQ0KICAgIH0sDQogICAgDQogICAgLy8g5aSN5qC45Yy65Z+f5piv5ZCm5Y+v57yW6L6R77yI5Y+q5pyJ5aSN5qC45Lit54q25oCB5Y+v57yW6L6R77yJDQogICAgaXNSZXZpZXdFZGl0YWJsZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLmRpc2Vhc2VEZXRhaWwuZGlzZWFzZVN0YXR1cyA9PT0gJ3Jldmlld2luZycNCiAgICB9LA0KICAgIA0KICAgIC8vIOaYr+WQpuaYvuekuuWIpOWumuaPkOS6pOaMiemSrg0KICAgIHNob3dKdWRnZVN1Ym1pdEJ1dHRvbigpIHsNCiAgICAgIHJldHVybiB0aGlzLmRpc2Vhc2VEZXRhaWwuZGlzZWFzZVN0YXR1cyA9PT0gJ2p1ZGdpbmcnDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrlpI3moLjmj5DkuqTmjInpkq4NCiAgICBzaG93UmV2aWV3U3VibWl0QnV0dG9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzZWFzZURldGFpbC5kaXNlYXNlU3RhdHVzID09PSAncmV2aWV3aW5nJw0KICAgIH0sDQogICAgDQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgYXdhaXQgdGhpcy5pbml0UGFnZURhdGEoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLi4ubWFwQWN0aW9ucygnaW5zcGVjdGlvbicsIFsnZmV0Y2hEaXNlYXNlRGV0YWlsJ10pLA0KICAgIA0KICAgIC8vIOWIneWni+WMlumhtemdouaVsOaNrg0KICAgIGFzeW5jIGluaXRQYWdlRGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIA0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5LyY5YWI5L2/55So6Lev55Sx5Lyg6YCS55qE5pWw5o2u77yM5aaC5p6c5rKh5pyJ5YiZ5LuOQVBJ6I635Y+WDQogICAgICAgIGlmICh0aGlzLnJvdXRlRGlzZWFzZURhdGEpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55So6Lev55Sx5Lyg6YCS55qE55eF5a6z5pWw5o2uOicsIHRoaXMucm91dGVEaXNlYXNlRGF0YSkNCiAgICAgICAgICB0aGlzLmRpc2Vhc2VEZXRhaWwgPSB7IC4uLnRoaXMucm91dGVEaXNlYXNlRGF0YSB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgYXdhaXQgdGhpcy5mZXRjaERpc2Vhc2VEZXRhaWwodGhpcy5kaXNlYXNlSWQpDQogICAgICAgICAgdGhpcy5kaXNlYXNlRGV0YWlsID0gdGhpcy5jdXJyZW50RGlzZWFzZURldGFpbCB8fCB0aGlzLmdldERlZmF1bHREaXNlYXNlRGV0YWlsKCkNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgLy8g5qC55o2uYWN0aW9u6K6+572u57yW6L6R54q25oCBDQogICAgICAgIHRoaXMuc2V0RWRpdGluZ1N0YXRlKCkNCiAgICAgICAgDQogICAgICAgIC8vIOWIneWni+WMlue8lui+keeKtuaAgSAtIOehruS/neWcqOWvueW6lOmYtuautem7mOiupOWPr+e8lui+kQ0KICAgICAgICB0aGlzLmluaXRFZGl0aW5nU3RhdGUoKQ0KICAgICAgICANCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veeXheWus+ivpuaDheWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L2955eF5a6z6K+m5oOF5aSx6LSlJykNCiAgICAgICAgDQogICAgICAgIC8vIOS9v+eUqOm7mOiupOaVsOaNrg0KICAgICAgICB0aGlzLmRpc2Vhc2VEZXRhaWwgPSB0aGlzLmdldERlZmF1bHREaXNlYXNlRGV0YWlsKCkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDorr7nva7nvJbovpHnirbmgIENCiAgICBzZXRFZGl0aW5nU3RhdGUoKSB7DQogICAgICB0aGlzLmlzRWRpdGluZ0p1ZGdlID0gdGhpcy5jdXJyZW50QWN0aW9uID09PSAnanVkZ2UnDQogICAgICB0aGlzLmlzRWRpdGluZ0Rpc3Bvc2UgPSB0aGlzLmN1cnJlbnRBY3Rpb24gPT09ICdkaXNwb3NlJw0KICAgICAgdGhpcy5pc0VkaXRpbmdSZXZpZXcgPSB0aGlzLmN1cnJlbnRBY3Rpb24gPT09ICdyZXZpZXcnDQogICAgfSwNCiAgICANCiAgICAvLyDliJ3lp4vljJbnvJbovpHnirbmgIENCiAgICBpbml0RWRpdGluZ1N0YXRlKCkgew0KICAgICAgY29uc3Qgc3RhdHVzID0gdGhpcy5kaXNlYXNlRGV0YWlsLmRpc2Vhc2VTdGF0dXMNCiAgICAgIA0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5oyH5a6aYWN0aW9u77yM5YiZ5qC55o2u54q25oCB6buY6K6k6K6+572u57yW6L6R54q25oCBDQogICAgICBpZiAoIXRoaXMuY3VycmVudEFjdGlvbiB8fCB0aGlzLmN1cnJlbnRBY3Rpb24gPT09ICd2aWV3Jykgew0KICAgICAgICBpZiAoc3RhdHVzID09PSAnanVkZ2luZycpIHsNCiAgICAgICAgICB0aGlzLmlzRWRpdGluZ0p1ZGdlID0gdHJ1ZQ0KICAgICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gJ3BsYW5uaW5nJyB8fCBzdGF0dXMgPT09ICdkaXNwb3NpbmcnKSB7DQogICAgICAgICAgdGhpcy5pc0VkaXRpbmdEaXNwb3NlID0gdHJ1ZQ0KICAgICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gJ3Jldmlld2luZycpIHsNCiAgICAgICAgICB0aGlzLmlzRWRpdGluZ1JldmlldyA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W6buY6K6k55eF5a6z6K+m5oOF5pWw5o2uDQogICAgZ2V0RGVmYXVsdERpc2Vhc2VEZXRhaWwoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBpZDogdGhpcy5kaXNlYXNlSWQsDQogICAgICAgIGJyaWRnZU5hbWU6ICfmqZjlrZDmtLLlpKfmoaUnLA0KICAgICAgICBpbnNwZWN0b3I6ICflvKDkuIknLA0KICAgICAgICBpbnNwZWN0aW9uVW5pdDogJ+mVv+aymeW4guaUv+WFu+aKpOWNleS9jScsDQogICAgICAgIHJlcG9ydEF0dHJpYnV0ZTogJ2RhaWx5JywNCiAgICAgICAgZGlzZWFzZVR5cGU6ICfoo4LnvJ0nLA0KICAgICAgICBkaXNlYXNlQ291bnQ6IDMsDQogICAgICAgIGRpc2Vhc2VMb2NhdGlvbjogJ+aJv+i9veahgSsyMDBt77yM6Led5q2i5rWL5omA57qmNTPnsbPvvIzlhbEz5aSE5qGl6Z2i55qE6KOC57ydJywNCiAgICAgICAgZGlzZWFzZURlc2NyaXB0aW9uOiAn5qGl6Z2i5p2/5Ye6546wM+WkhOeahOijgue8ne+8jOacgOWkp+WuveW6pjMwLjNtbe+8jOmVv+W6pjEuMm3vvIzlj6/og73lvbHlk43nu5PmnoTogJDkuYXmgKfvvIzlu7rorq7lj4rml7blpITnkIbjgIInLA0KICAgICAgICBkaXNlYXNlU3RhdHVzOiAnanVkZ2luZycsIC8vIOm7mOiupOS4uuWIpOWumuS4reeKtuaAge+8jOi/meagt+WPr+S7pea1i+ivlee8lui+keWKn+iDvQ0KICAgICAgICByZXBvcnRUaW1lOiAnMjAyNC0wMS0wOCAwOTozMDowMCcsDQogICAgICAgIC8vIOa3u+WKoOWIpOWumuS/oeaBr+ekuuS+i+aVsOaNrg0KICAgICAgICBqdWRnZVR5cGU6ICdkYWlseV9tYWludGVuYW5jZScsDQogICAgICAgIGp1ZGdlQ29tbWVudDogJ+e7j+WIpOWumu+8jOivpeeXheWus+WxnuS6juaXpeW4uOWFu+aKpOiMg+WbtO+8jOW7uuiuruWcqOS4gOWRqOWGheWujOaIkOS/ruWkjeW3peS9nOOAguijgue8neS9jee9ruS4jeW9seWTjee7k+aehOWuieWFqO+8jOS9humcgOWPiuaXtuWkhOeQhumYsuatoui/m+S4gOatpeaJqeWkp+OAgicsDQogICAgICAgIHJlcXVpcmVkRmluaXNoVGltZTogJzIwMjQtMDEtMTUgMTg6MDA6MDAnLA0KICAgICAgICBqdWRnZXI6ICfmnY7lt6XnqIvluIgnLA0KICAgICAgICBqdWRnZVRpbWU6ICcyMDI0LTAxLTA4IDE0OjMwOjAwJywNCiAgICAgICAgZGlzZWFzZUltYWdlczogWw0KICAgICAgICAgIHsgdXJsOiByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZXMvdGVzdC9kaXNlYXNlMS5wbmcnKSwgYWx0OiAn55eF5a6z5Zu+54mHMScgfSwNCiAgICAgICAgICB7IHVybDogcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL3Rlc3QvZGlzZWFzZTIucG5nJyksIGFsdDogJ+eXheWus+WbvueJhzInIH0sDQogICAgICAgICAgeyB1cmw6IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy90ZXN0L2Rpc2Vhc2UzLnBuZycpLCBhbHQ6ICfnl4XlrrPlm77niYczJyB9LA0KICAgICAgICAgIHsgdXJsOiByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZXMvdGVzdC9kaXNlYXNlNC5wbmcnKSwgYWx0OiAn55eF5a6z5Zu+54mHNCcgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDkv53lrZjliKTlrprkv6Hmga8NCiAgICBhc3luYyBoYW5kbGVTYXZlSnVkZ2UoanVkZ2VEYXRhKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDov5nph4zosIPnlKjkv53lrZjliKTlrppBUEkNCiAgICAgICAgLy8gYXdhaXQgc2F2ZURpc2Vhc2VKdWRnZSh0aGlzLmRpc2Vhc2VJZCwganVkZ2VEYXRhKQ0KICAgICAgICANCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5Yik5a6a5L+h5oGv5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkv53lrZjlpLHotKUnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5o+Q5Lqk5Yik5a6a5L+h5oGvDQogICAgYXN5bmMgaGFuZGxlU3VibWl0SnVkZ2UoanVkZ2VEYXRhKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDov5nph4zosIPnlKjmj5DkuqTliKTlrppBUEkNCiAgICAgICAgLy8gYXdhaXQgc3VibWl0RGlzZWFzZUp1ZGdlKHRoaXMuZGlzZWFzZUlkLCBqdWRnZURhdGEpDQogICAgICAgIA0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIpOWumuS/oeaBr+aPkOS6pOaIkOWKnycpDQogICAgICAgIC8vIOaPkOS6pOaIkOWKn+WQjuWIt+aWsOmhtemdouaVsOaNruaIlui3s+i9rA0KICAgICAgICBhd2FpdCB0aGlzLmluaXRQYWdlRGF0YSgpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTliKTlrprkv6Hmga/lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aPkOS6pOWksei0pScpDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICANCiAgICAvLyDkv53lrZjlpITnva7kv6Hmga8NCiAgICBhc3luYyBoYW5kbGVTYXZlRGlzcG9zZShkaXNwb3NlRGF0YSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6L+Z6YeM6LCD55So5L+d5a2Y5aSE572uQVBJDQogICAgICAgIC8vIGF3YWl0IHNhdmVEaXNlYXNlRGlzcG9zZSh0aGlzLmRpc2Vhc2VJZCwgZGlzcG9zZURhdGEpDQogICAgICAgIA0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjlpITnva7kv6Hmga/lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWksei0pScpDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICANCiAgICAvLyDkv53lrZjlpI3moLjkv6Hmga8NCiAgICBhc3luYyBoYW5kbGVTYXZlUmV2aWV3KHJldmlld0RhdGEpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOi/memHjOiwg+eUqOS/neWtmOWkjeaguEFQSQ0KICAgICAgICAvLyBhd2FpdCBzYXZlRGlzZWFzZVJldmlldyh0aGlzLmRpc2Vhc2VJZCwgcmV2aWV3RGF0YSkNCiAgICAgICAgDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOWkjeaguOS/oeaBr+Wksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJykNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOaPkOS6pOWkjeaguOS/oeaBrw0KICAgIGFzeW5jIGhhbmRsZVN1Ym1pdFJldmlldyhyZXZpZXdEYXRhKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDov5nph4zosIPnlKjmj5DkuqTlpI3moLhBUEkNCiAgICAgICAgLy8gYXdhaXQgc3VibWl0RGlzZWFzZVJldmlldyh0aGlzLmRpc2Vhc2VJZCwgcmV2aWV3RGF0YSkNCiAgICAgICAgDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5aSN5qC45L+h5oGv5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgLy8g5o+Q5Lqk5oiQ5Yqf5ZCO5Yi35paw6aG16Z2i5pWw5o2u5oiW6Lez6L2sDQogICAgICAgIGF3YWl0IHRoaXMuaW5pdFBhZ2VEYXRhKCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aPkOS6pOWkjeaguOS/oeaBr+Wksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5o+Q5Lqk5aSx6LSlJykNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIA0KICAgIC8vIOWPlua2iOe8lui+kQ0KICAgIGhhbmRsZUNhbmNlbEVkaXQoKSB7DQogICAgICB0aGlzLmlzRWRpdGluZ0p1ZGdlID0gZmFsc2UNCiAgICAgIHRoaXMuaXNFZGl0aW5nRGlzcG9zZSA9IGZhbHNlDQogICAgICB0aGlzLmlzRWRpdGluZ1JldmlldyA9IGZhbHNlDQogICAgICANCiAgICAgIC8vIOenu+mZpGFjdGlvbuWPguaVsA0KICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2Uoew0KICAgICAgICBuYW1lOiAnRGlzZWFzZURldGFpbCcsDQogICAgICAgIHBhcmFtczogeyBpZDogdGhpcy5kaXNlYXNlSWQgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIA0KICAgIA0KICAgIC8vIOi/lOWbnuWIl+ihqA0KICAgIGhhbmRsZUdvQmFjaygpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOinpuWPkeWIpOWumuaPkOS6pO+8iOmAmui/hyRyZWZz6LCD55So5a2Q57uE5Lu25pa55rOV77yJDQogICAgdHJpZ2dlckp1ZGdlU3VibWl0KCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55So5Yik5a6a6KGo5Y2V57uE5Lu255qE5o+Q5Lqk5pa55rOV77yM5oiW6ICF6Kem5Y+R5o+Q5Lqk5LqL5Lu2DQogICAgICAvLyDlpoLmnpzliKTlrprooajljZXnu4Tku7bmnIlyZWbvvIzlj6/ku6Xnm7TmjqXosIPnlKg6IHRoaXMuJHJlZnMuanVkZ2VGb3JtLnN1Ym1pdCgpDQogICAgICAvLyDnm67liY3mqKHmi5/ooajljZXmlbDmja7lubbosIPnlKjmj5DkuqTmlrnms5UNCiAgICAgIGNvbnN0IG1vY2tKdWRnZURhdGEgPSB7DQogICAgICAgIGp1ZGdlVHlwZTogJ2RhaWx5X21haW50ZW5hbmNlJywNCiAgICAgICAganVkZ2VDb21tZW50OiAn5rWL6K+V5Yik5a6a5L+h5oGvJywNCiAgICAgICAgcmVxdWlyZWRGaW5pc2hUaW1lOiAnMjAyNC0wMS0xNSAxODowMDowMCcNCiAgICAgIH0NCiAgICAgIHRoaXMuaGFuZGxlU3VibWl0SnVkZ2UobW9ja0p1ZGdlRGF0YSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOinpuWPkeWkjeaguOaPkOS6pO+8iOmAmui/hyRyZWZz6LCD55So5a2Q57uE5Lu25pa55rOV77yJDQogICAgdHJpZ2dlclJldmlld1N1Ym1pdCgpIHsNCiAgICAgIC8vIOi/memHjOWPr+S7peiwg+eUqOWkjeaguOihqOWNlee7hOS7tueahOaPkOS6pOaWueazle+8jOaIluiAheinpuWPkeaPkOS6pOS6i+S7tg0KICAgICAgLy8g5aaC5p6c5aSN5qC46KGo5Y2V57uE5Lu25pyJcmVm77yM5Y+v5Lul55u05o6l6LCD55SoOiB0aGlzLiRyZWZzLnJldmlld0Zvcm0uc3VibWl0KCkNCiAgICAgIC8vIOebruWJjeaooeaLn+ihqOWNleaVsOaNruW5tuiwg+eUqOaPkOS6pOaWueazlQ0KICAgICAgY29uc3QgbW9ja1Jldmlld0RhdGEgPSB7DQogICAgICAgIHJldmlld1Jlc3VsdDogJ2FwcHJvdmVkJywNCiAgICAgICAgcmV2aWV3Q29tbWVudDogJ+a1i+ivleWkjeaguOS/oeaBrycNCiAgICAgIH0NCiAgICAgIHRoaXMuaGFuZGxlU3VibWl0UmV2aWV3KG1vY2tSZXZpZXdEYXRhKQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W5q2l6aqk5Zu+5qCHDQogICAgZ2V0U3RlcEljb24oc3RlcEtleSkgew0KICAgICAgY29uc3QgaWNvbk1hcCA9IHsNCiAgICAgICAgJ3JlcG9ydCc6ICdlbC1pY29uLWVkaXQnLA0KICAgICAgICAnanVkZ2UnOiAnZWwtaWNvbi1zLWNsYWltJywNCiAgICAgICAgJ2Rpc3Bvc2UnOiAnZWwtaWNvbi1zLXRvb2xzJywNCiAgICAgICAgJ3Jldmlldyc6ICdlbC1pY29uLXZpZXcnLA0KICAgICAgICAnYXJjaGl2ZSc6ICdlbC1pY29uLWZvbGRlcicNCiAgICAgIH0NCiAgICAgIHJldHVybiBpY29uTWFwW3N0ZXBLZXldIHx8ICdlbC1pY29uLWNpcmNsZS1jaGVjaycNCiAgICB9LA0KICAgIA0KICAgIC8vIOiOt+WPluatpemqpOaPj+i/sA0KICAgIGdldFN0ZXBEZXNjcmlwdGlvbihzdGVwS2V5KSB7DQogICAgICBjb25zdCBkZXNjTWFwID0gew0KICAgICAgICAncmVwb3J0JzogJ+eXheWus+S/oeaBr+W3suS4iuaKpScsDQogICAgICAgICdqdWRnZSc6ICfkuJPlrrbov5vooYznl4XlrrPliKTlrponLA0KICAgICAgICAnZGlzcG9zZSc6ICfliLblrprlpITnva7mlrnmoYjlubbmiafooYwnLA0KICAgICAgICAncmV2aWV3JzogJ+mqjOaUtuWkhOe9ruaViOaenCcsDQogICAgICAgICdhcmNoaXZlJzogJ+eXheWus+WkhOeQhuWujOaIkOW9kuahoycNCiAgICAgIH0NCiAgICAgIHJldHVybiBkZXNjTWFwW3N0ZXBLZXldIHx8ICcnDQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5bmraXpqqTlrozmiJDml7bpl7QNCiAgICBnZXRTdGVwVGltZShzdGVwS2V5KSB7DQogICAgICBjb25zdCB0aW1lTWFwID0gew0KICAgICAgICAncmVwb3J0JzogdGhpcy5kaXNlYXNlRGV0YWlsLnJlcG9ydFRpbWUsDQogICAgICAgICdqdWRnZSc6IHRoaXMuZGlzZWFzZURldGFpbC5qdWRnZVRpbWUsDQogICAgICAgICdkaXNwb3NlJzogdGhpcy5kaXNlYXNlRGV0YWlsLmRpc3Bvc2VUaW1lLA0KICAgICAgICAncmV2aWV3JzogdGhpcy5kaXNlYXNlRGV0YWlsLnJldmlld1RpbWUsDQogICAgICAgICdhcmNoaXZlJzogdGhpcy5kaXNlYXNlRGV0YWlsLmFyY2hpdmVUaW1lDQogICAgICB9DQogICAgICByZXR1cm4gdGltZU1hcFtzdGVwS2V5XSB8fCAnJw0KICAgIH0sDQogICAgDQogICAgLy8g5qC85byP5YyW5pe26Ze05pi+56S6DQogICAgZm9ybWF0VGltZSh0aW1lKSB7DQogICAgICBpZiAoIXRpbWUpIHJldHVybiAnJw0KICAgICAgDQogICAgICAvLyDlpoLmnpzmmK/ml6XmnJ/lrZfnrKbkuLLvvIzovazmjaLkuLrmm7Tlj4vlpb3nmoTmoLzlvI8NCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKQ0KICAgICAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkgcmV0dXJuIHRpbWUNCiAgICAgIA0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKQ0KICAgICAgY29uc3QgZGlmZiA9IG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKQ0KICAgICAgY29uc3QgZGF5cyA9IE1hdGguZmxvb3IoZGlmZiAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkNCiAgICAgIA0KICAgICAgaWYgKGRheXMgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICfku4rlpKkgJyArIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCd6aC1DTicsIHsgaG91cjogJzItZGlnaXQnLCBtaW51dGU6ICcyLWRpZ2l0JyB9KQ0KICAgICAgfSBlbHNlIGlmIChkYXlzID09PSAxKSB7DQogICAgICAgIHJldHVybiAn5pio5aSpICcgKyBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygnemgtQ04nLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSkNCiAgICAgIH0gZWxzZSBpZiAoZGF5cyA8IDcpIHsNCiAgICAgICAgcmV0dXJuIGAke2RheXN95aSp5YmNYA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1DTicpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/inspection/diseases", "sourcesContent": ["<template>\r\n  <div class=\"disease-detail\">\r\n    <!-- 使用传统的卡片布局结构，保持现有功能 -->\r\n    <div class=\"page-container\">\r\n      <!-- 状态流程卡片 -->\r\n      <el-card class=\"workflow-card\" shadow=\"never\">\r\n        <div class=\"workflow-header\">\r\n          <div class=\"header-left\">\r\n            <i class=\"el-icon-s-grid workflow-icon\"></i>\r\n            <h3>病害处理流程</h3>\r\n          </div>\r\n          <div class=\"current-handler\" v-if=\"currentHandler\">\r\n            <i class=\"el-icon-user\"></i>\r\n            <span>当前处理人：{{ currentHandler }}</span>\r\n            <span v-if=\"currentHandleTime\" class=\"handle-time\">{{ formatTime(currentHandleTime) }}</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"workflow-container\">\r\n          <div class=\"workflow-steps\">\r\n            <div \r\n              v-for=\"(step, index) in workflowSteps\" \r\n              :key=\"step.key\"\r\n              class=\"workflow-step\"\r\n              :class=\"{ \r\n                'active': index === currentWorkflowStep,\r\n                'completed': index < currentWorkflowStep,\r\n                'pending': index > currentWorkflowStep\r\n              }\"\r\n            >\r\n              <div class=\"step-circle\">\r\n                <div class=\"step-icon\">\r\n                  <i v-if=\"index < currentWorkflowStep\" class=\"el-icon-check\"></i>\r\n                  <i v-else-if=\"index === currentWorkflowStep\" :class=\"getStepIcon(step.key)\"></i>\r\n                  <span v-else>{{ index + 1 }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"step-content\">\r\n                <div class=\"step-title\">{{ step.title }}</div>\r\n                <div class=\"step-desc\">{{ getStepDescription(step.key) }}</div>\r\n                <div class=\"step-time\" v-if=\"getStepTime(step.key)\">{{ formatTime(getStepTime(step.key)) }}</div>\r\n              </div>\r\n              <div \r\n                v-if=\"index < workflowSteps.length - 1\" \r\n                class=\"step-connector\"\r\n                :class=\"{ 'active': index < currentWorkflowStep }\"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 状态说明 -->\r\n          <div class=\"status-legend\">\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot completed\"></div>\r\n              <span>已完成</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot active\"></div>\r\n              <span>当前阶段</span>\r\n            </div>\r\n            <div class=\"legend-item\">\r\n              <div class=\"legend-dot pending\"></div>\r\n              <span>未开始</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 病害基本信息卡片 -->\r\n      <el-card class=\"info-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-info\"></i> 病害基本信息</h3>\r\n        </div>\r\n        \r\n        <div class=\"info-section\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">巡检人员</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.inspector || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">巡检单位</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.inspectionUnit || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">上报属性</div>\r\n                <div class=\"info-value\">\r\n                  <StatusTag \r\n                    v-if=\"diseaseDetail.reportAttribute\"\r\n                    :status=\"diseaseDetail.reportAttribute\"\r\n                    type=\"inspection\"\r\n                  />\r\n                  <span v-else>-</span>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">所属桥梁/隧道</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.bridgeName || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害类型</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseType || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害数量</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseCount || 0 }}处</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害位置</div>\r\n                <div class=\"info-value\">{{ diseaseDetail.diseaseLocation || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <div class=\"info-label\">病害描述</div>\r\n                <div class=\"info-value description\">{{ diseaseDetail.diseaseDescription || '-' }}</div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          \r\n          <!-- 病害照片 -->\r\n          <div v-if=\"diseaseDetail.diseaseImages && diseaseDetail.diseaseImages.length > 0\" class=\"disease-images\">\r\n            <h4>病害照片</h4>\r\n            <ImageViewer\r\n              :images=\"diseaseDetail.diseaseImages\"\r\n              :grid-type=\"4\"\r\n              :show-upload=\"false\"\r\n              :show-delete=\"false\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 判定信息卡片 -->\r\n      <el-card v-if=\"showJudgeInfo\" class=\"judge-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-edit\"></i> 判定信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseJudgeForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isJudgeEditable\"\r\n          @save=\"handleSaveJudge\"\r\n          @submit=\"handleSubmitJudge\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 处置信息卡片 -->\r\n      <el-card v-if=\"showDisposeInfo\" class=\"dispose-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-setting\"></i> 处置信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseDisposeForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isDisposeEditable\"\r\n          @save=\"handleSaveDispose\"\r\n          @submit=\"handleSubmitDispose\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 复核信息卡片 -->\r\n      <el-card v-if=\"showReviewInfo\" class=\"review-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-check\"></i> 复核信息</h3>\r\n        </div>\r\n        \r\n        <DiseaseReviewForm\r\n          :disease-detail=\"diseaseDetail\"\r\n          :readonly=\"!isReviewEditable\"\r\n          @save=\"handleSaveReview\"\r\n          @submit=\"handleSubmitReview\"\r\n          @cancel=\"handleCancelEdit\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 归档信息卡片 -->\r\n      <el-card v-if=\"showArchiveInfo\" class=\"archive-card\" shadow=\"never\">\r\n        <div class=\"card-header\">\r\n          <h3><i class=\"el-icon-folder\"></i> 归档信息</h3>\r\n        </div>\r\n        \r\n        <div class=\"archive-info\">\r\n          <div class=\"info-item\">\r\n            <label>归档时间</label>\r\n            <span>{{ diseaseDetail.archiveTime || '-' }}</span>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <!-- 操作按钮区 -->\r\n      <div class=\"action-buttons\">\r\n        <el-button @click=\"handleGoBack\">返回</el-button>\r\n        \r\n        <!-- 判定提交按钮（判定中状态显示） -->\r\n        <el-button \r\n          v-if=\"showJudgeSubmitButton\"\r\n          type=\"primary\" \r\n          @click=\"triggerJudgeSubmit\"\r\n        >\r\n          判定提交\r\n        </el-button>\r\n        \r\n        <!-- 复核提交按钮（复核中状态显示） -->\r\n        <el-button \r\n          v-if=\"showReviewSubmitButton\"\r\n          type=\"primary\" \r\n          @click=\"triggerReviewSubmit\"\r\n        >\r\n          复核提交\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions, mapGetters } from 'vuex'\r\nimport { StatusTag, ImageViewer } from '@/components/Inspection'\r\nimport DiseaseJudgeForm from './components/DiseaseJudgeForm'\r\nimport DiseaseDisposeForm from './components/DiseaseDisposeForm'\r\nimport DiseaseReviewForm from './components/DiseaseReviewForm'\r\n\r\nexport default {\r\n  name: 'DiseaseDetail',\r\n  components: {\r\n    StatusTag,\r\n    ImageViewer,\r\n    DiseaseJudgeForm,\r\n    DiseaseDisposeForm,\r\n    DiseaseReviewForm\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      diseaseDetail: {},\r\n      \r\n      // 工作流步骤配置\r\n      workflowSteps: [\r\n        { key: 'report', title: '上报' },\r\n        { key: 'judge', title: '判定' },\r\n        { key: 'dispose', title: '处置' },\r\n        { key: 'review', title: '复核' },\r\n        { key: 'archive', title: '归档' }\r\n      ],\r\n      \r\n      // 编辑状态\r\n      isEditingJudge: false,\r\n      isEditingDispose: false,\r\n      isEditingReview: false\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('inspection', ['currentDiseaseDetail']),\r\n    \r\n    diseaseId() {\r\n      return this.$route.params.id\r\n    },\r\n    \r\n    currentAction() {\r\n      return this.$route.query.action || 'view'\r\n    },\r\n    \r\n    // 从路由参数获取病害数据\r\n    routeDiseaseData() {\r\n      return this.$route.params.diseaseData || null\r\n    },\r\n    \r\n    // 当前工作流步骤\r\n    currentWorkflowStep() {\r\n      const statusMap = {\r\n        'judging': 1,\r\n        'planning': 2,\r\n        'disposing': 2,\r\n        'reviewing': 3,\r\n        'archived': 4\r\n      }\r\n      return statusMap[this.diseaseDetail.diseaseStatus] || 0\r\n    },\r\n    \r\n    // 当前处理人信息\r\n    currentHandler() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      if (status === 'judging') {\r\n        return this.diseaseDetail.currentJudger || ''\r\n      } else if (status === 'disposing') {\r\n        return this.diseaseDetail.currentProcessor || ''\r\n      } else if (status === 'reviewing') {\r\n        return this.diseaseDetail.currentReviewer || ''\r\n      }\r\n      return ''\r\n    },\r\n    \r\n    // 当前处理时间\r\n    currentHandleTime() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      if (status === 'judging') {\r\n        return this.diseaseDetail.judgeStartTime || ''\r\n      } else if (status === 'disposing') {\r\n        return this.diseaseDetail.disposeStartTime || ''\r\n      } else if (status === 'reviewing') {\r\n        return this.diseaseDetail.reviewStartTime || ''\r\n      }\r\n      return ''\r\n    },\r\n    \r\n    // 是否显示判定信息\r\n    showJudgeInfo() {\r\n      return ['judging', 'planning', 'disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示处置信息\r\n    showDisposeInfo() {\r\n      return ['disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示复核信息\r\n    showReviewInfo() {\r\n      return ['reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 是否显示归档信息\r\n    showArchiveInfo() {\r\n      return this.diseaseDetail.diseaseStatus === 'archived'\r\n    },\r\n    \r\n    // 是否显示提交按钮\r\n    showSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 判定区域是否可编辑（只有判定中状态可编辑）\r\n    isJudgeEditable() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 处置区域是否可编辑（只有处置中状态可编辑）\r\n    isDisposeEditable() {\r\n      return ['planning', 'disposing'].includes(this.diseaseDetail.diseaseStatus)\r\n    },\r\n    \r\n    // 复核区域是否可编辑（只有复核中状态可编辑）\r\n    isReviewEditable() {\r\n      return this.diseaseDetail.diseaseStatus === 'reviewing'\r\n    },\r\n    \r\n    // 是否显示判定提交按钮\r\n    showJudgeSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'judging'\r\n    },\r\n    \r\n    // 是否显示复核提交按钮\r\n    showReviewSubmitButton() {\r\n      return this.diseaseDetail.diseaseStatus === 'reviewing'\r\n    },\r\n    \r\n  },\r\n  async created() {\r\n    await this.initPageData()\r\n  },\r\n  methods: {\r\n    ...mapActions('inspection', ['fetchDiseaseDetail']),\r\n    \r\n    // 初始化页面数据\r\n    async initPageData() {\r\n      this.loading = true\r\n      \r\n      try {\r\n        // 优先使用路由传递的数据，如果没有则从API获取\r\n        if (this.routeDiseaseData) {\r\n          console.log('使用路由传递的病害数据:', this.routeDiseaseData)\r\n          this.diseaseDetail = { ...this.routeDiseaseData }\r\n        } else {\r\n          await this.fetchDiseaseDetail(this.diseaseId)\r\n          this.diseaseDetail = this.currentDiseaseDetail || this.getDefaultDiseaseDetail()\r\n        }\r\n        \r\n        // 根据action设置编辑状态\r\n        this.setEditingState()\r\n        \r\n        // 初始化编辑状态 - 确保在对应阶段默认可编辑\r\n        this.initEditingState()\r\n        \r\n      } catch (error) {\r\n        console.error('加载病害详情失败:', error)\r\n        this.$message.error('加载病害详情失败')\r\n        \r\n        // 使用默认数据\r\n        this.diseaseDetail = this.getDefaultDiseaseDetail()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 设置编辑状态\r\n    setEditingState() {\r\n      this.isEditingJudge = this.currentAction === 'judge'\r\n      this.isEditingDispose = this.currentAction === 'dispose'\r\n      this.isEditingReview = this.currentAction === 'review'\r\n    },\r\n    \r\n    // 初始化编辑状态\r\n    initEditingState() {\r\n      const status = this.diseaseDetail.diseaseStatus\r\n      \r\n      // 如果没有指定action，则根据状态默认设置编辑状态\r\n      if (!this.currentAction || this.currentAction === 'view') {\r\n        if (status === 'judging') {\r\n          this.isEditingJudge = true\r\n        } else if (status === 'planning' || status === 'disposing') {\r\n          this.isEditingDispose = true\r\n        } else if (status === 'reviewing') {\r\n          this.isEditingReview = true\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取默认病害详情数据\r\n    getDefaultDiseaseDetail() {\r\n      return {\r\n        id: this.diseaseId,\r\n        bridgeName: '橘子洲大桥',\r\n        inspector: '张三',\r\n        inspectionUnit: '长沙市政养护单位',\r\n        reportAttribute: 'daily',\r\n        diseaseType: '裂缝',\r\n        diseaseCount: 3,\r\n        diseaseLocation: '承载桁+200m，距止测所约53米，共3处桥面的裂缝',\r\n        diseaseDescription: '桥面板出现3处的裂缝，最大宽度30.3mm，长度1.2m，可能影响结构耐久性，建议及时处理。',\r\n        diseaseStatus: 'judging', // 默认为判定中状态，这样可以测试编辑功能\r\n        reportTime: '2024-01-08 09:30:00',\r\n        // 添加判定信息示例数据\r\n        judgeType: 'daily_maintenance',\r\n        judgeComment: '经判定，该病害属于日常养护范围，建议在一周内完成修复工作。裂缝位置不影响结构安全，但需及时处理防止进一步扩大。',\r\n        requiredFinishTime: '2024-01-15 18:00:00',\r\n        judger: '李工程师',\r\n        judgeTime: '2024-01-08 14:30:00',\r\n        diseaseImages: [\r\n          { url: require('@/assets/images/test/disease1.png'), alt: '病害图片1' },\r\n          { url: require('@/assets/images/test/disease2.png'), alt: '病害图片2' },\r\n          { url: require('@/assets/images/test/disease3.png'), alt: '病害图片3' },\r\n          { url: require('@/assets/images/test/disease4.png'), alt: '病害图片4' }\r\n        ]\r\n      }\r\n    },\r\n    \r\n    // 保存判定信息\r\n    async handleSaveJudge(judgeData) {\r\n      try {\r\n        // 这里调用保存判定API\r\n        // await saveDiseaseJudge(this.diseaseId, judgeData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存判定信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    // 提交判定信息\r\n    async handleSubmitJudge(judgeData) {\r\n      try {\r\n        // 这里调用提交判定API\r\n        // await submitDiseaseJudge(this.diseaseId, judgeData)\r\n        \r\n        this.$message.success('判定信息提交成功')\r\n        // 提交成功后刷新页面数据或跳转\r\n        await this.initPageData()\r\n      } catch (error) {\r\n        console.error('提交判定信息失败:', error)\r\n        this.$message.error('提交失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 保存处置信息\r\n    async handleSaveDispose(disposeData) {\r\n      try {\r\n        // 这里调用保存处置API\r\n        // await saveDiseaseDispose(this.diseaseId, disposeData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存处置信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 保存复核信息\r\n    async handleSaveReview(reviewData) {\r\n      try {\r\n        // 这里调用保存复核API\r\n        // await saveDiseaseReview(this.diseaseId, reviewData)\r\n        \r\n        this.$message.success('保存成功')\r\n      } catch (error) {\r\n        console.error('保存复核信息失败:', error)\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    \r\n    // 提交复核信息\r\n    async handleSubmitReview(reviewData) {\r\n      try {\r\n        // 这里调用提交复核API\r\n        // await submitDiseaseReview(this.diseaseId, reviewData)\r\n        \r\n        this.$message.success('复核信息提交成功')\r\n        // 提交成功后刷新页面数据或跳转\r\n        await this.initPageData()\r\n      } catch (error) {\r\n        console.error('提交复核信息失败:', error)\r\n        this.$message.error('提交失败')\r\n      }\r\n    },\r\n    \r\n    \r\n    // 取消编辑\r\n    handleCancelEdit() {\r\n      this.isEditingJudge = false\r\n      this.isEditingDispose = false\r\n      this.isEditingReview = false\r\n      \r\n      // 移除action参数\r\n      this.$router.replace({\r\n        name: 'DiseaseDetail',\r\n        params: { id: this.diseaseId }\r\n      })\r\n    },\r\n    \r\n    \r\n    // 返回列表\r\n    handleGoBack() {\r\n      this.$router.go(-1)\r\n    },\r\n    \r\n    // 触发判定提交（通过$refs调用子组件方法）\r\n    triggerJudgeSubmit() {\r\n      // 这里可以调用判定表单组件的提交方法，或者触发提交事件\r\n      // 如果判定表单组件有ref，可以直接调用: this.$refs.judgeForm.submit()\r\n      // 目前模拟表单数据并调用提交方法\r\n      const mockJudgeData = {\r\n        judgeType: 'daily_maintenance',\r\n        judgeComment: '测试判定信息',\r\n        requiredFinishTime: '2024-01-15 18:00:00'\r\n      }\r\n      this.handleSubmitJudge(mockJudgeData)\r\n    },\r\n    \r\n    // 触发复核提交（通过$refs调用子组件方法）\r\n    triggerReviewSubmit() {\r\n      // 这里可以调用复核表单组件的提交方法，或者触发提交事件\r\n      // 如果复核表单组件有ref，可以直接调用: this.$refs.reviewForm.submit()\r\n      // 目前模拟表单数据并调用提交方法\r\n      const mockReviewData = {\r\n        reviewResult: 'approved',\r\n        reviewComment: '测试复核信息'\r\n      }\r\n      this.handleSubmitReview(mockReviewData)\r\n    },\r\n    \r\n    // 获取步骤图标\r\n    getStepIcon(stepKey) {\r\n      const iconMap = {\r\n        'report': 'el-icon-edit',\r\n        'judge': 'el-icon-s-claim',\r\n        'dispose': 'el-icon-s-tools',\r\n        'review': 'el-icon-view',\r\n        'archive': 'el-icon-folder'\r\n      }\r\n      return iconMap[stepKey] || 'el-icon-circle-check'\r\n    },\r\n    \r\n    // 获取步骤描述\r\n    getStepDescription(stepKey) {\r\n      const descMap = {\r\n        'report': '病害信息已上报',\r\n        'judge': '专家进行病害判定',\r\n        'dispose': '制定处置方案并执行',\r\n        'review': '验收处置效果',\r\n        'archive': '病害处理完成归档'\r\n      }\r\n      return descMap[stepKey] || ''\r\n    },\r\n    \r\n    // 获取步骤完成时间\r\n    getStepTime(stepKey) {\r\n      const timeMap = {\r\n        'report': this.diseaseDetail.reportTime,\r\n        'judge': this.diseaseDetail.judgeTime,\r\n        'dispose': this.diseaseDetail.disposeTime,\r\n        'review': this.diseaseDetail.reviewTime,\r\n        'archive': this.diseaseDetail.archiveTime\r\n      }\r\n      return timeMap[stepKey] || ''\r\n    },\r\n    \r\n    // 格式化时间显示\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      \r\n      // 如果是日期字符串，转换为更友好的格式\r\n      const date = new Date(time)\r\n      if (isNaN(date.getTime())) return time\r\n      \r\n      const now = new Date()\r\n      const diff = now.getTime() - date.getTime()\r\n      const days = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n      \r\n      if (days === 0) {\r\n        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })\r\n      } else if (days === 1) {\r\n        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })\r\n      } else if (days < 7) {\r\n        return `${days}天前`\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 导入主题样式和通用混入\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/mixins/inspection-common.scss';\r\n\r\n.disease-detail {\r\n  @include inspection-page-container;\r\n  background: var(--inspection-bg-primary);\r\n  min-height: 100%; // 适应父容器高度\r\n  overflow-y: visible;\r\n  \r\n  .page-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 24px;\r\n    padding-bottom: 50px; // 确保底部内容不被遮挡\r\n    \r\n    .workflow-card,\r\n    .info-card,\r\n    .judge-card,\r\n    .dispose-card,\r\n    .review-card,\r\n    .archive-card {\r\n      margin-bottom: 20px;\r\n      background: linear-gradient(135deg, rgba(9, 26, 75, 0.95) 0%, rgba(30, 58, 138, 0.9) 100%);\r\n      border: 1px solid rgba(92, 157, 255, 0.3);\r\n      border-radius: 16px;\r\n      color: #f1f5f9;\r\n      box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3);\r\n      \r\n      &:hover {\r\n        box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.3), 0 8px 25px rgba(92, 157, 255, 0.2);\r\n        transform: translateY(-2px);\r\n      }\r\n    }\r\n    \r\n    // 状态流程卡片样式 - 使用深色主题\r\n    .workflow-card {\r\n      background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;\r\n      border: 1px solid rgba(79, 70, 229, 0.3) !important;\r\n      border-radius: 16px !important;\r\n      box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;\r\n      backdrop-filter: blur(10px) !important;\r\n      overflow: hidden;\r\n      color: #f1f5f9 !important;\r\n      \r\n      :deep(.el-card__body) {\r\n        padding: 24px !important;\r\n      }\r\n      \r\n        .workflow-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          margin-bottom: 32px;\r\n          padding-bottom: 16px;\r\n          border-bottom: 2px solid rgba(255, 255, 255, 0.1);\r\n        \r\n        .header-left {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .workflow-icon {\r\n            font-size: 20px;\r\n            color: #5C9DFF;\r\n            margin-right: 12px;\r\n            padding: 8px;\r\n            background: rgba(92, 157, 255, 0.2);\r\n            border-radius: 8px;\r\n          }\r\n          \r\n          h3 {\r\n            margin: 0;\r\n            font-size: 20px;\r\n            font-weight: 700;\r\n            color: #f1f5f9;\r\n            letter-spacing: -0.025em;\r\n          }\r\n        }\r\n        \r\n        .current-handler {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 8px 16px;\r\n          background: linear-gradient(135deg, rgba(92, 157, 255, 0.15), rgba(116, 167, 245, 0.1));\r\n          border-radius: 12px;\r\n          font-size: 14px;\r\n          color: #e2e8f0;\r\n          border: 1px solid rgba(92, 157, 255, 0.3);\r\n          \r\n          i {\r\n            margin-right: 6px;\r\n            color: #5C9DFF;\r\n          }\r\n          \r\n          .handle-time {\r\n            margin-left: 8px;\r\n            padding: 2px 8px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border-radius: 6px;\r\n            font-size: 12px;\r\n            color: #cbd5e1;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .workflow-container {\r\n        .workflow-steps {\r\n          display: flex;\r\n          flex-direction: row;\r\n          justify-content: space-between;\r\n          align-items: flex-start;\r\n          gap: 20px;\r\n          position: relative;\r\n          \r\n          // 横向连接线\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 24px;\r\n            left: 60px;\r\n            right: 60px;\r\n            height: 2px;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            z-index: 1;\r\n          }\r\n          \r\n          .workflow-step {\r\n            flex: 1;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            text-align: center;\r\n            position: relative;\r\n            z-index: 2;\r\n            \r\n            .step-circle {\r\n              margin-bottom: 12px;\r\n              \r\n              .step-icon {\r\n                width: 48px;\r\n                height: 48px;\r\n                border-radius: 50%;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                font-weight: 600;\r\n                font-size: 16px;\r\n                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n                position: relative;\r\n                background: white;\r\n                \r\n                i {\r\n                  font-size: 18px;\r\n                }\r\n                \r\n                span {\r\n                  font-size: 16px;\r\n                  font-weight: 700;\r\n                }\r\n              }\r\n            }\r\n            \r\n            .step-content {\r\n              text-align: center;\r\n              \r\n              .step-title {\r\n                font-size: 14px;\r\n                font-weight: 600;\r\n                margin-bottom: 4px;\r\n                letter-spacing: -0.025em;\r\n              }\r\n              \r\n              .step-desc {\r\n                font-size: 12px;\r\n                color: #64748B;\r\n                line-height: 1.4;\r\n                margin-bottom: 6px;\r\n              }\r\n              \r\n              .step-time {\r\n                font-size: 11px;\r\n                color: #94A3B8;\r\n                font-weight: 500;\r\n                padding: 2px 6px;\r\n                background: rgba(148, 163, 184, 0.1);\r\n                border-radius: 4px;\r\n                display: inline-block;\r\n              }\r\n            }\r\n            \r\n            // 未开始状态 - 灰色\r\n            &.pending {\r\n              .step-circle .step-icon {\r\n                background: rgba(148, 163, 184, 0.2);\r\n                color: #94A3B8;\r\n                border: 2px solid rgba(148, 163, 184, 0.3);\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #94A3B8;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #64748B;\r\n                }\r\n              }\r\n            }\r\n            \r\n            // 当前阶段 - 蓝色（项目主题色）\r\n            &.active {\r\n              .step-circle .step-icon {\r\n                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);\r\n                color: white;\r\n                border: 3px solid rgba(92, 157, 255, 0.3);\r\n                box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n                transform: scale(1.05);\r\n                animation: pulse 2s infinite;\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #f1f5f9;\r\n                  font-weight: 700;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #e2e8f0;\r\n                }\r\n                \r\n                .step-time {\r\n                  background: rgba(92, 157, 255, 0.2);\r\n                  color: #5C9DFF;\r\n                }\r\n              }\r\n            }\r\n            \r\n            // 已完成状态 - 绿色\r\n            &.completed {\r\n              .step-circle .step-icon {\r\n                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);\r\n                color: white;\r\n                border: 2px solid rgba(48, 176, 143, 0.3);\r\n                box-shadow: 0 4px 12px rgba(48, 176, 143, 0.2);\r\n                \r\n                i {\r\n                  font-size: 20px;\r\n                  font-weight: bold;\r\n                }\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  color: #f1f5f9;\r\n                  font-weight: 600;\r\n                }\r\n                \r\n                .step-desc {\r\n                  color: #e2e8f0;\r\n                }\r\n                \r\n                .step-time {\r\n                  background: rgba(48, 176, 143, 0.2);\r\n                  color: #30B08F;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        .status-legend {\r\n          margin-top: 32px;\r\n          padding-top: 20px;\r\n          border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n          display: flex;\r\n          justify-content: center;\r\n          gap: 32px;\r\n          \r\n          .legend-item {\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 13px;\r\n            color: #cbd5e1;\r\n            font-weight: 500;\r\n            \r\n            .legend-dot {\r\n              width: 12px;\r\n              height: 12px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              \r\n              &.completed {\r\n                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);\r\n              }\r\n              \r\n              &.active {\r\n                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);\r\n                animation: pulse-dot 2s infinite;\r\n              }\r\n              \r\n              &.pending {\r\n                background: rgba(148, 163, 184, 0.3);\r\n                border: 2px solid rgba(148, 163, 184, 0.2);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .card-header {\r\n      margin-bottom: 20px;\r\n      \r\n      h3 {\r\n        display: flex;\r\n        align-items: center;\r\n        margin: 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: #f1f5f9;\r\n        \r\n        i {\r\n          margin-right: 8px;\r\n          color: #5C9DFF;\r\n          padding: 6px;\r\n          background: rgba(92, 157, 255, 0.2);\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .info-section {\r\n      .info-item {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        margin-bottom: 16px;\r\n        padding: 12px;\r\n        background: rgba(255, 255, 255, 0.05);\r\n        border-radius: 8px;\r\n        border-left: 3px solid #5C9DFF;\r\n        \r\n        .info-label {\r\n          width: 120px;\r\n          font-weight: 600;\r\n          color: #ffffff;\r\n          flex-shrink: 0;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        .info-value {\r\n          color: #e2e8f0;\r\n          word-break: break-all;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n          \r\n          &.description {\r\n            line-height: 1.6;\r\n            color: #e2e8f0;\r\n          }\r\n        }\r\n        \r\n        // 兼容旧的label和span结构\r\n        label {\r\n          width: 120px;\r\n          font-weight: 600;\r\n          color: #ffffff;\r\n          flex-shrink: 0;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        span {\r\n          color: #e2e8f0;\r\n          word-break: break-all;\r\n          font-size: 14px;\r\n          line-height: 1.5;\r\n        }\r\n        \r\n        .description {\r\n          margin: 0;\r\n          line-height: 1.6;\r\n          color: #e2e8f0;\r\n        }\r\n      }\r\n      \r\n      .disease-images {\r\n        margin-top: 24px;\r\n        \r\n        h4 {\r\n          margin: 0 0 16px 0;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #f1f5f9;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .archive-info {\r\n      .info-item {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        label {\r\n          width: 120px;\r\n          font-weight: 500;\r\n          color: #cbd5e1;\r\n        }\r\n        \r\n        span {\r\n          color: #f1f5f9;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .action-buttons {\r\n      text-align: center;\r\n      padding: 20px 0;\r\n      \r\n      .el-button {\r\n        margin: 0 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画定义\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n  }\r\n  50% {\r\n    box-shadow: 0 0 0 8px rgba(92, 157, 255, 0.2), 0 8px 20px rgba(92, 157, 255, 0.3);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);\r\n  }\r\n}\r\n\r\n@keyframes pulse-dot {\r\n  0% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 深色主题统一样式 - 与检查模块整体风格保持一致\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .disease-detail {\r\n    .page-container {\r\n      padding: 16px;\r\n      \r\n      .workflow-card {\r\n        .workflow-header {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          gap: 12px;\r\n          \r\n          .header-left h3 {\r\n            font-size: 18px;\r\n          }\r\n          \r\n          .current-handler {\r\n            align-self: stretch;\r\n          }\r\n        }\r\n        \r\n        .workflow-container {\r\n          .workflow-steps {\r\n            .workflow-step {\r\n              .step-circle .step-icon {\r\n                width: 40px;\r\n                height: 40px;\r\n                \r\n                i {\r\n                  font-size: 16px;\r\n                }\r\n                \r\n                span {\r\n                  font-size: 14px;\r\n                }\r\n              }\r\n              \r\n              .step-content {\r\n                .step-title {\r\n                  font-size: 14px;\r\n                }\r\n                \r\n                .step-desc {\r\n                  font-size: 13px;\r\n                }\r\n              }\r\n              \r\n              .step-connector {\r\n                left: 19px;\r\n              }\r\n            }\r\n          }\r\n          \r\n          .status-legend {\r\n            flex-direction: column;\r\n            gap: 16px;\r\n            align-items: flex-start;\r\n            \r\n            .legend-item {\r\n              font-size: 12px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .card-header h3 {\r\n        font-size: 16px;\r\n        \r\n        i {\r\n          font-size: 18px;\r\n          padding: 6px;\r\n        }\r\n      }\r\n      \r\n      .info-section {\r\n        .info-item {\r\n          flex-direction: column;\r\n          align-items: flex-start;\r\n          padding: 12px;\r\n          \r\n          .info-label,\r\n          label {\r\n            width: auto;\r\n            margin-bottom: 8px;\r\n            \r\n            &::after {\r\n              display: none;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        padding: 24px 0;\r\n        \r\n        .el-button {\r\n          width: 120px;\r\n          margin: 6px 4px;\r\n          padding: 10px 16px;\r\n          font-size: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .disease-detail {\r\n    .page-container {\r\n      padding: 12px;\r\n      \r\n      .workflow-container {\r\n        .status-legend {\r\n          flex-direction: row;\r\n          flex-wrap: wrap;\r\n          justify-content: center;\r\n          gap: 20px;\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        .el-button {\r\n          width: 100%;\r\n          margin: 6px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}</style>\r\n"]}]}