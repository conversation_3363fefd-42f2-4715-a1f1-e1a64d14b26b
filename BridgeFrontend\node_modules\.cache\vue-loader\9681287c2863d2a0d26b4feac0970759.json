{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue", "mtime": 1758806998018}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgU3RlcE5hdmlnYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL01haW50ZW5hbmNlL1N0ZXBOYXZpZ2F0aW9uJw0KaW1wb3J0IEJhc2ljSW5mbyBmcm9tICdAL3ZpZXdzL21haW50ZW5hbmNlL3Byb2plY3RzL2NyZWF0ZS9jb21wb25lbnRzL0Jhc2ljSW5mbycNCmltcG9ydCBQcm9qZWN0Q29uZmlnIGZyb20gJ0Avdmlld3MvbWFpbnRlbmFuY2UvcHJvamVjdHMvY3JlYXRlL2NvbXBvbmVudHMvUHJvamVjdENvbmZpZycNCmltcG9ydCBEaXNlYXNlQ29uZmlnIGZyb20gJ0Avdmlld3MvbWFpbnRlbmFuY2UvcHJvamVjdHMvY3JlYXRlL2NvbXBvbmVudHMvRGlzZWFzZUNvbmZpZycNCmltcG9ydCBCcmlkZ2VDb25maWcgZnJvbSAnQC92aWV3cy9tYWludGVuYW5jZS9wcm9qZWN0cy9jcmVhdGUvY29tcG9uZW50cy9CcmlkZ2VDb25maWcnDQppbXBvcnQgSW1wbGVtZW50YXRpb25Db25maWcgZnJvbSAnQC92aWV3cy9tYWludGVuYW5jZS9wcm9qZWN0cy9jcmVhdGUvY29tcG9uZW50cy9JbXBsZW1lbnRhdGlvbkNvbmZpZycNCmltcG9ydCBDb21wbGV0aW9uSW5mbyBmcm9tICdAL3ZpZXdzL21haW50ZW5hbmNlL3Byb2plY3RzL2NyZWF0ZS9jb21wb25lbnRzL0NvbXBsZXRpb25JbmZvJw0KaW1wb3J0IEFwcHJvdmFsSW5mbyBmcm9tICdAL3ZpZXdzL21haW50ZW5hbmNlL3Byb2plY3RzL2NyZWF0ZS9jb21wb25lbnRzL0FwcHJvdmFsSW5mbycNCmltcG9ydCB7IGdldFByb2plY3REZXRhaWwsIGNyZWF0ZVByb2plY3QsIHVwZGF0ZVByb2plY3QgfSBmcm9tICdAL2FwaS9tYWludGVuYW5jZS9wcm9qZWN0cycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUHJvamVjdERpYWxvZycsDQogIGNvbXBvbmVudHM6IHsNCiAgICBTdGVwTmF2aWdhdGlvbiwNCiAgICBCYXNpY0luZm8sDQogICAgUHJvamVjdENvbmZpZywNCiAgICBEaXNlYXNlQ29uZmlnLA0KICAgIEJyaWRnZUNvbmZpZywNCiAgICBJbXBsZW1lbnRhdGlvbkNvbmZpZywNCiAgICBDb21wbGV0aW9uSW5mbywNCiAgICBBcHByb3ZhbEluZm8NCiAgfSwNCiAgcHJvcHM6IHsNCiAgICB2aXNpYmxlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIG1vZGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgdmFsaWRhdG9yOiB2YWx1ZSA9PiBbJ2NyZWF0ZScsICdlZGl0JywgJ3ZpZXcnLCAnYXBwcm92ZSddLmluY2x1ZGVzKHZhbHVlKQ0KICAgIH0sDQogICAgcHJvamVjdElkOiB7DQogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0sDQogICAgaW5mcmFzdHJ1Y3R1cmVUeXBlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnYnJpZGdlJw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBjdXJyZW50U3RlcDogMCwNCiAgICAgIHByb2plY3RUeXBlOiAnbW9udGhseScsIC8vIOm7mOiupOS4uuaciOW6puWFu+aKpA0KICAgICAgDQogICAgICAvLyDooajljZXmlbDmja4NCiAgICAgIGZvcm1EYXRhOiB7DQogICAgICAgIGJhc2ljSW5mbzoge30sDQogICAgICAgIHByb2plY3RDb25maWc6IHt9LA0KICAgICAgICBkaXNlYXNlQ29uZmlnOiB7fSwNCiAgICAgICAgaW1wbGVtZW50YXRpb25Db25maWc6IHt9LA0KICAgICAgICBicmlkZ2VDb25maWc6IHt9LA0KICAgICAgICBjb21wbGV0aW9uSW5mbzoge30sDQogICAgICAgIGFwcHJvdmFsSW5mbzoge30NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgZGlhbG9nVmlzaWJsZTogew0KICAgICAgZ2V0KCkgew0KICAgICAgICByZXR1cm4gdGhpcy52aXNpYmxlDQogICAgICB9LA0KICAgICAgc2V0KHZhbHVlKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgdmFsdWUpDQogICAgICB9DQogICAgfSwNCg0KICAgIG1vZGFsVGl0bGUoKSB7DQogICAgICBjb25zdCB0aXRsZXMgPSB7DQogICAgICAgIGNyZWF0ZTogJ+aWsOWinuWFu+aKpOmhueebricsDQogICAgICAgIGVkaXQ6ICfnvJbovpHlhbvmiqTpobnnm64nLA0KICAgICAgICB2aWV3OiAn5p+l55yL5YW75oqk6aG555uuJywNCiAgICAgICAgYXBwcm92ZTogJ+WuoeaJueWFu+aKpOmhueebricNCiAgICAgIH0NCiAgICAgIHJldHVybiB0aXRsZXNbdGhpcy5tb2RlXSB8fCAn5YW75oqk6aG555uuJw0KICAgIH0sDQoNCiAgICBjdXJyZW50UHJvamVjdElkKCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvamVjdElkDQogICAgfSwNCiAgICANCiAgICBpc0VkaXRNb2RlKCkgew0KICAgICAgcmV0dXJuIHRoaXMubW9kZSA9PT0gJ2VkaXQnDQogICAgfSwNCiAgICANCiAgICBpc1ZpZXdNb2RlKCkgew0KICAgICAgcmV0dXJuIHRoaXMubW9kZSA9PT0gJ3ZpZXcnDQogICAgfSwNCiAgICANCiAgICBpc0FwcHJvdmFsTW9kZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLm1vZGUgPT09ICdhcHByb3ZlJw0KICAgIH0sDQogICAgDQogICAgLy8g5piv5ZCm5Li65Y+q6K+75qih5byP77yI5p+l55yL5oiW5a6h5om577yJDQogICAgaXNSZWFkb25seU1vZGUoKSB7DQogICAgICByZXR1cm4gdGhpcy5pc1ZpZXdNb2RlIHx8IHRoaXMuaXNBcHByb3ZhbE1vZGUNCiAgICB9LA0KICAgIA0KICAgIC8vIOaYr+WQpuWFgeiuuOatpemqpOeCueWHuw0KICAgIGFsbG93U3RlcENsaWNrKCkgew0KICAgICAgLy8g5p+l55yL5ZKM5a6h5om55qih5byP5aeL57uI5YWB6K6454K55Ye7DQogICAgICBpZiAodGhpcy5pc1ZpZXdNb2RlIHx8IHRoaXMuaXNBcHByb3ZhbE1vZGUpIHsNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0NCiAgICAgIC8vIOaWsOWinuWSjOS/ruaUueaooeW8j+WFgeiuuOacieadoeS7tueahOeCueWHu++8iOeUsWNsaWNrYWJsZVN0ZXBz5o6n5Yi25YW35L2T5ZOq5Lqb5q2l6aqk5Y+v54K55Ye777yJDQogICAgICByZXR1cm4gdHJ1ZQ0KICAgIH0sDQogICAgDQogICAgLy8g5Y+v54K55Ye755qE5q2l6aqk5YiX6KGoDQogICAgY2xpY2thYmxlU3RlcHMoKSB7DQogICAgICBpZiAodGhpcy5pc1ZpZXdNb2RlIHx8IHRoaXMuaXNBcHByb3ZhbE1vZGUgfHwgdGhpcy5pc0VkaXRNb2RlKSB7DQogICAgICAgIC8vIOafpeeci+OAgeWuoeaJueWSjOS/ruaUueaooeW8j+aJgOacieatpemqpOmDveWPr+eCueWHuw0KICAgICAgICByZXR1cm4gQXJyYXkuZnJvbSh7IGxlbmd0aDogdGhpcy5zdGVwcy5sZW5ndGggfSwgKF8sIGkpID0+IGkpDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOaWsOWinuaooeW8j++8muW9k+WJjeatpemqpOWPiuS5i+WJjeeahOatpemqpOWPr+eCueWHuw0KICAgICAgcmV0dXJuIEFycmF5LmZyb20oeyBsZW5ndGg6IHRoaXMuY3VycmVudFN0ZXAgKyAxIH0sIChfLCBpKSA9PiBpKQ0KICAgIH0sDQogICAgDQogICAgLy8g5qC55o2u6aG555uu57G75Z6L56Gu5a6a5q2l6aqkDQogICAgc3RlcHMoKSB7DQogICAgICBjb25zdCBiYXNlU3RlcHMgPSBbDQogICAgICAgIHsgdGl0bGU6ICfln7rmnKzkv6Hmga8nIH0NCiAgICAgIF0NCiAgICAgIA0KICAgICAgbGV0IHByb2plY3RTdGVwcyA9IFtdDQogICAgICANCiAgICAgIGlmICh0aGlzLnByb2plY3RUeXBlID09PSAnbW9udGhseScpIHsNCiAgICAgICAgLy8g5pyI5bqm5YW75oqk77ya5Z+65pys5L+h5oGvIOKGkiDlhbvmiqTpobnnm64g4oaSIOeXheWus+WFu+aKpCDihpIg5YW75oqk5qGl5qKBDQogICAgICAgIHByb2plY3RTdGVwcyA9IFsNCiAgICAgICAgICAuLi5iYXNlU3RlcHMsDQogICAgICAgICAgeyB0aXRsZTogJ+WFu+aKpOmhueebricgfSwNCiAgICAgICAgICB7IHRpdGxlOiAn55eF5a6z5YW75oqkJyB9LA0KICAgICAgICAgIHsgdGl0bGU6ICflhbvmiqTmoaXmooEnIH0NCiAgICAgICAgXQ0KICAgICAgfSBlbHNlIGlmICh0aGlzLnByb2plY3RUeXBlID09PSAnY2xlYW5pbmcnKSB7DQogICAgICAgIC8vIOS/nea0gemhueebru+8muWfuuacrOS/oeaBryDihpIg5L+d5rSB6aG555uuIOKGkiDkv53mtIHmoaXmooENCiAgICAgICAgcHJvamVjdFN0ZXBzID0gWw0KICAgICAgICAgIC4uLmJhc2VTdGVwcywNCiAgICAgICAgICB7IHRpdGxlOiAn5L+d5rSB6aG555uuJyB9LA0KICAgICAgICAgIHsgdGl0bGU6ICfkv53mtIHmoaXmooEnIH0NCiAgICAgICAgXQ0KICAgICAgfSBlbHNlIGlmICh0aGlzLnByb2plY3RUeXBlID09PSAnZW1lcmdlbmN5Jykgew0KICAgICAgICAvLyDlupTmgKXlhbvmiqTvvJrln7rmnKzkv6Hmga8g4oaSIOW6lOaApemhueebriDihpIg5YW75oqk5qGl5qKBDQogICAgICAgIHByb2plY3RTdGVwcyA9IFsNCiAgICAgICAgICAuLi5iYXNlU3RlcHMsDQogICAgICAgICAgeyB0aXRsZTogJ+W6lOaApemhueebricgfSwNCiAgICAgICAgICB7IHRpdGxlOiAn5YW75oqk5qGl5qKBJyB9DQogICAgICAgIF0NCiAgICAgIH0gZWxzZSBpZiAodGhpcy5wcm9qZWN0VHlwZSA9PT0gJ3ByZXZlbnRpdmUnKSB7DQogICAgICAgIC8vIOmihOmYsuWFu+aKpO+8muWfuuacrOS/oeaBryDihpIg5YWz6IGU55eF5a6zIOKGkiDlrp7mlr3kv6Hmga8g4oaSIOero+W3peS/oeaBrw0KICAgICAgICBwcm9qZWN0U3RlcHMgPSBbDQogICAgICAgICAgLi4uYmFzZVN0ZXBzLA0KICAgICAgICAgIHsgdGl0bGU6ICflhbPogZTnl4XlrrMnIH0sDQogICAgICAgICAgeyB0aXRsZTogJ+WunuaWveS/oeaBrycgfSwNCiAgICAgICAgICB7IHRpdGxlOiAn56uj5bel5L+h5oGvJyB9DQogICAgICAgIF0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOm7mOiupOatpemqpA0KICAgICAgICBwcm9qZWN0U3RlcHMgPSBbDQogICAgICAgICAgLi4uYmFzZVN0ZXBzLA0KICAgICAgICAgIHsgdGl0bGU6ICfpobnnm67phY3nva4nIH0sDQogICAgICAgICAgeyB0aXRsZTogJ+WFs+iBlOWvueixoScgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOWuoeaJueaooeW8j+OAgeafpeeci+aooeW8j+OAgeS/ruaUueaooeW8j+S4i+a3u+WKoOWuoeaJueS/oeaBr+atpemqpA0KICAgICAgaWYgKHRoaXMuaXNBcHByb3ZhbE1vZGUgfHwgdGhpcy5pc1ZpZXdNb2RlIHx8IHRoaXMuaXNFZGl0TW9kZSkgew0KICAgICAgICBwcm9qZWN0U3RlcHMucHVzaCh7IHRpdGxlOiAn5a6h5om55L+h5oGvJyB9KQ0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gcHJvamVjdFN0ZXBzDQogICAgfSwNCiAgICANCiAgICAvLyDmmK/lkKbmmL7npLrpobnnm67phY3nva7mraXpqqQNCiAgICBzaG93UHJvamVjdENvbmZpZygpIHsNCiAgICAgIHJldHVybiBbJ21vbnRobHknLCAnY2xlYW5pbmcnLCAnZW1lcmdlbmN5J10uaW5jbHVkZXModGhpcy5wcm9qZWN0VHlwZSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOaYr+WQpuaYvuekuueXheWus+mFjee9ruatpemqpA0KICAgIHNob3dEaXNlYXNlQ29uZmlnKCkgew0KICAgICAgcmV0dXJuIFsnbW9udGhseScsICdwcmV2ZW50aXZlJ10uaW5jbHVkZXModGhpcy5wcm9qZWN0VHlwZSkNCiAgICB9LA0KICAgIA0KICAgIC8vIOaYr+WQpuWPr+S7pei/m+ihjOWuoeaJueaTjeS9nA0KICAgIGNhbkFwcHJvdmUoKSB7DQogICAgICAvLyDlnKjlrp7pmYXpobnnm67kuK3vvIzov5nph4zlupTor6XmoLnmja7lvZPliY3nlKjmiLfmnYPpmZDlkozpobnnm67nirbmgIHmnaXliKTmlq0NCiAgICAgIC8vIOebruWJjeeugOWMluS4uuWuoeaJueaooeW8j+S4i+WwseWPr+S7peWuoeaJuQ0KICAgICAgcmV0dXJuIHRoaXMuaXNBcHByb3ZhbE1vZGUNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgdmlzaWJsZShuZXdWYWwpIHsNCiAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgLy8g56uL5Y2z5bqU55So5qC35byP77yM6YG/5YWN55m96Imy6Zeq54OBDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmZvcmNlQXBwbHlEaWFsb2dTdHlsZXMoKQ0KICAgICAgICB9KQ0KICAgICAgICANCiAgICAgICAgLy8g5bu26L+f5YaN5qyh5bqU55So77yM56Gu5L+dRWxlbWVudCBVSeWQjue7reeahOagt+W8j+S/ruaUueiiq+imhueblg0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLmZvcmNlQXBwbHlEaWFsb2dTdHlsZXMoKQ0KICAgICAgICB9LCAyMDApDQogICAgICAgIA0KICAgICAgICAvLyDpop3lpJblu7bov5/lupTnlKjooajmoLzmoLflvI/vvIznoa7kv53ooajmoLzmuLLmn5PlrozmiJDlkI7moLflvI/mraPnoa4NCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5mb3JjZUFwcGx5RGlhbG9nU3R5bGVzKCkNCiAgICAgICAgfSwgNTAwKQ0KICAgICAgICANCiAgICAgICAgLy8g5pyA57uI56Gu5L+d5qC35byP5q2j56Gu5bqU55SoDQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuZm9yY2VBcHBseURpYWxvZ1N0eWxlcygpDQogICAgICAgIH0sIDEwMDApDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDnm5HlkKxUYWLliIfmjaLvvIznoa7kv53mr4/kuKpUYWLnmoTmoLflvI/pg73mraPnoa4NCiAgICBjdXJyZW50U3RlcChuZXdWYWwpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5mb3JjZUFwcGx5RGlhbG9nU3R5bGVzKCkNCiAgICAgIH0pDQogICAgICANCiAgICAgIC8vIOW7tui/n+W6lOeUqO+8jOehruS/nVRhYuWGheWuuea4suafk+WujOaIkA0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuZm9yY2VBcHBseURpYWxvZ1N0eWxlcygpDQogICAgICB9LCAxMDApDQogICAgICANCiAgICAgIC8vIOmineWkluW7tui/n++8jOehruS/neihqOagvOaVsOaNruWKoOi9veWujOaIkA0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuZm9yY2VBcHBseURpYWxvZ1N0eWxlcygpDQogICAgICB9LCAzMDApDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIGlmICh0aGlzLmlzRWRpdE1vZGUgfHwgdGhpcy5pc1ZpZXdNb2RlIHx8IHRoaXMuaXNBcHByb3ZhbE1vZGUpIHsNCiAgICAgIGF3YWl0IHRoaXMubG9hZFByb2plY3REYXRhKCkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliqDovb3pobnnm67mlbDmja7vvIjnvJbovpHmqKHlvI/lkozmn6XnnIvmqKHlvI/vvIkNCiAgICBhc3luYyBsb2FkUHJvamVjdERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAgIA0KICAgICAgICAvLyDkuLTml7bkvb/nlKjpnZnmgIHmlbDmja7vvIzlm6DkuLrlkI7nq6/mjqXlj6PmnKrlhazluIMNCiAgICAgICAgLy8g5a6e6ZmF6aG555uu5Lit5bqU6K+l5L2/55So77yaY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRQcm9qZWN0RGV0YWlsKHRoaXMuY3VycmVudFByb2plY3RJZCkNCiAgICAgICAgY29uc3QgbW9ja1Byb2plY3QgPSB0aGlzLmdldE1vY2tQcm9qZWN0RGF0YSgpDQogICAgICAgIA0KICAgICAgICB0aGlzLnByb2plY3RUeXBlID0gbW9ja1Byb2plY3QucHJvamVjdFR5cGUNCiAgICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgICBiYXNpY0luZm86IG1vY2tQcm9qZWN0LmJhc2ljSW5mbyB8fCB7fSwNCiAgICAgICAgICBwcm9qZWN0Q29uZmlnOiBtb2NrUHJvamVjdC5wcm9qZWN0Q29uZmlnIHx8IHt9LA0KICAgICAgICAgIGRpc2Vhc2VDb25maWc6IG1vY2tQcm9qZWN0LmRpc2Vhc2VDb25maWcgfHwge30sDQogICAgICAgICAgaW1wbGVtZW50YXRpb25Db25maWc6IG1vY2tQcm9qZWN0LmltcGxlbWVudGF0aW9uQ29uZmlnIHx8IHt9LA0KICAgICAgICAgIGNvbXBsZXRpb25JbmZvOiBtb2NrUHJvamVjdC5jb21wbGV0aW9uSW5mbyB8fCB7fSwNCiAgICAgICAgICBicmlkZ2VDb25maWc6IG1vY2tQcm9qZWN0LmJyaWRnZUNvbmZpZyB8fCB7fSwNCiAgICAgICAgICBhcHByb3ZhbEluZm86IG1vY2tQcm9qZWN0LmFwcHJvdmFsSW5mbyB8fCB7fQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICAvLyDlrqHmibnmqKHlvI/kuIvpu5jorqTot7PovazliLDlrqHmibnkv6Hmga/pobXpnaLvvIjmnIDlkI7kuIDmraXvvIkNCiAgICAgICAgaWYgKHRoaXMuaXNBcHByb3ZhbE1vZGUpIHsNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmN1cnJlbnRTdGVwID0gdGhpcy5nZXRMYXN0U3RlcEluZGV4KCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3pobnnm67mlbDmja7lpLHotKUnKQ0KICAgICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5bmqKHmi5/pobnnm67mlbDmja4NCiAgICBnZXRNb2NrUHJvamVjdERhdGEoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBwcm9qZWN0VHlwZTogJ21vbnRobHknLA0KICAgICAgICBiYXNpY0luZm86IHsNCiAgICAgICAgICBwcm9qZWN0TmFtZTogJzIwMjTlubTluqbmoaXmooHmnIjluqblhbvmiqTpobnnm64nLA0KICAgICAgICAgIHByb2plY3RUeXBlOiAnbW9udGhseScsDQogICAgICAgICAgc3RhcnREYXRlOiAnMjAyNC0wMS0wMScsDQogICAgICAgICAgZW5kRGF0ZTogJzIwMjQtMTItMzEnLA0KICAgICAgICAgIG1hbmFnZW1lbnRVbml0OiAn5biC5qGl5qKB566h55CG5aSEJywNCiAgICAgICAgICBzdXBlcnZpc2lvblVuaXQ6ICfluILkuqTpgJrlsYAnLA0KICAgICAgICAgIG1haW50ZW5hbmNlVW5pdDogJ+ahpeaigeWFu+aKpOaciemZkOWFrOWPuCcsDQogICAgICAgICAgbWFuYWdlcjogJ+W8oOS4iScsDQogICAgICAgICAgY29udGFjdFBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICAgIHdvcmtsb2FkOiAnMTAw5LiH5YWDJywNCiAgICAgICAgICBwcm9qZWN0Q29udGVudDogJ+Wvuei+luWMuuWGheahpeaigei/m+ihjOaciOW6puW4uOinhOWFu+aKpO+8jOWMheaLrOa4hea0geOAgeajgOafpeOAgeWwj+S/ruetieW3peS9nCcsDQogICAgICAgICAgYXR0YWNobWVudHM6IFtdDQogICAgICAgIH0sDQogICAgICAgIHByb2plY3RDb25maWc6IHsNCiAgICAgICAgICBwcm9qZWN0czogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5qGl6Z2i5riF5rSBJywgZnJlcXVlbmN5OiAzMCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5qCP5p2G57u05oqkJywgZnJlcXVlbmN5OiA2MCB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5Ly457yp57yd5qOA5p+lJywgZnJlcXVlbmN5OiA5MCB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICBkaXNlYXNlQ29uZmlnOiB7DQogICAgICAgICAgZGlzZWFzZXM6IFsNCiAgICAgICAgICAgIHsgaWQ6IDEsIG5hbWU6ICfmoaXpnaLnoLTmjZ8nLCBsZXZlbDogJ+i9u+W+ricgfSwNCiAgICAgICAgICAgIHsgaWQ6IDIsIG5hbWU6ICfmoI/mnYbmnb7liqgnLCBsZXZlbDogJ+S4gOiIrCcgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgaW1wbGVtZW50YXRpb25Db25maWc6IHsNCiAgICAgICAgICB3b3JrczogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5pa95bel5YeG5aSHJyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn546w5Zy65L2c5LiaJyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn6LSo6YeP5qOA5p+lJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICBjb21wbGV0aW9uSW5mbzogew0KICAgICAgICAgIGNvbXBsZXRpb25EYXRlOiAnMjAyNC0xMi0zMScsDQogICAgICAgICAgYWNjZXB0YW5jZURhdGU6ICcyMDI1LTAxLTE1JywNCiAgICAgICAgICBxdWFsaXR5R3JhZGU6ICflkIjmoLwnLA0KICAgICAgICAgIHJlbWFyazogJ+mhueebruaMieacn+WujOaIkO+8jOi0qOmHj+i+vuaghycNCiAgICAgICAgfSwNCiAgICAgICAgYnJpZGdlQ29uZmlnOiB7DQogICAgICAgICAgYnJpZGdlczogWw0KICAgICAgICAgICAgeyBpZDogMSwgbmFtZTogJ+S6uuawkeahpScsIGxvY2F0aW9uOiAn5Lq65rCR6LevJywgc3BhbjogJzUwbScgfSwNCiAgICAgICAgICAgIHsgaWQ6IDIsIG5hbWU6ICfog5zliKnmoaUnLCBsb2NhdGlvbjogJ+iDnOWIqei3rycsIHNwYW46ICczMG0nIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIGFwcHJvdmFsSW5mbzogew0KICAgICAgICAgIGFwcHJvdmFsSGlzdG9yeTogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBpZDogMSwNCiAgICAgICAgICAgICAgc3RlcE5hbWU6ICflvIDlp4vnlLPor7cnLA0KICAgICAgICAgICAgICBhcHByb3ZlcjogJ+m7hOaYreiogCcsDQogICAgICAgICAgICAgIHN0YXR1czogJ+mAmui/hycsDQogICAgICAgICAgICAgIGNvbW1lbnQ6ICfml6DlvILorq4nLA0KICAgICAgICAgICAgICBkZXBhcnRtZW50OiAn5YW75oqk5YWs5Y+4JywNCiAgICAgICAgICAgICAgcmVjZWl2ZVRpbWU6ICcyMDI1LTA5LTE4XG4xMDo0MycsDQogICAgICAgICAgICAgIHByb2Nlc3NUaW1lOiAnMjAyNS0wOS0xOFxuMTA6NDMnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBpZDogMiwNCiAgICAgICAgICAgICAgc3RlcE5hbWU6ICflhbvmiqTpobnnm67lrqHmibko5LiA57qnKScsDQogICAgICAgICAgICAgIGFwcHJvdmVyOiAn5YiY6Zuo5qGQJywNCiAgICAgICAgICAgICAgc3RhdHVzOiAn6YCa6L+HJywNCiAgICAgICAgICAgICAgY29tbWVudDogJ+mhueebruaWueahiOWQiOeQhu+8jOWQjOaEj+W8gOWxlScsDQogICAgICAgICAgICAgIGRlcGFydG1lbnQ6ICfmioDmnK/pg6jpl6gnLA0KICAgICAgICAgICAgICByZWNlaXZlVGltZTogJzIwMjUtMDktMThcbjExOjAwJywNCiAgICAgICAgICAgICAgcHJvY2Vzc1RpbWU6ICcyMDI1LTA5LTE4XG4xMTozMCcNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGlkOiAzLA0KICAgICAgICAgICAgICBzdGVwTmFtZTogJ+WFu+aKpOmhueebruWuoeaJuSjkuoznuqcpJywNCiAgICAgICAgICAgICAgYXBwcm92ZXI6ICfnvZfnoJrnp4snLA0KICAgICAgICAgICAgICBzdGF0dXM6ICfpgJrov4cnLA0KICAgICAgICAgICAgICBjb21tZW50OiAn6aKE566X5ZCI55CG77yM5om55YeG5omn6KGMJywNCiAgICAgICAgICAgICAgZGVwYXJ0bWVudDogJ+i0ouWKoemDqOmXqCcsDQogICAgICAgICAgICAgIHJlY2VpdmVUaW1lOiAnMjAyNS0wOS0xOFxuMTQ6MDAnLA0KICAgICAgICAgICAgICBwcm9jZXNzVGltZTogJzIwMjUtMDktMThcbjE1OjIwJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W5pyA5ZCO5LiA5q2l55qE57Si5byVDQogICAgZ2V0TGFzdFN0ZXBJbmRleCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnN0ZXBzLmxlbmd0aCAtIDENCiAgICB9LA0KICAgIA0KICAgIC8vIOmhueebruexu+Wei+WPmOWMlg0KICAgIGhhbmRsZVByb2plY3RUeXBlQ2hhbmdlKHR5cGUpIHsNCiAgICAgIGlmICh0aGlzLnByb2plY3RUeXBlID09PSB0eXBlKSB7DQogICAgICAgIHJldHVybiAvLyDpmLLmraLph43lpI3op6blj5ENCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5wcm9qZWN0VHlwZSA9IHR5cGUNCiAgICAgIA0KICAgICAgLy8g5L2/55SobmV4dFRpY2vnoa7kv53mraXpqqTorqHnrpflrozmiJDlkI7lho3mo4Dmn6XlvZPliY3mraXpqqQNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g6YeN572u5ZCO57ut5q2l6aqk55qE5pWw5o2uDQogICAgICAgIHRoaXMuZm9ybURhdGEucHJvamVjdENvbmZpZyA9IHt9DQogICAgICAgIHRoaXMuZm9ybURhdGEuZGlzZWFzZUNvbmZpZyA9IHt9DQogICAgICAgIHRoaXMuZm9ybURhdGEuYnJpZGdlQ29uZmlnID0ge30NCiAgICAgICAgDQogICAgICAgIC8vIOWmguaenOW9k+WJjeatpemqpOi2heWHuuaWsOeahOatpemqpOiMg+WbtO+8jOWbnuWIsOesrOS4gOatpQ0KICAgICAgICBpZiAodGhpcy5jdXJyZW50U3RlcCA+PSB0aGlzLnN0ZXBzLmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICANCiAgICAvLyDmraXpqqTngrnlh7sNCiAgICBhc3luYyBoYW5kbGVTdGVwQ2xpY2soc3RlcEluZGV4KSB7DQogICAgICAvLyDmn6XnnIvjgIHlrqHmibnlkozkv67mlLnmqKHlvI/lj6/ku6Xoh6rnlLHliIfmjaINCiAgICAgIGlmICh0aGlzLmlzVmlld01vZGUgfHwgdGhpcy5pc0FwcHJvdmFsTW9kZSB8fCB0aGlzLmlzRWRpdE1vZGUpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IHN0ZXBJbmRleA0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5paw5aKe5qih5byP55qE6YC76L6RDQogICAgICAvLyDlpoLmnpzngrnlh7vnmoTmmK/lvZPliY3mraXpqqTmiJbkuYvliY3nmoTmraXpqqTvvIzlhYHorrjnm7TmjqXliIfmjaINCiAgICAgIGlmIChzdGVwSW5kZXggPD0gdGhpcy5jdXJyZW50U3RlcCkgew0KICAgICAgICB0aGlzLmN1cnJlbnRTdGVwID0gc3RlcEluZGV4DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDlpoLmnpzot7PovazliLDlkI7pnaLnmoTmraXpqqTvvIzpnIDopoHpqozor4Hku47lvZPliY3mraXpqqTliLDnm67moIfmraXpqqTnmoTmiYDmnInmraXpqqQNCiAgICAgIGZvciAobGV0IGkgPSB0aGlzLmN1cnJlbnRTdGVwOyBpIDwgc3RlcEluZGV4OyBpKyspIHsNCiAgICAgICAgY29uc3QgaXNWYWxpZCA9IGF3YWl0IHRoaXMudmFsaWRhdGVTdGVwQnlJbmRleChpKQ0KICAgICAgICBpZiAoIWlzVmFsaWQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOivt+WFiOWujOaIkOesrCR7aSArIDF95q2l55qE5b+F5aGr5L+h5oGvYCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyDmiYDmnInpqozor4HpgJrov4fvvIzot7PovazliLDnm67moIfmraXpqqQNCiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSBzdGVwSW5kZXgNCiAgICB9LA0KICAgIA0KICAgIC8vIOS4iuS4gOatpQ0KICAgIGhhbmRsZVByZXZTdGVwKCkgew0KICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPiAwKSB7DQogICAgICAgIHRoaXMuY3VycmVudFN0ZXAtLQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5LiL5LiA5q2lDQogICAgYXN5bmMgaGFuZGxlTmV4dFN0ZXAoKSB7DQogICAgICBjb25zdCBpc1ZhbGlkID0gYXdhaXQgdGhpcy52YWxpZGF0ZUN1cnJlbnRTdGVwKCkNCiAgICAgIGlmICghaXNWYWxpZCkgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgaWYgKHRoaXMuY3VycmVudFN0ZXAgPCB0aGlzLmdldExhc3RTdGVwSW5kZXgoKSkgew0KICAgICAgICB0aGlzLmN1cnJlbnRTdGVwKysNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOmqjOivgeW9k+WJjeatpemqpA0KICAgIGFzeW5jIHZhbGlkYXRlQ3VycmVudFN0ZXAoKSB7DQogICAgICByZXR1cm4gYXdhaXQgdGhpcy52YWxpZGF0ZVN0ZXBCeUluZGV4KHRoaXMuY3VycmVudFN0ZXApDQogICAgfSwNCiAgICANCiAgICAvLyDpqozor4HmjIflrprmraXpqqQNCiAgICBhc3luYyB2YWxpZGF0ZVN0ZXBCeUluZGV4KHN0ZXBJbmRleCkgew0KICAgICAgY29uc3QgY29tcG9uZW50TmFtZSA9IHRoaXMuZ2V0U3RlcENvbXBvbmVudEJ5SW5kZXgoc3RlcEluZGV4KQ0KICAgICAgDQogICAgICBpZiAoY29tcG9uZW50TmFtZSAmJiB0aGlzLiRyZWZzW2NvbXBvbmVudE5hbWVdKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzdWx0ID0gdGhpcy4kcmVmc1tjb21wb25lbnROYW1lXS52YWxpZGF0ZSgpDQogICAgICAgICAgLy8g5aaC5p6c6L+U5ZueUHJvbWlzZe+8jOetieW+hee7k+aenA0KICAgICAgICAgIGlmIChyZXN1bHQgJiYgdHlwZW9mIHJlc3VsdC50aGVuID09PSAnZnVuY3Rpb24nKSB7DQogICAgICAgICAgICByZXR1cm4gYXdhaXQgcmVzdWx0DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIOWmguaenOi/lOWbnmJvb2xlYW7vvIznm7TmjqXov5Tlm54NCiAgICAgICAgICByZXR1cm4gcmVzdWx0DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6aqM6K+B5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiB0cnVlDQogICAgfSwNCiAgICANCiAgICAvLyDmoLnmja7mraXpqqTntKLlvJXojrflj5bnu4Tku7blkI0NCiAgICBnZXRTdGVwQ29tcG9uZW50QnlJbmRleChzdGVwSW5kZXgpIHsNCiAgICAgIGlmIChzdGVwSW5kZXggPT09IDApIHsNCiAgICAgICAgcmV0dXJuICdiYXNpY0luZm8nDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOagueaNrumhueebruexu+Wei+WSjOatpemqpOe0ouW8leehruWumue7hOS7tg0KICAgICAgaWYgKHRoaXMucHJvamVjdFR5cGUgPT09ICdtb250aGx5Jykgew0KICAgICAgICAvLyDmnIjluqblhbvmiqTvvJrln7rmnKzkv6Hmga8oMCkg4oaSIOWFu+aKpOmhueebrigxKSDihpIg55eF5a6z5YW75oqkKDIpIOKGkiDlhbvmiqTmoaXmooEoMykNCiAgICAgICAgaWYgKHN0ZXBJbmRleCA9PT0gMSkgcmV0dXJuICdwcm9qZWN0Q29uZmlnJw0KICAgICAgICBpZiAoc3RlcEluZGV4ID09PSAyKSByZXR1cm4gJ2Rpc2Vhc2VDb25maWcnDQogICAgICAgIGlmIChzdGVwSW5kZXggPT09IDMpIHJldHVybiAnYnJpZGdlQ29uZmlnJw0KICAgICAgfSBlbHNlIGlmICh0aGlzLnByb2plY3RUeXBlID09PSAnY2xlYW5pbmcnIHx8IHRoaXMucHJvamVjdFR5cGUgPT09ICdlbWVyZ2VuY3knKSB7DQogICAgICAgIC8vIOS/nea0gS/lupTmgKXvvJrln7rmnKzkv6Hmga8oMCkg4oaSIOmhueebrumFjee9rigxKSDihpIg5qGl5qKB6YWN572uKDIpDQogICAgICAgIGlmIChzdGVwSW5kZXggPT09IDEpIHJldHVybiAncHJvamVjdENvbmZpZycNCiAgICAgICAgaWYgKHN0ZXBJbmRleCA9PT0gMikgcmV0dXJuICdicmlkZ2VDb25maWcnDQogICAgICB9IGVsc2UgaWYgKHRoaXMucHJvamVjdFR5cGUgPT09ICdwcmV2ZW50aXZlJykgew0KICAgICAgICAvLyDpooTpmLLlhbvmiqTvvJrln7rmnKzkv6Hmga8oMCkg4oaSIOWFs+iBlOeXheWusygxKSDihpIg5a6e5pa95L+h5oGvKDIpIOKGkiDnq6Plt6Xkv6Hmga8oMykNCiAgICAgICAgaWYgKHN0ZXBJbmRleCA9PT0gMSkgcmV0dXJuICdkaXNlYXNlQ29uZmlnJw0KICAgICAgICBpZiAoc3RlcEluZGV4ID09PSAyKSByZXR1cm4gJ2ltcGxlbWVudGF0aW9uQ29uZmlnJw0KICAgICAgICBpZiAoc3RlcEluZGV4ID09PSAzKSByZXR1cm4gJ2NvbXBsZXRpb25JbmZvJyAvLyDnq6Plt6Xkv6Hmga8NCiAgICAgICAgcmV0dXJuIG51bGwgLy8g6aKE6Ziy5YW75oqk6aG555uu5rKh5pyJ56ysNOatpQ0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gbnVsbA0KICAgIH0sDQogICAgDQogICAgLy8g5YiH5o2i5Yiw57yW6L6R5qih5byPDQogICAgaGFuZGxlRWRpdCgpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ2VkaXQnLCB0aGlzLmN1cnJlbnRQcm9qZWN0SWQpDQogICAgfSwNCiAgICANCiAgICAvLyDkv53lrZjojYnnqL8NCiAgICBhc3luYyBoYW5kbGVTYXZlKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICAgIC4uLnRoaXMuZm9ybURhdGEsDQogICAgICAgICAgc3RhdHVzOiAnZHJhZnQnLA0KICAgICAgICAgIGluZnJhc3RydWN0dXJlVHlwZTogdGhpcy5pbmZyYXN0cnVjdHVyZVR5cGUNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgaWYgKHRoaXMuaXNFZGl0TW9kZSkgew0KICAgICAgICAgIGF3YWl0IHVwZGF0ZVByb2plY3QodGhpcy5jdXJyZW50UHJvamVjdElkLCBkYXRhKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBhd2FpdCBjcmVhdGVQcm9qZWN0KGRhdGEpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKQ0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgICB0aGlzLiRlbWl0KCdzYXZlJywgZGF0YSkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g5o+Q5Lqk6aG555uuDQogICAgYXN5bmMgaGFuZGxlU3VibWl0KCkgew0KICAgICAgLy8g6aqM6K+B5omA5pyJ5q2l6aqkDQogICAgICBpZiAoIXRoaXMudmFsaWRhdGVBbGxTdGVwcygpKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgLi4udGhpcy5mb3JtRGF0YSwNCiAgICAgICAgICBzdGF0dXM6ICdwZW5kaW5nJywNCiAgICAgICAgICBpbmZyYXN0cnVjdHVyZVR5cGU6IHRoaXMuaW5mcmFzdHJ1Y3R1cmVUeXBlDQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIGlmICh0aGlzLmlzRWRpdE1vZGUpIHsNCiAgICAgICAgICBhd2FpdCB1cGRhdGVQcm9qZWN0KHRoaXMuY3VycmVudFByb2plY3RJZCwgZGF0YSkNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+abtOaWsOaIkOWKnycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgYXdhaXQgY3JlYXRlUHJvamVjdChkYXRhKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgdGhpcy4kZW1pdCgnc3VibWl0JywgZGF0YSkNCiAgICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHRoaXMuaXNFZGl0TW9kZSA/ICfmm7TmlrDlpLHotKUnIDogJ+aPkOS6pOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6aqM6K+B5omA5pyJ5q2l6aqkDQogICAgdmFsaWRhdGVBbGxTdGVwcygpIHsNCiAgICAgIC8vIOi/memHjOW6lOivpemqjOivgeaJgOacieatpemqpOeahOaVsOaNrg0KICAgICAgcmV0dXJuIHRydWUNCiAgICB9LA0KICAgIA0KICAgIC8vIOWkhOeQhuWuoeaJueaPkOS6pOe7k+aenA0KICAgIGhhbmRsZUFwcHJvdmFsU3VibWl0dGVkKGRhdGEpIHsNCiAgICAgIGNvbnN0IHsgcmVzdWx0IH0gPSBkYXRhDQogICAgICANCiAgICAgIGlmIChyZXN1bHQgPT09ICdhcHByb3ZlZCcpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflrqHmibnpgJrov4fmiJDlip8nKQ0KICAgICAgfSBlbHNlIGlmIChyZXN1bHQgPT09ICdyZWplY3RlZCcpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LpqbPlm57nlLPor7cnKQ0KICAgICAgfSBlbHNlIGlmIChyZXN1bHQgPT09ICdyZXR1cm5lZCcpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7LpgIDlm57kv67mlLknKQ0KICAgICAgfQ0KICAgICAgDQogICAgICB0aGlzLiRlbWl0KCdhcHByb3ZhbC1jb21wbGV0ZWQnLCBkYXRhKQ0KICAgICAgDQogICAgICAvLyDlrqHmibnlrozmiJDlkI7lhbPpl63pobXpnaINCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICAgIH0sIDE1MDApDQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIblrqHmibnlj5bmtogNCiAgICBoYW5kbGVBcHByb3ZhbENhbmNlbCgpIHsNCiAgICAgIC8vIOWPr+S7pemAieaLqeWFs+mXremhtemdouaIluWbnuWIsOS4iuS4gOatpQ0KICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpDQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIblrqHmibnpgJrov4cNCiAgICBhc3luYyBoYW5kbGVBcHByb3ZlKCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMuYXBwcm92YWxJbmZvKSB7DQogICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMuJHJlZnMuYXBwcm92YWxJbmZvLmFwcHJvdmVQcm9qZWN0KCkNCiAgICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAgIC8vIOWuoeaJueaIkOWKn++8jOWPr+S7peWFs+mXreW8ueeql+aIlui/m+ihjOWFtuS7luaTjeS9nA0KICAgICAgICAgIC8vIHRoaXMuaGFuZGxlQ2xvc2UoKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDlpITnkIblrqHmibnpgIDlm54NCiAgICBhc3luYyBoYW5kbGVSZWplY3QoKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy5hcHByb3ZhbEluZm8pIHsNCiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy4kcmVmcy5hcHByb3ZhbEluZm8ucmVqZWN0UHJvamVjdCgpDQogICAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgICAvLyDpgIDlm57miJDlip/vvIzlj6/ku6XlhbPpl63lvLnnqpfmiJbov5vooYzlhbbku5bmk43kvZwNCiAgICAgICAgICAvLyB0aGlzLmhhbmRsZUNsb3NlKCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlupTnlKjmt7HoibLkuLvpopjmoLflvI8NCiAgICBmb3JjZUFwcGx5RGlhbG9nU3R5bGVzKCkgew0KICAgICAgLy8g5Y+C6ICD5beh5qOA5by55qGG55qE5a6e546w77yM5by65Yi25bqU55So5rex6Imy5Li76aKY5qC35byPDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgY29uc3QgZGlhbG9nID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmVsLWRpYWxvZy5wcm9qZWN0LWRpYWxvZycpIHx8DQogICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5lbC1kaWFsb2cuaW5zcGVjdGlvbi1kaWFsb2ctYmFzZScpIHx8DQogICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5lbC1kaWFsb2cnKQ0KICAgICAgICANCiAgICAgICAgaWYgKGRpYWxvZykgew0KICAgICAgICAgIC8vIOW6lOeUqOa3seiJsuS4u+mimOWIsOWQhOS4quWMuuWfnw0KICAgICAgICAgIHRoaXMuYXBwbHlEYXJrVGhlbWVUb0FsbEFyZWFzKGRpYWxvZykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzov5jmsqHmib7liLDvvIzlho3mrKHlsJ3or5UNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHRoaXMuZm9yY2VBcHBseURpYWxvZ1N0eWxlcygpLCAyMDApDQogICAgICAgIH0NCiAgICAgIH0sIDEwMCkNCiAgICB9LA0KICAgIA0KICAgIC8vIOW6lOeUqOa3seiJsuS4u+mimOWIsOW8ueahhueahOaJgOacieWMuuWfnyAtIOWPguiAg+W3oeajgOW8ueahhuWunueOsA0KICAgIGFwcGx5RGFya1RoZW1lVG9BbGxBcmVhcyhkaWFsb2dFbGVtZW50ID0gbnVsbCkgew0KICAgICAgLy8g5aaC5p6c5rKh5pyJ5Lyg5YWlZGlhbG9n5YWD57Sg77yM5bCd6K+V5p+l5om+DQogICAgICBjb25zdCBkaWFsb2cgPSBkaWFsb2dFbGVtZW50IHx8IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5lbC1kaWFsb2cnKQ0KICAgICAgDQogICAgICBpZiAoIWRpYWxvZykgew0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g6aaW5YWI5by65Yi26K6+572u5by55qGG5pys6Lqr55qE5bC65a+4DQogICAgICBjb25zdCBkaWFsb2dTdHlsZXMgPSB7DQogICAgICAgICd3aWR0aCc6ICc3MCUnLA0KICAgICAgICAnbWluLXdpZHRoJzogJzgwMHB4JywNCiAgICAgICAgJ2hlaWdodCc6ICc4NXZoJywNCiAgICAgICAgJ21heC1oZWlnaHQnOiAnODV2aCcsDQogICAgICAgICdtYXJnaW4tdG9wJzogJzcuNXZoJywNCiAgICAgICAgJ3Bvc2l0aW9uJzogJ3JlbGF0aXZlJw0KICAgICAgfQ0KICAgICAgT2JqZWN0LmtleXMoZGlhbG9nU3R5bGVzKS5mb3JFYWNoKHByb3BlcnR5ID0+IHsNCiAgICAgICAgZGlhbG9nLnN0eWxlLnNldFByb3BlcnR5KHByb3BlcnR5LCBkaWFsb2dTdHlsZXNbcHJvcGVydHldLCAnaW1wb3J0YW50JykNCiAgICAgIH0pDQogICAgICANCiAgICAgIC8vIEhlYWRlcuWMuuWfnyAtIOS9v+eUqGRpYWxvZ+WFg+e0oOS9nOS4uuWfuuehgOafpeaJvg0KICAgICAgY29uc3QgaGVhZGVyID0gZGlhbG9nLnF1ZXJ5U2VsZWN0b3IoJy5lbC1kaWFsb2dfX2hlYWRlcicpDQogICAgICANCiAgICAgIGlmIChoZWFkZXIpIHsNCiAgICAgICAgY29uc3QgaGVhZGVyU3R5bGVzID0gew0KICAgICAgICAgICdiYWNrZ3JvdW5kJzogJyMwOTFBNEInLA0KICAgICAgICAgICdjb2xvcic6ICcjZjFmNWY5JywNCiAgICAgICAgICAnYm9yZGVyLWJvdHRvbSc6ICcxcHggc29saWQgI2ZmZmZmZicsIC8vIOeZveiJsuWIhuWJsue6v++8jOS4juW3oeajgOaXpeW/l+S4gOiHtA0KICAgICAgICAgICdwYWRkaW5nJzogJzIwcHggMjRweCcNCiAgICAgICAgfQ0KICAgICAgICBPYmplY3Qua2V5cyhoZWFkZXJTdHlsZXMpLmZvckVhY2gocHJvcGVydHkgPT4gew0KICAgICAgICAgIGhlYWRlci5zdHlsZS5zZXRQcm9wZXJ0eShwcm9wZXJ0eSwgaGVhZGVyU3R5bGVzW3Byb3BlcnR5XSwgJ2ltcG9ydGFudCcpDQogICAgICAgIH0pDQogICAgICAgIA0KICAgICAgICBjb25zdCB0aXRsZSA9IGhlYWRlci5xdWVyeVNlbGVjdG9yKCcuZWwtZGlhbG9nX190aXRsZScpDQogICAgICAgIGlmICh0aXRsZSkgew0KICAgICAgICAgIHRpdGxlLnN0eWxlLnNldFByb3BlcnR5KCdjb2xvcicsICcjZmZmZmZmJywgJ2ltcG9ydGFudCcpDQogICAgICAgICAgdGl0bGUuc3R5bGUuc2V0UHJvcGVydHkoJ2ZvbnQtd2VpZ2h0JywgJzYwMCcsICdpbXBvcnRhbnQnKQ0KICAgICAgICAgIHRpdGxlLnN0eWxlLnNldFByb3BlcnR5KCdmb250LXNpemUnLCAnMThweCcsICdpbXBvcnRhbnQnKQ0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC8vIEJvZHnljLrln58gLSDlvLrliLborr7nva7lm7rlrprpq5jluqblkozmu5rliqgNCiAgICAgIGNvbnN0IGJvZHkgPSBkaWFsb2cucXVlcnlTZWxlY3RvcignLmVsLWRpYWxvZ19fYm9keScpDQogICAgICBpZiAoYm9keSkgew0KICAgICAgICBjb25zdCBib2R5U3R5bGVzID0gew0KICAgICAgICAgICdiYWNrZ3JvdW5kJzogJyMwOTFBNEInLA0KICAgICAgICAgICdjb2xvcic6ICcjZjFmNWY5JywNCiAgICAgICAgICAnaGVpZ2h0JzogJ2NhbGMoODV2aCAtIDE0MHB4KScsDQogICAgICAgICAgJ21heC1oZWlnaHQnOiAnY2FsYyg4NXZoIC0gMTQwcHgpJywNCiAgICAgICAgICAnb3ZlcmZsb3cteSc6ICdhdXRvJywNCiAgICAgICAgICAncGFkZGluZyc6ICcwJywNCiAgICAgICAgICAnYm94LXNpemluZyc6ICdib3JkZXItYm94Jw0KICAgICAgICB9DQogICAgICAgIE9iamVjdC5rZXlzKGJvZHlTdHlsZXMpLmZvckVhY2gocHJvcGVydHkgPT4gew0KICAgICAgICAgIGJvZHkuc3R5bGUuc2V0UHJvcGVydHkocHJvcGVydHksIGJvZHlTdHlsZXNbcHJvcGVydHldLCAnaW1wb3J0YW50JykNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOWFs+mXreW8ueahhg0KICAgIGhhbmRsZUNsb3NlKCkgew0KICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IDANCiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgZmFsc2UpDQogICAgICB0aGlzLiRlbWl0KCdjbG9zZScpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["ProjectDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ProjectDialog.vue", "sourceRoot": "src/components/Maintenance", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    :title=\"modalTitle\"\r\n    :visible.sync=\"dialogVisible\"\r\n    :before-close=\"handleClose\"\r\n    :append-to-body=\"true\"\r\n    :modal-append-to-body=\"true\"\r\n    :close-on-click-modal=\"false\"\r\n    :show-close=\"false\"\r\n    custom-class=\"project-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\r\n    top=\"5vh\"\r\n  >\r\n    <!-- 自定义关闭按钮 -->\r\n    <div class=\"custom-close-btn\" @click=\"handleClose\">\r\n      <svg width=\"36\" height=\"36\" viewBox=\"0 0 36 36\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n        <path d=\"M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z\" fill=\"white\"/>\r\n      </svg>\r\n    </div>\r\n\r\n    <!-- 步骤导航 -->\r\n    <step-navigation\r\n      :steps=\"steps\"\r\n      :current-step=\"currentStep\"\r\n      :allow-click=\"allowStepClick\"\r\n      :clickable-steps=\"clickableSteps\"\r\n      @step-click=\"handleStepClick\"\r\n    />\r\n    \r\n    <!-- 步骤内容 -->\r\n    <div class=\"step-content\">\r\n      <!-- 基本信息 -->\r\n      <basic-info\r\n        v-if=\"currentStep === 0\"\r\n        ref=\"basicInfo\"\r\n        v-model=\"formData.basicInfo\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n        @project-type-change=\"handleProjectTypeChange\"\r\n      />\r\n      \r\n      <!-- 养护项目配置 -->\r\n      <project-config\r\n        v-if=\"currentStep === 1 && showProjectConfig\"\r\n        ref=\"projectConfig\"\r\n        v-model=\"formData.projectConfig\"\r\n        :project-type=\"projectType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 病害养护配置 -->\r\n      <disease-config\r\n        v-if=\"(currentStep === 2 && projectType === 'monthly') || (currentStep === 1 && projectType === 'preventive')\"\r\n        ref=\"diseaseConfig\"\r\n        v-model=\"formData.diseaseConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 实施信息配置 - 预防养护第三步 -->\r\n      <implementation-config\r\n        v-if=\"currentStep === 2 && projectType === 'preventive'\"\r\n        ref=\"implementationConfig\"\r\n        v-model=\"formData.implementationConfig\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 竣工信息配置 - 预防养护第四步 -->\r\n      <completion-info\r\n        v-if=\"currentStep === 3 && projectType === 'preventive'\"\r\n        ref=\"completionInfo\"\r\n        v-model=\"formData.completionInfo\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 养护桥梁配置 - 只在非审批/查看/修改模式下显示 -->\r\n      <bridge-config\r\n        v-if=\"currentStep === getLastStepIndex() && projectType !== 'preventive' && !isApprovalMode && !isViewMode && !isEditMode\"\r\n        ref=\"bridgeConfig\"\r\n        v-model=\"formData.bridgeConfig\"\r\n        :infrastructure-type=\"infrastructureType\"\r\n        :readonly=\"isReadonlyMode\"\r\n      />\r\n      \r\n      <!-- 审批信息 - 审批模式、查看模式、修改模式的最后一步显示 -->\r\n      <approval-info\r\n        v-if=\"currentStep === getLastStepIndex() && (isApprovalMode || isViewMode || isEditMode)\"\r\n        ref=\"approvalInfo\"\r\n        v-model=\"formData.approvalInfo\"\r\n        :project-id=\"currentProjectId\"\r\n        :show-approval-form=\"canApprove\"\r\n        @approval-submitted=\"handleApprovalSubmitted\"\r\n        @cancel=\"handleApprovalCancel\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 弹框底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <template v-if=\"isReadonlyMode && !isApprovalMode\">\r\n        <!-- 只读模式只显示关闭按钮 -->\r\n        <el-button @click=\"handleClose\">关闭</el-button>\r\n        <el-button v-if=\"isViewMode\" type=\"primary\" @click=\"handleEdit\">编辑</el-button>\r\n      </template>\r\n      <template v-else-if=\"isApprovalMode\">\r\n        <!-- 审批模式显示审批按钮 -->\r\n        <el-button @click=\"handleReject\" class=\"reject-btn\">退回</el-button>\r\n        <el-button type=\"primary\" @click=\"handleApprove\" class=\"approve-btn\">通过</el-button>\r\n      </template>\r\n      <template v-else-if=\"isEditMode && currentStep === getLastStepIndex()\">\r\n        <!-- 修改模式：在审批信息页只显示保存/提交，不显示上一步和更新 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\">提交</el-button>\r\n      </template>\r\n      <template v-else>\r\n        <!-- 非修改模式或非最后一步：显示正常的步骤按钮 -->\r\n        <el-button @click=\"handleSave\">保存</el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep > 0\"\r\n          @click=\"handlePrevStep\"\r\n        >\r\n          上一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep < getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleNextStep\"\r\n        >\r\n          下一步\r\n        </el-button>\r\n        \r\n        <el-button\r\n          v-if=\"currentStep === getLastStepIndex()\"\r\n          type=\"primary\"\r\n          @click=\"handleSubmit\"\r\n        >\r\n          {{ isEditMode ? '更新' : '提交' }}\r\n        </el-button>\r\n      </template>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport StepNavigation from '@/components/Maintenance/StepNavigation'\r\nimport BasicInfo from '@/views/maintenance/projects/create/components/BasicInfo'\r\nimport ProjectConfig from '@/views/maintenance/projects/create/components/ProjectConfig'\r\nimport DiseaseConfig from '@/views/maintenance/projects/create/components/DiseaseConfig'\r\nimport BridgeConfig from '@/views/maintenance/projects/create/components/BridgeConfig'\r\nimport ImplementationConfig from '@/views/maintenance/projects/create/components/ImplementationConfig'\r\nimport CompletionInfo from '@/views/maintenance/projects/create/components/CompletionInfo'\r\nimport ApprovalInfo from '@/views/maintenance/projects/create/components/ApprovalInfo'\r\nimport { getProjectDetail, createProject, updateProject } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ProjectDialog',\r\n  components: {\r\n    StepNavigation,\r\n    BasicInfo,\r\n    ProjectConfig,\r\n    DiseaseConfig,\r\n    BridgeConfig,\r\n    ImplementationConfig,\r\n    CompletionInfo,\r\n    ApprovalInfo\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    mode: {\r\n      type: String,\r\n      required: true,\r\n      validator: value => ['create', 'edit', 'view', 'approve'].includes(value)\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    infrastructureType: {\r\n      type: String,\r\n      default: 'bridge'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      currentStep: 0,\r\n      projectType: 'monthly', // 默认为月度养护\r\n      \r\n      // 表单数据\r\n      formData: {\r\n        basicInfo: {},\r\n        projectConfig: {},\r\n        diseaseConfig: {},\r\n        implementationConfig: {},\r\n        bridgeConfig: {},\r\n        completionInfo: {},\r\n        approvalInfo: {}\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    dialogVisible: {\r\n      get() {\r\n        return this.visible\r\n      },\r\n      set(value) {\r\n        this.$emit('update:visible', value)\r\n      }\r\n    },\r\n\r\n    modalTitle() {\r\n      const titles = {\r\n        create: '新增养护项目',\r\n        edit: '编辑养护项目',\r\n        view: '查看养护项目',\r\n        approve: '审批养护项目'\r\n      }\r\n      return titles[this.mode] || '养护项目'\r\n    },\r\n\r\n    currentProjectId() {\r\n      return this.projectId\r\n    },\r\n    \r\n    isEditMode() {\r\n      return this.mode === 'edit'\r\n    },\r\n    \r\n    isViewMode() {\r\n      return this.mode === 'view'\r\n    },\r\n    \r\n    isApprovalMode() {\r\n      return this.mode === 'approve'\r\n    },\r\n    \r\n    // 是否为只读模式（查看或审批）\r\n    isReadonlyMode() {\r\n      return this.isViewMode || this.isApprovalMode\r\n    },\r\n    \r\n    // 是否允许步骤点击\r\n    allowStepClick() {\r\n      // 查看和审批模式始终允许点击\r\n      if (this.isViewMode || this.isApprovalMode) {\r\n        return true\r\n      }\r\n      // 新增和修改模式允许有条件的点击（由clickableSteps控制具体哪些步骤可点击）\r\n      return true\r\n    },\r\n    \r\n    // 可点击的步骤列表\r\n    clickableSteps() {\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        // 查看、审批和修改模式所有步骤都可点击\r\n        return Array.from({ length: this.steps.length }, (_, i) => i)\r\n      }\r\n      \r\n      // 新增模式：当前步骤及之前的步骤可点击\r\n      return Array.from({ length: this.currentStep + 1 }, (_, i) => i)\r\n    },\r\n    \r\n    // 根据项目类型确定步骤\r\n    steps() {\r\n      const baseSteps = [\r\n        { title: '基本信息' }\r\n      ]\r\n      \r\n      let projectSteps = []\r\n      \r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息 → 养护项目 → 病害养护 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '养护项目' },\r\n          { title: '病害养护' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'cleaning') {\r\n        // 保洁项目：基本信息 → 保洁项目 → 保洁桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '保洁项目' },\r\n          { title: '保洁桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'emergency') {\r\n        // 应急养护：基本信息 → 应急项目 → 养护桥梁\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '应急项目' },\r\n          { title: '养护桥梁' }\r\n        ]\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息 → 关联病害 → 实施信息 → 竣工信息\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '关联病害' },\r\n          { title: '实施信息' },\r\n          { title: '竣工信息' }\r\n        ]\r\n      } else {\r\n        // 默认步骤\r\n        projectSteps = [\r\n          ...baseSteps,\r\n          { title: '项目配置' },\r\n          { title: '关联对象' }\r\n        ]\r\n      }\r\n      \r\n      // 审批模式、查看模式、修改模式下添加审批信息步骤\r\n      if (this.isApprovalMode || this.isViewMode || this.isEditMode) {\r\n        projectSteps.push({ title: '审批信息' })\r\n      }\r\n      \r\n      return projectSteps\r\n    },\r\n    \r\n    // 是否显示项目配置步骤\r\n    showProjectConfig() {\r\n      return ['monthly', 'cleaning', 'emergency'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否显示病害配置步骤\r\n    showDiseaseConfig() {\r\n      return ['monthly', 'preventive'].includes(this.projectType)\r\n    },\r\n    \r\n    // 是否可以进行审批操作\r\n    canApprove() {\r\n      // 在实际项目中，这里应该根据当前用户权限和项目状态来判断\r\n      // 目前简化为审批模式下就可以审批\r\n      return this.isApprovalMode\r\n    }\r\n  },\r\n  watch: {\r\n    visible(newVal) {\r\n      if (newVal) {\r\n        // 立即应用样式，避免白色闪烁\r\n        this.$nextTick(() => {\r\n          this.forceApplyDialogStyles()\r\n        })\r\n        \r\n        // 延迟再次应用，确保Element UI后续的样式修改被覆盖\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 200)\r\n        \r\n        // 额外延迟应用表格样式，确保表格渲染完成后样式正确\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 500)\r\n        \r\n        // 最终确保样式正确应用\r\n        setTimeout(() => {\r\n          this.forceApplyDialogStyles()\r\n        }, 1000)\r\n      }\r\n    },\r\n    \r\n    // 监听Tab切换，确保每个Tab的样式都正确\r\n    currentStep(newVal) {\r\n      this.$nextTick(() => {\r\n        this.forceApplyDialogStyles()\r\n      })\r\n      \r\n      // 延迟应用，确保Tab内容渲染完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 100)\r\n      \r\n      // 额外延迟，确保表格数据加载完成\r\n      setTimeout(() => {\r\n        this.forceApplyDialogStyles()\r\n      }, 300)\r\n    }\r\n  },\r\n  async created() {\r\n    if (this.isEditMode || this.isViewMode || this.isApprovalMode) {\r\n      await this.loadProjectData()\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载项目数据（编辑模式和查看模式）\r\n    async loadProjectData() {\r\n      try {\r\n        this.loading = true\r\n        \r\n        // 临时使用静态数据，因为后端接口未公布\r\n        // 实际项目中应该使用：const response = await getProjectDetail(this.currentProjectId)\r\n        const mockProject = this.getMockProjectData()\r\n        \r\n        this.projectType = mockProject.projectType\r\n        this.formData = {\r\n          basicInfo: mockProject.basicInfo || {},\r\n          projectConfig: mockProject.projectConfig || {},\r\n          diseaseConfig: mockProject.diseaseConfig || {},\r\n          implementationConfig: mockProject.implementationConfig || {},\r\n          completionInfo: mockProject.completionInfo || {},\r\n          bridgeConfig: mockProject.bridgeConfig || {},\r\n          approvalInfo: mockProject.approvalInfo || {}\r\n        }\r\n        \r\n        // 审批模式下默认跳转到审批信息页面（最后一步）\r\n        if (this.isApprovalMode) {\r\n          this.$nextTick(() => {\r\n            this.currentStep = this.getLastStepIndex()\r\n          })\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载项目数据失败')\r\n        this.handleClose()\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 获取模拟项目数据\r\n    getMockProjectData() {\r\n      return {\r\n        projectType: 'monthly',\r\n        basicInfo: {\r\n          projectName: '2024年度桥梁月度养护项目',\r\n          projectType: 'monthly',\r\n          startDate: '2024-01-01',\r\n          endDate: '2024-12-31',\r\n          managementUnit: '市桥梁管理处',\r\n          supervisionUnit: '市交通局',\r\n          maintenanceUnit: '桥梁养护有限公司',\r\n          manager: '张三',\r\n          contactPhone: '13800138000',\r\n          workload: '100万元',\r\n          projectContent: '对辖区内桥梁进行月度常规养护，包括清洁、检查、小修等工作',\r\n          attachments: []\r\n        },\r\n        projectConfig: {\r\n          projects: [\r\n            { name: '桥面清洁', frequency: 30 },\r\n            { name: '栏杆维护', frequency: 60 },\r\n            { name: '伸缩缝检查', frequency: 90 }\r\n          ]\r\n        },\r\n        diseaseConfig: {\r\n          diseases: [\r\n            { id: 1, name: '桥面破损', level: '轻微' },\r\n            { id: 2, name: '栏杆松动', level: '一般' }\r\n          ]\r\n        },\r\n        implementationConfig: {\r\n          works: [\r\n            { name: '施工准备' },\r\n            { name: '现场作业' },\r\n            { name: '质量检查' }\r\n          ]\r\n        },\r\n        completionInfo: {\r\n          completionDate: '2024-12-31',\r\n          acceptanceDate: '2025-01-15',\r\n          qualityGrade: '合格',\r\n          remark: '项目按期完成，质量达标'\r\n        },\r\n        bridgeConfig: {\r\n          bridges: [\r\n            { id: 1, name: '人民桥', location: '人民路', span: '50m' },\r\n            { id: 2, name: '胜利桥', location: '胜利路', span: '30m' }\r\n          ]\r\n        },\r\n        approvalInfo: {\r\n          approvalHistory: [\r\n            {\r\n              id: 1,\r\n              stepName: '开始申请',\r\n              approver: '黄昭言',\r\n              status: '通过',\r\n              comment: '无异议',\r\n              department: '养护公司',\r\n              receiveTime: '2025-09-18\\n10:43',\r\n              processTime: '2025-09-18\\n10:43'\r\n            },\r\n            {\r\n              id: 2,\r\n              stepName: '养护项目审批(一级)',\r\n              approver: '刘雨桐',\r\n              status: '通过',\r\n              comment: '项目方案合理，同意开展',\r\n              department: '技术部门',\r\n              receiveTime: '2025-09-18\\n11:00',\r\n              processTime: '2025-09-18\\n11:30'\r\n            },\r\n            {\r\n              id: 3,\r\n              stepName: '养护项目审批(二级)',\r\n              approver: '罗砚秋',\r\n              status: '通过',\r\n              comment: '预算合理，批准执行',\r\n              department: '财务部门',\r\n              receiveTime: '2025-09-18\\n14:00',\r\n              processTime: '2025-09-18\\n15:20'\r\n            }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取最后一步的索引\r\n    getLastStepIndex() {\r\n      return this.steps.length - 1\r\n    },\r\n    \r\n    // 项目类型变化\r\n    handleProjectTypeChange(type) {\r\n      if (this.projectType === type) {\r\n        return // 防止重复触发\r\n      }\r\n      \r\n      this.projectType = type\r\n      \r\n      // 使用nextTick确保步骤计算完成后再检查当前步骤\r\n      this.$nextTick(() => {\r\n        // 重置后续步骤的数据\r\n        this.formData.projectConfig = {}\r\n        this.formData.diseaseConfig = {}\r\n        this.formData.bridgeConfig = {}\r\n        \r\n        // 如果当前步骤超出新的步骤范围，回到第一步\r\n        if (this.currentStep >= this.steps.length) {\r\n          this.currentStep = 0\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 步骤点击\r\n    async handleStepClick(stepIndex) {\r\n      // 查看、审批和修改模式可以自由切换\r\n      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 新增模式的逻辑\r\n      // 如果点击的是当前步骤或之前的步骤，允许直接切换\r\n      if (stepIndex <= this.currentStep) {\r\n        this.currentStep = stepIndex\r\n        return\r\n      }\r\n      \r\n      // 如果跳转到后面的步骤，需要验证从当前步骤到目标步骤的所有步骤\r\n      for (let i = this.currentStep; i < stepIndex; i++) {\r\n        const isValid = await this.validateStepByIndex(i)\r\n        if (!isValid) {\r\n          this.$message.warning(`请先完成第${i + 1}步的必填信息`)\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 所有验证通过，跳转到目标步骤\r\n      this.currentStep = stepIndex\r\n    },\r\n    \r\n    // 上一步\r\n    handlePrevStep() {\r\n      if (this.currentStep > 0) {\r\n        this.currentStep--\r\n      }\r\n    },\r\n    \r\n    // 下一步\r\n    async handleNextStep() {\r\n      const isValid = await this.validateCurrentStep()\r\n      if (!isValid) {\r\n        return\r\n      }\r\n      \r\n      if (this.currentStep < this.getLastStepIndex()) {\r\n        this.currentStep++\r\n      }\r\n    },\r\n    \r\n    // 验证当前步骤\r\n    async validateCurrentStep() {\r\n      return await this.validateStepByIndex(this.currentStep)\r\n    },\r\n    \r\n    // 验证指定步骤\r\n    async validateStepByIndex(stepIndex) {\r\n      const componentName = this.getStepComponentByIndex(stepIndex)\r\n      \r\n      if (componentName && this.$refs[componentName]) {\r\n        try {\r\n          const result = this.$refs[componentName].validate()\r\n          // 如果返回Promise，等待结果\r\n          if (result && typeof result.then === 'function') {\r\n            return await result\r\n          }\r\n          // 如果返回boolean，直接返回\r\n          return result\r\n        } catch (error) {\r\n          console.error('验证失败:', error)\r\n          return false\r\n        }\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    // 根据步骤索引获取组件名\r\n    getStepComponentByIndex(stepIndex) {\r\n      if (stepIndex === 0) {\r\n        return 'basicInfo'\r\n      }\r\n      \r\n      // 根据项目类型和步骤索引确定组件\r\n      if (this.projectType === 'monthly') {\r\n        // 月度养护：基本信息(0) → 养护项目(1) → 病害养护(2) → 养护桥梁(3)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'diseaseConfig'\r\n        if (stepIndex === 3) return 'bridgeConfig'\r\n      } else if (this.projectType === 'cleaning' || this.projectType === 'emergency') {\r\n        // 保洁/应急：基本信息(0) → 项目配置(1) → 桥梁配置(2)\r\n        if (stepIndex === 1) return 'projectConfig'\r\n        if (stepIndex === 2) return 'bridgeConfig'\r\n      } else if (this.projectType === 'preventive') {\r\n        // 预防养护：基本信息(0) → 关联病害(1) → 实施信息(2) → 竣工信息(3)\r\n        if (stepIndex === 1) return 'diseaseConfig'\r\n        if (stepIndex === 2) return 'implementationConfig'\r\n        if (stepIndex === 3) return 'completionInfo' // 竣工信息\r\n        return null // 预防养护项目没有第4步\r\n      }\r\n      \r\n      return null\r\n    },\r\n    \r\n    // 切换到编辑模式\r\n    handleEdit() {\r\n      this.$emit('edit', this.currentProjectId)\r\n    },\r\n    \r\n    // 保存草稿\r\n    async handleSave() {\r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'draft',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('保存成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('保存成功')\r\n        }\r\n        \r\n        this.$emit('save', data)\r\n      } catch (error) {\r\n        this.$message.error('保存失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 提交项目\r\n    async handleSubmit() {\r\n      // 验证所有步骤\r\n      if (!this.validateAllSteps()) {\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.loading = true\r\n        const data = {\r\n          ...this.formData,\r\n          status: 'pending',\r\n          infrastructureType: this.infrastructureType\r\n        }\r\n        \r\n        if (this.isEditMode) {\r\n          await updateProject(this.currentProjectId, data)\r\n          this.$message.success('更新成功')\r\n        } else {\r\n          await createProject(data)\r\n          this.$message.success('提交成功')\r\n        }\r\n        \r\n        this.$emit('submit', data)\r\n        this.handleClose()\r\n      } catch (error) {\r\n        this.$message.error(this.isEditMode ? '更新失败' : '提交失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 验证所有步骤\r\n    validateAllSteps() {\r\n      // 这里应该验证所有步骤的数据\r\n      return true\r\n    },\r\n    \r\n    // 处理审批提交结果\r\n    handleApprovalSubmitted(data) {\r\n      const { result } = data\r\n      \r\n      if (result === 'approved') {\r\n        this.$message.success('审批通过成功')\r\n      } else if (result === 'rejected') {\r\n        this.$message.success('已驳回申请')\r\n      } else if (result === 'returned') {\r\n        this.$message.success('已退回修改')\r\n      }\r\n      \r\n      this.$emit('approval-completed', data)\r\n      \r\n      // 审批完成后关闭页面\r\n      setTimeout(() => {\r\n        this.handleClose()\r\n      }, 1500)\r\n    },\r\n    \r\n    // 处理审批取消\r\n    handleApprovalCancel() {\r\n      // 可以选择关闭页面或回到上一步\r\n      this.handleClose()\r\n    },\r\n    \r\n    // 处理审批通过\r\n    async handleApprove() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.approveProject()\r\n        if (result) {\r\n          // 审批成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理审批退回\r\n    async handleReject() {\r\n      if (this.$refs.approvalInfo) {\r\n        const result = await this.$refs.approvalInfo.rejectProject()\r\n        if (result) {\r\n          // 退回成功，可以关闭弹窗或进行其他操作\r\n          // this.handleClose()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 应用深色主题样式\r\n    forceApplyDialogStyles() {\r\n      // 参考巡检弹框的实现，强制应用深色主题样式\r\n      setTimeout(() => {\r\n        const dialog = document.querySelector('.el-dialog.project-dialog') ||\r\n                       document.querySelector('.el-dialog.inspection-dialog-base') ||\r\n                       document.querySelector('.el-dialog')\r\n        \r\n        if (dialog) {\r\n          // 应用深色主题到各个区域\r\n          this.applyDarkThemeToAllAreas(dialog)\r\n        } else {\r\n          // 如果还没找到，再次尝试\r\n          setTimeout(() => this.forceApplyDialogStyles(), 200)\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 应用深色主题到弹框的所有区域 - 参考巡检弹框实现\r\n    applyDarkThemeToAllAreas(dialogElement = null) {\r\n      // 如果没有传入dialog元素，尝试查找\r\n      const dialog = dialogElement || document.querySelector('.el-dialog')\r\n      \r\n      if (!dialog) {\r\n        return\r\n      }\r\n      \r\n      // 首先强制设置弹框本身的尺寸\r\n      const dialogStyles = {\r\n        'width': '70%',\r\n        'min-width': '800px',\r\n        'height': '85vh',\r\n        'max-height': '85vh',\r\n        'margin-top': '7.5vh',\r\n        'position': 'relative'\r\n      }\r\n      Object.keys(dialogStyles).forEach(property => {\r\n        dialog.style.setProperty(property, dialogStyles[property], 'important')\r\n      })\r\n      \r\n      // Header区域 - 使用dialog元素作为基础查找\r\n      const header = dialog.querySelector('.el-dialog__header')\r\n      \r\n      if (header) {\r\n        const headerStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'border-bottom': '1px solid #ffffff', // 白色分割线，与巡检日志一致\r\n          'padding': '20px 24px'\r\n        }\r\n        Object.keys(headerStyles).forEach(property => {\r\n          header.style.setProperty(property, headerStyles[property], 'important')\r\n        })\r\n        \r\n        const title = header.querySelector('.el-dialog__title')\r\n        if (title) {\r\n          title.style.setProperty('color', '#ffffff', 'important')\r\n          title.style.setProperty('font-weight', '600', 'important')\r\n          title.style.setProperty('font-size', '18px', 'important')\r\n        }\r\n      }\r\n      \r\n      // Body区域 - 强制设置固定高度和滚动\r\n      const body = dialog.querySelector('.el-dialog__body')\r\n      if (body) {\r\n        const bodyStyles = {\r\n          'background': '#091A4B',\r\n          'color': '#f1f5f9',\r\n          'height': 'calc(85vh - 140px)',\r\n          'max-height': 'calc(85vh - 140px)',\r\n          'overflow-y': 'auto',\r\n          'padding': '0',\r\n          'box-sizing': 'border-box'\r\n        }\r\n        Object.keys(bodyStyles).forEach(property => {\r\n          body.style.setProperty(property, bodyStyles[property], 'important')\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 关闭弹框\r\n    handleClose() {\r\n      this.currentStep = 0\r\n      this.$emit('update:visible', false)\r\n      this.$emit('close')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 导入巡检主题样式和maintenance主题样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/assets/styles/maintenance-theme.scss';\r\n@import '@/styles/components/dialog.scss';\r\n\r\n// 项目弹框使用公共样式，无需自定义样式\r\n// 通过 common-dialog、inspection-tabs、inspection-table 等类继承公共样式\r\n\r\n// 项目弹框固定尺寸样式 - 强制覆盖所有可能的样式\r\n.project-dialog-fixed-size {\r\n  // 最高优先级样式设置\r\n  :deep(.el-dialog) {\r\n    width: 70% !important;\r\n    min-width: 800px !important;\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    margin-top: 7.5vh !important;\r\n    position: relative !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n      overflow-y: auto !important;\r\n      padding: 0 !important;\r\n      box-sizing: border-box !important;\r\n    }\r\n  }\r\n  \r\n  // 响应式设计\r\n  @media (max-width: 1200px) {\r\n    :deep(.el-dialog) {\r\n      width: 80% !important;\r\n      min-width: 700px !important;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    :deep(.el-dialog) {\r\n      width: 95% !important;\r\n      min-width: auto !important;\r\n      height: 90vh !important;\r\n      max-height: 90vh !important;\r\n      margin-top: 5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(90vh - 120px) !important;\r\n        max-height: calc(90vh - 120px) !important;\r\n      }\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 480px) {\r\n    :deep(.el-dialog) {\r\n      width: 98% !important;\r\n      height: 95vh !important;\r\n      max-height: 95vh !important;\r\n      margin-top: 2.5vh !important;\r\n      \r\n      .el-dialog__body {\r\n        height: calc(95vh - 100px) !important;\r\n        max-height: calc(95vh - 100px) !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 针对特定样式类的强制覆盖 - 确保即使有公共样式也能生效\r\n.project-dialog-fixed-size.common-dialog-wide {\r\n  :deep(.el-dialog) {\r\n    height: 85vh !important;\r\n    max-height: 85vh !important;\r\n    \r\n    .el-dialog__body {\r\n      height: calc(85vh - 140px) !important;\r\n      max-height: calc(85vh - 140px) !important;\r\n    }\r\n  }\r\n}\r\n\r\n// 强制应用到所有可能的Element UI弹框类名\r\n.el-dialog.project-dialog-fixed-size,\r\n.el-dialog__wrapper .project-dialog-fixed-size,\r\n.project-dialog-fixed-size .el-dialog {\r\n  height: 85vh !important;\r\n  max-height: 85vh !important;\r\n  \r\n  .el-dialog__body {\r\n    height: calc(85vh - 140px) !important;\r\n    max-height: calc(85vh - 140px) !important;\r\n    overflow-y: auto !important;\r\n  }\r\n}\r\n\r\n.project-dialog {\r\n  .step-content {\r\n    padding: 24px;\r\n    min-height: 400px;\r\n    background: var(--inspection-bg-primary, #091A4B);\r\n    color: #f1f5f9;\r\n    // 内容区域可以滚动，移除min-height限制\r\n    height: auto;\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  // 确保关闭按钮样式正确应用\r\n  .custom-close-btn {\r\n    position: absolute !important;\r\n    top: 20px !important;\r\n    right: 24px !important;\r\n    width: 36px !important;\r\n    height: 36px !important;\r\n    cursor: pointer !important;\r\n    z-index: 100 !important;\r\n    transition: all 0.3s ease !important;\r\n\r\n    svg {\r\n      width: 100% !important;\r\n      height: 100% !important;\r\n      transition: all 0.3s ease !important;\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1) !important;\r\n      opacity: 0.8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}