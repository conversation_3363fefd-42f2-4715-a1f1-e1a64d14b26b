{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue?vue&type=style&index=0&id=77100e5e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsKQGltcG9ydCAnQC9hc3NldHMvc3R5bGVzL21haW50ZW5hbmNlLXRoZW1lLnNjc3MnOwoKLnBhZ2UtaGVhZGVyIHsKICBAZXh0ZW5kIC5jb21tb24tcGFnZS1oZWFkZXI7Cn0KCi5wcm9qZWN0LXN1bW1hcnkgewogIHBhZGRpbmc6IDI0cHg7CiAgYmFja2dyb3VuZDogcmdiYSg1OSwgMTMwLCAyNDYsIDAuMSk7CiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSg1OSwgMTMwLCAyNDYsIDAuMyk7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG1hcmdpbi1ib3R0b206IDI0cHg7CiAgCiAgLnN1bW1hcnktaW5mbyB7CiAgICBoMyB7CiAgICAgIGNvbG9yOiAjZmZmZmZmOwogICAgICBmb250LXNpemU6IDIwcHg7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogICAgfQogICAgCiAgICAubWV0YS1pbmZvIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgZmxleC13cmFwOiB3cmFwOwogICAgICBnYXA6IDI0cHg7CiAgICAgIAogICAgICAubWV0YS1pdGVtIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgZ2FwOiA4cHg7CiAgICAgICAgY29sb3I6ICM5Y2EzYWY7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIAogICAgICAgIGkgewogICAgICAgICAgY29sb3I6ICMzYjgyZjY7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQogIAogIC5zdW1tYXJ5LXN0YXR1cyB7CiAgICAuc3RhdHVzLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwogICAgICAKICAgICAgbGFiZWwgewogICAgICAgIGNvbG9yOiAjOWNhM2FmOwogICAgICAgIG1pbi13aWR0aDogODBweDsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7CiAgICAgIH0KICAgICAgCiAgICAgIHNwYW4gewogICAgICAgIGNvbG9yOiAjZmZmZmZmOwogICAgICB9CiAgICB9CiAgfQp9CgoudGFiLW5hdmlnYXRpb24gewogIEBleHRlbmQgLmNvbW1vbi10YWItbmF2aWdhdGlvbjsKfQoKLnRhYi1jb250ZW50IHsKICBwYWRkaW5nOiAyNHB4OwogIG1pbi1oZWlnaHQ6IDQwMHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0OA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/audit/detail", "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题栏 -->\n        <div class=\"page-header\">\n          <div class=\"header-left\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-arrow-left\"\n              class=\"back-btn\"\n              @click=\"handleBack\"\n            >\n              返回\n            </el-button>\n            <h2>审核详情</h2>\n          </div>\n          \n          <div class=\"header-right\">\n            <el-button\n              v-if=\"canAudit\"\n              type=\"primary\"\n              @click=\"handleAudit\"\n            >\n              审核\n            </el-button>\n          </div>\n        </div>\n        \n        <!-- 项目基本信息 -->\n        <div class=\"project-summary\">\n          <el-row :gutter=\"24\">\n            <el-col :span=\"18\">\n              <div class=\"summary-info\">\n                <h3>{{ projectData.projectName }}</h3>\n                <div class=\"meta-info\">\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-folder\"></i>\n                    {{ getProjectTypeText(projectData.projectType) }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-user\"></i>\n                    {{ projectData.submitter }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-time\"></i>\n                    {{ projectData.submitTime }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-office-building\"></i>\n                    {{ projectData.maintenanceUnit }}\n                  </span>\n                </div>\n              </div>\n            </el-col>\n            \n            <el-col :span=\"6\">\n              <div class=\"summary-status\">\n                <div class=\"status-item\">\n                  <label>当前状态:</label>\n                  <status-tag :status=\"projectData.auditStatus\" type=\"audit\" />\n                </div>\n                <div class=\"status-item\">\n                  <label>当前审核人:</label>\n                  <span>{{ projectData.currentAuditor || '-' }}</span>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'basic' }\"\n              @click=\"switchTab('basic')\"\n            >\n              基本信息\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tasks' }\"\n              @click=\"switchTab('tasks')\"\n            >\n              养护项目\n            </div>\n            <div \n              v-if=\"showDiseaseTab\"\n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'diseases' }\"\n              @click=\"switchTab('diseases')\"\n            >\n              病害养护\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'history' }\"\n              @click=\"switchTab('history')\"\n            >\n              审核历史\n            </div>\n          </div>\n        </div>\n        \n        <!-- 标签内容 -->\n        <div class=\"tab-content\">\n          <!-- 基本信息 -->\n          <div v-if=\"activeTab === 'basic'\" class=\"basic-info\">\n            <basic-info-view :project-data=\"projectData\" />\n          </div>\n          \n          <!-- 养护项目 -->\n          <div v-if=\"activeTab === 'tasks'\" class=\"tasks-info\">\n            <tasks-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 病害养护 -->\n          <div v-if=\"activeTab === 'diseases' && showDiseaseTab\" class=\"diseases-info\">\n            <diseases-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 审核历史 -->\n          <div v-if=\"activeTab === 'history'\" class=\"history-info\">\n            <audit-history-view :project-id=\"projectId\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getAuditDetail } from '@/api/maintenance/audit'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport BasicInfoView from '../../repairs/detail/components/BasicInfoView'\nimport TasksInfoView from '../../repairs/detail/components/TasksInfoView'\nimport DiseasesInfoView from '../../repairs/detail/components/DiseasesInfoView'\nimport AuditHistoryView from './components/AuditHistoryView'\n\nexport default {\n  name: 'MaintenanceAuditDetail',\n  components: {\n    StatusTag,\n    BasicInfoView,\n    TasksInfoView,\n    DiseasesInfoView,\n    AuditHistoryView\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'basic',\n      projectData: {},\n      projectId: ''\n    }\n  },\n  computed: {\n    // 是否显示病害养护标签\n    showDiseaseTab() {\n      return this.projectData.projectType === 'monthly'\n    },\n    \n    // 是否可以审核\n    canAudit() {\n      const userRole = this.$store.getters.userRole || 'company_admin'\n      \n      if (userRole === 'company_admin') {\n        return this.projectData.auditStatus === 'primary_audit'\n      } else if (userRole === 'bridge_center') {\n        return this.projectData.auditStatus === 'secondary_audit'\n      }\n      \n      return false\n    }\n  },\n  async created() {\n    this.projectId = this.$route.params.id\n    await this.loadProjectDetail()\n  },\n  methods: {\n    // 加载项目详情\n    async loadProjectDetail() {\n      try {\n        this.loading = true\n        const response = await getAuditDetail(this.projectId)\n        this.projectData = response.data || {}\n      } catch (error) {\n        this.$message.error('加载项目详情失败')\n        this.handleBack()\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n    },\n    \n    // 返回列表\n    handleBack() {\n      this.$router.push('/maintenance/audit')\n    },\n    \n    // 审核项目\n    handleAudit() {\n      this.$router.push(`/maintenance/projects/approve/${this.projectId}`)\n    },\n    \n    // 获取项目类型文本\n    getProjectTypeText(type) {\n      const typeMap = {\n        monthly: '月度养护',\n        cleaning: '保洁项目',\n        emergency: '应急养护',\n        preventive: '预防养护'\n      }\n      return typeMap[type] || type\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.page-header {\n  @extend .common-page-header;\n}\n\n.project-summary {\n  padding: 24px;\n  background: rgba(59, 130, 246, 0.1);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  border-radius: 8px;\n  margin-bottom: 24px;\n  \n  .summary-info {\n    h3 {\n      color: #ffffff;\n      font-size: 20px;\n      font-weight: bold;\n      margin-bottom: 12px;\n    }\n    \n    .meta-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 24px;\n      \n      .meta-item {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        color: #9ca3af;\n        font-size: 14px;\n        \n        i {\n          color: #3b82f6;\n        }\n      }\n    }\n  }\n  \n  .summary-status {\n    .status-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 12px;\n      \n      label {\n        color: #9ca3af;\n        min-width: 80px;\n        margin-right: 12px;\n      }\n      \n      span {\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.tab-content {\n  padding: 24px;\n  min-height: 400px;\n}\n</style>\n"]}]}