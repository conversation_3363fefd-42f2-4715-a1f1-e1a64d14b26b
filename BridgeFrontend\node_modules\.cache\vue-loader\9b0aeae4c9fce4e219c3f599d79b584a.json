{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldE1hbmFnZW1lbnRVbml0cywgZ2V0TWFpbnRlbmFuY2VVbml0cywgZ2V0UHJvamVjdE1hbmFnZXJzIH0gZnJvbSAnQC9hcGkvbWFpbnRlbmFuY2UvcHJvamVjdHMnCmltcG9ydCBGaWxlVXBsb2FkIGZyb20gJ0AvY29tcG9uZW50cy9NYWludGVuYW5jZS9GaWxlVXBsb2FkJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCYXNpY0luZm8nLAogIGNvbXBvbmVudHM6IHsKICAgIEZpbGVVcGxvYWQKICB9LAogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkKICAgIH0sCiAgICBwcm9qZWN0VHlwZTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9LAogICAgcmVhZG9ubHk6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtRGF0YTogewogICAgICAgIHByb2plY3ROYW1lOiAnJywKICAgICAgICBwcm9qZWN0VHlwZTogJycsCiAgICAgICAgc3RhcnREYXRlOiAnJywKICAgICAgICBlbmREYXRlOiAnJywKICAgICAgICBtYW5hZ2VtZW50VW5pdDogJycsCiAgICAgICAgc3VwZXJ2aXNpb25Vbml0OiAnJywKICAgICAgICBtYWludGVuYW5jZVVuaXQ6ICcnLAogICAgICAgIG1hbmFnZXI6ICcnLAogICAgICAgIGNvbnRhY3RQaG9uZTogJycsCiAgICAgICAgd29ya2xvYWQ6ICcnLAogICAgICAgIHByb2plY3RDb250ZW50OiAnJywKICAgICAgICBhdHRhY2htZW50czogW10KICAgICAgfSwKICAgICAgCiAgICAgIC8vIOmAiemhueaVsOaNrgogICAgICBtYW5hZ2VtZW50VW5pdHM6IFtdLAogICAgICBtYWludGVuYW5jZVVuaXRzOiBbXSwKICAgICAgcHJvamVjdE1hbmFnZXJzOiBbXSwKICAgICAgCiAgICAgIC8vIOmhueebruexu+Wei+mAiemhuQogICAgICBwcm9qZWN0VHlwZXM6IFsKICAgICAgICB7IGxhYmVsOiAn5pyI5bqm5YW75oqkJywgdmFsdWU6ICdtb250aGx5JyB9LAogICAgICAgIHsgbGFiZWw6ICfkv53mtIHpobnnm64nLCB2YWx1ZTogJ2NsZWFuaW5nJyB9LAogICAgICAgIHsgbGFiZWw6ICflupTmgKXlhbvmiqQnLCB2YWx1ZTogJ2VtZXJnZW5jeScgfSwKICAgICAgICB7IGxhYmVsOiAn6aKE6Ziy5YW75oqkJywgdmFsdWU6ICdwcmV2ZW50aXZlJyB9CiAgICAgIF0sCiAgICAgIAogICAgICAvLyDooajljZXpqozor4Hop4TliJkKICAgICAgcnVsZXM6IHsKICAgICAgICBwcm9qZWN0TmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemhueebruWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyBtaW46IDIsIG1heDogNTAsIG1lc3NhZ2U6ICfpobnnm67lkI3np7Dplb/luqblnKggMiDliLAgNTAg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIHByb2plY3RUeXBlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6aG555uu57G75Z6LJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgc3RhcnREYXRlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5byA5aeL5pe26Ze0JywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgZW5kRGF0ZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqee7k+adn+aXtumXtCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0sCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZUVuZERhdGUsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIG1hbmFnZW1lbnRVbml0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup566h55CG5Y2V5L2NJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgbWFpbnRlbmFuY2VVbml0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5YW75oqk5Y2V5L2NJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgbWFuYWdlcjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemhueebrui0n+i0o+S6uicsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIGNvbnRhY3RQaG9uZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiBlOezu+aWueW8jycsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgICAgeyBwYXR0ZXJuOiAvXjFbMy05XVxkezl9JC8sIG1lc3NhZ2U6ICfor7fovpPlhaXmraPnoa7nmoTmiYvmnLrlj7fnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIC8vIOaYr+WQpuaYvuekuuW3peS9nOmHj+Wtl+autQogICAgc2hvd1dvcmtsb2FkKCkgewogICAgICByZXR1cm4gWydjbGVhbmluZycsICdlbWVyZ2VuY3knLCAncHJldmVudGl2ZSddLmluY2x1ZGVzKHRoaXMuZm9ybURhdGEucHJvamVjdFR5cGUpCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8g5Y+q55uR5ZCs5aSW6YOo5Lyg5YWl55qEdmFsdWXvvIzljZXlkJHmlbDmja7mtYEKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCAmJiBPYmplY3Qua2V5cyhuZXdWYWwpLmxlbmd0aCA+IDApIHsKICAgICAgICAgIC8vIOWPquWcqOaVsOaNruecn+ato+S4jeWQjOaXtuaJjeabtOaWsO+8jOmBv+WFjeW+queOrwogICAgICAgICAgY29uc3QgaGFzQ2hhbmdlcyA9IE9iamVjdC5rZXlzKG5ld1ZhbCkuc29tZShrZXkgPT4gCiAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KG5ld1ZhbFtrZXldKSAhPT0gSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtRGF0YVtrZXldKQogICAgICAgICAgKQogICAgICAgICAgaWYgKGhhc0NoYW5nZXMpIHsKICAgICAgICAgICAgdGhpcy5mb3JtRGF0YSA9IHsgLi4udGhpcy5mb3JtRGF0YSwgLi4ubmV3VmFsIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgZGVlcDogdHJ1ZQogICAgfSwKICAgIAogICAgLy8g55uR5ZCs5aSW6YOo6aG555uu57G75Z6L5Y+Y5YyWCiAgICBwcm9qZWN0VHlwZTogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmIChuZXdWYWwgJiYgbmV3VmFsICE9PSB0aGlzLmZvcm1EYXRhLnByb2plY3RUeXBlKSB7CiAgICAgICAgICB0aGlzLmZvcm1EYXRhLnByb2plY3RUeXBlID0gbmV3VmFsCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBhd2FpdCB0aGlzLmxvYWRPcHRpb25zKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWKoOi9vemAiemhueaVsOaNrgogICAgYXN5bmMgbG9hZE9wdGlvbnMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgW21hbmFnZW1lbnRSZXMsIG1haW50ZW5hbmNlUmVzXSA9IGF3YWl0IFByb21pc2UuYWxsKFsKICAgICAgICAgIGdldE1hbmFnZW1lbnRVbml0cygpLAogICAgICAgICAgZ2V0TWFpbnRlbmFuY2VVbml0cygpCiAgICAgICAgXSkKICAgICAgICAKICAgICAgICB0aGlzLm1hbmFnZW1lbnRVbml0cyA9IG1hbmFnZW1lbnRSZXMuZGF0YSB8fCBbXQogICAgICAgIHRoaXMubWFpbnRlbmFuY2VVbml0cyA9IG1haW50ZW5hbmNlUmVzLmRhdGEgfHwgW10KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3pgInpobnmlbDmja7lpLHotKUnKQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDnu5/kuIDnmoTovpPlhaXlpITnkIbmlrnms5UKICAgIGhhbmRsZUlucHV0KCkgewogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCB7IC4uLnRoaXMuZm9ybURhdGEgfSkKICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOmhueebruexu+Wei+WPmOWMlgogICAgaGFuZGxlUHJvamVjdFR5cGVDaGFuZ2UodHlwZSkgewogICAgICAvLyDlj6rmnInlvZPlgLznnJ/mraPlj5jljJbml7bmiY3op6blj5Hkuovku7YKICAgICAgaWYgKHR5cGUgIT09IHRoaXMucHJvamVjdFR5cGUpIHsKICAgICAgICB0aGlzLiRlbWl0KCdwcm9qZWN0LXR5cGUtY2hhbmdlJywgdHlwZSkKICAgICAgfQogICAgICB0aGlzLmhhbmRsZUlucHV0KCkKICAgIH0sCiAgICAKICAgIC8vIOWFu+aKpOWNleS9jeWPmOWMlgogICAgYXN5bmMgaGFuZGxlTWFpbnRlbmFuY2VVbml0Q2hhbmdlKHVuaXRJZCkgewogICAgICB0aGlzLmZvcm1EYXRhLm1hbmFnZXIgPSAnJwogICAgICB0aGlzLmZvcm1EYXRhLmNvbnRhY3RQaG9uZSA9ICcnCiAgICAgIHRoaXMucHJvamVjdE1hbmFnZXJzID0gW10KICAgICAgCiAgICAgIGlmICh1bml0SWQpIHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRQcm9qZWN0TWFuYWdlcnModW5pdElkKQogICAgICAgICAgdGhpcy5wcm9qZWN0TWFuYWdlcnMgPSByZXNwb25zZS5kYXRhIHx8IFtdCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9vemhueebrui0n+i0o+S6uuWksei0pScpCiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuaGFuZGxlSW5wdXQoKQogICAgfSwKICAgIAogICAgLy8g6aG555uu6LSf6LSj5Lq65Y+Y5YyWIC0g6Ieq5Yqo5aGr5YWF6IGU57O75pa55byPCiAgICBoYW5kbGVNYW5hZ2VyQ2hhbmdlKG1hbmFnZXJJZCkgewogICAgICBpZiAobWFuYWdlcklkKSB7CiAgICAgICAgY29uc3Qgc2VsZWN0ZWRNYW5hZ2VyID0gdGhpcy5wcm9qZWN0TWFuYWdlcnMuZmluZChtYW5hZ2VyID0+IG1hbmFnZXIuaWQgPT09IG1hbmFnZXJJZCkKICAgICAgICBpZiAoc2VsZWN0ZWRNYW5hZ2VyKSB7CiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmNvbnRhY3RQaG9uZSA9IHNlbGVjdGVkTWFuYWdlci5waG9uZQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm1EYXRhLmNvbnRhY3RQaG9uZSA9ICcnCiAgICAgIH0KICAgICAgdGhpcy5oYW5kbGVJbnB1dCgpCiAgICB9LAogICAgCiAgICAvLyDpqozor4Hnu5PmnZ/ml7bpl7QKICAgIHZhbGlkYXRlRW5kRGF0ZShydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKHZhbHVlICYmIHRoaXMuZm9ybURhdGEuc3RhcnREYXRlKSB7CiAgICAgICAgaWYgKG5ldyBEYXRlKHZhbHVlKSA8PSBuZXcgRGF0ZSh0aGlzLmZvcm1EYXRhLnN0YXJ0RGF0ZSkpIHsKICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign57uT5p2f5pe26Ze05b+F6aG75aSn5LqO5byA5aeL5pe26Ze0JykpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKQogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDooajljZXpqozor4EKICAgIHZhbGlkYXRlKCkgewogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsKICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgICBpZiAoIXZhbGlkKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+WujOWWhOWfuuacrOS/oeaBrycpCiAgICAgICAgICB9CiAgICAgICAgICByZXNvbHZlKHZhbGlkKQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["BasicInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4MA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BasicInfo.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"basic-info-form\">\n    <el-form\n      ref=\"form\"\n      :model=\"formData\"\n      :rules=\"rules\"\n      label-width=\"120px\"\n      class=\"maintenance-form\"\n    >\n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"项目名称\" prop=\"projectName\" required>\n            <el-input\n              v-model=\"formData.projectName\"\n              placeholder=\"请输入项目名称\"\n              maxlength=\"50\"\n              show-word-limit\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目类型\" prop=\"projectType\" required>\n            <el-select\n              v-model=\"formData.projectType\"\n              placeholder=\"请选择项目类型\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleProjectTypeChange\"\n            >\n              <el-option\n                v-for=\"type in projectTypes\"\n                :key=\"type.value\"\n                :label=\"type.label\"\n                :value=\"type.value\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"项目开始时间\" prop=\"startDate\" required>\n            <el-date-picker\n              v-model=\"formData.startDate\"\n              type=\"date\"\n              placeholder=\"选择开始时间\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目结束时间\" prop=\"endDate\" required>\n            <el-date-picker\n              v-model=\"formData.endDate\"\n              type=\"date\"\n              placeholder=\"选择结束时间\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"管理单位\" prop=\"managementUnit\" required>\n            <el-select\n              v-model=\"formData.managementUnit\"\n              placeholder=\"请选择管理单位\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            >\n              <el-option\n                v-for=\"unit in managementUnits\"\n                :key=\"unit.id\"\n                :label=\"unit.name\"\n                :value=\"unit.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"监理单位\">\n            <el-input\n              v-model=\"formData.supervisionUnit\"\n              placeholder=\"请输入监理单位\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"养护单位\" prop=\"maintenanceUnit\" required>\n            <el-select\n              v-model=\"formData.maintenanceUnit\"\n              placeholder=\"请选择养护单位\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleMaintenanceUnitChange\"\n            >\n              <el-option\n                v-for=\"unit in maintenanceUnits\"\n                :key=\"unit.id\"\n                :label=\"unit.name\"\n                :value=\"unit.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目负责人\" prop=\"manager\" required>\n            <el-select\n              v-model=\"formData.manager\"\n              placeholder=\"请选择项目负责人\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleManagerChange\"\n            >\n              <el-option\n                v-for=\"manager in projectManagers\"\n                :key=\"manager.id\"\n                :label=\"manager.name\"\n                :value=\"manager.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"联系方式\" prop=\"contactPhone\" required>\n            <el-input\n              v-model=\"formData.contactPhone\"\n              placeholder=\"请输入联系方式\"\n              maxlength=\"11\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\" v-if=\"showWorkload\">\n          <el-form-item label=\"工作量\">\n            <el-input\n              v-model=\"formData.workload\"\n              placeholder=\"请输入工作量\"\n              type=\"number\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-form-item label=\"项目内容\">\n        <el-input\n          v-model=\"formData.projectContent\"\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"请输入项目内容\"\n          maxlength=\"500\"\n          show-word-limit\n          :disabled=\"readonly\"\n          @input=\"handleInput\"\n        />\n      </el-form-item>\n      \n      <el-form-item label=\"附件\">\n        <file-upload\n          v-model=\"formData.attachments\"\n          :multiple=\"true\"\n          accept=\".jpg,.jpeg,.png,.pdf,.doc,.docx\"\n          :max-size=\"10 * 1024 * 1024\"\n          :disabled=\"readonly\"\n          @input=\"handleInput\"\n        />\n      </el-form-item>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { getManagementUnits, getMaintenanceUnits, getProjectManagers } from '@/api/maintenance/projects'\nimport FileUpload from '@/components/Maintenance/FileUpload'\n\nexport default {\n  name: 'BasicInfo',\n  components: {\n    FileUpload\n  },\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    projectType: {\n      type: String,\n      default: ''\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        projectName: '',\n        projectType: '',\n        startDate: '',\n        endDate: '',\n        managementUnit: '',\n        supervisionUnit: '',\n        maintenanceUnit: '',\n        manager: '',\n        contactPhone: '',\n        workload: '',\n        projectContent: '',\n        attachments: []\n      },\n      \n      // 选项数据\n      managementUnits: [],\n      maintenanceUnits: [],\n      projectManagers: [],\n      \n      // 项目类型选项\n      projectTypes: [\n        { label: '月度养护', value: 'monthly' },\n        { label: '保洁项目', value: 'cleaning' },\n        { label: '应急养护', value: 'emergency' },\n        { label: '预防养护', value: 'preventive' }\n      ],\n      \n      // 表单验证规则\n      rules: {\n        projectName: [\n          { required: true, message: '请输入项目名称', trigger: 'blur' },\n          { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }\n        ],\n        projectType: [\n          { required: true, message: '请选择项目类型', trigger: 'change' }\n        ],\n        startDate: [\n          { required: true, message: '请选择开始时间', trigger: 'change' }\n        ],\n        endDate: [\n          { required: true, message: '请选择结束时间', trigger: 'change' },\n          { validator: this.validateEndDate, trigger: 'change' }\n        ],\n        managementUnit: [\n          { required: true, message: '请选择管理单位', trigger: 'change' }\n        ],\n        maintenanceUnit: [\n          { required: true, message: '请选择养护单位', trigger: 'change' }\n        ],\n        manager: [\n          { required: true, message: '请选择项目负责人', trigger: 'change' }\n        ],\n        contactPhone: [\n          { required: true, message: '请输入联系方式', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    // 是否显示工作量字段\n    showWorkload() {\n      return ['cleaning', 'emergency', 'preventive'].includes(this.formData.projectType)\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          // 只在数据真正不同时才更新，避免循环\n          const hasChanges = Object.keys(newVal).some(key => \n            JSON.stringify(newVal[key]) !== JSON.stringify(this.formData[key])\n          )\n          if (hasChanges) {\n            this.formData = { ...this.formData, ...newVal }\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    // 监听外部项目类型变化\n    projectType: {\n      handler(newVal) {\n        if (newVal && newVal !== this.formData.projectType) {\n          this.formData.projectType = newVal\n        }\n      },\n      immediate: true\n    }\n  },\n  async created() {\n    await this.loadOptions()\n  },\n  methods: {\n    // 加载选项数据\n    async loadOptions() {\n      try {\n        const [managementRes, maintenanceRes] = await Promise.all([\n          getManagementUnits(),\n          getMaintenanceUnits()\n        ])\n        \n        this.managementUnits = managementRes.data || []\n        this.maintenanceUnits = maintenanceRes.data || []\n      } catch (error) {\n        this.$message.error('加载选项数据失败')\n      }\n    },\n    \n    // 统一的输入处理方法\n    handleInput() {\n      this.$nextTick(() => {\n        this.$emit('input', { ...this.formData })\n      })\n    },\n    \n    // 项目类型变化\n    handleProjectTypeChange(type) {\n      // 只有当值真正变化时才触发事件\n      if (type !== this.projectType) {\n        this.$emit('project-type-change', type)\n      }\n      this.handleInput()\n    },\n    \n    // 养护单位变化\n    async handleMaintenanceUnitChange(unitId) {\n      this.formData.manager = ''\n      this.formData.contactPhone = ''\n      this.projectManagers = []\n      \n      if (unitId) {\n        try {\n          const response = await getProjectManagers(unitId)\n          this.projectManagers = response.data || []\n        } catch (error) {\n          this.$message.error('加载项目负责人失败')\n        }\n      }\n      this.handleInput()\n    },\n    \n    // 项目负责人变化 - 自动填充联系方式\n    handleManagerChange(managerId) {\n      if (managerId) {\n        const selectedManager = this.projectManagers.find(manager => manager.id === managerId)\n        if (selectedManager) {\n          this.formData.contactPhone = selectedManager.phone\n        }\n      } else {\n        this.formData.contactPhone = ''\n      }\n      this.handleInput()\n    },\n    \n    // 验证结束时间\n    validateEndDate(rule, value, callback) {\n      if (value && this.formData.startDate) {\n        if (new Date(value) <= new Date(this.formData.startDate)) {\n          callback(new Error('结束时间必须大于开始时间'))\n        } else {\n          callback()\n        }\n      } else {\n        callback()\n      }\n    },\n    \n    // 表单验证\n    validate() {\n      return new Promise((resolve) => {\n        this.$refs.form.validate((valid) => {\n          if (!valid) {\n            this.$message.error('请完善基本信息')\n          }\n          resolve(valid)\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n\n.basic-info-form {\n  @extend .common-form;\n  \n  // 覆盖特定样式以适应创建表单\n  .maintenance-form {\n    .el-form-item {\n      margin-bottom: 24px;\n    }\n  }\n}\n</style>\n"]}]}