{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue", "mtime": 1758809365043}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ApprovalInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA", "file": "ApprovalInfo.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\r\n  <div class=\"approval-info\">\r\n    <!-- 审批记录 -->\r\n    <div class=\"approval-records\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-document\"></i>\r\n        审批记录\r\n      </div>\r\n      \r\n      <el-table\r\n        :data=\"approvalHistory\"\r\n        class=\"approval-table\"\r\n        border\r\n      >\r\n        <el-table-column\r\n          prop=\"id\"\r\n          label=\"序号\"\r\n          min-width=\"60\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"stepName\"\r\n          label=\"审批环节\"\r\n          min-width=\"140\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"approver\"\r\n          label=\"处理人\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"审批状态\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"comment\"\r\n          label=\"审批意见\"\r\n          min-width=\"80\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"department\"\r\n          label=\"处理人部门\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"receiveTime\"\r\n          label=\"接收时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        \r\n        <el-table-column\r\n          prop=\"processTime\"\r\n          label=\"办理时间\"\r\n          min-width=\"100\"\r\n          align=\"center\"\r\n        />\r\n      </el-table>\r\n    </div>\r\n    \r\n    <!-- 无审批记录时显示提示 -->\r\n    <div v-if=\"!hasApprovalHistory\" class=\"no-data\">\r\n      <p>暂无审批记录</p>\r\n    </div>\r\n    \r\n    <!-- 审批处理（仅在审批模式下显示） -->\r\n    <div v-if=\"showApprovalForm\" class=\"approval-process\">\r\n      <div class=\"section-title\">\r\n        <i class=\"el-icon-edit\"></i>\r\n        审批处理\r\n      </div>\r\n      \r\n      <div class=\"process-form\">\r\n        <div class=\"comment-label\">*处理意见:</div>\r\n        <el-input\r\n          v-model=\"approvalFormData.comment\"\r\n          type=\"textarea\"\r\n          :rows=\"6\"\r\n          placeholder=\"请输入\"\r\n          class=\"comment-input\"\r\n        />\r\n      </div>\r\n      \r\n    </div>\r\n    \r\n    <!-- 注意：审批信息组件内不显示任何操作按钮，所有按钮都统一在父组件中控制 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { approveProject, getProjectApprovalHistory } from '@/api/maintenance/projects'\r\n\r\nexport default {\r\n  name: 'ApprovalInfo',\r\n  components: {\r\n  },\r\n  props: {\r\n    value: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    projectId: {\r\n      type: [String, Number],\r\n      default: null\r\n    },\r\n    // 是否显示审批表单（审批模式 vs 查看模式）\r\n    showApprovalForm: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      \r\n      // 审批历史记录\r\n      approvalHistory: [],\r\n      \r\n      // 审批表单数据\r\n      approvalFormData: {\r\n        comment: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否有审批历史记录\r\n    hasApprovalHistory() {\r\n      return this.approvalHistory && this.approvalHistory.length > 0\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          this.initializeData(newVal)\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true\r\n    },\r\n    \r\n    projectId: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.loadApprovalHistory()\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    initializeData(data) {\r\n      if (data && data.approvalHistory) {\r\n        this.approvalHistory = data.approvalHistory\r\n      }\r\n    },\r\n    \r\n    // 加载审批历史\r\n    async loadApprovalHistory() {\r\n      if (!this.projectId) return\r\n      \r\n      try {\r\n        this.loading = true\r\n        const response = await getProjectApprovalHistory(this.projectId)\r\n        if (response.code === 200) {\r\n          this.approvalHistory = response.data || []\r\n        }\r\n      } catch (error) {\r\n        console.error('加载审批历史失败:', error)\r\n        this.$message.error('加载审批历史失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批通过（供父组件调用）\r\n    async approveProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认通过该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'approved',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('审批通过成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'approved',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('审批失败:', error)\r\n          this.$message.error(error.message || '审批失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 公共方法：审批退回（供父组件调用）\r\n    async rejectProject() {\r\n      if (!this.approvalFormData.comment.trim()) {\r\n        this.$message.error('请输入处理意见')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        await this.$confirm('确认退回该申请？', '确认操作', {\r\n          confirmButtonText: '确认',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n        \r\n        this.loading = true\r\n        \r\n        const response = await approveProject(this.projectId, {\r\n          result: 'returned',\r\n          comment: this.approvalFormData.comment\r\n        })\r\n        \r\n        if (response.code === 200) {\r\n          this.$message.success('退回成功')\r\n          this.$emit('approval-submitted', {\r\n            result: 'returned',\r\n            data: response.data\r\n          })\r\n          this.approvalFormData.comment = ''\r\n          await this.loadApprovalHistory()\r\n          return true\r\n        } else {\r\n          throw new Error(response.message || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        if (error.message !== 'cancel') {\r\n          console.error('退回失败:', error)\r\n          this.$message.error(error.message || '退回失败')\r\n        }\r\n        return false\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    \r\n    // 验证方法（为了保持接口一致性）\r\n    validate() {\r\n      return Promise.resolve(true)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.approval-info {\r\n  color: #ffffff;\r\n  \r\n  \r\n  // 无数据提示\r\n  .no-data {\r\n    text-align: center;\r\n    padding: 40px 0;\r\n    color: #9ca3af;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .section-title {\r\n    color: #ffffff;\r\n    font-size: 16px;\r\n    font-weight: normal;\r\n    margin-bottom: 20px;\r\n    text-align: left;\r\n    display: flex;\r\n    align-items: center;\r\n    \r\n    i {\r\n      margin-right: 8px;\r\n      color: #409eff;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  \r\n  // 审批记录表格\r\n  .approval-records {\r\n    margin-bottom: 32px;\r\n    \r\n    .approval-table {\r\n      width: 100%;\r\n      \r\n      :deep(.el-table) {\r\n        background: transparent;\r\n        color: #ffffff;\r\n        width: 100%;\r\n        \r\n        th {\r\n          background: rgba(255, 255, 255, 0.05);\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          font-weight: normal;\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        td {\r\n          background: transparent;\r\n          color: #ffffff;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          text-align: center;\r\n          white-space: pre-line; // 支持换行显示\r\n          padding: 12px 8px;\r\n        }\r\n        \r\n        &::before {\r\n          background: rgba(255, 255, 255, 0.1);\r\n        }\r\n        \r\n        .el-table__empty-block {\r\n          background: transparent;\r\n          color: #9ca3af;\r\n        }\r\n        \r\n        // 确保表格占满容器宽度\r\n        .el-table__header-wrapper,\r\n        .el-table__body-wrapper {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 审批处理区域\r\n  .approval-process {\r\n    margin-bottom: 32px;\r\n    \r\n    .process-form {\r\n      margin-bottom: 24px;\r\n      \r\n      .comment-label {\r\n        color: #ffffff;\r\n        margin-bottom: 8px;\r\n        font-size: 14px;\r\n      }\r\n      \r\n      .comment-input {\r\n        :deep(.el-textarea__inner) {\r\n          background: #374151;\r\n          border: 1px solid #9ca3af;\r\n          color: #ffffff;\r\n          \r\n          &::placeholder {\r\n            color: #9ca3af;\r\n          }\r\n          \r\n          &:focus {\r\n            border-color: #409eff;\r\n            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n</style>"]}]}