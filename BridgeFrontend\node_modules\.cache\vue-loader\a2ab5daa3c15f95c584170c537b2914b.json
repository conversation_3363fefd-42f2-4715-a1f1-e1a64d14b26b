{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue?vue&type=template&id=36cb142e&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue", "mtime": 1758804563532}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}