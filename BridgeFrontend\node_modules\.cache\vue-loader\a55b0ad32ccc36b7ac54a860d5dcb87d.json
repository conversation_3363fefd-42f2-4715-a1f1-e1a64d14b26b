{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue?vue&type=style&index=0&id=3addb4a0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BridgeConfig.vue", "mtime": 1758807462383}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvbWFpbnRlbmFuY2UtdGhlbWUuc2Nzcyc7CgouYnJpZGdlLWNvbmZpZyB7CiAgLmNvbmZpZy1oZWFkZXIgewogICAgbWFyZ2luLWJvdHRvbTogMjRweDsKICAgIAogICAgLmVsLWJ1dHRvbiB7CiAgICAgIGkgewogICAgICAgIG1hcmdpbi1yaWdodDogNHB4OwogICAgICB9CiAgICB9CiAgfQogIAogIC5jb21tb24tdGFibGUgewogICAgLm1haW50ZW5hbmNlLXRhYmxlIHsKICAgICAgbWluLWhlaWdodDogMjAwcHg7CiAgICB9CiAgfQogIAogIC5kYW5nZXItdGV4dCB7CiAgICBjb2xvcjogI2VmNDQ0NCAhaW1wb3J0YW50OwogICAgCiAgICAmOmhvdmVyIHsKICAgICAgY29sb3I6ICNkYzI2MjYgIWltcG9ydGFudDsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["BridgeConfig.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuKA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "BridgeConfig.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"bridge-config\">\n    <div v-if=\"!readonly\" class=\"config-header\">\n      <el-button\n        type=\"primary\"\n        icon=\"el-icon-connection\"\n        @click=\"showBridgeSelector = true\"\n      >\n        <i class=\"el-icon-s-home\" v-if=\"infrastructureType === 'bridge'\"></i>\n        <i class=\"el-icon-place\" v-else></i>\n        关联{{ infrastructureType === 'bridge' ? '桥梁' : '隧道' }}\n      </el-button>\n    </div>\n    \n    <!-- 已关联桥梁/隧道列表 -->\n    <div class=\"common-table\">\n      <el-table\n        :data=\"bridgeList\"\n        class=\"maintenance-table\"\n        :empty-text=\"`暂无关联${infrastructureType === 'bridge' ? '桥梁' : '隧道'}`\"\n      >\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n        \n        <el-table-column \n          prop=\"name\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}名称`\" \n          min-width=\"120\" \n          show-overflow-tooltip \n        />\n        \n        <el-table-column \n          prop=\"code\" \n          :label=\"`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}编号`\" \n          width=\"120\" \n          align=\"center\" \n        />\n        \n        <el-table-column prop=\"road\" label=\"所在道路\" width=\"120\" align=\"center\" />\n        \n        <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceContent\" label=\"养护内容\" min-width=\"150\" show-overflow-tooltip />\n        \n        <el-table-column prop=\"maintenanceStaff\" label=\"养护人员\" width=\"100\" align=\"center\" />\n        \n        <el-table-column v-if=\"!readonly\" label=\"操作\" width=\"80\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"mini\"\n              class=\"danger-text\"\n              @click=\"removeBridgeAssociation(scope.$index)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    \n    <!-- 桥梁/隧道选择器 -->\n    <bridge-selector\n      :visible.sync=\"showBridgeSelector\"\n      :multiple=\"true\"\n      :selected-data=\"bridgeList\"\n      :infrastructure-type=\"infrastructureType\"\n      @confirm=\"handleBridgeSelection\"\n    />\n  </div>\n</template>\n\n<script>\nimport BridgeSelector from '@/components/Maintenance/BridgeSelector'\n\nexport default {\n  name: 'BridgeConfig',\n  components: {\n    BridgeSelector\n  },\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge', // bridge, tunnel\n      validator: value => ['bridge', 'tunnel'].includes(value)\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      bridgeList: [],\n      showBridgeSelector: false\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && newVal.bridges && Array.isArray(newVal.bridges)) {\n          // 只在数据真正不同时才更新，避免循环\n          if (JSON.stringify(newVal.bridges) !== JSON.stringify(this.bridgeList)) {\n            this.bridgeList = [...newVal.bridges]\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    showBridgeSelector(visible) {\n      // 桥梁选择器显示状态变化\n    }\n  },\n  methods: {\n    // 统一的数据更新方法\n    emitChange() {\n      this.$nextTick(() => {\n        this.$emit('input', {\n          bridges: this.bridgeList\n        })\n      })\n    },\n    \n    // 处理桥梁选择\n    handleBridgeSelection(selectedBridges) {\n      // 为新选择的桥梁/隧道设置默认的养护内容和人员\n      const processedBridges = selectedBridges.map(bridge => ({\n        ...bridge,\n        maintenanceContent: bridge.maintenanceProject || '排水系统养护',\n        maintenanceStaff: bridge.maintenanceStaff || '待分配'\n      }))\n\n      this.bridgeList = processedBridges\n\n      if (processedBridges.length > 0) {\n        this.$message.success(`成功关联 ${processedBridges.length} 个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        this.emitChange()\n      }\n    },\n\n    // 移除桥梁/隧道关联\n    removeBridgeAssociation(index) {\n      this.bridgeList.splice(index, 1)\n      this.$message.success(`已移除${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}关联`)\n      this.emitChange()\n    },\n    \n    // 表单验证\n    validate() {\n      if (this.bridgeList.length === 0) {\n        this.$message.error(`请至少关联一个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)\n        return false\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/styles/maintenance-theme.scss';\n\n.bridge-config {\n  .config-header {\n    margin-bottom: 24px;\n    \n    .el-button {\n      i {\n        margin-right: 4px;\n      }\n    }\n  }\n  \n  .common-table {\n    .maintenance-table {\n      min-height: 200px;\n    }\n  }\n  \n  .danger-text {\n    color: #ef4444 !important;\n    \n    &:hover {\n      color: #dc2626 !important;\n    }\n  }\n}\n</style>\n"]}]}