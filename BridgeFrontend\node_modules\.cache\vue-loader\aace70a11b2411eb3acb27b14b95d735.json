{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue?vue&type=template&id=09e43857&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}