{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue?vue&type=style&index=0&id=6b388d54&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\BridgeSelector.vue", "mtime": 1758808367674}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsKQGltcG9ydCAnQC9hc3NldHMvc3R5bGVzL21haW50ZW5hbmNlLXRoZW1lLnNjc3MnOwoKOmRlZXAoLmJyaWRnZS1zZWxlY3Rvci1kaWFsb2cpIHsKICAvLyDlvLnnqpfmoLflvI/opobnm5YgLSDpgbXlvqrorr7orqHmlofmoaM2LjcuN+inhOiMgwogIC5lbC1kaWFsb2cgewogICAgYmFja2dyb3VuZDogIzA5MUE0QiAhaW1wb3J0YW50OyAvLyDkuI7kuLvopoHog4zmma/oibLkuIDoh7QKICAgIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsKICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjQpICFpbXBvcnRhbnQ7CiAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsKICAgIHdpZHRoOiA3MHZ3ICFpbXBvcnRhbnQ7CiAgICBtYXgtd2lkdGg6IDEyMDBweCAhaW1wb3J0YW50OwogICAgbWluLXdpZHRoOiA4MDBweCAhaW1wb3J0YW50OwogIH0KICAKICAuZWwtZGlhbG9nX19oZWFkZXIgewogICAgYmFja2dyb3VuZDogIzA5MUE0QiAhaW1wb3J0YW50OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICMzNzQxNTEgIWltcG9ydGFudDsKICAgIHBhZGRpbmc6IDE2cHggMjRweCAhaW1wb3J0YW50OwogICAgaGVpZ2h0OiA1NnB4ICFpbXBvcnRhbnQ7IC8vIOaMieiuvuiuoeaWh+aho+inhOiMgwogICAgCiAgICAuZWwtZGlhbG9nX190aXRsZSB7CiAgICAgIGNvbG9yOiAjZjhmYWZjICFpbXBvcnRhbnQ7IC8vIDYuNy4xIOS4u+imgeaWh+Wtl+minOiJsgogICAgICBmb250LXNpemU6IDE4cHggIWltcG9ydGFudDsKICAgICAgZm9udC13ZWlnaHQ6IG5vcm1hbCAhaW1wb3J0YW50OyAvLyDmraPluLjlrZfph40KICAgICAgZm9udC1mYW1pbHk6ICJQaW5nRmFuZyBTQyIsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgIkhpcmFnaW5vIFNhbnMgR0IiLCAiTWljcm9zb2Z0IFlhSGVpIiAhaW1wb3J0YW50OwogICAgfQogICAgCiAgICAuZWwtZGlhbG9nX19jbG9zZSB7CiAgICAgIGNvbG9yOiAjOTRhM2I4ICFpbXBvcnRhbnQ7CiAgICAgIGZvbnQtc2l6ZTogMThweCAhaW1wb3J0YW50OwogICAgICB3aWR0aDogMzJweCAhaW1wb3J0YW50OwogICAgICBoZWlnaHQ6IDMycHggIWltcG9ydGFudDsKICAgICAgbGluZS1oZWlnaHQ6IDMycHggIWltcG9ydGFudDsKICAgICAgCiAgICAgICY6aG92ZXIgewogICAgICAgIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7CiAgICAgICAgYmFja2dyb3VuZDogI2VmNDQ0NCAhaW1wb3J0YW50OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJSAhaW1wb3J0YW50OwogICAgICB9CiAgICB9CiAgfQogIAogIC5lbC1kaWFsb2dfX2JvZHkgewogICAgYmFja2dyb3VuZDogIzA5MUE0QiAhaW1wb3J0YW50OwogICAgcGFkZGluZzogMCAhaW1wb3J0YW50OyAvLyDnp7vpmaTpu5jorqRwYWRkaW5n77yM6K6p5YaF5a655Yy65Z+f6Ieq6KGM5o6n5Yi2CiAgICBjb2xvcjogI2Y4ZmFmYyAhaW1wb3J0YW50OwogICAgbWF4LWhlaWdodDogY2FsYyg5MHZoIC0gMTIwcHgpICFpbXBvcnRhbnQ7IC8vIOaMieiuvuiuoeaWh+aho+inhOiMgwogICAgb3ZlcmZsb3cteTogYXV0byAhaW1wb3J0YW50OwogIH0KICAKICAuZWwtZGlhbG9nX19mb290ZXIgewogICAgYmFja2dyb3VuZDogIzA5MUE0QiAhaW1wb3J0YW50OwogICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMzNzQxNTEgIWltcG9ydGFudDsKICAgIHBhZGRpbmc6IDE2cHggMjRweCAhaW1wb3J0YW50OwogICAgdGV4dC1hbGlnbjogcmlnaHQgIWltcG9ydGFudDsKICB9CiAgCiAgLnNlbGVjdG9yLWNvbnRlbnQgewogICAgcGFkZGluZzogMjRweCAhaW1wb3J0YW50OyAvLyDlhoXlrrnljLrln5/nu5/kuIDlhoXovrnot50KICAgIAogICAgLy8g5pCc57Si562b6YCJ5Yy65Z+fIC0g6YG15b6qNi43LjjovpPlhaXmoYbnu4Tku7bmoLflvI8KICAgIC5zZWFyY2gtc2VjdGlvbiB7CiAgICAgIHBhZGRpbmc6IDIwcHggIWltcG9ydGFudDsKICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7IC8vIDYuNy44IOi+k+WFpeahhuiDjOaZrwogICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMikgIWltcG9ydGFudDsgLy8gNi43Ljgg6L6T5YWl5qGG6L655qGGCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweCAhaW1wb3J0YW50OwogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4ICFpbXBvcnRhbnQ7CiAgICAgIAogICAgICAvLyDmkJzntKLovpPlhaXmoYbmoLflvI8KICAgICAgLmVsLWlucHV0IHsKICAgICAgICAuZWwtaW5wdXRfX2lubmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OyAvLyA2LjcuOCDovpPlhaXmoYbog4zmma8KICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSAhaW1wb3J0YW50OyAvLyA2LjcuOCDovpPlhaXmoYbovrnmoYYKICAgICAgICAgIGNvbG9yOiAjZjhmYWZjICFpbXBvcnRhbnQ7IC8vIDYuNy4xIOS4u+imgeaWh+Wtl+minOiJsgogICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4ICFpbXBvcnRhbnQ7IC8vIDYuNy44IOi+k+WFpeahhuWchuinkgogICAgICAgICAgaGVpZ2h0OiA0MHB4ICFpbXBvcnRhbnQ7IC8vIDYuNy44IOi+k+WFpeahhumrmOW6pgogICAgICAgICAgZm9udC1mYW1pbHk6ICJQaW5nRmFuZyBTQyIsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgIkhpcmFnaW5vIFNhbnMgR0IiLCAiTWljcm9zb2Z0IFlhSGVpIiAhaW1wb3J0YW50OwogICAgICAgICAgZm9udC1zaXplOiAxNnB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICBmb250LXdlaWdodDogbm9ybWFsICFpbXBvcnRhbnQ7CiAgICAgICAgICAKICAgICAgICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KSAhaW1wb3J0YW50OyAvLyA2LjcuOCDljaDkvY3nrKbpopzoibIKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgJjpmb2N1cyB7CiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpICFpbXBvcnRhbnQ7IC8vIDYuNy44IOaCrOWBnOi+ueahhgogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNCkgIWltcG9ydGFudDsgLy8gNi43Ljgg5oKs5YGc6L655qGGCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgICAKICAgIAogICAgLy8g5qGl5qKB5YiX6KGo6KGo5qC8IC0g6YG15b6qNi43LjTooajmoLznu4Tku7bmoLflvI8KICAgIC5icmlkZ2UtbGlzdCB7CiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgICAgIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50OwogICAgICBib3JkZXItcmFkaXVzOiA4cHggIWltcG9ydGFudDsKICAgICAgCiAgICAgIC8vIOihqOagvOagt+W8j+imhuebliAtIOWujOWFqOmBteW+qm1haW50ZW5hbmNlLXRoZW1lLnNjc3PnmoTooajmoLzmoLflvI8KICAgICAgLmVsLXRhYmxlIHsKICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50OwogICAgICAgIGNvbG9yOiAjZjhmYWZjICFpbXBvcnRhbnQ7IC8vIDYuNy4xIOS4u+imgeaWh+Wtl+minOiJsgogICAgICAgIGZvbnQtZmFtaWx5OiAiUGluZ0ZhbmcgU0MiLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICJTZWdvZSBVSSIsICJIaXJhZ2lubyBTYW5zIEdCIiwgIk1pY3Jvc29mdCBZYUhlaSIgIWltcG9ydGFudDsKICAgICAgICBib3JkZXI6IG5vbmUgIWltcG9ydGFudDsKICAgICAgICAKICAgICAgICAvLyDml6DovrnmoYborr7orqEKICAgICAgICAmOjpiZWZvcmUgewogICAgICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OwogICAgICAgIH0KICAgICAgICAKICAgICAgICAvLyDooajlpLTmoLflvI8gLSDlrozlhajmjInnhaforr7orqHmlofmoaM2LjcuNOinhOiMgwogICAgICAgIC5lbC10YWJsZV9faGVhZGVyLXdyYXBwZXIgewogICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzY3NzE4RiAwJSwgIzdCODVBMyAxMDAlKSAhaW1wb3J0YW50OwogICAgICAgICAgCiAgICAgICAgICB0aCB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICM2NzcxOEYgMCUsICM3Qjg1QTMgMTAwJSkgIWltcG9ydGFudDsKICAgICAgICAgICAgY29sb3I6ICNGRkYgIWltcG9ydGFudDsKICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDAgIWltcG9ydGFudDsgLy8g5q2j5bi45a2X6YeNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxNDAlICFpbXBvcnRhbnQ7IC8vIDIyLjRweAogICAgICAgICAgICBoZWlnaHQ6IDQ0cHggIWltcG9ydGFudDsKICAgICAgICAgICAgcGFkZGluZzogMTRweCAxMnB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsKICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcCAhaW1wb3J0YW50OwogICAgICAgICAgICAKICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgICAgICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICAKICAgICAgICAvLyDmlbDmja7ooYzmoLflvI8gLSDlrozlhajmjInnhaforr7orqHmlofmoaM2LjcuNOinhOiMgwogICAgICAgIC5lbC10YWJsZV9fYm9keS13cmFwcGVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgICAgICAgICAKICAgICAgICAgIHRyIHsKICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzI0MzA2NiAwJSwgIzFDMkE0RSAxMDAlKSAhaW1wb3J0YW50OwogICAgICAgICAgICAKICAgICAgICAgICAgLy8g56e76Zmk5Y+M5pWw6KGM55qE6IOM5pmv6Imy5beu5byCCiAgICAgICAgICAgICY6bnRoLWNoaWxkKG9kZCkgewogICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMyNDMwNjYgMCUsICMxQzJBNEUgMTAwJSkgIWltcG9ydGFudDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgJjpudGgtY2hpbGQoZXZlbikgewogICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxODBkZWcsICMyNDMwNjYgMCUsICMxQzJBNEUgMTAwJSkgIWltcG9ydGFudDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgLy8g5oKs5YGc5pWI5p6cCiAgICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSkgIWltcG9ydGFudDsKICAgICAgICAgICAgICAKICAgICAgICAgICAgICB0ZCB7CiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAvLyDmnIDlkI7kuIDooYzml6DlupXpg6jovrnmoYYKICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgICAgICB0ZCB7CiAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICB0ZCB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzI0MzA2NiAwJSwgIzFDMkE0RSAxMDAlKSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGNvbG9yOiAjRkZGICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMCAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxMjAlICFpbXBvcnRhbnQ7IC8vIDE2LjhweAogICAgICAgICAgICAgIHBhZGRpbmc6IDRweCAxMnB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHsKICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICAKICAgICAgICAvLyDnqbrnirbmgIHmoLflvI8KICAgICAgICAuZWwtdGFibGVfX2VtcHR5LWJsb2NrIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgICAgICAgICAKICAgICAgICAgIC5lbC10YWJsZV9fZW1wdHktdGV4dCB7CiAgICAgICAgICAgIGNvbG9yOiAjOTRhM2I4ICFpbXBvcnRhbnQ7IC8vIDYuNy4xIOmdmem7mOaWh+Wtl+minOiJsgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICAKICAgICAgLy8g5aSN6YCJ5qGG5qC35byPCiAgICAgIC5lbC1jaGVja2JveCB7CiAgICAgICAgLmVsLWNoZWNrYm94X19pbm5lciB7CiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7CiAgICAgICAgICAKICAgICAgICAgICY6aG92ZXIgewogICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KSAhaW1wb3J0YW50OwogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICAmLmlzLWNoZWNrZWQgewogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjNUM5REZGICFpbXBvcnRhbnQ7IC8vIDYuNy4xIOiTneiJsuW8uuiwg+iJsgogICAgICAgICAgICBib3JkZXItY29sb3I6ICM1QzlERkYgIWltcG9ydGFudDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgCiAgICAgICAgLmVsLWNoZWNrYm94X19sYWJlbCB7CiAgICAgICAgICBjb2xvcjogI2Y4ZmFmYyAhaW1wb3J0YW50OyAvLyA2LjcuMSDkuLvopoHmloflrZfpopzoibIKICAgICAgICB9CiAgICAgIH0KICAgICAgCiAgICAgIC5icmlkZ2UtdHlwZSB7CiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDsKICAgICAgICBnYXA6IDRweCAhaW1wb3J0YW50OwogICAgICAgIAogICAgICAgIGkgewogICAgICAgICAgY29sb3I6ICM1QzlERkYgIWltcG9ydGFudDsgLy8gNi43LjEg6JOd6Imy5by66LCD6ImyCiAgICAgICAgfQogICAgICB9CiAgICAgIAogICAgICAuc2VsZWN0ZWQtYnRuIHsKICAgICAgICBjb2xvcjogIzEwYjk4MSAhaW1wb3J0YW50OyAvLyDnu7/oibLooajnpLrlt7LpgInmi6kKICAgICAgICAKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGNvbG9yOiAjMzRkMzk5ICFpbXBvcnRhbnQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIAogICAgICAvLyDliIbpobXlrrnlmajmoLflvI8KICAgICAgLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICAgICAgICBwYWRkaW5nOiAyMHB4IDAgIWltcG9ydGFudDsKICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsKICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50OwogICAgICAgIAogICAgICAgIC8vIOWIhumhtee7hOS7tuagt+W8jyAtIOmBteW+quiuvuiuoeaWh+aho+inhOiMgwogICAgICAgIC5lbC1wYWdpbmF0aW9uIHsKICAgICAgICAgIC5lbC1wYWdpbmF0aW9uX190b3RhbCwKICAgICAgICAgIC5lbC1wYWdpbmF0aW9uX19qdW1wIHsKICAgICAgICAgICAgY29sb3I6ICM5NGEzYjggIWltcG9ydGFudDsgLy8gNi43LjEg6Z2Z6buY5paH5a2X6aKc6ImyCiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiAiUGluZ0ZhbmcgU0MiLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICJTZWdvZSBVSSIsICJIaXJhZ2lubyBTYW5zIEdCIiwgIk1pY3Jvc29mdCBZYUhlaSIgIWltcG9ydGFudDsKICAgICAgICAgIH0KICAgICAgICAgIAogICAgICAgICAgLmJ0bi1wcmV2LAogICAgICAgICAgLmJ0bi1uZXh0LAogICAgICAgICAgLmVsLXBhZ2VyIGxpIHsKICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzRiNTU2MyAhaW1wb3J0YW50OwogICAgICAgICAgICBjb2xvcjogIzk0YTNiOCAhaW1wb3J0YW50OwogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHggIWltcG9ydGFudDsKICAgICAgICAgICAgbWFyZ2luOiAwIDJweCAhaW1wb3J0YW50OwogICAgICAgICAgICAKICAgICAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzM3NDE1MSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNCkgIWltcG9ydGFudDsKICAgICAgICAgICAgfQogICAgICAgICAgICAKICAgICAgICAgICAgJi5hY3RpdmUgewogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICM1QzlERkYgIWltcG9ydGFudDsgLy8gNi43LjEg6JOd6Imy5by66LCD6ImyCiAgICAgICAgICAgICAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsKICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM1QzlERkYgIWltcG9ydGFudDsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgCiAgICAgICAgICAuZWwtcGFnaW5hdGlvbl9fanVtcCB7CiAgICAgICAgICAgIC5lbC1pbnB1dF9faW5uZXIgewogICAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgY29sb3I6ICNmOGZhZmMgIWltcG9ydGFudDsKICAgICAgICAgICAgICAKICAgICAgICAgICAgICAmOmZvY3VzIHsKICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpICFpbXBvcnRhbnQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQogIAogIC8vIOW6lemDqOaMiemSruagt+W8jyAtIOmBteW+quiuvuiuoeaWh+aho+inhOiMgwogIC5kaWFsb2ctZm9vdGVyIHsKICAgIHRleHQtYWxpZ246IHJpZ2h0ICFpbXBvcnRhbnQ7CiAgICAKICAgIC5lbC1idXR0b24gewogICAgICBtaW4td2lkdGg6IDgwcHggIWltcG9ydGFudDsKICAgICAgaGVpZ2h0OiAzNnB4ICFpbXBvcnRhbnQ7CiAgICAgIGJvcmRlci1yYWRpdXM6IDZweCAhaW1wb3J0YW50OwogICAgICBmb250LXNpemU6IDE0cHggIWltcG9ydGFudDsKICAgICAgZm9udC1mYW1pbHk6ICJQaW5nRmFuZyBTQyIsIC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgIlNlZ29lIFVJIiwgIkhpcmFnaW5vIFNhbnMgR0IiLCAiTWljcm9zb2Z0IFlhSGVpIiAhaW1wb3J0YW50OwogICAgICBmb250LXdlaWdodDogbm9ybWFsICFpbXBvcnRhbnQ7CiAgICAgIHBhZGRpbmc6IDAgMTZweCAhaW1wb3J0YW50OwogICAgICBtYXJnaW4tbGVmdDogMTJweCAhaW1wb3J0YW50OwogICAgICAKICAgICAgLy8g6buY6K6k5oyJ6ZKu5qC35byPCiAgICAgICYuZWwtYnV0dG9uLS1kZWZhdWx0IHsKICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykgIWltcG9ydGFudDsKICAgICAgICBjb2xvcjogI2Y4ZmFmYyAhaW1wb3J0YW50OyAvLyA2LjcuMSDkuLvopoHmloflrZfpopzoibIKICAgICAgICAKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSkgIWltcG9ydGFudDsKICAgICAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjQpICFpbXBvcnRhbnQ7CiAgICAgICAgICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50OwogICAgICAgIH0KICAgICAgICAKICAgICAgICAmOmZvY3VzIHsKICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAhaW1wb3J0YW50OwogICAgICAgICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMykgIWltcG9ydGFudDsKICAgICAgICAgIGNvbG9yOiAjZjhmYWZjICFpbXBvcnRhbnQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIAogICAgICAvLyDkuLvopoHmjInpkq7moLflvI8KICAgICAgJi5lbC1idXR0b24tLXByaW1hcnkgewogICAgICAgIGJhY2tncm91bmQ6ICM1QzlERkYgIWltcG9ydGFudDsgLy8gNi43LjEg6JOd6Imy5by66LCD6ImyCiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNUM5REZGICFpbXBvcnRhbnQ7CiAgICAgICAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsKICAgICAgICAKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICM3NGE3ZjUgIWltcG9ydGFudDsgLy8gNi43LjEg5rWF6JOd6ImyCiAgICAgICAgICBib3JkZXItY29sb3I6ICM3NGE3ZjUgIWltcG9ydGFudDsKICAgICAgICAgIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7CiAgICAgICAgfQogICAgICAgIAogICAgICAgICY6Zm9jdXMgewogICAgICAgICAgYmFja2dyb3VuZDogIzVDOURGRiAhaW1wb3J0YW50OwogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNUM5REZGICFpbXBvcnRhbnQ7CiAgICAgICAgICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50OwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["BridgeSelector.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0XA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BridgeSelector.vue", "sourceRoot": "src/components/Maintenance", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"关联桥梁\"\n    :visible.sync=\"dialogVisible\"\n    custom-class=\"bridge-selector-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size\"\n    :close-on-click-modal=\"false\"\n    :modal-append-to-body=\"true\"\n    :append-to-body=\"true\"\n    top=\"5vh\"\n    destroy-on-close\n  >\n    <div class=\"dialog-content\">\n      <!-- 搜索表单 - 复用通用搜索表单样式 -->\n      <div class=\"search-form\">\n        <el-form :model=\"queryParams\" inline>\n          <el-form-item label=\"桥梁名称\">\n            <el-input\n              v-model=\"queryParams.name\"\n              placeholder=\"输入桥梁名称或编号\"\n              clearable\n              style=\"width: 200px\"\n              @keyup.enter.native=\"handleQuery\"\n              @clear=\"handleQuery\"\n            />\n          </el-form-item>\n          \n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n      \n      \n      <!-- 桥梁列表 - 复用通用表格样式 -->\n      <div class=\"common-table\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"bridgeList\"\n          class=\"maintenance-table\"\n          style=\"width: 100%\"\n          :row-style=\"{ height: '32px' }\"\n          size=\"small\"\n          @selection-change=\"handleSelectionChange\"\n        >\n          <el-table-column\n            type=\"selection\"\n            width=\"55\"\n            :selectable=\"isSelectable\"\n          />\n          \n          <el-table-column prop=\"project\" label=\"养护项目\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"maintainer\" label=\"养护人员\" width=\"100\" align=\"center\" />\n          \n          <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n          \n          <el-table-column prop=\"name\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"code\" label=\"桥梁编号\" width=\"120\" align=\"center\" />\n          \n          <el-table-column prop=\"road\" label=\"所在道路\" min-width=\"120\" show-overflow-tooltip />\n          \n          <el-table-column prop=\"managementUnit\" label=\"管理单位\" min-width=\"120\" show-overflow-tooltip />\n        </el-table>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"dialogVisible = false\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\">\n        确定 ({{ selectedBridges.length }})\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getBridgeList } from '@/api/maintenance/projects'\n\nexport default {\n  name: 'BridgeSelector',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    multiple: {\n      type: Boolean,\n      default: true\n    },\n    selectedData: {\n      type: Array,\n      default: () => []\n    },\n    infrastructureType: {\n      type: String,\n      default: 'bridge' // bridge, tunnel\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      bridgeList: [],\n      selectedBridges: [],\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: 'bridge',\n        name: '',\n        district: '',\n        managementUnit: ''\n      },\n      \n      // 选项数据\n      districtOptions: [\n        { label: '岳麓区', value: 'yuelu' },\n        { label: '芙蓉区', value: 'furong' },\n        { label: '天心区', value: 'tianxin' },\n        { label: '开福区', value: 'kaifu' },\n        { label: '雨花区', value: 'yuhua' },\n        { label: '望城区', value: 'wangcheng' }\n      ],\n      \n      unitOptions: [\n        { label: '长沙市桥梁管理处', value: 'changsha_bridge' },\n        { label: '长沙市隧道管理处', value: 'changsha_tunnel' },\n        { label: '岳麓区市政局', value: 'yuelu_municipal' },\n        { label: '芙蓉区市政局', value: 'furong_municipal' }\n      ]\n    }\n  },\n  computed: {\n    dialogVisible: {\n      get() {\n        return this.visible\n      },\n      set(val) {\n        this.$emit('update:visible', val)\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initData()\n        this.getList()\n      }\n    },\n    \n    infrastructureType: {\n      immediate: true,\n      handler(val) {\n        this.queryParams.infrastructureType = val\n      }\n    }\n  },\n  methods: {\n    // 初始化数据\n    initData() {\n      this.selectedBridges = [...this.selectedData]\n      this.queryParams.infrastructureType = this.infrastructureType\n    },\n    \n    // 获取桥梁列表\n    async getList() {\n      try {\n        this.loading = true\n        const response = await getBridgeList(this.queryParams)\n        if (response.data) {\n          let bridgeData = response.data.rows || response.data.list || []\n          \n          // 为每条数据添加结构图所需的字段\n          this.bridgeList = bridgeData.map((bridge, index) => ({\n            ...bridge,\n            project: bridge.project || 'XXXXXX大桥', // 养护项目\n            maintainer: bridge.maintainer || ['黄昭言', '刘雨桐', '罗砚秋'][index % 3], // 养护人员\n            code: bridge.code || `CS-B-${String(index + 1).padStart(3, '0')}`, // 桥梁编号\n            road: bridge.road || '枫林一路', // 所在道路\n            managementUnit: bridge.managementUnit || '桥隧中心' // 管理单位\n          }))\n          this.total = response.data.total || 0\n        } else {\n          // 如果接口失败，使用模拟数据展示结构\n          this.bridgeList = [\n            {\n              id: 1,\n              project: 'XXXXXX大桥',\n              maintainer: '黄昭言',\n              name: 'XXX大桥',\n              code: 'CS-B-001',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 2,\n              project: 'XXXXXX大桥',\n              maintainer: '刘雨桐',\n              name: '刘雨桐',\n              code: 'CS-B-002', \n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            },\n            {\n              id: 3,\n              project: 'XXXXXX大桥',\n              maintainer: '罗砚秋',\n              name: '罗砚秋',\n              code: 'CS-B-003',\n              road: '枫林一路',\n              managementUnit: '桥隧中心'\n            }\n          ]\n          this.total = 3\n        }\n      } catch (error) {\n        console.error('获取桥梁列表失败:', error)\n        this.$message.error('获取桥梁列表失败')\n        \n        // 错误时也显示模拟数据以展示结构\n        this.bridgeList = [\n          {\n            id: 1,\n            project: 'XXXXXX大桥',\n            maintainer: '黄昭言',\n            name: 'XXX大桥',\n            code: 'CS-B-001',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 2,\n            project: 'XXXXXX大桥',\n            maintainer: '刘雨桐',\n            name: '刘雨桐',\n            code: 'CS-B-002',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          },\n          {\n            id: 3,\n            project: 'XXXXXX大桥',\n            maintainer: '罗砚秋',\n            name: '罗砚秋',\n            code: 'CS-B-003',\n            road: '枫林一路',\n            managementUnit: '桥隧中心'\n          }\n        ]\n        this.total = 3\n        \n        // 设置已选中的行\n        this.$nextTick(() => {\n          if (this.$refs.bridgeTable && this.selectedData && this.selectedData.length > 0) {\n            this.bridgeList.forEach(bridge => {\n              const isSelected = this.selectedData.some(selected => selected.id === bridge.id)\n              if (isSelected) {\n                this.$refs.bridgeTable.toggleRowSelection(bridge, true)\n              }\n            })\n          }\n        })\n        \n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        infrastructureType: this.infrastructureType,\n        name: '',\n        district: '',\n        managementUnit: ''\n      }\n      this.getList()\n    },\n    \n    // 选择桥梁\n    selectBridge(bridge) {\n      if (!this.multiple) {\n        this.selectedBridges = [bridge]\n      } else {\n        if (!this.isSelected(bridge)) {\n          this.selectedBridges.push(bridge)\n        }\n      }\n    },\n    \n    // 移除选择\n    removeSelected(bridge) {\n      const index = this.selectedBridges.findIndex(item => item.id === bridge.id)\n      if (index > -1) {\n        this.selectedBridges.splice(index, 1)\n      }\n    },\n    \n    // 清空选择\n    clearSelected() {\n      this.selectedBridges = []\n    },\n    \n    // 判断是否已选择\n    isSelected(bridge) {\n      return this.selectedBridges.some(item => item.id === bridge.id)\n    },\n    \n    // 判断是否可选择\n    isSelectable(row) {\n      return true // 允许选择和取消选择\n    },\n    \n    // 表格选择变化\n    handleSelectionChange(selection) {\n      if (this.multiple) {\n        // 直接使用当前页面的选择结果\n        this.selectedBridges = selection\n      }\n    },\n    \n    // 确认选择\n    confirmSelection() {\n      this.$emit('confirm', this.selectedBridges)\n      this.dialogVisible = false\n    },\n    \n    // 获取养护等级类型\n    getMaintenanceLevelType(level) {\n      const typeMap = {\n        '一级': 'danger',\n        '二级': 'warning',\n        '三级': 'info',\n        '四级': 'success'\n      }\n      return typeMap[level] || 'info'\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n:deep(.bridge-selector-dialog) {\n  // 弹窗样式覆盖 - 遵循设计文档6.7.7规范\n  .el-dialog {\n    background: #091A4B !important; // 与主要背景色一致\n    border-radius: 12px !important;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;\n    border: none !important;\n    width: 70vw !important;\n    max-width: 1200px !important;\n    min-width: 800px !important;\n  }\n  \n  .el-dialog__header {\n    background: #091A4B !important;\n    border-bottom: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    height: 56px !important; // 按设计文档规范\n    \n    .el-dialog__title {\n      color: #f8fafc !important; // 6.7.1 主要文字颜色\n      font-size: 18px !important;\n      font-weight: normal !important; // 正常字重\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n    }\n    \n    .el-dialog__close {\n      color: #94a3b8 !important;\n      font-size: 18px !important;\n      width: 32px !important;\n      height: 32px !important;\n      line-height: 32px !important;\n      \n      &:hover {\n        color: #ffffff !important;\n        background: #ef4444 !important;\n        border-radius: 50% !important;\n      }\n    }\n  }\n  \n  .el-dialog__body {\n    background: #091A4B !important;\n    padding: 0 !important; // 移除默认padding，让内容区域自行控制\n    color: #f8fafc !important;\n    max-height: calc(90vh - 120px) !important; // 按设计文档规范\n    overflow-y: auto !important;\n  }\n  \n  .el-dialog__footer {\n    background: #091A4B !important;\n    border-top: 1px solid #374151 !important;\n    padding: 16px 24px !important;\n    text-align: right !important;\n  }\n  \n  .selector-content {\n    padding: 24px !important; // 内容区域统一内边距\n    \n    // 搜索筛选区域 - 遵循6.7.8输入框组件样式\n    .search-section {\n      padding: 20px !important;\n      background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n      border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n      border-radius: 8px !important;\n      margin-bottom: 20px !important;\n      \n      // 搜索输入框样式\n      .el-input {\n        .el-input__inner {\n          background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景\n          border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n          border-radius: 8px !important; // 6.7.8 输入框圆角\n          height: 40px !important; // 6.7.8 输入框高度\n          font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          font-size: 16px !important;\n          font-weight: normal !important;\n          \n          &::placeholder {\n            color: rgba(255, 255, 255, 0.5) !important; // 6.7.8 占位符颜色\n          }\n          \n          &:focus {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框\n          }\n        }\n      }\n    }\n    \n    \n    // 桥梁列表表格 - 遵循6.7.4表格组件样式\n    .bridge-list {\n      background: transparent !important;\n      border: none !important;\n      border-radius: 8px !important;\n      \n      // 表格样式覆盖 - 完全遵循maintenance-theme.scss的表格样式\n      .el-table {\n        background: transparent !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n        border: none !important;\n        \n        // 无边框设计\n        &::before {\n          display: none !important;\n        }\n        \n        // 表头样式 - 完全按照设计文档6.7.4规范\n        .el-table__header-wrapper {\n          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n          \n          th {\n            background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;\n            color: #FFF !important;\n            font-size: 16px !important;\n            font-weight: 400 !important; // 正常字重\n            line-height: 140% !important; // 22.4px\n            height: 44px !important;\n            padding: 14px 12px !important;\n            border: 1px solid rgba(255, 255, 255, 0.1) !important;\n            text-align: center !important;\n            white-space: nowrap !important;\n            \n            &:last-child {\n              border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n            }\n          }\n        }\n        \n        // 数据行样式 - 完全按照设计文档6.7.4规范\n        .el-table__body-wrapper {\n          background: transparent !important;\n          \n          tr {\n            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            \n            // 移除双数行的背景色差异\n            &:nth-child(odd) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            &:nth-child(even) {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n            }\n            \n            // 悬停效果\n            &:hover {\n              background: rgba(255, 255, 255, 0.05) !important;\n              \n              td {\n                background: rgba(255, 255, 255, 0.05) !important;\n              }\n            }\n            \n            // 最后一行无底部边框\n            &:last-child {\n              td {\n                border-bottom: none !important;\n              }\n            }\n            \n            td {\n              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;\n              color: #FFF !important;\n              font-size: 14px !important;\n              font-weight: 400 !important;\n              line-height: 120% !important; // 16.8px\n              padding: 4px 12px !important;\n              border: 1px solid rgba(255, 255, 255, 0.1) !important;\n              text-align: center !important;\n              height: 32px !important;\n              \n              &:last-child {\n                border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n              }\n            }\n          }\n        }\n        \n        // 空状态样式\n        .el-table__empty-block {\n          background: transparent !important;\n          \n          .el-table__empty-text {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n          }\n        }\n      }\n      \n      // 复选框样式\n      .el-checkbox {\n        .el-checkbox__inner {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.2) !important;\n          \n          &:hover {\n            border-color: rgba(255, 255, 255, 0.4) !important;\n          }\n          \n          &.is-checked {\n            background: #5C9DFF !important; // 6.7.1 蓝色强调色\n            border-color: #5C9DFF !important;\n          }\n        }\n        \n        .el-checkbox__label {\n          color: #f8fafc !important; // 6.7.1 主要文字颜色\n        }\n      }\n      \n      .bridge-type {\n        display: flex !important;\n        align-items: center !important;\n        gap: 4px !important;\n        \n        i {\n          color: #5C9DFF !important; // 6.7.1 蓝色强调色\n        }\n      }\n      \n      .selected-btn {\n        color: #10b981 !important; // 绿色表示已选择\n        \n        &:hover {\n          color: #34d399 !important;\n        }\n      }\n      \n      // 分页容器样式\n      .pagination-container {\n        padding: 20px 0 !important;\n        text-align: center !important;\n        background: transparent !important;\n        \n        // 分页组件样式 - 遵循设计文档规范\n        .el-pagination {\n          .el-pagination__total,\n          .el-pagination__jump {\n            color: #94a3b8 !important; // 6.7.1 静默文字颜色\n            font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n          }\n          \n          .btn-prev,\n          .btn-next,\n          .el-pager li {\n            background: transparent !important;\n            border: 1px solid #4b5563 !important;\n            color: #94a3b8 !important;\n            border-radius: 4px !important;\n            margin: 0 2px !important;\n            \n            &:hover {\n              background: #374151 !important;\n              color: #ffffff !important;\n              border-color: rgba(255, 255, 255, 0.4) !important;\n            }\n            \n            &.active {\n              background: #5C9DFF !important; // 6.7.1 蓝色强调色\n              color: #ffffff !important;\n              border-color: #5C9DFF !important;\n            }\n          }\n          \n          .el-pagination__jump {\n            .el-input__inner {\n              background: rgba(255, 255, 255, 0.1) !important;\n              border-color: rgba(255, 255, 255, 0.2) !important;\n              color: #f8fafc !important;\n              \n              &:focus {\n                border-color: rgba(255, 255, 255, 0.4) !important;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  \n  // 底部按钮样式 - 遵循设计文档规范\n  .dialog-footer {\n    text-align: right !important;\n    \n    .el-button {\n      min-width: 80px !important;\n      height: 36px !important;\n      border-radius: 6px !important;\n      font-size: 14px !important;\n      font-family: \"PingFang SC\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Hiragino Sans GB\", \"Microsoft YaHei\" !important;\n      font-weight: normal !important;\n      padding: 0 16px !important;\n      margin-left: 12px !important;\n      \n      // 默认按钮样式\n      &.el-button--default {\n        background: rgba(255, 255, 255, 0.1) !important;\n        border: 1px solid rgba(255, 255, 255, 0.3) !important;\n        color: #f8fafc !important; // 6.7.1 主要文字颜色\n        \n        &:hover {\n          background: rgba(255, 255, 255, 0.15) !important;\n          border-color: rgba(255, 255, 255, 0.4) !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: rgba(255, 255, 255, 0.1) !important;\n          border-color: rgba(255, 255, 255, 0.3) !important;\n          color: #f8fafc !important;\n        }\n      }\n      \n      // 主要按钮样式\n      &.el-button--primary {\n        background: #5C9DFF !important; // 6.7.1 蓝色强调色\n        border-color: #5C9DFF !important;\n        color: #ffffff !important;\n        \n        &:hover {\n          background: #74a7f5 !important; // 6.7.1 浅蓝色\n          border-color: #74a7f5 !important;\n          color: #ffffff !important;\n        }\n        \n        &:focus {\n          background: #5C9DFF !important;\n          border-color: #5C9DFF !important;\n          color: #ffffff !important;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}