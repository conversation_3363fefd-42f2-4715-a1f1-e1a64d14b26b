{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue?vue&type=template&id=b3f555b6", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\components\\Maintenance\\ProjectDialog.vue", "mtime": 1758806998018}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJlbC1kaWFsb2ciLAogICAgewogICAgICBhdHRyczogewogICAgICAgIHRpdGxlOiBfdm0ubW9kYWxUaXRsZSwKICAgICAgICB2aXNpYmxlOiBfdm0uZGlhbG9nVmlzaWJsZSwKICAgICAgICAiYmVmb3JlLWNsb3NlIjogX3ZtLmhhbmRsZUNsb3NlLAogICAgICAgICJhcHBlbmQtdG8tYm9keSI6IHRydWUsCiAgICAgICAgIm1vZGFsLWFwcGVuZC10by1ib2R5IjogdHJ1ZSwKICAgICAgICAiY2xvc2Utb24tY2xpY2stbW9kYWwiOiBmYWxzZSwKICAgICAgICAic2hvdy1jbG9zZSI6IGZhbHNlLAogICAgICAgICJjdXN0b20tY2xhc3MiOgogICAgICAgICAgInByb2plY3QtZGlhbG9nIGluc3BlY3Rpb24tZGlhbG9nLWJhc2UgbW9kZXJuLWRpYWxvZyBkYXJrLXRoZW1lIGZvcmNlLWhpZ2gtemluZGV4IGNvbW1vbi1kaWFsb2ctd2lkZSBwcm9qZWN0LWRpYWxvZy1maXhlZC1zaXplIiwKICAgICAgICB0b3A6ICI1dmgiLAogICAgICB9LAogICAgICBvbjogewogICAgICAgICJ1cGRhdGU6dmlzaWJsZSI6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgIF92bS5kaWFsb2dWaXNpYmxlID0gJGV2ZW50CiAgICAgICAgfSwKICAgICAgfSwKICAgIH0sCiAgICBbCiAgICAgIF9jKAogICAgICAgICJkaXYiLAogICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjdXN0b20tY2xvc2UtYnRuIiwgb246IHsgY2xpY2s6IF92bS5oYW5kbGVDbG9zZSB9IH0sCiAgICAgICAgWwogICAgICAgICAgX2MoCiAgICAgICAgICAgICJzdmciLAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgIHdpZHRoOiAiMzYiLAogICAgICAgICAgICAgICAgaGVpZ2h0OiAiMzYiLAogICAgICAgICAgICAgICAgdmlld0JveDogIjAgMCAzNiAzNiIsCiAgICAgICAgICAgICAgICBmaWxsOiAibm9uZSIsCiAgICAgICAgICAgICAgICB4bWxuczogImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoInBhdGgiLCB7CiAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICBkOiAiTTE1LjkgMThMMTIuNzUgMTQuODVMMTQuODUgMTIuNzVMMTggMTUuOUwyMS4xNSAxMi43NUwyMy4yNSAxNC44NUwyMC4xIDE4TDIzLjI1IDIxLjE1TDIxLjE1IDIzLjI1TDE4IDIwLjFMMTQuODUgMjMuMjVMMTIuNzUgMjEuMTVMMTUuOSAxOFpNMTggMzBDMTEuNCAzMCA2IDI0LjYgNiAxOEM2IDExLjQgMTEuNCA2IDE4IDZDMjQuNiA2IDMwIDExLjQgMzAgMThDMzAgMjQuNiAyNC42IDMwIDE4IDMwWk0xOCAyN0MyMi45NSAyNyAyNyAyMi45NSAyNyAxOEMyNyAxMy4wNSAyMi45NSA5IDE4IDlDMTMuMDUgOSA5IDEzLjA1IDkgMThDOSAyMi45NSAxMy4wNSAyNyAxOCAyN1oiLAogICAgICAgICAgICAgICAgICBmaWxsOiAid2hpdGUiLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgXQogICAgICAgICAgKSwKICAgICAgICBdCiAgICAgICksCiAgICAgIF9jKCJzdGVwLW5hdmlnYXRpb24iLCB7CiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgIHN0ZXBzOiBfdm0uc3RlcHMsCiAgICAgICAgICAiY3VycmVudC1zdGVwIjogX3ZtLmN1cnJlbnRTdGVwLAogICAgICAgICAgImFsbG93LWNsaWNrIjogX3ZtLmFsbG93U3RlcENsaWNrLAogICAgICAgICAgImNsaWNrYWJsZS1zdGVwcyI6IF92bS5jbGlja2FibGVTdGVwcywKICAgICAgICB9LAogICAgICAgIG9uOiB7ICJzdGVwLWNsaWNrIjogX3ZtLmhhbmRsZVN0ZXBDbGljayB9LAogICAgICB9KSwKICAgICAgX2MoCiAgICAgICAgImRpdiIsCiAgICAgICAgeyBzdGF0aWNDbGFzczogInN0ZXAtY29udGVudCIgfSwKICAgICAgICBbCiAgICAgICAgICBfdm0uY3VycmVudFN0ZXAgPT09IDAKICAgICAgICAgICAgPyBfYygiYmFzaWMtaW5mbyIsIHsKICAgICAgICAgICAgICAgIHJlZjogImJhc2ljSW5mbyIsCiAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAicHJvamVjdC10eXBlIjogX3ZtLnByb2plY3RUeXBlLAogICAgICAgICAgICAgICAgICByZWFkb25seTogX3ZtLmlzUmVhZG9ubHlNb2RlLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uOiB7ICJwcm9qZWN0LXR5cGUtY2hhbmdlIjogX3ZtLmhhbmRsZVByb2plY3RUeXBlQ2hhbmdlIH0sCiAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLmZvcm1EYXRhLmJhc2ljSW5mbywKICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICBfdm0uJHNldChfdm0uZm9ybURhdGEsICJiYXNpY0luZm8iLCAkJHYpCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJmb3JtRGF0YS5iYXNpY0luZm8iLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB9KQogICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgX3ZtLmN1cnJlbnRTdGVwID09PSAxICYmIF92bS5zaG93UHJvamVjdENvbmZpZwogICAgICAgICAgICA/IF9jKCJwcm9qZWN0LWNvbmZpZyIsIHsKICAgICAgICAgICAgICAgIHJlZjogInByb2plY3RDb25maWciLAogICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgInByb2plY3QtdHlwZSI6IF92bS5wcm9qZWN0VHlwZSwKICAgICAgICAgICAgICAgICAgcmVhZG9ubHk6IF92bS5pc1JlYWRvbmx5TW9kZSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLmZvcm1EYXRhLnByb2plY3RDb25maWcsCiAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgX3ZtLiRzZXQoX3ZtLmZvcm1EYXRhLCAicHJvamVjdENvbmZpZyIsICQkdikKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImZvcm1EYXRhLnByb2plY3RDb25maWciLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB9KQogICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgKF92bS5jdXJyZW50U3RlcCA9PT0gMiAmJiBfdm0ucHJvamVjdFR5cGUgPT09ICJtb250aGx5IikgfHwKICAgICAgICAgIChfdm0uY3VycmVudFN0ZXAgPT09IDEgJiYgX3ZtLnByb2plY3RUeXBlID09PSAicHJldmVudGl2ZSIpCiAgICAgICAgICAgID8gX2MoImRpc2Vhc2UtY29uZmlnIiwgewogICAgICAgICAgICAgICAgcmVmOiAiZGlzZWFzZUNvbmZpZyIsCiAgICAgICAgICAgICAgICBhdHRyczogeyByZWFkb25seTogX3ZtLmlzUmVhZG9ubHlNb2RlIH0sCiAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLmZvcm1EYXRhLmRpc2Vhc2VDb25maWcsCiAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgX3ZtLiRzZXQoX3ZtLmZvcm1EYXRhLCAiZGlzZWFzZUNvbmZpZyIsICQkdikKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImZvcm1EYXRhLmRpc2Vhc2VDb25maWciLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB9KQogICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgX3ZtLmN1cnJlbnRTdGVwID09PSAyICYmIF92bS5wcm9qZWN0VHlwZSA9PT0gInByZXZlbnRpdmUiCiAgICAgICAgICAgID8gX2MoImltcGxlbWVudGF0aW9uLWNvbmZpZyIsIHsKICAgICAgICAgICAgICAgIHJlZjogImltcGxlbWVudGF0aW9uQ29uZmlnIiwKICAgICAgICAgICAgICAgIGF0dHJzOiB7IHJlYWRvbmx5OiBfdm0uaXNSZWFkb25seU1vZGUgfSwKICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uZm9ybURhdGEuaW1wbGVtZW50YXRpb25Db25maWcsCiAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgX3ZtLiRzZXQoX3ZtLmZvcm1EYXRhLCAiaW1wbGVtZW50YXRpb25Db25maWciLCAkJHYpCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJmb3JtRGF0YS5pbXBsZW1lbnRhdGlvbkNvbmZpZyIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICBfdm0uY3VycmVudFN0ZXAgPT09IDMgJiYgX3ZtLnByb2plY3RUeXBlID09PSAicHJldmVudGl2ZSIKICAgICAgICAgICAgPyBfYygiY29tcGxldGlvbi1pbmZvIiwgewogICAgICAgICAgICAgICAgcmVmOiAiY29tcGxldGlvbkluZm8iLAogICAgICAgICAgICAgICAgYXR0cnM6IHsgcmVhZG9ubHk6IF92bS5pc1JlYWRvbmx5TW9kZSB9LAogICAgICAgICAgICAgICAgbW9kZWw6IHsKICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5mb3JtRGF0YS5jb21wbGV0aW9uSW5mbywKICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICBfdm0uJHNldChfdm0uZm9ybURhdGEsICJjb21wbGV0aW9uSW5mbyIsICQkdikKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImZvcm1EYXRhLmNvbXBsZXRpb25JbmZvIiwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgIF92bS5jdXJyZW50U3RlcCA9PT0gX3ZtLmdldExhc3RTdGVwSW5kZXgoKSAmJgogICAgICAgICAgX3ZtLnByb2plY3RUeXBlICE9PSAicHJldmVudGl2ZSIgJiYKICAgICAgICAgICFfdm0uaXNBcHByb3ZhbE1vZGUgJiYKICAgICAgICAgICFfdm0uaXNWaWV3TW9kZSAmJgogICAgICAgICAgIV92bS5pc0VkaXRNb2RlCiAgICAgICAgICAgID8gX2MoImJyaWRnZS1jb25maWciLCB7CiAgICAgICAgICAgICAgICByZWY6ICJicmlkZ2VDb25maWciLAogICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgImluZnJhc3RydWN0dXJlLXR5cGUiOiBfdm0uaW5mcmFzdHJ1Y3R1cmVUeXBlLAogICAgICAgICAgICAgICAgICByZWFkb25seTogX3ZtLmlzUmVhZG9ubHlNb2RlLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uZm9ybURhdGEuYnJpZGdlQ29uZmlnLAogICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24gKCQkdikgewogICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KF92bS5mb3JtRGF0YSwgImJyaWRnZUNvbmZpZyIsICQkdikKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImZvcm1EYXRhLmJyaWRnZUNvbmZpZyIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICBfdm0uY3VycmVudFN0ZXAgPT09IF92bS5nZXRMYXN0U3RlcEluZGV4KCkgJiYKICAgICAgICAgIChfdm0uaXNBcHByb3ZhbE1vZGUgfHwgX3ZtLmlzVmlld01vZGUgfHwgX3ZtLmlzRWRpdE1vZGUpCiAgICAgICAgICAgID8gX2MoImFwcHJvdmFsLWluZm8iLCB7CiAgICAgICAgICAgICAgICByZWY6ICJhcHByb3ZhbEluZm8iLAogICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgInByb2plY3QtaWQiOiBfdm0uY3VycmVudFByb2plY3RJZCwKICAgICAgICAgICAgICAgICAgInNob3ctYXBwcm92YWwtZm9ybSI6IF92bS5jYW5BcHByb3ZlLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICJhcHByb3ZhbC1zdWJtaXR0ZWQiOiBfdm0uaGFuZGxlQXBwcm92YWxTdWJtaXR0ZWQsCiAgICAgICAgICAgICAgICAgIGNhbmNlbDogX3ZtLmhhbmRsZUFwcHJvdmFsQ2FuY2VsLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uZm9ybURhdGEuYXBwcm92YWxJbmZvLAogICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24gKCQkdikgewogICAgICAgICAgICAgICAgICAgIF92bS4kc2V0KF92bS5mb3JtRGF0YSwgImFwcHJvdmFsSW5mbyIsICQkdikKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImZvcm1EYXRhLmFwcHJvdmFsSW5mbyIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJkaXYiLAogICAgICAgIHsKICAgICAgICAgIHN0YXRpY0NsYXNzOiAiZGlhbG9nLWZvb3RlciIsCiAgICAgICAgICBhdHRyczogeyBzbG90OiAiZm9vdGVyIiB9LAogICAgICAgICAgc2xvdDogImZvb3RlciIsCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfdm0uaXNSZWFkb25seU1vZGUgJiYgIV92bS5pc0FwcHJvdmFsTW9kZQogICAgICAgICAgICA/IFsKICAgICAgICAgICAgICAgIF9jKCJlbC1idXR0b24iLCB7IG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlQ2xvc2UgfSB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi5YWz6ZetIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF92bS5pc1ZpZXdNb2RlCiAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdHlwZTogInByaW1hcnkiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlRWRpdCB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIue8lui+kSIpXQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIDogX3ZtLmlzQXBwcm92YWxNb2RlCiAgICAgICAgICAgID8gWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJyZWplY3QtYnRuIiwKICAgICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmhhbmRsZVJlamVjdCB9LAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICBbX3ZtLl92KCLpgIDlm54iKV0KICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImFwcHJvdmUtYnRuIiwKICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSwKICAgICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmhhbmRsZUFwcHJvdmUgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgW192bS5fdigi6YCa6L+HIildCiAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgIF0KICAgICAgICAgICAgOiBfdm0uaXNFZGl0TW9kZSAmJiBfdm0uY3VycmVudFN0ZXAgPT09IF92bS5nZXRMYXN0U3RlcEluZGV4KCkKICAgICAgICAgICAgPyBbCiAgICAgICAgICAgICAgICBfYygiZWwtYnV0dG9uIiwgeyBvbjogeyBjbGljazogX3ZtLmhhbmRsZVNhdmUgfSB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi5L+d5a2YIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiB9LAogICAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uaGFuZGxlU3VibWl0IH0sCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuaPkOS6pCIpXQogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIDogWwogICAgICAgICAgICAgICAgX2MoImVsLWJ1dHRvbiIsIHsgb246IHsgY2xpY2s6IF92bS5oYW5kbGVTYXZlIH0gfSwgWwogICAgICAgICAgICAgICAgICBfdm0uX3YoIuS/neWtmCIpLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfdm0uY3VycmVudFN0ZXAgPiAwCiAgICAgICAgICAgICAgICAgID8gX2MoImVsLWJ1dHRvbiIsIHsgb246IHsgY2xpY2s6IF92bS5oYW5kbGVQcmV2U3RlcCB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiIOS4iuS4gOatpSAiKSwKICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgX3ZtLmN1cnJlbnRTdGVwIDwgX3ZtLmdldExhc3RTdGVwSW5kZXgoKQogICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiB9LAogICAgICAgICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmhhbmRsZU5leHRTdGVwIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgW192bS5fdigiIOS4i+S4gOatpSAiKV0KICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICBfdm0uY3VycmVudFN0ZXAgPT09IF92bS5nZXRMYXN0U3RlcEluZGV4KCkKICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgb246IHsgY2xpY2s6IF92bS5oYW5kbGVTdWJtaXQgfSwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgICAiICIgKyBfdm0uX3MoX3ZtLmlzRWRpdE1vZGUgPyAi5pu05pawIiA6ICLmj5DkuqQiKSArICIgIgogICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICBdLAogICAgICAgIF0sCiAgICAgICAgMgogICAgICApLAogICAgXSwKICAgIDEKICApCn0KdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}