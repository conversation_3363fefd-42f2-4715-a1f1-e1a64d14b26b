{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue?vue&type=style&index=0&id=db291eb6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\emergency\\index.vue", "mtime": 1758810696266}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsKQGltcG9ydCAnQC9hc3NldHMvc3R5bGVzL21haW50ZW5hbmNlLXRoZW1lLnNjc3MnOwoKLnRhYi1uYXZpZ2F0aW9uIHsKICBAZXh0ZW5kIC5jb21tb24tdGFiLW5hdmlnYXRpb247Cn0KCi5zdWItbmF2aWdhdGlvbiB7CiAgQGV4dGVuZCAuY29tbW9uLXNlY29uZGFyeS1uYXZpZ2F0aW9uOwogIAogIC5zdWItdGFiLWNvbnRhaW5lciB7CiAgICBAZXh0ZW5kIC5zdWItdGFiLWNvbnRhaW5lcjsKICB9Cn0KCi5maWx0ZXItZm9ybSB7CiAgcGFkZGluZzogMjRweDsKICBiYWNrZ3JvdW5kOiAjMWUzYThhOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjNGI1NTYzOwp9CgoudGFibGUtY29udGFpbmVyIHsKICBwYWRkaW5nOiAwIDI0cHg7CiAgCiAgLmRpc2Vhc2UtY291bnQgewogICAgY29sb3I6ICMzYjgyZjY7CiAgICBmb250LXdlaWdodDogYm9sZDsKICB9Cn0KCi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgcGFkZGluZzogMTZweCAyNHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsVA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/repairs/emergency", "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题 -->\n        <div class=\"page-header\">\n          <h2>应急维修管理</h2>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'bridge' }\"\n              @click=\"switchTab('bridge')\"\n            >\n              <i class=\"el-icon-s-home\"></i>\n              桥梁养护维修\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tunnel' }\"\n              @click=\"switchTab('tunnel')\"\n            >\n              <i class=\"el-icon-place\"></i>\n              隧道养护维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级导航 -->\n        <div class=\"sub-navigation\">\n          <div class=\"sub-tab-container\">\n            <div \n              class=\"sub-tab-item\"\n              @click=\"$router.push('/maintenance/repairs')\"\n            >\n              养护/保洁项目\n            </div>\n            <div \n              class=\"sub-tab-item is-active\"\n            >\n              应急维修\n            </div>\n          </div>\n        </div>\n        \n        <!-- 筛选表单 -->\n        <div class=\"filter-form\">\n          <el-form :model=\"queryParams\" inline>\n            <el-form-item label=\"桥梁名称\">\n              <el-select\n                v-model=\"queryParams.bridgeName\"\n                placeholder=\"请选择桥梁\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"bridge in bridgeOptions\"\n                  :key=\"bridge.value\"\n                  :label=\"bridge.label\"\n                  :value=\"bridge.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"状态\">\n              <el-select\n                v-model=\"queryParams.status\"\n                placeholder=\"请选择状态\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"status in statusOptions\"\n                  :key=\"status.value\"\n                  :label=\"status.label\"\n                  :value=\"status.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"病害类型\">\n              <el-select\n                v-model=\"queryParams.diseaseType\"\n                placeholder=\"请选择类型\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"type in diseaseTypes\"\n                  :key=\"type.value\"\n                  :label=\"type.label\"\n                  :value=\"type.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"负责人\">\n              <el-select\n                v-model=\"queryParams.manager\"\n                placeholder=\"请选择负责人\"\n                clearable\n                style=\"width: 120px\"\n              >\n                <el-option\n                  v-for=\"manager in managerOptions\"\n                  :key=\"manager.value\"\n                  :label=\"manager.label\"\n                  :value=\"manager.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item label=\"养护单位\">\n              <el-select\n                v-model=\"queryParams.maintenanceUnit\"\n                placeholder=\"请选择单位\"\n                clearable\n                style=\"width: 150px\"\n              >\n                <el-option\n                  v-for=\"unit in unitOptions\"\n                  :key=\"unit.value\"\n                  :label=\"unit.label\"\n                  :value=\"unit.value\"\n                />\n              </el-select>\n            </el-form-item>\n            \n            <el-form-item>\n              <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n              <el-button @click=\"resetQuery\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        \n        <!-- 数据表格 -->\n        <div class=\"table-container\">\n          <el-table\n            v-loading=\"loading\"\n            :data=\"emergencyList\"\n            class=\"maintenance-table\"\n          >\n            <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\" />\n            \n            <el-table-column prop=\"bridgeName\" label=\"桥梁名称\" min-width=\"120\" show-overflow-tooltip />\n            \n            <el-table-column prop=\"reporter\" label=\"上报人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"reportTime\" label=\"上报时间\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"contactPhone\" label=\"联系方式\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <status-tag :status=\"scope.row.status\" type=\"task\" />\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"diseaseCode\" label=\"病害编号\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseasePart\" label=\"病害部位\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseType\" label=\"病害类型\" width=\"120\" align=\"center\" />\n            \n            <el-table-column prop=\"diseaseCount\" label=\"病害数量\" width=\"100\" align=\"center\">\n              <template slot-scope=\"scope\">\n                <span class=\"disease-count\">{{ scope.row.diseaseCount }}</span>\n              </template>\n            </el-table-column>\n            \n            <el-table-column prop=\"manager\" label=\"负责人\" width=\"100\" align=\"center\" />\n            \n            <el-table-column prop=\"maintenanceUnit\" label=\"养护单位\" min-width=\"150\" show-overflow-tooltip />\n            \n            <el-table-column label=\"操作\" width=\"80\" align=\"center\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"handleView(scope.row)\"\n                >\n                  查看\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        \n        <!-- 分页 -->\n        <div class=\"pagination-container\">\n          <el-pagination\n            :current-page=\"queryParams.pageNum\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"queryParams.pageSize\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n      </div>\n    </div>\n    \n    <!-- 应急维修详情弹窗 -->\n    <emergency-detail-dialog\n      :visible.sync=\"showDetailDialog\"\n      :emergency-data=\"selectedEmergency\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getEmergencyRepairList } from '@/api/maintenance/repairs'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport EmergencyDetailDialog from './components/EmergencyDetailDialog'\n\nexport default {\n  name: 'MaintenanceEmergencyRepairs',\n  components: {\n    StatusTag,\n    EmergencyDetailDialog\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'bridge', // bridge, tunnel\n      emergencyList: [],\n      selectedEmergency: {},\n      showDetailDialog: false,\n      total: 0,\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: 'bridge'\n      },\n      \n      // 选项数据\n      bridgeOptions: [],\n      managerOptions: [],\n      unitOptions: [],\n      \n      // 状态选项\n      statusOptions: [\n        { label: '待处理', value: 'pending' },\n        { label: '处理审批中', value: 'in_review' },\n        { label: '退回', value: 'rejected' },\n        { label: '已处理', value: 'completed' }\n      ],\n      \n      // 病害类型选项\n      diseaseTypes: [\n        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },\n        { label: '照明设施缺失', value: 'lighting_missing' },\n        { label: '护栏损坏', value: 'guardrail_damage' },\n        { label: '桥面破损', value: 'deck_damage' },\n        { label: '排水不畅', value: 'drainage_poor' }\n      ]\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 获取应急维修列表\n    async getList() {\n      this.loading = true\n      try {\n        this.queryParams.infrastructureType = this.activeTab\n        \n        const response = await getEmergencyRepairList(this.queryParams)\n        this.emergencyList = response.data.list || []\n        this.total = response.data.total || 0\n        this.bridgeOptions = response.data.bridgeOptions || []\n        this.managerOptions = response.data.managerOptions || []\n        this.unitOptions = response.data.unitOptions || []\n      } catch (error) {\n        this.$message.error('获取应急维修列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 20,\n        bridgeName: '',\n        status: '',\n        diseaseType: '',\n        manager: '',\n        maintenanceUnit: '',\n        infrastructureType: this.activeTab\n      }\n      this.getList()\n    },\n    \n    // 查看详情\n    handleView(row) {\n      this.selectedEmergency = row\n      this.showDetailDialog = true\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.getList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.getList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.sub-navigation {\n  @extend .common-secondary-navigation;\n  \n  .sub-tab-container {\n    @extend .sub-tab-container;\n  }\n}\n\n.filter-form {\n  padding: 24px;\n  background: #1e3a8a;\n  border-bottom: 1px solid #4b5563;\n}\n\n.table-container {\n  padding: 0 24px;\n  \n  .disease-count {\n    color: #3b82f6;\n    font-weight: bold;\n  }\n}\n\n.pagination-container {\n  padding: 16px 24px;\n  text-align: center;\n}\n</style>\n"]}]}