{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue?vue&type=style&index=0&id=745c7ae6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsNCg0KLmJhc2ljLWluZm8tdmlldyB7DQogIEBleHRlbmQgLnJlYWRvbmx5LWZvcm07DQogIA0KICAuYXR0YWNobWVudC1saXN0IHsNCiAgICBAZXh0ZW5kIC5jb21tb24tYXR0YWNobWVudC1saXN0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["BasicInfoView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyLA;;AAEA;AACA;;AAEA;AACA;AACA;AACA", "file": "BasicInfoView.vue", "sourceRoot": "src/views/maintenance/repairs/components/detail", "sourcesContent": ["<template>\r\n  <div class=\"basic-info-view\">\r\n    <el-form\r\n      :model=\"repairData\"\r\n      label-width=\"120px\"\r\n      class=\"maintenance-form\"\r\n    >\r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目名称\">\r\n            <el-input\r\n              :value=\"repairData.projectName\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目类型\">\r\n            <el-input\r\n              :value=\"getProjectTypeLabel(repairData.projectType)\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目开始时间\">\r\n            <el-input\r\n              :value=\"repairData.startDate\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目结束时间\">\r\n            <el-input\r\n              :value=\"repairData.endDate\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"管理单位\">\r\n            <el-input\r\n              :value=\"repairData.managementUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"监理单位\">\r\n            <el-input\r\n              :value=\"repairData.supervisionUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"养护单位\">\r\n            <el-input\r\n              :value=\"repairData.maintenanceUnit\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"项目负责人\">\r\n            <el-input\r\n              :value=\"repairData.manager\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"联系方式\">\r\n            <el-input\r\n              :value=\"repairData.contactPhone\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\" v-if=\"showWorkload\">\r\n          <el-form-item label=\"工作量\">\r\n            <el-input\r\n              :value=\"repairData.workload\"\r\n              readonly\r\n              class=\"readonly-input\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"项目内容\">\r\n            <el-input\r\n              :value=\"repairData.projectContent\"\r\n              type=\"textarea\"\r\n              :rows=\"4\"\r\n              readonly\r\n              class=\"readonly-textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      \r\n      <el-row :gutter=\"24\">\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"附件\">\r\n            <div class=\"attachment-list\">\r\n              <div\r\n                v-for=\"(file, index) in repairData.attachments\"\r\n                :key=\"index\"\r\n                class=\"attachment-item\"\r\n              >\r\n                <i class=\"el-icon-document\"></i>\r\n                <span class=\"file-name\">{{ file.name }}</span>\r\n                <span class=\"file-size\">({{ file.size }})</span>\r\n              </div>\r\n              <div v-if=\"!repairData.attachments || repairData.attachments.length === 0\" class=\"no-attachments\">\r\n                暂无附件\r\n              </div>\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoView',\r\n  props: {\r\n    repairData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  computed: {\r\n    // 是否显示工作量字段 - 与养护项目保持一致\r\n    showWorkload() {\r\n      return ['cleaning', 'emergency', 'preventive'].includes(this.repairData.projectType)\r\n    }\r\n  },\r\n  methods: {\r\n    getProjectTypeLabel(type) {\r\n      const typeMap = {\r\n        'monthly': '月度养护',\r\n        'preventive': '预防养护',\r\n        'emergency': '应急养护',\r\n        'cleaning': '保洁项目'\r\n      }\r\n      return typeMap[type] || type\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/styles/inspection-theme.scss';\r\n\r\n.basic-info-view {\r\n  @extend .readonly-form;\r\n  \r\n  .attachment-list {\r\n    @extend .common-attachment-list;\r\n  }\r\n}\r\n</style>\r\n"]}]}