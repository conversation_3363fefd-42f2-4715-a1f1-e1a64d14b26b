{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue?vue&type=style&index=0&id=5c65999f&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\TrendChart.vue", "mtime": 1758804563534}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TrendChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TrendChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"trendChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'Trend<PERSON><PERSON>',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    chartType: {\r\n      type: String,\r\n      default: 'line' // line | bar\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initChart()\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    },\r\n    chartType() {\r\n      this.updateChart()\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      if (this.$refs.trendChart) {\r\n        this.chartInstance = echarts.init(this.$refs.trendChart)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['市府区', '美湖区', '龙泉区', '开福区', '芙蓉区'],\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '12%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            boundaryGap: this.chartType === 'bar',\r\n            data: data.categories,\r\n            axisLabel: {\r\n              color: '#ffffff'\r\n            },\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '次数',\r\n            max: 900,\r\n            interval: 100,\r\n            nameTextStyle: {\r\n              color: '#ffffff'\r\n            },\r\n            axisLabel: {\r\n              formatter: '{value}',\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '市府区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.shifuqu,\r\n            itemStyle: {\r\n              color: '#40E0D0'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '美湖区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.meihuqu,\r\n            itemStyle: {\r\n              color: '#FFD700'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '龙泉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.longquanqu,\r\n            itemStyle: {\r\n              color: '#FFFFFF'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '开福区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.kaifuqu,\r\n            itemStyle: {\r\n              color: '#FF6B6B'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          },\r\n          {\r\n            name: '芙蓉区',\r\n            type: 'line',\r\n            smooth: true,\r\n            data: data.districtData.furongqu,\r\n            itemStyle: {\r\n              color: '#FF8C00'\r\n            },\r\n            lineStyle: {\r\n              width: 2\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      // 如果有传入数据，处理为区域数据格式\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      // 如果chartData有区域数据，使用传入的数据\r\n      const districtData = {\r\n        shifuqu: this.chartData.shifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: this.chartData.meihuqu || [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: this.chartData.longquanqu || [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: this.chartData.kaifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: this.chartData.furongqu || [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]\r\n      \r\n      const districtData = {\r\n        shifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        meihuqu: [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],\r\n        longquanqu: [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],\r\n        kaifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],\r\n        furongqu: [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]\r\n      }\r\n      \r\n      return {\r\n        categories,\r\n        districtData\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与DamageTypeChart保持一致的最小高度\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"trendChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 280px; // 🔧 与DamageTypeChart内部图表保持一致的最小高度\r\n}\r\n</style>\r\n"]}]}