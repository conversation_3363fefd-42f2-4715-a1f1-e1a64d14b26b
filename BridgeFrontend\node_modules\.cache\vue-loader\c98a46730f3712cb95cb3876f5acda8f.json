{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue?vue&type=style&index=0&id=36cb142e&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\Top10RankingChart.vue", "mtime": 1758804563532}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Top10RankingChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Top10RankingChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"top10-ranking-chart\" v-loading=\"loading\">\r\n    <div class=\"ranking-list\">\r\n      <div\r\n        v-for=\"(item, index) in displayData\"\r\n        :key=\"index\"\r\n        class=\"ranking-item\"\r\n        :class=\"{ 'top-three': index < 3 }\"\r\n      >\r\n        <div class=\"rank-badge\" :class=\"`rank-${index + 1}`\">\r\n          TOP{{ index + 1 }}\r\n        </div>\r\n        <div class=\"bridge-info\">\r\n          <span class=\"bridge-name\">{{ item.bridgeName || item.name }}</span>\r\n          <div class=\"progress-container\">\r\n            <div \r\n              class=\"progress-bar\"\r\n              :style=\"{ width: `${getProgressWidth(item.count || item.value)}%` }\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n        <span class=\"count\">{{ item.count || item.value }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Top10RankingChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    loading: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    }\r\n  },\r\n  computed: {\r\n    displayData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return [\r\n          { bridgeName: 'XXXX病害', count: 932 },\r\n          { bridgeName: 'XXXX病害', count: 899 },\r\n          { bridgeName: 'XXXX病害', count: 702 },\r\n          { bridgeName: 'XXXX病害', count: 543 },\r\n          { bridgeName: 'XXXX病害', count: 208 }\r\n        ]\r\n      }\r\n      return this.chartData.slice(0, 5)\r\n    },\r\n    maxCount() {\r\n      if (this.displayData.length === 0) return 1\r\n      return Math.max(...this.displayData.map(item => item.count || item.value || 0))\r\n    }\r\n  },\r\n  methods: {\r\n    getProgressWidth(count) {\r\n      if (this.maxCount === 0) return 0\r\n      return Math.max((count / this.maxCount) * 100, 5) // 最小宽度5%\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.top10-ranking-chart {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 320px !important; // 🔧 与右侧容器设置保持一致\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n  \r\n  .ranking-list {\r\n    position: relative;\r\n    z-index: 3; // 确保内容在伪元素之上\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    width: 100%;\r\n    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间\r\n    justify-content: center;\r\n    \r\n    .ranking-item {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 6px 0;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover {\r\n        background-color: rgba(64, 158, 255, 0.05);\r\n        border-radius: 4px;\r\n        transform: translateX(2px);\r\n      }\r\n      \r\n      &.top-three {\r\n        .rank-badge {\r\n          background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);\r\n          color: #fff;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      \r\n      .rank-badge {\r\n        width: 50px;\r\n        height: 20px;\r\n        border-radius: 10px;\r\n        background: #40E0D0;\r\n        color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 10px;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        \r\n        &.rank-1 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-2 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-3 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-4 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.rank-5 {\r\n          background: #40E0D0;\r\n          color: #fff;\r\n        }\r\n      }\r\n      \r\n      .bridge-info {\r\n        flex: 1;\r\n        min-width: 0;\r\n        \r\n        .bridge-name {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #ffffff;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          margin-bottom: 2px;\r\n        }\r\n        \r\n        .progress-container {\r\n          width: 100%;\r\n          height: 6px;\r\n          background-color: #f5f7fa;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n          \r\n          .progress-bar {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);\r\n            border-radius: 3px;\r\n            transition: width 0.6s ease;\r\n            animation: progressAnimation 1s ease-out;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .count {\r\n        font-size: 11px;\r\n        color: #ffffff;\r\n        font-weight: 600;\r\n        flex-shrink: 0;\r\n        min-width: 20px;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes progressAnimation {\r\n  from {\r\n    width: 0%;\r\n  }\r\n  to {\r\n    width: var(--target-width);\r\n  }\r\n}\r\n\r\n// 响应式优化\r\n@media (max-width: 768px) {\r\n  .top10-ranking-chart {\r\n    .ranking-list {\r\n      .ranking-item {\r\n        .bridge-info {\r\n          .bridge-name {\r\n            font-size: 11px;\r\n          }\r\n        }\r\n        \r\n        .count {\r\n          font-size: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}