{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue?vue&type=template&id=fd5921f4&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\ApprovalInfo.vue", "mtime": 1758809365043}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}