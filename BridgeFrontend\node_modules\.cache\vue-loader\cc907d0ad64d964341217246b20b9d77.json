{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\data-center\\correlation-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\data-center\\correlation-analysis\\index.vue", "mtime": 1758804563523}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdDb3JyZWxhdGlvbkFuYWx5c2lzJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBzaG93UmVzdWx0OiBmYWxzZSwNCiAgICAgIA0KICAgICAgLy8g55uR5rWL54K5MemFjee9rg0KICAgICAgbW9uaXRvclBvaW50MTogew0KICAgICAgICB0eXBlOiAnc3RyZXNzJywgLy8g6buY6K6k6YCJ5oup5bqU5Yqb5bqU5Y+YDQogICAgICAgIHBvaW50OiAnQlBKRFEtUlNHLUcwMy0wMS0wMScgLy8g6buY6K6k6YCJ5oup56ys5LiA5Liq54K5DQogICAgICB9LA0KICAgICAgDQogICAgICAvLyDnm5HmtYvngrky6YWN572uDQogICAgICBtb25pdG9yUG9pbnQyOiB7DQogICAgICAgIHR5cGU6ICdzdHJlc3MnLCAvLyDpu5jorqTpgInmi6nlupTlipvlupTlj5gNCiAgICAgICAgcG9pbnQ6ICdCUEpEUS1SU0ctRzAzLTAxLTAyJyAvLyDpu5jorqTpgInmi6nnrKzkuozkuKrngrkNCiAgICAgIH0sDQogICAgICANCiAgICAgIC8vIOmAieS4reeahOahpeaigQ0KICAgICAgc2VsZWN0ZWRCcmlkZ2U6ICcnLA0KICAgICAgDQogICAgICAvLyDmoaXmooHpgInpobkNCiAgICAgIGJyaWRnZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+eZveWdoeWkp+ahpScsIHZhbHVlOiAnYmFpcG8nIH0sDQogICAgICAgIHsgbGFiZWw6ICfplb/msZ/lpKfmoaUnLCB2YWx1ZTogJ2NoYW5namlhbmcnIH0sDQogICAgICAgIHsgbGFiZWw6ICfpu4TmsrPlpKfmoaUnLCB2YWx1ZTogJ2h1YW5naGUnIH0sDQogICAgICAgIHsgbGFiZWw6ICfnj6DmsZ/lpKfmoaUnLCB2YWx1ZTogJ3podWppYW5nJyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDml6XmnJ/ojIPlm7QNCiAgICAgIGRhdGVSYW5nZTogew0KICAgICAgICBzdGFydDogJycsDQogICAgICAgIGVuZDogJycNCiAgICAgIH0sDQogICAgICANCiAgICAgIC8vIOebkea1i+exu+Wei+mAiemhuQ0KICAgICAgbW9uaXRvclR5cGVzOiBbDQogICAgICAgIHsgbGFiZWw6ICfkuLvmooHlj5jlkJHkvY3np7snLCB2YWx1ZTogJ2Rpc3BsYWNlbWVudCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+W6lOWKm+W6lOWPmCcsIHZhbHVlOiAnc3RyZXNzJyB9LA0KICAgICAgICB7IGxhYmVsOiAn5qKB56uv5L2N56e7JywgdmFsdWU6ICdiZWFtLWRpc3BsYWNlbWVudCcgfQ0KICAgICAgXSwNCiAgICAgIA0KICAgICAgLy8g55uR5rWL54K55L2N6YCJ6aG5ICjmqKHmi5/mlbDmja4pDQogICAgICBtb25pdG9yUG9pbnRzOiBbDQogICAgICAgIHsgbGFiZWw6ICdCUEpEUS1SU0ctRzAzLTAxLTAxJywgdmFsdWU6ICdCUEpEUS1SU0ctRzAzLTAxLTAxJyB9LA0KICAgICAgICB7IGxhYmVsOiAnQlBKRFEtUlNHLUcwMy0wMS0wMicsIHZhbHVlOiAnQlBKRFEtUlNHLUcwMy0wMS0wMicgfSwNCiAgICAgICAgeyBsYWJlbDogJ0JQSkRRLVJTRy1HMDMtMDEtMDMnLCB2YWx1ZTogJ0JQSkRRLVJTRy1HMDMtMDEtMDMnIH0sDQogICAgICAgIHsgbGFiZWw6ICdCUEpEUS1SU0ctRzAzLTAxLTA0JywgdmFsdWU6ICdCUEpEUS1SU0ctRzAzLTAxLTA0JyB9LA0KICAgICAgICB7IGxhYmVsOiAnQlBKRFEtUlNHLUcwMy0wMS0wNScsIHZhbHVlOiAnQlBKRFEtUlNHLUcwMy0wMS0wNScgfSwNCiAgICAgICAgeyBsYWJlbDogJ0JQSkRRLVJTRy1HMDMtMDItMDEnLCB2YWx1ZTogJ0JQSkRRLVJTRy1HMDMtMDItMDEnIH0sDQogICAgICAgIHsgbGFiZWw6ICdCUEpEUS1SU0ctRzAzLTAyLTAyJywgdmFsdWU6ICdCUEpEUS1SU0ctRzAzLTAyLTAyJyB9LA0KICAgICAgICB7IGxhYmVsOiAnQlBKRFEtUlNHLUcwMy0wMi0wMycsIHZhbHVlOiAnQlBKRFEtUlNHLUcwMy0wMi0wMycgfSwNCiAgICAgICAgeyBsYWJlbDogJ0JQSkRRLVJTRy1HMDMtMDItMDQnLCB2YWx1ZTogJ0JQSkRRLVJTRy1HMDMtMDItMDQnIH0sDQogICAgICAgIHsgbGFiZWw6ICdCUEpEUS1SU0ctRzAzLTAyLTA1JywgdmFsdWU6ICdCUEpEUS1SU0ctRzAzLTAyLTA1JyB9DQogICAgICBdLA0KICAgICAgDQogICAgICAvLyDlsZXlvIDnirbmgIHmjqfliLYNCiAgICAgIGV4cGFuZGVkU2VjdGlvbnM6IHsNCiAgICAgICAgcG9pbnQxOiBmYWxzZSwNCiAgICAgICAgcG9pbnQyOiBmYWxzZQ0KICAgICAgfSwNCiAgICAgIA0KICAgICAgLy8g5Y+v6KeB55uR5rWL54K55pWw6YePDQogICAgICB2aXNpYmxlUG9pbnRzQ291bnQ6IDUNCiAgICB9DQogIH0sDQogIA0KICBtZXRob2RzOiB7DQogICAgLy8g55uR5rWL57G75Z6L6YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlTW9uaXRvclR5cGVDaGFuZ2UocG9pbnRJbmRleCwgdHlwZVZhbHVlKSB7DQogICAgICBpZiAocG9pbnRJbmRleCA9PT0gMSkgew0KICAgICAgICB0aGlzLm1vbml0b3JQb2ludDEudHlwZSA9IHR5cGVWYWx1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tb25pdG9yUG9pbnQyLnR5cGUgPSB0eXBlVmFsdWUNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOebkea1i+eCueS9jemAieaLqeWPmOWMlg0KICAgIGhhbmRsZU1vbml0b3JQb2ludENoYW5nZShwb2ludEluZGV4LCBwb2ludFZhbHVlKSB7DQogICAgICBpZiAocG9pbnRJbmRleCA9PT0gMSkgew0KICAgICAgICB0aGlzLm1vbml0b3JQb2ludDEucG9pbnQgPSBwb2ludFZhbHVlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1vbml0b3JQb2ludDIucG9pbnQgPSBwb2ludFZhbHVlDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDmoaXmooHpgInmi6nlj5jljJYNCiAgICBoYW5kbGVCcmlkZ2VDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfpgInmi6nmoaXmooE6JywgdmFsdWUpDQogICAgICAvLyDov5nph4zlj6/ku6XmoLnmja7moaXmooHpgInmi6nmm7TmlrDnm5HmtYvngrnkvY3mlbDmja4NCiAgICB9LA0KICAgIA0KICAgIC8vIOWIh+aNouWxleW8gOeKtuaAgQ0KICAgIHRvZ2dsZUV4cGFuZChwb2ludEluZGV4KSB7DQogICAgICBpZiAocG9pbnRJbmRleCA9PT0gMSkgew0KICAgICAgICB0aGlzLmV4cGFuZGVkU2VjdGlvbnMucG9pbnQxID0gIXRoaXMuZXhwYW5kZWRTZWN0aW9ucy5wb2ludDENCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZXhwYW5kZWRTZWN0aW9ucy5wb2ludDIgPSAhdGhpcy5leHBhbmRlZFNlY3Rpb25zLnBvaW50Mg0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W5Y+v6KeB55qE55uR5rWL54K55L2NDQogICAgZ2V0VmlzaWJsZVBvaW50cyhwb2ludEluZGV4KSB7DQogICAgICBjb25zdCBpc0V4cGFuZGVkID0gcG9pbnRJbmRleCA9PT0gMSA/IHRoaXMuZXhwYW5kZWRTZWN0aW9ucy5wb2ludDEgOiB0aGlzLmV4cGFuZGVkU2VjdGlvbnMucG9pbnQyDQogICAgICBpZiAoaXNFeHBhbmRlZCkgew0KICAgICAgICByZXR1cm4gdGhpcy5tb25pdG9yUG9pbnRzDQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy5tb25pdG9yUG9pbnRzLnNsaWNlKDAsIHRoaXMudmlzaWJsZVBvaW50c0NvdW50KQ0KICAgIH0sDQogICAgDQogICAgLy8g5p+l6K+i5pON5L2cDQogICAgYXN5bmMgaGFuZGxlUXVlcnkoKSB7DQogICAgICAvLyDpqozor4Hlv4XloavpobkNCiAgICAgIGlmICghdGhpcy5tb25pdG9yUG9pbnQxLnR5cGUgfHwgIXRoaXMubW9uaXRvclBvaW50MS5wb2ludCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeebkea1i+eCueWuuTHnmoTnsbvlnovlkozngrnkvY0nKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgaWYgKCF0aGlzLm1vbml0b3JQb2ludDIudHlwZSB8fCAhdGhpcy5tb25pdG9yUG9pbnQyLnBvaW50KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup55uR5rWL54K55a65MueahOexu+Wei+WSjOeCueS9jScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgDQogICAgICBpZiAoIXRoaXMuZGF0ZVJhbmdlLnN0YXJ0IHx8ICF0aGlzLmRhdGVSYW5nZS5lbmQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nmn6Xor6Lml6XmnJ/ojIPlm7QnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgDQogICAgICB0cnkgew0KICAgICAgICAvLyDmqKHmi59BUEnosIPnlKgNCiAgICAgICAgYXdhaXQgdGhpcy5mZXRjaENvcnJlbGF0aW9uRGF0YSgpDQogICAgICAgIHRoaXMuc2hvd1Jlc3VsdCA9IHRydWUNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmn6Xor6LmiJDlip8nKQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l6K+i5aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDph43nva7mk43kvZwNCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIHRoaXMubW9uaXRvclBvaW50MSA9IHsNCiAgICAgICAgdHlwZTogJ3N0cmVzcycsDQogICAgICAgIHBvaW50OiAnQlBKRFEtUlNHLUcwMy0wMS0wMScNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5tb25pdG9yUG9pbnQyID0gew0KICAgICAgICB0eXBlOiAnc3RyZXNzJywgDQogICAgICAgIHBvaW50OiAnQlBKRFEtUlNHLUcwMy0wMS0wMicNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5zZWxlY3RlZEJyaWRnZSA9ICcnDQogICAgICANCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gew0KICAgICAgICBzdGFydDogJycsDQogICAgICAgIGVuZDogJycNCiAgICAgIH0NCiAgICAgIA0KICAgICAgdGhpcy5leHBhbmRlZFNlY3Rpb25zID0gew0KICAgICAgICBwb2ludDE6IGZhbHNlLA0KICAgICAgICBwb2ludDI6IGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHRoaXMuc2hvd1Jlc3VsdCA9IGZhbHNlDQogICAgfSwNCiAgICANCiAgICAvLyDnp7vpmaTlt7LpgInmnaHku7YNCiAgICByZW1vdmVDb25kaXRpb24oY29uZGl0aW9uVHlwZSkgew0KICAgICAgc3dpdGNoIChjb25kaXRpb25UeXBlKSB7DQogICAgICAgIGNhc2UgJ3BvaW50MS10eXBlJzoNCiAgICAgICAgICB0aGlzLm1vbml0b3JQb2ludDEudHlwZSA9ICcnDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgY2FzZSAncG9pbnQxLXBvaW50JzoNCiAgICAgICAgICB0aGlzLm1vbml0b3JQb2ludDEucG9pbnQgPSAnJw0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGNhc2UgJ3BvaW50Mi10eXBlJzoNCiAgICAgICAgICB0aGlzLm1vbml0b3JQb2ludDIudHlwZSA9ICcnDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgY2FzZSAncG9pbnQyLXBvaW50JzoNCiAgICAgICAgICB0aGlzLm1vbml0b3JQb2ludDIucG9pbnQgPSAnJw0KICAgICAgICAgIGJyZWFrDQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDojrflj5bnm5HmtYvnsbvlnovlkI3np7ANCiAgICBnZXRNb25pdG9yVHlwZU5hbWUodmFsdWUpIHsNCiAgICAgIGNvbnN0IHR5cGUgPSB0aGlzLm1vbml0b3JUeXBlcy5maW5kKHQgPT4gdC52YWx1ZSA9PT0gdmFsdWUpDQogICAgICByZXR1cm4gdHlwZSA/IHR5cGUubGFiZWwgOiB2YWx1ZQ0KICAgIH0sDQogICAgDQogICAgLy8g6I635Y+W55uR5rWL54K55L2N5ZCN56ewICANCiAgICBnZXRNb25pdG9yUG9pbnROYW1lKHZhbHVlKSB7DQogICAgICBjb25zdCBwb2ludCA9IHRoaXMubW9uaXRvclBvaW50cy5maW5kKHAgPT4gcC52YWx1ZSA9PT0gdmFsdWUpDQogICAgICByZXR1cm4gcG9pbnQgPyBwb2ludC5sYWJlbCA6IHZhbHVlDQogICAgfSwNCiAgICANCiAgICAvLyDmqKHmi5/ojrflj5blhbPogZTmgKfliIbmnpDmlbDmja4NCiAgICBhc3luYyBmZXRjaENvcnJlbGF0aW9uRGF0YSgpIHsNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICByZXNvbHZlKHsNCiAgICAgICAgICAgIGNvcnJlbGF0aW9uOiAwLjg1LA0KICAgICAgICAgICAgdHJlbmQ6ICdwb3NpdGl2ZScNCiAgICAgICAgICB9KQ0KICAgICAgICB9LCAxNTAwKQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/data-center/correlation-analysis", "sourcesContent": ["<template>\r\n  <div class=\"correlation-analysis inspection-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 桥梁选择 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"bridge-selector\">\r\n          <span class=\"bridge-label\">选择桥梁:</span>\r\n          <el-select\r\n            v-model=\"selectedBridge\"\r\n            placeholder=\"请选择桥梁\"\r\n            size=\"small\"\r\n            class=\"bridge-dropdown\"\r\n            @change=\"handleBridgeChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"bridge in bridgeOptions\"\r\n              :key=\"bridge.value\"\r\n              :label=\"bridge.label\"\r\n              :value=\"bridge.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 监测内容1 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"section-title\">监测内容1:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测类型选项卡 -->\r\n          <div class=\"monitor-type-tabs\">\r\n            <el-button \r\n              v-for=\"type in monitorTypes\"\r\n              :key=\"type.value\"\r\n              :type=\"monitorPoint1.type === type.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorTypeChange(1, type.value)\"\r\n              class=\"monitor-tab-btn\"\r\n            >\r\n              {{ type.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"section-title\">监测点位1:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测点位选择器 -->\r\n          <div class=\"monitor-point-selector\">\r\n            <el-button \r\n              v-for=\"(point, index) in getVisiblePoints(1)\"\r\n              :key=\"point.value\"\r\n              :type=\"monitorPoint1.point === point.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorPointChange(1, point.value)\"\r\n              class=\"point-selector-btn\"\r\n            >\r\n              {{ point.label }}\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"!expandedSections.point1 && monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"text\" \r\n              class=\"more-btn\"\r\n            >\r\n              ...\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"primary\" \r\n              class=\"expand-btn\"\r\n              @click=\"toggleExpand(1)\"\r\n            >\r\n              {{ expandedSections.point1 ? '收起' : '展开' }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 监测内容2 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"section-title\">监测内容2:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测类型选项卡 -->\r\n          <div class=\"monitor-type-tabs\">\r\n            <el-button \r\n              v-for=\"type in monitorTypes\"\r\n              :key=\"type.value\"\r\n              :type=\"monitorPoint2.type === type.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorTypeChange(2, type.value)\"\r\n              class=\"monitor-tab-btn\"\r\n            >\r\n              {{ type.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"section-title\">监测点位2:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测点位选择器 -->\r\n          <div class=\"monitor-point-selector\">\r\n            <el-button \r\n              v-for=\"(point, index) in getVisiblePoints(2)\"\r\n              :key=\"point.value\"\r\n              :type=\"monitorPoint2.point === point.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorPointChange(2, point.value)\"\r\n              class=\"point-selector-btn\"\r\n            >\r\n              {{ point.label }}\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"!expandedSections.point2 && monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"text\" \r\n              class=\"more-btn\"\r\n            >\r\n              ...\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"primary\" \r\n              class=\"expand-btn\"\r\n              @click=\"toggleExpand(2)\"\r\n            >\r\n              {{ expandedSections.point2 ? '收起' : '展开' }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 日期选择和操作按钮 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"date-inputs\">\r\n          <div class=\"date-input-group\">\r\n            <span class=\"date-label\">选择日期:</span>\r\n            <el-date-picker\r\n              v-model=\"dateRange.start\"\r\n              type=\"date\"\r\n              placeholder=\"开始日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              size=\"small\"\r\n              class=\"date-picker\"\r\n            />\r\n            <span class=\"date-separator\">-</span>\r\n            <el-date-picker\r\n              v-model=\"dateRange.end\"\r\n              type=\"date\"\r\n              placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              size=\"small\"\r\n              class=\"date-picker\"\r\n            />\r\n          </div>\r\n          <div class=\"action-buttons\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleQuery\" :loading=\"loading\">\r\n              查询\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"handleReset\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 已选条件 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"condition-title\">已选条件:</div>\r\n        <div class=\"condition-tags\">\r\n          <el-tag \r\n            v-if=\"monitorPoint1.type\"\r\n            size=\"small\" \r\n            closable\r\n            @close=\"removeCondition('point1-type')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorTypeName(monitorPoint1.type) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint1.point\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point1-point')\" \r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorPointName(monitorPoint1.point) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint2.type\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point2-type')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorTypeName(monitorPoint2.type) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint2.point\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point2-point')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorPointName(monitorPoint2.point) }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分析结果展示区域 -->\r\n      <div class=\"analysis-result\" v-if=\"showResult\">\r\n        <div class=\"result-header\">\r\n          <h3>关联性分析结果</h3>\r\n        </div>\r\n        <div class=\"result-content\">\r\n          <p>分析结果将在这里显示...</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CorrelationAnalysis',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      showResult: false,\r\n      \r\n      // 监测点1配置\r\n      monitorPoint1: {\r\n        type: 'stress', // 默认选择应力应变\r\n        point: 'BPJDQ-RSG-G03-01-01' // 默认选择第一个点\r\n      },\r\n      \r\n      // 监测点2配置\r\n      monitorPoint2: {\r\n        type: 'stress', // 默认选择应力应变\r\n        point: 'BPJDQ-RSG-G03-01-02' // 默认选择第二个点\r\n      },\r\n      \r\n      // 选中的桥梁\r\n      selectedBridge: '',\r\n      \r\n      // 桥梁选项\r\n      bridgeOptions: [\r\n        { label: '白坡大桥', value: 'baipo' },\r\n        { label: '长江大桥', value: 'changjiang' },\r\n        { label: '黄河大桥', value: 'huanghe' },\r\n        { label: '珠江大桥', value: 'zhujiang' }\r\n      ],\r\n      \r\n      // 日期范围\r\n      dateRange: {\r\n        start: '',\r\n        end: ''\r\n      },\r\n      \r\n      // 监测类型选项\r\n      monitorTypes: [\r\n        { label: '主梁变向位移', value: 'displacement' },\r\n        { label: '应力应变', value: 'stress' },\r\n        { label: '梁端位移', value: 'beam-displacement' }\r\n      ],\r\n      \r\n      // 监测点位选项 (模拟数据)\r\n      monitorPoints: [\r\n        { label: 'BPJDQ-RSG-G03-01-01', value: 'BPJDQ-RSG-G03-01-01' },\r\n        { label: 'BPJDQ-RSG-G03-01-02', value: 'BPJDQ-RSG-G03-01-02' },\r\n        { label: 'BPJDQ-RSG-G03-01-03', value: 'BPJDQ-RSG-G03-01-03' },\r\n        { label: 'BPJDQ-RSG-G03-01-04', value: 'BPJDQ-RSG-G03-01-04' },\r\n        { label: 'BPJDQ-RSG-G03-01-05', value: 'BPJDQ-RSG-G03-01-05' },\r\n        { label: 'BPJDQ-RSG-G03-02-01', value: 'BPJDQ-RSG-G03-02-01' },\r\n        { label: 'BPJDQ-RSG-G03-02-02', value: 'BPJDQ-RSG-G03-02-02' },\r\n        { label: 'BPJDQ-RSG-G03-02-03', value: 'BPJDQ-RSG-G03-02-03' },\r\n        { label: 'BPJDQ-RSG-G03-02-04', value: 'BPJDQ-RSG-G03-02-04' },\r\n        { label: 'BPJDQ-RSG-G03-02-05', value: 'BPJDQ-RSG-G03-02-05' }\r\n      ],\r\n      \r\n      // 展开状态控制\r\n      expandedSections: {\r\n        point1: false,\r\n        point2: false\r\n      },\r\n      \r\n      // 可见监测点数量\r\n      visiblePointsCount: 5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 监测类型选择变化\r\n    handleMonitorTypeChange(pointIndex, typeValue) {\r\n      if (pointIndex === 1) {\r\n        this.monitorPoint1.type = typeValue\r\n      } else {\r\n        this.monitorPoint2.type = typeValue\r\n      }\r\n    },\r\n    \r\n    // 监测点位选择变化\r\n    handleMonitorPointChange(pointIndex, pointValue) {\r\n      if (pointIndex === 1) {\r\n        this.monitorPoint1.point = pointValue\r\n      } else {\r\n        this.monitorPoint2.point = pointValue\r\n      }\r\n    },\r\n    \r\n    // 桥梁选择变化\r\n    handleBridgeChange(value) {\r\n      console.log('选择桥梁:', value)\r\n      // 这里可以根据桥梁选择更新监测点位数据\r\n    },\r\n    \r\n    // 切换展开状态\r\n    toggleExpand(pointIndex) {\r\n      if (pointIndex === 1) {\r\n        this.expandedSections.point1 = !this.expandedSections.point1\r\n      } else {\r\n        this.expandedSections.point2 = !this.expandedSections.point2\r\n      }\r\n    },\r\n    \r\n    // 获取可见的监测点位\r\n    getVisiblePoints(pointIndex) {\r\n      const isExpanded = pointIndex === 1 ? this.expandedSections.point1 : this.expandedSections.point2\r\n      if (isExpanded) {\r\n        return this.monitorPoints\r\n      }\r\n      return this.monitorPoints.slice(0, this.visiblePointsCount)\r\n    },\r\n    \r\n    // 查询操作\r\n    async handleQuery() {\r\n      // 验证必填项\r\n      if (!this.monitorPoint1.type || !this.monitorPoint1.point) {\r\n        this.$message.warning('请选择监测点容1的类型和点位')\r\n        return\r\n      }\r\n      \r\n      if (!this.monitorPoint2.type || !this.monitorPoint2.point) {\r\n        this.$message.warning('请选择监测点容2的类型和点位')\r\n        return\r\n      }\r\n      \r\n      if (!this.dateRange.start || !this.dateRange.end) {\r\n        this.$message.warning('请选择查询日期范围')\r\n        return\r\n      }\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        // 模拟API调用\r\n        await this.fetchCorrelationData()\r\n        this.showResult = true\r\n        this.$message.success('查询成功')\r\n      } catch (error) {\r\n        this.$message.error('查询失败: ' + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 重置操作\r\n    handleReset() {\r\n      this.monitorPoint1 = {\r\n        type: 'stress',\r\n        point: 'BPJDQ-RSG-G03-01-01'\r\n      }\r\n      \r\n      this.monitorPoint2 = {\r\n        type: 'stress', \r\n        point: 'BPJDQ-RSG-G03-01-02'\r\n      }\r\n      \r\n      this.selectedBridge = ''\r\n      \r\n      this.dateRange = {\r\n        start: '',\r\n        end: ''\r\n      }\r\n      \r\n      this.expandedSections = {\r\n        point1: false,\r\n        point2: false\r\n      }\r\n      \r\n      this.showResult = false\r\n    },\r\n    \r\n    // 移除已选条件\r\n    removeCondition(conditionType) {\r\n      switch (conditionType) {\r\n        case 'point1-type':\r\n          this.monitorPoint1.type = ''\r\n          break\r\n        case 'point1-point':\r\n          this.monitorPoint1.point = ''\r\n          break\r\n        case 'point2-type':\r\n          this.monitorPoint2.type = ''\r\n          break\r\n        case 'point2-point':\r\n          this.monitorPoint2.point = ''\r\n          break\r\n      }\r\n    },\r\n    \r\n    // 获取监测类型名称\r\n    getMonitorTypeName(value) {\r\n      const type = this.monitorTypes.find(t => t.value === value)\r\n      return type ? type.label : value\r\n    },\r\n    \r\n    // 获取监测点位名称  \r\n    getMonitorPointName(value) {\r\n      const point = this.monitorPoints.find(p => p.value === value)\r\n      return point ? point.label : value\r\n    },\r\n    \r\n    // 模拟获取关联性分析数据\r\n    async fetchCorrelationData() {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            correlation: 0.85,\r\n            trend: 'positive'\r\n          })\r\n        }, 1500)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 引入公共样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/mixins/inspection-common.scss';\r\n\r\n.correlation-analysis {\r\n  // 使用公共样式混入\r\n  @include inspection-page-container;\r\n  \r\n  // 重写筛选区域样式，取消外框，添加横线分隔\r\n  .filter-section {\r\n    background: transparent !important;\r\n    border: none !important;\r\n    border-radius: 0 !important;\r\n    margin-bottom: 8px !important;\r\n    padding: 8px 0 !important;\r\n    position: relative;\r\n    \r\n    // 取消伪元素\r\n    &::before,\r\n    &::after {\r\n      display: none !important;\r\n    }\r\n    \r\n    // 添加底部横线分隔\r\n    &:not(:last-child) {\r\n      border-bottom: 1px solid var(--inspection-border) !important;\r\n      padding-bottom: 8px !important;\r\n    }\r\n    \r\n    // 最后一个筛选区域不显示底部横线\r\n    &:last-child {\r\n      border-bottom: none !important;\r\n      margin-bottom: 0 !important;\r\n    }\r\n  }\r\n  \r\n  // 筛选区域标题样式\r\n  .section-title,\r\n  .condition-title {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: var(--inspection-text-primary);\r\n    margin-bottom: 12px;\r\n    display: inline-flex;\r\n    align-items: center;\r\n    margin-right: 16px;\r\n    min-height: 32px;\r\n  }\r\n  \r\n  .tab-and-selector {\r\n    display: inline-block;\r\n    width: calc(100% - 120px);\r\n  }\r\n  \r\n  // 监测点位选择器样式\r\n  .monitor-type-tabs {\r\n    margin-bottom: 12px;\r\n    \r\n    .monitor-tab-btn {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n      border-radius: 16px;\r\n      padding: 6px 16px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  \r\n  .monitor-point-selector {\r\n    .point-selector-btn,\r\n    .more-btn,\r\n    .expand-btn {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n      border-radius: 16px;\r\n      padding: 4px 12px;\r\n      font-size: 12px;\r\n    }\r\n    \r\n    .more-btn {\r\n      color: var(--inspection-text-muted);\r\n      border: none;\r\n      background: none;\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      padding: 4px 8px;\r\n    }\r\n    \r\n    .expand-btn {\r\n      background: var(--inspection-success);\r\n      border-color: var(--inspection-success);\r\n      color: white;\r\n    }\r\n  }\r\n  \r\n  // 桥梁选择器样式\r\n  .bridge-selector {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    \r\n    .bridge-label {\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n      color: var(--inspection-text-primary);\r\n      min-width: 80px;\r\n    }\r\n    \r\n    .bridge-dropdown {\r\n      width: 200px;\r\n    }\r\n  }\r\n  \r\n  // 日期输入样式\r\n  .date-inputs {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: 16px;\r\n    \r\n    .date-input-group {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      \r\n      .date-label {\r\n        font-size: 14px;\r\n        color: var(--inspection-text-primary);\r\n        font-weight: 600;\r\n      }\r\n      \r\n      .date-picker {\r\n        width: 140px;\r\n      }\r\n      \r\n      .date-separator {\r\n        color: var(--inspection-text-muted);\r\n        margin: 0 4px;\r\n      }\r\n    }\r\n    \r\n    .action-buttons {\r\n      .el-button {\r\n        margin-left: 8px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 条件标签样式\r\n  .condition-tags {\r\n    display: inline-block;\r\n    \r\n    .condition-tag {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n    }\r\n  }\r\n  \r\n  // 分析结果样式\r\n  .analysis-result {\r\n    margin-top: 32px;\r\n    padding: 24px;\r\n    background: var(--inspection-card-bg);\r\n    border-radius: 8px;\r\n    border: 1px solid var(--inspection-card-border);\r\n    \r\n    .result-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: var(--inspection-text-primary);\r\n      }\r\n    }\r\n    \r\n    .result-content {\r\n      p {\r\n        margin: 0;\r\n        color: var(--inspection-text-secondary);\r\n        line-height: 1.6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式适配\r\n@media (max-width: 768px) {\r\n  .correlation-analysis {\r\n    .section-title,\r\n    .condition-title {\r\n      display: block;\r\n      margin-bottom: 8px;\r\n    }\r\n    \r\n    .tab-and-selector {\r\n      display: block;\r\n      width: 100%;\r\n    }\r\n    \r\n    .date-inputs {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      \r\n      .date-input-group {\r\n        width: 100%;\r\n        \r\n        .date-picker {\r\n          flex: 1;\r\n          min-width: 120px;\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        width: 100%;\r\n        \r\n        .el-button {\r\n          margin-left: 0;\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}