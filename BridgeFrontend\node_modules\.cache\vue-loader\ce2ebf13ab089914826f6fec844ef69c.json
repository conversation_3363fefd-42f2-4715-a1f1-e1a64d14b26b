{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue?vue&type=template&id=9856d310&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}