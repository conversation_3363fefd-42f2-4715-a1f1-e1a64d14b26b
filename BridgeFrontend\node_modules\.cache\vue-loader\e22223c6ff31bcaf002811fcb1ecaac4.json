{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\data-center\\correlation-analysis\\index.vue?vue&type=style&index=0&id=44acc38a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\data-center\\correlation-analysis\\index.vue", "mtime": 1758804563523}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIOW8leWFpeWFrOWFseagt+W8jw0KQGltcG9ydCAnQC9zdHlsZXMvaW5zcGVjdGlvbi10aGVtZS5zY3NzJzsNCkBpbXBvcnQgJ0Avc3R5bGVzL21peGlucy9pbnNwZWN0aW9uLWNvbW1vbi5zY3NzJzsNCg0KLmNvcnJlbGF0aW9uLWFuYWx5c2lzIHsNCiAgLy8g5L2/55So5YWs5YWx5qC35byP5re35YWlDQogIEBpbmNsdWRlIGluc3BlY3Rpb24tcGFnZS1jb250YWluZXI7DQogIA0KICAvLyDph43lhpnnrZvpgInljLrln5/moLflvI/vvIzlj5bmtojlpJbmoYbvvIzmt7vliqDmqKrnur/liIbpmpQNCiAgLmZpbHRlci1zZWN0aW9uIHsNCiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50Ow0KICAgIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KICAgIGJvcmRlci1yYWRpdXM6IDAgIWltcG9ydGFudDsNCiAgICBtYXJnaW4tYm90dG9tOiA4cHggIWltcG9ydGFudDsNCiAgICBwYWRkaW5nOiA4cHggMCAhaW1wb3J0YW50Ow0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICANCiAgICAvLyDlj5bmtojkvKrlhYPntKANCiAgICAmOjpiZWZvcmUsDQogICAgJjo6YWZ0ZXIgew0KICAgICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50Ow0KICAgIH0NCiAgICANCiAgICAvLyDmt7vliqDlupXpg6jmqKrnur/liIbpmpQNCiAgICAmOm5vdCg6bGFzdC1jaGlsZCkgew0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWluc3BlY3Rpb24tYm9yZGVyKSAhaW1wb3J0YW50Ow0KICAgICAgcGFkZGluZy1ib3R0b206IDhweCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgICANCiAgICAvLyDmnIDlkI7kuIDkuKrnrZvpgInljLrln5/kuI3mmL7npLrlupXpg6jmqKrnur8NCiAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZSAhaW1wb3J0YW50Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KICANCiAgLy8g562b6YCJ5Yy65Z+f5qCH6aKY5qC35byPDQogIC5zZWN0aW9uLXRpdGxlLA0KICAuY29uZGl0aW9uLXRpdGxlIHsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogdmFyKC0taW5zcGVjdGlvbi10ZXh0LXByaW1hcnkpOw0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgZGlzcGxheTogaW5saW5lLWZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7DQogICAgbWluLWhlaWdodDogMzJweDsNCiAgfQ0KICANCiAgLnRhYi1hbmQtc2VsZWN0b3Igew0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICB3aWR0aDogY2FsYygxMDAlIC0gMTIwcHgpOw0KICB9DQogIA0KICAvLyDnm5HmtYvngrnkvY3pgInmi6nlmajmoLflvI8NCiAgLm1vbml0b3ItdHlwZS10YWJzIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgIA0KICAgIC5tb25pdG9yLXRhYi1idG4gew0KICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICAgICAgcGFkZGluZzogNnB4IDE2cHg7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgfQ0KICB9DQogIA0KICAubW9uaXRvci1wb2ludC1zZWxlY3RvciB7DQogICAgLnBvaW50LXNlbGVjdG9yLWJ0biwNCiAgICAubW9yZS1idG4sDQogICAgLmV4cGFuZC1idG4gew0KICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICAgICAgcGFkZGluZzogNHB4IDEycHg7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgfQ0KICAgIA0KICAgIC5tb3JlLWJ0biB7DQogICAgICBjb2xvcjogdmFyKC0taW5zcGVjdGlvbi10ZXh0LW11dGVkKTsNCiAgICAgIGJvcmRlcjogbm9uZTsNCiAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgIHBhZGRpbmc6IDRweCA4cHg7DQogICAgfQ0KICAgIA0KICAgIC5leHBhbmQtYnRuIHsNCiAgICAgIGJhY2tncm91bmQ6IHZhcigtLWluc3BlY3Rpb24tc3VjY2Vzcyk7DQogICAgICBib3JkZXItY29sb3I6IHZhcigtLWluc3BlY3Rpb24tc3VjY2Vzcyk7DQogICAgICBjb2xvcjogd2hpdGU7DQogICAgfQ0KICB9DQogIA0KICAvLyDmoaXmooHpgInmi6nlmajmoLflvI8NCiAgLmJyaWRnZS1zZWxlY3RvciB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGdhcDogMTJweDsNCiAgICANCiAgICAuYnJpZGdlLWxhYmVsIHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICBjb2xvcjogdmFyKC0taW5zcGVjdGlvbi10ZXh0LXByaW1hcnkpOw0KICAgICAgbWluLXdpZHRoOiA4MHB4Ow0KICAgIH0NCiAgICANCiAgICAuYnJpZGdlLWRyb3Bkb3duIHsNCiAgICAgIHdpZHRoOiAyMDBweDsNCiAgICB9DQogIH0NCiAgDQogIC8vIOaXpeacn+i+k+WFpeagt+W8jw0KICAuZGF0ZS1pbnB1dHMgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgZ2FwOiAxNnB4Ow0KICAgIA0KICAgIC5kYXRlLWlucHV0LWdyb3VwIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgZ2FwOiA4cHg7DQogICAgICANCiAgICAgIC5kYXRlLWxhYmVsIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogdmFyKC0taW5zcGVjdGlvbi10ZXh0LXByaW1hcnkpOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgfQ0KICAgICAgDQogICAgICAuZGF0ZS1waWNrZXIgew0KICAgICAgICB3aWR0aDogMTQwcHg7DQogICAgICB9DQogICAgICANCiAgICAgIC5kYXRlLXNlcGFyYXRvciB7DQogICAgICAgIGNvbG9yOiB2YXIoLS1pbnNwZWN0aW9uLXRleHQtbXV0ZWQpOw0KICAgICAgICBtYXJnaW46IDAgNHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgICANCiAgICAuYWN0aW9uLWJ1dHRvbnMgew0KICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7DQogICAgICB9DQogICAgfQ0KICB9DQogIA0KICAvLyDmnaHku7bmoIfnrb7moLflvI8NCiAgLmNvbmRpdGlvbi10YWdzIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgDQogICAgLmNvbmRpdGlvbi10YWcgew0KICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogICAgfQ0KICB9DQogIA0KICAvLyDliIbmnpDnu5PmnpzmoLflvI8NCiAgLmFuYWx5c2lzLXJlc3VsdCB7DQogICAgbWFyZ2luLXRvcDogMzJweDsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KICAgIGJhY2tncm91bmQ6IHZhcigtLWluc3BlY3Rpb24tY2FyZC1iZyk7DQogICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWluc3BlY3Rpb24tY2FyZC1ib3JkZXIpOw0KICAgIA0KICAgIC5yZXN1bHQtaGVhZGVyIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgICANCiAgICAgIGgzIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGNvbG9yOiB2YXIoLS1pbnNwZWN0aW9uLXRleHQtcHJpbWFyeSk7DQogICAgICB9DQogICAgfQ0KICAgIA0KICAgIC5yZXN1bHQtY29udGVudCB7DQogICAgICBwIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBjb2xvcjogdmFyKC0taW5zcGVjdGlvbi10ZXh0LXNlY29uZGFyeSk7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi8vIOWTjeW6lOW8j+mAgumFjQ0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5jb3JyZWxhdGlvbi1hbmFseXNpcyB7DQogICAgLnNlY3Rpb24tdGl0bGUsDQogICAgLmNvbmRpdGlvbi10aXRsZSB7DQogICAgICBkaXNwbGF5OiBibG9jazsNCiAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICB9DQogICAgDQogICAgLnRhYi1hbmQtc2VsZWN0b3Igew0KICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICB9DQogICAgDQogICAgLmRhdGUtaW5wdXRzIHsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICAgIA0KICAgICAgLmRhdGUtaW5wdXQtZ3JvdXAgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgDQogICAgICAgIC5kYXRlLXBpY2tlciB7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICBtaW4td2lkdGg6IDEyMHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICANCiAgICAgIC5hY3Rpb24tYnV0dG9ucyB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICANCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDA7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAybA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/data-center/correlation-analysis", "sourcesContent": ["<template>\r\n  <div class=\"correlation-analysis inspection-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 桥梁选择 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"bridge-selector\">\r\n          <span class=\"bridge-label\">选择桥梁:</span>\r\n          <el-select\r\n            v-model=\"selectedBridge\"\r\n            placeholder=\"请选择桥梁\"\r\n            size=\"small\"\r\n            class=\"bridge-dropdown\"\r\n            @change=\"handleBridgeChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"bridge in bridgeOptions\"\r\n              :key=\"bridge.value\"\r\n              :label=\"bridge.label\"\r\n              :value=\"bridge.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 监测内容1 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"section-title\">监测内容1:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测类型选项卡 -->\r\n          <div class=\"monitor-type-tabs\">\r\n            <el-button \r\n              v-for=\"type in monitorTypes\"\r\n              :key=\"type.value\"\r\n              :type=\"monitorPoint1.type === type.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorTypeChange(1, type.value)\"\r\n              class=\"monitor-tab-btn\"\r\n            >\r\n              {{ type.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"section-title\">监测点位1:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测点位选择器 -->\r\n          <div class=\"monitor-point-selector\">\r\n            <el-button \r\n              v-for=\"(point, index) in getVisiblePoints(1)\"\r\n              :key=\"point.value\"\r\n              :type=\"monitorPoint1.point === point.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorPointChange(1, point.value)\"\r\n              class=\"point-selector-btn\"\r\n            >\r\n              {{ point.label }}\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"!expandedSections.point1 && monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"text\" \r\n              class=\"more-btn\"\r\n            >\r\n              ...\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"primary\" \r\n              class=\"expand-btn\"\r\n              @click=\"toggleExpand(1)\"\r\n            >\r\n              {{ expandedSections.point1 ? '收起' : '展开' }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 监测内容2 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"section-title\">监测内容2:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测类型选项卡 -->\r\n          <div class=\"monitor-type-tabs\">\r\n            <el-button \r\n              v-for=\"type in monitorTypes\"\r\n              :key=\"type.value\"\r\n              :type=\"monitorPoint2.type === type.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorTypeChange(2, type.value)\"\r\n              class=\"monitor-tab-btn\"\r\n            >\r\n              {{ type.label }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"section-title\">监测点位2:</div>\r\n        <div class=\"tab-and-selector\">\r\n          <!-- 监测点位选择器 -->\r\n          <div class=\"monitor-point-selector\">\r\n            <el-button \r\n              v-for=\"(point, index) in getVisiblePoints(2)\"\r\n              :key=\"point.value\"\r\n              :type=\"monitorPoint2.point === point.value ? 'primary' : 'default'\"\r\n              size=\"small\"\r\n              @click=\"handleMonitorPointChange(2, point.value)\"\r\n              class=\"point-selector-btn\"\r\n            >\r\n              {{ point.label }}\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"!expandedSections.point2 && monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"text\" \r\n              class=\"more-btn\"\r\n            >\r\n              ...\r\n            </el-button>\r\n            <el-button \r\n              v-if=\"monitorPoints.length > visiblePointsCount\"\r\n              size=\"small\" \r\n              type=\"primary\" \r\n              class=\"expand-btn\"\r\n              @click=\"toggleExpand(2)\"\r\n            >\r\n              {{ expandedSections.point2 ? '收起' : '展开' }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 日期选择和操作按钮 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"date-inputs\">\r\n          <div class=\"date-input-group\">\r\n            <span class=\"date-label\">选择日期:</span>\r\n            <el-date-picker\r\n              v-model=\"dateRange.start\"\r\n              type=\"date\"\r\n              placeholder=\"开始日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              size=\"small\"\r\n              class=\"date-picker\"\r\n            />\r\n            <span class=\"date-separator\">-</span>\r\n            <el-date-picker\r\n              v-model=\"dateRange.end\"\r\n              type=\"date\"\r\n              placeholder=\"结束日期\"\r\n              format=\"yyyy-MM-dd\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              size=\"small\"\r\n              class=\"date-picker\"\r\n            />\r\n          </div>\r\n          <div class=\"action-buttons\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleQuery\" :loading=\"loading\">\r\n              查询\r\n            </el-button>\r\n            <el-button size=\"small\" @click=\"handleReset\">\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 已选条件 -->\r\n      <div class=\"filter-section\">\r\n        <div class=\"condition-title\">已选条件:</div>\r\n        <div class=\"condition-tags\">\r\n          <el-tag \r\n            v-if=\"monitorPoint1.type\"\r\n            size=\"small\" \r\n            closable\r\n            @close=\"removeCondition('point1-type')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorTypeName(monitorPoint1.type) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint1.point\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point1-point')\" \r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorPointName(monitorPoint1.point) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint2.type\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point2-type')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorTypeName(monitorPoint2.type) }}\r\n          </el-tag>\r\n          <el-tag \r\n            v-if=\"monitorPoint2.point\"\r\n            size=\"small\"\r\n            closable\r\n            @close=\"removeCondition('point2-point')\"\r\n            class=\"condition-tag\"\r\n          >\r\n            {{ getMonitorPointName(monitorPoint2.point) }}\r\n          </el-tag>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分析结果展示区域 -->\r\n      <div class=\"analysis-result\" v-if=\"showResult\">\r\n        <div class=\"result-header\">\r\n          <h3>关联性分析结果</h3>\r\n        </div>\r\n        <div class=\"result-content\">\r\n          <p>分析结果将在这里显示...</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CorrelationAnalysis',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      showResult: false,\r\n      \r\n      // 监测点1配置\r\n      monitorPoint1: {\r\n        type: 'stress', // 默认选择应力应变\r\n        point: 'BPJDQ-RSG-G03-01-01' // 默认选择第一个点\r\n      },\r\n      \r\n      // 监测点2配置\r\n      monitorPoint2: {\r\n        type: 'stress', // 默认选择应力应变\r\n        point: 'BPJDQ-RSG-G03-01-02' // 默认选择第二个点\r\n      },\r\n      \r\n      // 选中的桥梁\r\n      selectedBridge: '',\r\n      \r\n      // 桥梁选项\r\n      bridgeOptions: [\r\n        { label: '白坡大桥', value: 'baipo' },\r\n        { label: '长江大桥', value: 'changjiang' },\r\n        { label: '黄河大桥', value: 'huanghe' },\r\n        { label: '珠江大桥', value: 'zhujiang' }\r\n      ],\r\n      \r\n      // 日期范围\r\n      dateRange: {\r\n        start: '',\r\n        end: ''\r\n      },\r\n      \r\n      // 监测类型选项\r\n      monitorTypes: [\r\n        { label: '主梁变向位移', value: 'displacement' },\r\n        { label: '应力应变', value: 'stress' },\r\n        { label: '梁端位移', value: 'beam-displacement' }\r\n      ],\r\n      \r\n      // 监测点位选项 (模拟数据)\r\n      monitorPoints: [\r\n        { label: 'BPJDQ-RSG-G03-01-01', value: 'BPJDQ-RSG-G03-01-01' },\r\n        { label: 'BPJDQ-RSG-G03-01-02', value: 'BPJDQ-RSG-G03-01-02' },\r\n        { label: 'BPJDQ-RSG-G03-01-03', value: 'BPJDQ-RSG-G03-01-03' },\r\n        { label: 'BPJDQ-RSG-G03-01-04', value: 'BPJDQ-RSG-G03-01-04' },\r\n        { label: 'BPJDQ-RSG-G03-01-05', value: 'BPJDQ-RSG-G03-01-05' },\r\n        { label: 'BPJDQ-RSG-G03-02-01', value: 'BPJDQ-RSG-G03-02-01' },\r\n        { label: 'BPJDQ-RSG-G03-02-02', value: 'BPJDQ-RSG-G03-02-02' },\r\n        { label: 'BPJDQ-RSG-G03-02-03', value: 'BPJDQ-RSG-G03-02-03' },\r\n        { label: 'BPJDQ-RSG-G03-02-04', value: 'BPJDQ-RSG-G03-02-04' },\r\n        { label: 'BPJDQ-RSG-G03-02-05', value: 'BPJDQ-RSG-G03-02-05' }\r\n      ],\r\n      \r\n      // 展开状态控制\r\n      expandedSections: {\r\n        point1: false,\r\n        point2: false\r\n      },\r\n      \r\n      // 可见监测点数量\r\n      visiblePointsCount: 5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 监测类型选择变化\r\n    handleMonitorTypeChange(pointIndex, typeValue) {\r\n      if (pointIndex === 1) {\r\n        this.monitorPoint1.type = typeValue\r\n      } else {\r\n        this.monitorPoint2.type = typeValue\r\n      }\r\n    },\r\n    \r\n    // 监测点位选择变化\r\n    handleMonitorPointChange(pointIndex, pointValue) {\r\n      if (pointIndex === 1) {\r\n        this.monitorPoint1.point = pointValue\r\n      } else {\r\n        this.monitorPoint2.point = pointValue\r\n      }\r\n    },\r\n    \r\n    // 桥梁选择变化\r\n    handleBridgeChange(value) {\r\n      console.log('选择桥梁:', value)\r\n      // 这里可以根据桥梁选择更新监测点位数据\r\n    },\r\n    \r\n    // 切换展开状态\r\n    toggleExpand(pointIndex) {\r\n      if (pointIndex === 1) {\r\n        this.expandedSections.point1 = !this.expandedSections.point1\r\n      } else {\r\n        this.expandedSections.point2 = !this.expandedSections.point2\r\n      }\r\n    },\r\n    \r\n    // 获取可见的监测点位\r\n    getVisiblePoints(pointIndex) {\r\n      const isExpanded = pointIndex === 1 ? this.expandedSections.point1 : this.expandedSections.point2\r\n      if (isExpanded) {\r\n        return this.monitorPoints\r\n      }\r\n      return this.monitorPoints.slice(0, this.visiblePointsCount)\r\n    },\r\n    \r\n    // 查询操作\r\n    async handleQuery() {\r\n      // 验证必填项\r\n      if (!this.monitorPoint1.type || !this.monitorPoint1.point) {\r\n        this.$message.warning('请选择监测点容1的类型和点位')\r\n        return\r\n      }\r\n      \r\n      if (!this.monitorPoint2.type || !this.monitorPoint2.point) {\r\n        this.$message.warning('请选择监测点容2的类型和点位')\r\n        return\r\n      }\r\n      \r\n      if (!this.dateRange.start || !this.dateRange.end) {\r\n        this.$message.warning('请选择查询日期范围')\r\n        return\r\n      }\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        // 模拟API调用\r\n        await this.fetchCorrelationData()\r\n        this.showResult = true\r\n        this.$message.success('查询成功')\r\n      } catch (error) {\r\n        this.$message.error('查询失败: ' + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 重置操作\r\n    handleReset() {\r\n      this.monitorPoint1 = {\r\n        type: 'stress',\r\n        point: 'BPJDQ-RSG-G03-01-01'\r\n      }\r\n      \r\n      this.monitorPoint2 = {\r\n        type: 'stress', \r\n        point: 'BPJDQ-RSG-G03-01-02'\r\n      }\r\n      \r\n      this.selectedBridge = ''\r\n      \r\n      this.dateRange = {\r\n        start: '',\r\n        end: ''\r\n      }\r\n      \r\n      this.expandedSections = {\r\n        point1: false,\r\n        point2: false\r\n      }\r\n      \r\n      this.showResult = false\r\n    },\r\n    \r\n    // 移除已选条件\r\n    removeCondition(conditionType) {\r\n      switch (conditionType) {\r\n        case 'point1-type':\r\n          this.monitorPoint1.type = ''\r\n          break\r\n        case 'point1-point':\r\n          this.monitorPoint1.point = ''\r\n          break\r\n        case 'point2-type':\r\n          this.monitorPoint2.type = ''\r\n          break\r\n        case 'point2-point':\r\n          this.monitorPoint2.point = ''\r\n          break\r\n      }\r\n    },\r\n    \r\n    // 获取监测类型名称\r\n    getMonitorTypeName(value) {\r\n      const type = this.monitorTypes.find(t => t.value === value)\r\n      return type ? type.label : value\r\n    },\r\n    \r\n    // 获取监测点位名称  \r\n    getMonitorPointName(value) {\r\n      const point = this.monitorPoints.find(p => p.value === value)\r\n      return point ? point.label : value\r\n    },\r\n    \r\n    // 模拟获取关联性分析数据\r\n    async fetchCorrelationData() {\r\n      return new Promise((resolve) => {\r\n        setTimeout(() => {\r\n          resolve({\r\n            correlation: 0.85,\r\n            trend: 'positive'\r\n          })\r\n        }, 1500)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 引入公共样式\r\n@import '@/styles/inspection-theme.scss';\r\n@import '@/styles/mixins/inspection-common.scss';\r\n\r\n.correlation-analysis {\r\n  // 使用公共样式混入\r\n  @include inspection-page-container;\r\n  \r\n  // 重写筛选区域样式，取消外框，添加横线分隔\r\n  .filter-section {\r\n    background: transparent !important;\r\n    border: none !important;\r\n    border-radius: 0 !important;\r\n    margin-bottom: 8px !important;\r\n    padding: 8px 0 !important;\r\n    position: relative;\r\n    \r\n    // 取消伪元素\r\n    &::before,\r\n    &::after {\r\n      display: none !important;\r\n    }\r\n    \r\n    // 添加底部横线分隔\r\n    &:not(:last-child) {\r\n      border-bottom: 1px solid var(--inspection-border) !important;\r\n      padding-bottom: 8px !important;\r\n    }\r\n    \r\n    // 最后一个筛选区域不显示底部横线\r\n    &:last-child {\r\n      border-bottom: none !important;\r\n      margin-bottom: 0 !important;\r\n    }\r\n  }\r\n  \r\n  // 筛选区域标题样式\r\n  .section-title,\r\n  .condition-title {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: var(--inspection-text-primary);\r\n    margin-bottom: 12px;\r\n    display: inline-flex;\r\n    align-items: center;\r\n    margin-right: 16px;\r\n    min-height: 32px;\r\n  }\r\n  \r\n  .tab-and-selector {\r\n    display: inline-block;\r\n    width: calc(100% - 120px);\r\n  }\r\n  \r\n  // 监测点位选择器样式\r\n  .monitor-type-tabs {\r\n    margin-bottom: 12px;\r\n    \r\n    .monitor-tab-btn {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n      border-radius: 16px;\r\n      padding: 6px 16px;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  \r\n  .monitor-point-selector {\r\n    .point-selector-btn,\r\n    .more-btn,\r\n    .expand-btn {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n      border-radius: 16px;\r\n      padding: 4px 12px;\r\n      font-size: 12px;\r\n    }\r\n    \r\n    .more-btn {\r\n      color: var(--inspection-text-muted);\r\n      border: none;\r\n      background: none;\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      padding: 4px 8px;\r\n    }\r\n    \r\n    .expand-btn {\r\n      background: var(--inspection-success);\r\n      border-color: var(--inspection-success);\r\n      color: white;\r\n    }\r\n  }\r\n  \r\n  // 桥梁选择器样式\r\n  .bridge-selector {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n    \r\n    .bridge-label {\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n      color: var(--inspection-text-primary);\r\n      min-width: 80px;\r\n    }\r\n    \r\n    .bridge-dropdown {\r\n      width: 200px;\r\n    }\r\n  }\r\n  \r\n  // 日期输入样式\r\n  .date-inputs {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    gap: 16px;\r\n    \r\n    .date-input-group {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      \r\n      .date-label {\r\n        font-size: 14px;\r\n        color: var(--inspection-text-primary);\r\n        font-weight: 600;\r\n      }\r\n      \r\n      .date-picker {\r\n        width: 140px;\r\n      }\r\n      \r\n      .date-separator {\r\n        color: var(--inspection-text-muted);\r\n        margin: 0 4px;\r\n      }\r\n    }\r\n    \r\n    .action-buttons {\r\n      .el-button {\r\n        margin-left: 8px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 条件标签样式\r\n  .condition-tags {\r\n    display: inline-block;\r\n    \r\n    .condition-tag {\r\n      margin-right: 8px;\r\n      margin-bottom: 8px;\r\n    }\r\n  }\r\n  \r\n  // 分析结果样式\r\n  .analysis-result {\r\n    margin-top: 32px;\r\n    padding: 24px;\r\n    background: var(--inspection-card-bg);\r\n    border-radius: 8px;\r\n    border: 1px solid var(--inspection-card-border);\r\n    \r\n    .result-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        color: var(--inspection-text-primary);\r\n      }\r\n    }\r\n    \r\n    .result-content {\r\n      p {\r\n        margin: 0;\r\n        color: var(--inspection-text-secondary);\r\n        line-height: 1.6;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式适配\r\n@media (max-width: 768px) {\r\n  .correlation-analysis {\r\n    .section-title,\r\n    .condition-title {\r\n      display: block;\r\n      margin-bottom: 8px;\r\n    }\r\n    \r\n    .tab-and-selector {\r\n      display: block;\r\n      width: 100%;\r\n    }\r\n    \r\n    .date-inputs {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      \r\n      .date-input-group {\r\n        width: 100%;\r\n        \r\n        .date-picker {\r\n          flex: 1;\r\n          min-width: 120px;\r\n        }\r\n      }\r\n      \r\n      .action-buttons {\r\n        width: 100%;\r\n        \r\n        .el-button {\r\n          margin-left: 0;\r\n          margin-right: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}