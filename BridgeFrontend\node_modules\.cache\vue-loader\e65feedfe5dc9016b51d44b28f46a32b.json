{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue?vue&type=style&index=0&id=09e43857&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\projects\\create\\components\\BasicInfo.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICdAL3N0eWxlcy9pbnNwZWN0aW9uLXRoZW1lLnNjc3MnOwoKLmJhc2ljLWluZm8tZm9ybSB7CiAgQGV4dGVuZCAuY29tbW9uLWZvcm07CiAgCiAgLy8g6KaG55uW54m55a6a5qC35byP5Lul6YCC5bqU5Yib5bu66KGo5Y2VCiAgLm1haW50ZW5hbmNlLWZvcm0gewogICAgLmVsLWZvcm0taXRlbSB7CiAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["BasicInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgaA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BasicInfo.vue", "sourceRoot": "src/views/maintenance/projects/create/components", "sourcesContent": ["<template>\n  <div class=\"basic-info-form\">\n    <el-form\n      ref=\"form\"\n      :model=\"formData\"\n      :rules=\"rules\"\n      label-width=\"120px\"\n      class=\"maintenance-form\"\n    >\n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"项目名称\" prop=\"projectName\" required>\n            <el-input\n              v-model=\"formData.projectName\"\n              placeholder=\"请输入项目名称\"\n              maxlength=\"50\"\n              show-word-limit\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目类型\" prop=\"projectType\" required>\n            <el-select\n              v-model=\"formData.projectType\"\n              placeholder=\"请选择项目类型\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleProjectTypeChange\"\n            >\n              <el-option\n                v-for=\"type in projectTypes\"\n                :key=\"type.value\"\n                :label=\"type.label\"\n                :value=\"type.value\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"项目开始时间\" prop=\"startDate\" required>\n            <el-date-picker\n              v-model=\"formData.startDate\"\n              type=\"date\"\n              placeholder=\"选择开始时间\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目结束时间\" prop=\"endDate\" required>\n            <el-date-picker\n              v-model=\"formData.endDate\"\n              type=\"date\"\n              placeholder=\"选择结束时间\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\"\n              style=\"width: 100%\"\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"管理单位\" prop=\"managementUnit\" required>\n            <el-select\n              v-model=\"formData.managementUnit\"\n              placeholder=\"请选择管理单位\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleInput\"\n            >\n              <el-option\n                v-for=\"unit in managementUnits\"\n                :key=\"unit.id\"\n                :label=\"unit.name\"\n                :value=\"unit.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"监理单位\">\n            <el-input\n              v-model=\"formData.supervisionUnit\"\n              placeholder=\"请输入监理单位\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"养护单位\" prop=\"maintenanceUnit\" required>\n            <el-select\n              v-model=\"formData.maintenanceUnit\"\n              placeholder=\"请选择养护单位\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleMaintenanceUnitChange\"\n            >\n              <el-option\n                v-for=\"unit in maintenanceUnits\"\n                :key=\"unit.id\"\n                :label=\"unit.name\"\n                :value=\"unit.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\">\n          <el-form-item label=\"项目负责人\" prop=\"manager\" required>\n            <el-select\n              v-model=\"formData.manager\"\n              placeholder=\"请选择项目负责人\"\n              style=\"width: 100%\"\n              filterable\n              :disabled=\"readonly\"\n              @change=\"handleManagerChange\"\n            >\n              <el-option\n                v-for=\"manager in projectManagers\"\n                :key=\"manager.id\"\n                :label=\"manager.name\"\n                :value=\"manager.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"24\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"联系方式\" prop=\"contactPhone\" required>\n            <el-input\n              v-model=\"formData.contactPhone\"\n              placeholder=\"请输入联系方式\"\n              maxlength=\"11\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n        \n        <el-col :span=\"12\" v-if=\"showWorkload\">\n          <el-form-item label=\"工作量\">\n            <el-input\n              v-model=\"formData.workload\"\n              placeholder=\"请输入工作量\"\n              type=\"number\"\n              :disabled=\"readonly\"\n              @input=\"handleInput\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-form-item label=\"项目内容\">\n        <el-input\n          v-model=\"formData.projectContent\"\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"请输入项目内容\"\n          maxlength=\"500\"\n          show-word-limit\n          :disabled=\"readonly\"\n          @input=\"handleInput\"\n        />\n      </el-form-item>\n      \n      <el-form-item label=\"附件\">\n        <file-upload\n          v-model=\"formData.attachments\"\n          :multiple=\"true\"\n          accept=\".jpg,.jpeg,.png,.pdf,.doc,.docx\"\n          :max-size=\"10 * 1024 * 1024\"\n          :disabled=\"readonly\"\n          @input=\"handleInput\"\n        />\n      </el-form-item>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport { getManagementUnits, getMaintenanceUnits, getProjectManagers } from '@/api/maintenance/projects'\nimport FileUpload from '@/components/Maintenance/FileUpload'\n\nexport default {\n  name: 'BasicInfo',\n  components: {\n    FileUpload\n  },\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    projectType: {\n      type: String,\n      default: ''\n    },\n    readonly: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      formData: {\n        projectName: '',\n        projectType: '',\n        startDate: '',\n        endDate: '',\n        managementUnit: '',\n        supervisionUnit: '',\n        maintenanceUnit: '',\n        manager: '',\n        contactPhone: '',\n        workload: '',\n        projectContent: '',\n        attachments: []\n      },\n      \n      // 选项数据\n      managementUnits: [],\n      maintenanceUnits: [],\n      projectManagers: [],\n      \n      // 项目类型选项\n      projectTypes: [\n        { label: '月度养护', value: 'monthly' },\n        { label: '保洁项目', value: 'cleaning' },\n        { label: '应急养护', value: 'emergency' },\n        { label: '预防养护', value: 'preventive' }\n      ],\n      \n      // 表单验证规则\n      rules: {\n        projectName: [\n          { required: true, message: '请输入项目名称', trigger: 'blur' },\n          { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }\n        ],\n        projectType: [\n          { required: true, message: '请选择项目类型', trigger: 'change' }\n        ],\n        startDate: [\n          { required: true, message: '请选择开始时间', trigger: 'change' }\n        ],\n        endDate: [\n          { required: true, message: '请选择结束时间', trigger: 'change' },\n          { validator: this.validateEndDate, trigger: 'change' }\n        ],\n        managementUnit: [\n          { required: true, message: '请选择管理单位', trigger: 'change' }\n        ],\n        maintenanceUnit: [\n          { required: true, message: '请选择养护单位', trigger: 'change' }\n        ],\n        manager: [\n          { required: true, message: '请选择项目负责人', trigger: 'change' }\n        ],\n        contactPhone: [\n          { required: true, message: '请输入联系方式', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  computed: {\n    // 是否显示工作量字段\n    showWorkload() {\n      return ['cleaning', 'emergency', 'preventive'].includes(this.formData.projectType)\n    }\n  },\n  watch: {\n    // 只监听外部传入的value，单向数据流\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          // 只在数据真正不同时才更新，避免循环\n          const hasChanges = Object.keys(newVal).some(key => \n            JSON.stringify(newVal[key]) !== JSON.stringify(this.formData[key])\n          )\n          if (hasChanges) {\n            this.formData = { ...this.formData, ...newVal }\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n    \n    // 监听外部项目类型变化\n    projectType: {\n      handler(newVal) {\n        if (newVal && newVal !== this.formData.projectType) {\n          this.formData.projectType = newVal\n        }\n      },\n      immediate: true\n    }\n  },\n  async created() {\n    await this.loadOptions()\n  },\n  methods: {\n    // 加载选项数据\n    async loadOptions() {\n      try {\n        const [managementRes, maintenanceRes] = await Promise.all([\n          getManagementUnits(),\n          getMaintenanceUnits()\n        ])\n        \n        this.managementUnits = managementRes.data || []\n        this.maintenanceUnits = maintenanceRes.data || []\n      } catch (error) {\n        this.$message.error('加载选项数据失败')\n      }\n    },\n    \n    // 统一的输入处理方法\n    handleInput() {\n      this.$nextTick(() => {\n        this.$emit('input', { ...this.formData })\n      })\n    },\n    \n    // 项目类型变化\n    handleProjectTypeChange(type) {\n      // 只有当值真正变化时才触发事件\n      if (type !== this.projectType) {\n        this.$emit('project-type-change', type)\n      }\n      this.handleInput()\n    },\n    \n    // 养护单位变化\n    async handleMaintenanceUnitChange(unitId) {\n      this.formData.manager = ''\n      this.formData.contactPhone = ''\n      this.projectManagers = []\n      \n      if (unitId) {\n        try {\n          const response = await getProjectManagers(unitId)\n          this.projectManagers = response.data || []\n        } catch (error) {\n          this.$message.error('加载项目负责人失败')\n        }\n      }\n      this.handleInput()\n    },\n    \n    // 项目负责人变化 - 自动填充联系方式\n    handleManagerChange(managerId) {\n      if (managerId) {\n        const selectedManager = this.projectManagers.find(manager => manager.id === managerId)\n        if (selectedManager) {\n          this.formData.contactPhone = selectedManager.phone\n        }\n      } else {\n        this.formData.contactPhone = ''\n      }\n      this.handleInput()\n    },\n    \n    // 验证结束时间\n    validateEndDate(rule, value, callback) {\n      if (value && this.formData.startDate) {\n        if (new Date(value) <= new Date(this.formData.startDate)) {\n          callback(new Error('结束时间必须大于开始时间'))\n        } else {\n          callback()\n        }\n      } else {\n        callback()\n      }\n    },\n    \n    // 表单验证\n    validate() {\n      return new Promise((resolve) => {\n        this.$refs.form.validate((valid) => {\n          if (!valid) {\n            this.$message.error('请完善基本信息')\n          }\n          resolve(valid)\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n\n.basic-info-form {\n  @extend .common-form;\n  \n  // 覆盖特定样式以适应创建表单\n  .maintenance-form {\n    .el-form-item {\n      margin-bottom: 24px;\n    }\n  }\n}\n</style>\n"]}]}