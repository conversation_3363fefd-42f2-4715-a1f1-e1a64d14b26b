{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\detail\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEF1ZGl0RGV0YWlsIH0gZnJvbSAnQC9hcGkvbWFpbnRlbmFuY2UvYXVkaXQnCmltcG9ydCBTdGF0dXNUYWcgZnJvbSAnQC9jb21wb25lbnRzL01haW50ZW5hbmNlL1N0YXR1c1RhZycKaW1wb3J0IEJhc2ljSW5mb1ZpZXcgZnJvbSAnLi4vLi4vcmVwYWlycy9kZXRhaWwvY29tcG9uZW50cy9CYXNpY0luZm9WaWV3JwppbXBvcnQgVGFza3NJbmZvVmlldyBmcm9tICcuLi8uLi9yZXBhaXJzL2RldGFpbC9jb21wb25lbnRzL1Rhc2tzSW5mb1ZpZXcnCmltcG9ydCBEaXNlYXNlc0luZm9WaWV3IGZyb20gJy4uLy4uL3JlcGFpcnMvZGV0YWlsL2NvbXBvbmVudHMvRGlzZWFzZXNJbmZvVmlldycKaW1wb3J0IEF1ZGl0SGlzdG9yeVZpZXcgZnJvbSAnLi9jb21wb25lbnRzL0F1ZGl0SGlzdG9yeVZpZXcnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ01haW50ZW5hbmNlQXVkaXREZXRhaWwnLAogIGNvbXBvbmVudHM6IHsKICAgIFN0YXR1c1RhZywKICAgIEJhc2ljSW5mb1ZpZXcsCiAgICBUYXNrc0luZm9WaWV3LAogICAgRGlzZWFzZXNJbmZvVmlldywKICAgIEF1ZGl0SGlzdG9yeVZpZXcKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgYWN0aXZlVGFiOiAnYmFzaWMnLAogICAgICBwcm9qZWN0RGF0YToge30sCiAgICAgIHByb2plY3RJZDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDmmK/lkKbmmL7npLrnl4XlrrPlhbvmiqTmoIfnrb4KICAgIHNob3dEaXNlYXNlVGFiKCkgewogICAgICByZXR1cm4gdGhpcy5wcm9qZWN0RGF0YS5wcm9qZWN0VHlwZSA9PT0gJ21vbnRobHknCiAgICB9LAogICAgCiAgICAvLyDmmK/lkKblj6/ku6XlrqHmoLgKICAgIGNhbkF1ZGl0KCkgewogICAgICBjb25zdCB1c2VyUm9sZSA9IHRoaXMuJHN0b3JlLmdldHRlcnMudXNlclJvbGUgfHwgJ2NvbXBhbnlfYWRtaW4nCiAgICAgIAogICAgICBpZiAodXNlclJvbGUgPT09ICdjb21wYW55X2FkbWluJykgewogICAgICAgIHJldHVybiB0aGlzLnByb2plY3REYXRhLmF1ZGl0U3RhdHVzID09PSAncHJpbWFyeV9hdWRpdCcKICAgICAgfSBlbHNlIGlmICh1c2VyUm9sZSA9PT0gJ2JyaWRnZV9jZW50ZXInKSB7CiAgICAgICAgcmV0dXJuIHRoaXMucHJvamVjdERhdGEuYXVkaXRTdGF0dXMgPT09ICdzZWNvbmRhcnlfYXVkaXQnCiAgICAgIH0KICAgICAgCiAgICAgIHJldHVybiBmYWxzZQogICAgfQogIH0sCiAgYXN5bmMgY3JlYXRlZCgpIHsKICAgIHRoaXMucHJvamVjdElkID0gdGhpcy4kcm91dGUucGFyYW1zLmlkCiAgICBhd2FpdCB0aGlzLmxvYWRQcm9qZWN0RGV0YWlsKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWKoOi9vemhueebruivpuaDhQogICAgYXN5bmMgbG9hZFByb2plY3REZXRhaWwoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0QXVkaXREZXRhaWwodGhpcy5wcm9qZWN0SWQpCiAgICAgICAgdGhpcy5wcm9qZWN0RGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwge30KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3pobnnm67or6bmg4XlpLHotKUnKQogICAgICAgIHRoaXMuaGFuZGxlQmFjaygpCiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5YiH5o2i5qCH562+CiAgICBzd2l0Y2hUYWIodGFiKSB7CiAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiCiAgICB9LAogICAgCiAgICAvLyDov5Tlm57liJfooagKICAgIGhhbmRsZUJhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvbWFpbnRlbmFuY2UvYXVkaXQnKQogICAgfSwKICAgIAogICAgLy8g5a6h5qC46aG555uuCiAgICBoYW5kbGVBdWRpdCgpIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goYC9tYWludGVuYW5jZS9wcm9qZWN0cy9hcHByb3ZlLyR7dGhpcy5wcm9qZWN0SWR9YCkKICAgIH0sCiAgICAKICAgIC8vIOiOt+W<PERSON><PERSON><PERSON><PERSON>b<PERSON>xu+Wei+aWh+acrAogICAgZ2V0UHJvamVjdFR5cGVUZXh0KHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICBtb250aGx5OiAn5pyI5bqm5YW75oqkJywKICAgICAgICBjbGVhbmluZzogJ+S/nea0gemhueebricsCiAgICAgICAgZW1lcmdlbmN5OiAn5bqU5oCl5YW75oqkJywKICAgICAgICBwcmV2ZW50aXZlOiAn6aKE6Ziy5YW75oqkJwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8IHR5cGUKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/audit/detail", "sourcesContent": ["<template>\n  <div class=\"maintenance-theme\">\n    <div class=\"page-container\">\n      <div class=\"card-container\">\n        <!-- 页面标题栏 -->\n        <div class=\"page-header\">\n          <div class=\"header-left\">\n            <el-button\n              type=\"text\"\n              icon=\"el-icon-arrow-left\"\n              class=\"back-btn\"\n              @click=\"handleBack\"\n            >\n              返回\n            </el-button>\n            <h2>审核详情</h2>\n          </div>\n          \n          <div class=\"header-right\">\n            <el-button\n              v-if=\"canAudit\"\n              type=\"primary\"\n              @click=\"handleAudit\"\n            >\n              审核\n            </el-button>\n          </div>\n        </div>\n        \n        <!-- 项目基本信息 -->\n        <div class=\"project-summary\">\n          <el-row :gutter=\"24\">\n            <el-col :span=\"18\">\n              <div class=\"summary-info\">\n                <h3>{{ projectData.projectName }}</h3>\n                <div class=\"meta-info\">\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-folder\"></i>\n                    {{ getProjectTypeText(projectData.projectType) }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-user\"></i>\n                    {{ projectData.submitter }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-time\"></i>\n                    {{ projectData.submitTime }}\n                  </span>\n                  <span class=\"meta-item\">\n                    <i class=\"el-icon-office-building\"></i>\n                    {{ projectData.maintenanceUnit }}\n                  </span>\n                </div>\n              </div>\n            </el-col>\n            \n            <el-col :span=\"6\">\n              <div class=\"summary-status\">\n                <div class=\"status-item\">\n                  <label>当前状态:</label>\n                  <status-tag :status=\"projectData.auditStatus\" type=\"audit\" />\n                </div>\n                <div class=\"status-item\">\n                  <label>当前审核人:</label>\n                  <span>{{ projectData.currentAuditor || '-' }}</span>\n                </div>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n        \n        <!-- 标签导航 -->\n        <div class=\"tab-navigation\">\n          <div class=\"tab-container\">\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'basic' }\"\n              @click=\"switchTab('basic')\"\n            >\n              基本信息\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'tasks' }\"\n              @click=\"switchTab('tasks')\"\n            >\n              养护项目\n            </div>\n            <div \n              v-if=\"showDiseaseTab\"\n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'diseases' }\"\n              @click=\"switchTab('diseases')\"\n            >\n              病害养护\n            </div>\n            <div \n              class=\"tab-item\"\n              :class=\"{ 'is-active': activeTab === 'history' }\"\n              @click=\"switchTab('history')\"\n            >\n              审核历史\n            </div>\n          </div>\n        </div>\n        \n        <!-- 标签内容 -->\n        <div class=\"tab-content\">\n          <!-- 基本信息 -->\n          <div v-if=\"activeTab === 'basic'\" class=\"basic-info\">\n            <basic-info-view :project-data=\"projectData\" />\n          </div>\n          \n          <!-- 养护项目 -->\n          <div v-if=\"activeTab === 'tasks'\" class=\"tasks-info\">\n            <tasks-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 病害养护 -->\n          <div v-if=\"activeTab === 'diseases' && showDiseaseTab\" class=\"diseases-info\">\n            <diseases-info-view \n              :project-id=\"projectId\"\n              :project-data=\"projectData\"\n              :readonly=\"true\"\n            />\n          </div>\n          \n          <!-- 审核历史 -->\n          <div v-if=\"activeTab === 'history'\" class=\"history-info\">\n            <audit-history-view :project-id=\"projectId\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getAuditDetail } from '@/api/maintenance/audit'\nimport StatusTag from '@/components/Maintenance/StatusTag'\nimport BasicInfoView from '../../repairs/detail/components/BasicInfoView'\nimport TasksInfoView from '../../repairs/detail/components/TasksInfoView'\nimport DiseasesInfoView from '../../repairs/detail/components/DiseasesInfoView'\nimport AuditHistoryView from './components/AuditHistoryView'\n\nexport default {\n  name: 'MaintenanceAuditDetail',\n  components: {\n    StatusTag,\n    BasicInfoView,\n    TasksInfoView,\n    DiseasesInfoView,\n    AuditHistoryView\n  },\n  data() {\n    return {\n      loading: false,\n      activeTab: 'basic',\n      projectData: {},\n      projectId: ''\n    }\n  },\n  computed: {\n    // 是否显示病害养护标签\n    showDiseaseTab() {\n      return this.projectData.projectType === 'monthly'\n    },\n    \n    // 是否可以审核\n    canAudit() {\n      const userRole = this.$store.getters.userRole || 'company_admin'\n      \n      if (userRole === 'company_admin') {\n        return this.projectData.auditStatus === 'primary_audit'\n      } else if (userRole === 'bridge_center') {\n        return this.projectData.auditStatus === 'secondary_audit'\n      }\n      \n      return false\n    }\n  },\n  async created() {\n    this.projectId = this.$route.params.id\n    await this.loadProjectDetail()\n  },\n  methods: {\n    // 加载项目详情\n    async loadProjectDetail() {\n      try {\n        this.loading = true\n        const response = await getAuditDetail(this.projectId)\n        this.projectData = response.data || {}\n      } catch (error) {\n        this.$message.error('加载项目详情失败')\n        this.handleBack()\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 切换标签\n    switchTab(tab) {\n      this.activeTab = tab\n    },\n    \n    // 返回列表\n    handleBack() {\n      this.$router.push('/maintenance/audit')\n    },\n    \n    // 审核项目\n    handleAudit() {\n      this.$router.push(`/maintenance/projects/approve/${this.projectId}`)\n    },\n    \n    // 获取项目类型文本\n    getProjectTypeText(type) {\n      const typeMap = {\n        monthly: '月度养护',\n        cleaning: '保洁项目',\n        emergency: '应急养护',\n        preventive: '预防养护'\n      }\n      return typeMap[type] || type\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.page-header {\n  @extend .common-page-header;\n}\n\n.project-summary {\n  padding: 24px;\n  background: rgba(59, 130, 246, 0.1);\n  border: 1px solid rgba(59, 130, 246, 0.3);\n  border-radius: 8px;\n  margin-bottom: 24px;\n  \n  .summary-info {\n    h3 {\n      color: #ffffff;\n      font-size: 20px;\n      font-weight: bold;\n      margin-bottom: 12px;\n    }\n    \n    .meta-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 24px;\n      \n      .meta-item {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        color: #9ca3af;\n        font-size: 14px;\n        \n        i {\n          color: #3b82f6;\n        }\n      }\n    }\n  }\n  \n  .summary-status {\n    .status-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 12px;\n      \n      label {\n        color: #9ca3af;\n        min-width: 80px;\n        margin-right: 12px;\n      }\n      \n      span {\n        color: #ffffff;\n      }\n    }\n  }\n}\n\n.tab-navigation {\n  @extend .common-tab-navigation;\n}\n\n.tab-content {\n  padding: 24px;\n  min-height: 400px;\n}\n</style>\n"]}]}