{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue?vue&type=template&id=0f55505c&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue", "mtime": 1758804563531}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNoYXJ0LXdyYXBwZXIiPgogIDxkaXYgCiAgICByZWY9InJlZ2lvbkNoYXJ0IgogICAgOnN0eWxlPSJ7IHdpZHRoOiAnMTAwJScsIGhlaWdodDogaGVpZ2h0IH0iCiAgICB2LWxvYWRpbmc9ImxvYWRpbmciCiAgPjwvZGl2Pgo8L2Rpdj4K"}, null]}