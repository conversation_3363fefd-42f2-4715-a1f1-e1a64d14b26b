{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue?vue&type=template&id=745c7ae6&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\repairs\\components\\detail\\BasicInfoView.vue", "mtime": 1758810696268}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1758366989922}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}