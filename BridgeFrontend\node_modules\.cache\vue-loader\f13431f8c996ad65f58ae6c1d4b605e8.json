{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\inspection\\statistics\\components\\RegionChart.vue", "mtime": 1758804563531}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1758366987461}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["RegionChart.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RegionChart.vue", "sourceRoot": "src/views/inspection/statistics/components", "sourcesContent": ["<template>\r\n  <div class=\"chart-wrapper\">\r\n    <div \r\n      ref=\"regionChart\"\r\n      :style=\"{ width: '100%', height: height }\"\r\n      v-loading=\"loading\"\r\n    ></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'RegionChart',\r\n  props: {\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '400px'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chartInstance: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chartInstance) {\r\n      this.chartInstance.dispose()\r\n    }\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      handler() {\r\n        this.updateChart()\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化图表\r\n    initChart() {\r\n      const element = this.$refs.regionChart\r\n      if (element) {\r\n        this.chartInstance = echarts.init(element)\r\n        this.updateChart()\r\n      }\r\n    },\r\n    \r\n    // 更新图表\r\n    updateChart() {\r\n      if (!this.chartInstance) return\r\n      \r\n      const data = this.getChartData()\r\n      \r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let result = `${params[0].name}<br/>`\r\n            params.forEach(param => {\r\n              result += `${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['计划巡检', '实际巡检'],\r\n          right: '10%',\r\n          top: '2%',\r\n          textStyle: {\r\n            color: '#ffffff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '4%',\r\n          bottom: '15%', // 🔧 减少底部边距，给图表更多空间\r\n          top: '15%', // 🔧 适度增加顶部边距，为图例留出空间\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: data.regions,\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          },\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 🔧 不旋转标签，保持水平显示\r\n            color: '#ffffff',\r\n            fontSize: 11,\r\n            margin: 8, // 🔧 减少标签与轴的距离\r\n            textStyle: {\r\n              baseline: 'top'\r\n            }\r\n          },\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数量',\r\n          min: 0,\r\n          max: 200,\r\n          interval: 20,\r\n          nameTextStyle: {\r\n            color: '#ffffff'\r\n          },\r\n          axisLabel: {\r\n            formatter: '{value}',\r\n            color: '#ffffff'\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '计划巡检',\r\n            type: 'bar',\r\n            data: data.plannedData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#87CEEB'\r\n                }, {\r\n                  offset: 1, color: '#B0E0E6'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          },\r\n          {\r\n            name: '实际巡检',\r\n            type: 'bar',\r\n            data: data.actualData,\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#4682B4'\r\n                }, {\r\n                  offset: 1, color: '#5F9EA0'\r\n                }]\r\n              }\r\n            },\r\n            barWidth: '30%',\r\n            label: {\r\n              show: true,\r\n              position: 'top',\r\n              formatter: '{c}',\r\n              fontSize: 12,\r\n              color: '#ffffff'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      this.chartInstance.setOption(option, true)\r\n      \r\n      // 强制重新渲染图表\r\n      setTimeout(() => {\r\n        if (this.chartInstance) {\r\n          this.chartInstance.resize()\r\n        }\r\n      }, 100)\r\n    },\r\n    \r\n    // 获取图表数据\r\n    getChartData() {\r\n      if (!this.chartData || this.chartData.length === 0) {\r\n        return this.getDefaultData()\r\n      }\r\n      \r\n      const regions = this.chartData.map(item => item.region)\r\n      const plannedData = this.chartData.map(item => item.planned || 0)\r\n      const actualData = this.chartData.map(item => item.actual || 0)\r\n      \r\n      return { regions, plannedData, actualData }\r\n    },\r\n    \r\n    // 获取默认数据\r\n    getDefaultData() {\r\n      return {\r\n        regions: ['岳麓区', '开福区', '天心区', '芙蓉区', '雨花区'],\r\n        plannedData: [140, 180, 120, 120, 180],\r\n        actualData: [37, 128, 94, 60, 18]\r\n      }\r\n    },\r\n    \r\n    // 窗口大小变化处理\r\n    handleResize() {\r\n      if (this.chartInstance) {\r\n        this.chartInstance.resize()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 图表外框容器样式 - 与筛选区域样式一致\r\n.chart-wrapper {\r\n  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.2) !important;\r\n  border-radius: 10px !important;\r\n  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理\r\n  min-height: 200px !important; // 🔧 减少最小高度，适应flex布局\r\n  height: 100% !important; // 🔧 使用100%高度适应父容器\r\n  width: 100% !important;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局\r\n  overflow: hidden; // 🔧 确保内容不会溢出边框\r\n  \r\n  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: -1px;\r\n    right: -1px;\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #2A3B6B;\r\n    border-top-right-radius: 10px;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    border-radius: 10px;\r\n    pointer-events: none;\r\n    z-index: 2;\r\n    // 只在左上角和右下角添加亮边框，与筛选区域保持一致\r\n    background:\r\n      // 左上角亮边框\r\n      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      // 右下角亮边框\r\n      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),\r\n      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);\r\n    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;\r\n    background-position: top left, top left, bottom right, bottom right;\r\n    background-repeat: no-repeat;\r\n  }\r\n}\r\n\r\n// 图表内容容器样式\r\ndiv[ref=\"regionChart\"] {\r\n  position: relative;\r\n  z-index: 3; // 确保图表在伪元素之上\r\n  width: 100% !important;\r\n  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%\r\n  min-height: 200px; // 🔧 减少最小高度，适应更紧凑的布局\r\n}\r\n</style>\r\n"]}]}