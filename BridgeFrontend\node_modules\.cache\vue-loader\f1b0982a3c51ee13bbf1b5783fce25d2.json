{"remainingRequest": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue?vue&type=style&index=0&id=9856d310&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\src\\views\\maintenance\\audit\\index.vue", "mtime": 1758810696259}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1758366986010}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1758366989807}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1758366987454}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1758366984757}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1758366984756}, {"path": "D:\\Work\\qiao\\BB\\BridgeFrontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1758366988606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICdAL3N0eWxlcy9pbnNwZWN0aW9uLXRoZW1lLnNjc3MnOwpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvbWFpbnRlbmFuY2UtdGhlbWUuc2Nzcyc7CgoubWFpbnRlbmFuY2UtYXVkaXQtcGFnZSB7CiAgLy8g5qC35byP5bey6YCa6L+H5Li76aKY5paH5Lu25o+Q5L6bCiAgCiAgLy8g5L2/55So5YWs5YWx54q25oCBVGFi5a+86Iiq5qC35byPCiAgLnNlY29uZGFyeS10YWItbmF2aWdhdGlvbiB7CiAgICBAZXh0ZW5kIC5jb21tb24tc3RhdHVzLXRhYi1uYXZpZ2F0aW9uOwogICAgCiAgICAuc2Vjb25kYXJ5LXRhYnMgewogICAgICBAZXh0ZW5kIC5zdGF0dXMtdGFiczsKICAgIH0KICB9Cn0KCi8vIEVsZW1lbnQgVUnkuIvmi4npgInpobnmoLflvI/opobnm5YKOmRlZXAoLmVsLXNlbGVjdC1kcm9wZG93bikgewogIGJhY2tncm91bmQ6ICMzNzQxNTE7CiAgYm9yZGVyOiAxcHggc29saWQgIzRiNTU2MzsKICAKICAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtIHsKICAgIGNvbG9yOiAjZmZmZmZmOwogICAgCiAgICAmOmhvdmVyIHsKICAgICAgYmFja2dyb3VuZDogIzRiNTU2MzsKICAgIH0KICAgIAogICAgJi5zZWxlY3RlZCB7CiAgICAgIGJhY2tncm91bmQ6ICMzYjgyZjY7CiAgICAgIGNvbG9yOiAjZmZmZmZmOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAweA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/maintenance/audit", "sourcesContent": ["<template>\n  <div class=\"maintenance-audit-page maintenance-theme inspection-container\">\n    <div class=\"page-container\">\n      <!-- TAB切换 -->\n      <div class=\"inspection-tabs\">\n        <TabSwitch\n          v-model=\"infrastructureType\"\n          :tabs=\"tabOptions\"\n          @tab-click=\"handleTabClick\"\n        />\n      </div>\n\n      <!-- 审核状态标签 -->\n      <div class=\"secondary-tab-navigation\">\n        <div class=\"secondary-tabs\">\n          <div \n            class=\"tab-item\"\n            :class=\"{ 'is-active': auditStatus === 'pending' }\"\n            @click=\"switchAuditStatus('pending')\"\n          >\n            <i class=\"el-icon-clock\"></i>\n            待审核\n          </div>\n          <div \n            class=\"tab-item\"\n            :class=\"{ 'is-active': auditStatus === 'completed' }\"\n            @click=\"switchAuditStatus('completed')\"\n          >\n            <i class=\"el-icon-check\"></i>\n            已审核\n          </div>\n        </div>\n      </div>\n\n      <!-- 筛选表单 -->\n      <div class=\"filter-section\">\n        <div class=\"filter-content\">\n          <el-input\n            v-model=\"queryParams.keyword\"\n            placeholder=\"搜索审核项目、发起人\"\n            clearable\n            class=\"filter-select\"\n          />\n\n          <el-select\n            v-model=\"queryParams.auditItem\"\n            placeholder=\"审核项\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部审核项\" value=\"\" />\n            <el-option \n              v-for=\"item in auditItems\" \n              :key=\"item.value\" \n              :label=\"item.label\" \n              :value=\"item.value\" \n            />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.initiator\"\n            placeholder=\"发起人\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部发起人\" value=\"\" />\n            <el-option \n              v-for=\"user in initiators\" \n              :key=\"user.value\" \n              :label=\"user.label\" \n              :value=\"user.value\" \n            />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.initiateTime\"\n            placeholder=\"发起时间\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部时间\" value=\"\" />\n            <el-option label=\"今天\" value=\"today\" />\n            <el-option label=\"本周\" value=\"week\" />\n            <el-option label=\"本月\" value=\"month\" />\n          </el-select>\n          \n          <el-select\n            v-model=\"queryParams.unit\"\n            placeholder=\"单位\"\n            clearable\n            class=\"filter-select\"\n          >\n            <el-option label=\"全部单位\" value=\"\" />\n            <el-option \n              v-for=\"unit in units\" \n              :key=\"unit.value\" \n              :label=\"unit.label\" \n              :value=\"unit.value\" \n            />\n          </el-select>\n          \n          <div class=\"filter-actions\">\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\n            <el-button @click=\"resetQuery\">重置</el-button>\n          </div>\n        </div>\n      </div>\n        \n      <!-- 数据表格 -->\n      <div class=\"inspection-table common-table\">\n        <el-table\n          v-loading=\"loading\"\n          :data=\"auditList\"\n          class=\"audit-table\"\n          stripe\n        >\n          <el-table-column \n            prop=\"serialNumber\" \n            label=\"序号\" \n            width=\"80\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"auditItem\" \n            label=\"审核项\" \n            min-width=\"200\"\n            show-overflow-tooltip \n          />\n          \n          <el-table-column \n            prop=\"initiator\" \n            label=\"发起人\" \n            width=\"120\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"initiateTime\" \n            label=\"发起时间\" \n            width=\"180\" \n            align=\"center\" \n          />\n          \n          <el-table-column \n            prop=\"unit\" \n            label=\"单位\" \n            min-width=\"180\"\n            show-overflow-tooltip \n          />\n          \n          <el-table-column \n            label=\"操作\" \n            width=\"100\" \n            align=\"center\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"mini\"\n                @click=\"auditStatus === 'pending' ? handleAudit(scope.row) : handleView(scope.row)\"\n              >\n                {{ auditStatus === 'pending' ? '审核' : '查看' }}\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n        \n      <!-- 分页 -->\n      <div class=\"inspection-pagination\">\n        <el-pagination\n          :current-page=\"queryParams.pageNum\"\n          :page-sizes=\"[2, 5, 10, 20]\"\n          :page-size=\"queryParams.pageSize\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          small\n        />\n      </div>\n    </div>\n\n    <!-- 审批弹框 -->\n    <ApprovalDialog\n      :visible.sync=\"approvalDialogVisible\"\n      :approval-data=\"currentApprovalData\"\n      :readonly=\"currentApprovalData.readonly || false\"\n      @approve=\"handleApprovalSuccess\"\n      @reject=\"handleApprovalSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport ApprovalDialog from './components/ApprovalDialog.vue'\nimport TabSwitch from '@/components/Inspection/TabSwitch'\n\nexport default {\n  name: 'MaintenanceAudit',\n  components: {\n    ApprovalDialog,\n    TabSwitch\n  },\n  data() {\n    return {\n      loading: false,\n      infrastructureType: 'bridge', // bridge, tunnel\n      auditStatus: 'pending', // pending, completed\n      auditList: [],\n      total: 0,\n      \n      // TAB选项\n      tabOptions: [\n        {\n          name: 'bridge',\n          label: '桥梁养护维修审核',\n          icon: 'bridge-icon'\n        },\n        {\n          name: 'tunnel',\n          label: '隧道养护维修审核',\n          icon: 'tunnel-icon'\n        }\n      ],\n      \n      // 审批弹框相关\n      approvalDialogVisible: false,\n      currentApprovalData: {},\n      \n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 2, // 设置小的分页大小以便看到分页组件\n        keyword: '',\n        auditItem: '',\n        initiator: '',\n        initiateTime: '',\n        unit: ''\n      },\n      \n      // 审核项选项\n      auditItems: [\n        { label: 'XXXXXX项目', value: 'project_1' },\n        { label: 'YYYYYY项目', value: 'project_2' },\n        { label: 'ZZZZZZ项目', value: 'project_3' }\n      ],\n      \n      // 发起人选项\n      initiators: [\n        { label: '周文革', value: 'zhouwenge' },\n        { label: '罗子安', value: 'luozian' },\n        { label: '江辞舟', value: 'jiangcizhou' },\n        { label: '朱建军', value: 'zhujianjun' },\n        { label: '王若峰', value: 'wangruofeng' },\n        { label: '林雨欣', value: 'linyuxin' },\n        { label: '郑辰逸', value: 'zhengchenyi' },\n        { label: '张格然', value: 'zhanggeran' },\n        { label: '李建军', value: 'lijianjun' },\n        { label: '赵晓红', value: 'zhaoxiaohong' },\n        { label: '黄淑芬', value: 'huangshufen' },\n        { label: '陈丽华', value: 'chenlihua' }\n      ],\n      \n      // 单位选项\n      units: [\n        { label: '长沙市桥梁管理处', value: 'changsha_bridge_mgmt' },\n        { label: '长沙市隧道管理处', value: 'changsha_tunnel_mgmt' },\n        { label: '湖南省交通厅', value: 'hunan_transport_dept' }\n      ],\n\n      // 静态测试数据 - 减少数据量以便看到分页组件\n      mockAuditData: {\n        bridge: {\n          pending: [\n            {\n              id: '001',\n              serialNumber: '001',\n              auditItem: 'XXXXXX项目',\n              initiator: '周文革',\n              initiateTime: '2025/09/01 16:16',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '002',\n              serialNumber: '002',\n              auditItem: 'YYYYYY项目',\n              initiator: '罗子安',\n              initiateTime: '2025/09/01 15:30',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '003',\n              serialNumber: '003',\n              auditItem: 'ZZZZZZ项目',\n              initiator: '江辞舟',\n              initiateTime: '2025/09/01 14:45',\n              unit: '长沙市桥梁管理处'\n            }\n          ],\n          completed: [\n            {\n              id: '004',\n              serialNumber: '004',\n              auditItem: 'AAAAAA项目',\n              initiator: '朱建军',\n              initiateTime: '2025/08/28 14:30',\n              unit: '长沙市桥梁管理处'\n            },\n            {\n              id: '005',\n              serialNumber: '005',\n              auditItem: 'BBBBBB项目',\n              initiator: '王若峰',\n              initiateTime: '2025/08/25 10:15',\n              unit: '长沙市桥梁管理处'\n            }\n          ]\n        },\n        tunnel: {\n          pending: [\n            {\n              id: '101',\n              serialNumber: '001',\n              auditItem: 'CCCCCC隧道项目',\n              initiator: '林雨欣',\n              initiateTime: '2025/09/01 15:30',\n              unit: '长沙市隧道管理处'\n            },\n            {\n              id: '102',\n              serialNumber: '002',\n              auditItem: 'DDDDDD隧道项目',\n              initiator: '郑辰逸',\n              initiateTime: '2025/09/01 14:20',\n              unit: '长沙市隧道管理处'\n            }\n          ],\n          completed: [\n            {\n              id: '103',\n              serialNumber: '003',\n              auditItem: 'EEEEEE隧道项目',\n              initiator: '张格然',\n              initiateTime: '2025/08/30 16:45',\n              unit: '长沙市隧道管理处'\n            }\n          ]\n        }\n      }\n    }\n  },\n  created() {\n    this.loadAuditList()\n  },\n  methods: {\n    // 获取审核列表\n    loadAuditList() {\n      this.loading = true\n      \n      // 获取当前数据源\n      const currentData = this.mockAuditData[this.infrastructureType][this.auditStatus]\n      \n      // 应用筛选条件\n      let filteredData = [...currentData]\n      \n      // 关键词搜索\n      if (this.queryParams.keyword) {\n        const keyword = this.queryParams.keyword.toLowerCase()\n        filteredData = filteredData.filter(item => \n          item.auditItem.toLowerCase().includes(keyword) ||\n          item.initiator.toLowerCase().includes(keyword) ||\n          item.unit.toLowerCase().includes(keyword)\n        )\n      }\n      \n      if (this.queryParams.auditItem) {\n        filteredData = filteredData.filter(item => \n          item.auditItem.includes(this.queryParams.auditItem) ||\n          this.auditItems.find(opt => opt.value === this.queryParams.auditItem)?.label === item.auditItem\n        )\n      }\n      \n      if (this.queryParams.initiator) {\n        filteredData = filteredData.filter(item => \n          item.initiator === this.queryParams.initiator ||\n          this.initiators.find(opt => opt.value === this.queryParams.initiator)?.label === item.initiator\n        )\n      }\n      \n      if (this.queryParams.unit) {\n        filteredData = filteredData.filter(item => \n          item.unit.includes(this.queryParams.unit) ||\n          this.units.find(opt => opt.value === this.queryParams.unit)?.label === item.unit\n        )\n      }\n      \n      // 分页处理\n      this.total = filteredData.length\n      const startIndex = (this.queryParams.pageNum - 1) * this.queryParams.pageSize\n      const endIndex = startIndex + this.queryParams.pageSize\n      \n      // 模拟异步加载\n      setTimeout(() => {\n        this.auditList = filteredData.slice(startIndex, endIndex)\n        this.loading = false\n      }, 300)\n    },\n    \n    // TAB切换处理\n    handleTabClick(tab) {\n      this.infrastructureType = tab.name\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 切换基础设施类型\n    switchInfrastructureType(type) {\n      this.infrastructureType = type\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 切换审核状态\n    switchAuditStatus(status) {\n      this.auditStatus = status\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 查询\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 重置查询\n    resetQuery() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 2, // 保持与初始设置一致\n        keyword: '',\n        auditItem: '',\n        initiator: '',\n        initiateTime: '',\n        unit: ''\n      }\n      this.loadAuditList()\n    },\n    \n    // 审核项目\n    handleAudit(row) {\n      this.currentApprovalData = row\n      this.approvalDialogVisible = true\n    },\n    \n    // 查看项目（已审核状态）\n    handleView(row) {\n      // 对于已审核的项目，以只读模式打开审批弹框\n      this.currentApprovalData = { ...row, readonly: true }\n      this.approvalDialogVisible = true\n    },\n    \n    // 审批操作成功\n    handleApprovalSuccess(data) {\n      this.$message.success(`审批操作成功`)\n      // 刷新列表\n      this.loadAuditList()\n    },\n    \n    // 分页大小变化\n    handleSizeChange(val) {\n      this.queryParams.pageSize = val\n      this.queryParams.pageNum = 1\n      this.loadAuditList()\n    },\n    \n    // 当前页变化\n    handleCurrentChange(val) {\n      this.queryParams.pageNum = val\n      this.loadAuditList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/styles/inspection-theme.scss';\n@import '@/assets/styles/maintenance-theme.scss';\n\n.maintenance-audit-page {\n  // 样式已通过主题文件提供\n  \n  // 使用公共状态Tab导航样式\n  .secondary-tab-navigation {\n    @extend .common-status-tab-navigation;\n    \n    .secondary-tabs {\n      @extend .status-tabs;\n    }\n  }\n}\n\n// Element UI下拉选项样式覆盖\n:deep(.el-select-dropdown) {\n  background: #374151;\n  border: 1px solid #4b5563;\n  \n  .el-select-dropdown__item {\n    color: #ffffff;\n    \n    &:hover {\n      background: #4b5563;\n    }\n    \n    &.selected {\n      background: #3b82f6;\n      color: #ffffff;\n    }\n  }\n}\n</style>\n"]}]}