/**
 * 应急处置模块常量配置
 * 包含所有下拉选项、状态映射、默认值等配置
 */

// ================================
// 事件阶段配置
// ================================
export const EVENT_STAGES = {
  REPORTED: 'reported',      // 已上报
  PENDING: 'pending',        // 待启动
  INPROGRESS: 'inprogress',  // 进行中
  FINISHED: 'finished',      // 已结束
  ARCHIVED: 'archived'       // 已归档
}

export const EVENT_STAGE_LABELS = {
  [EVENT_STAGES.REPORTED]: '已上报',
  [EVENT_STAGES.PENDING]: '待启动',
  [EVENT_STAGES.INPROGRESS]: '进行中',
  [EVENT_STAGES.FINISHED]: '已结束',
  [EVENT_STAGES.ARCHIVED]: '已归档'
}

// ================================
// 结构类型配置
// ================================
export const STRUCTURE_TYPES = {
  BRIDGE: 'bridge',  // 桥梁
  TUNNEL: 'tunnel'   // 隧道
}

export const STRUCTURE_TYPE_LABELS = {
  [STRUCTURE_TYPES.BRIDGE]: '桥梁',
  [STRUCTURE_TYPES.TUNNEL]: '隧道'
}

// ================================
// 事件类型配置
// ================================
export const EVENT_TYPES = [
  { value: '1', label: '桥梁隧道结构性损坏' },
  { value: '2', label: '桥梁隧道恶化老化物落' },
  { value: '3', label: '桥梁隧道灾门坍塌' },
  { value: '4', label: '桥梁隧道交通事故' },
  { value: '5', label: '自然灾害影响' },
  { value: '6', label: '突发公共卫生事件' },
  { value: '7', label: '社会安全事件' },
  { value: '8', label: '其他突发事件' }
]

// ================================
// 事件分类配置
// ================================
export const EVENT_CATEGORIES = [
  { value: '1', label: '自然灾害' },
  { value: '2', label: '事故灾难' },
  { value: '3', label: '公共卫生事件' },
  { value: '4', label: '社会安全事件' }
]

export const EVENT_CATEGORY_DESCRIPTIONS = {
  '1': '地震、洪涝、台风、地质灾害等自然因素引发的事件',
  '2': '生产安全事故、交通运输事故、环境污染等事故灾难',
  '3': '传染病疫情、群体性不明原因疾病等公共卫生事件',
  '4': '恐怖袭击、群体性事件等危害公共安全的事件'
}

// ================================
// 事件等级配置
// ================================
export const EVENT_LEVELS = [
  { value: '1', label: '较小事件' },
  { value: '2', label: '一般事件' },
  { value: '3', label: '较大事件' },
  { value: '4', label: '重大事件' },
  { value: '5', label: '特别重大事件' }
]

export const EVENT_LEVEL_DESCRIPTIONS = {
  '1': '影响范围小，损失较轻，可由市级部门处置',
  '2': '影响范围一般，损失中等，需要市级统一协调处置',
  '3': '影响范围大，损失严重，需要省市联合处置',
  '4': '影响范围很大，损失重大，需要国家层面统筹处置',
  '5': '影响范围极大，损失特别重大，需要国家最高层面处置'
}

// ================================
// 应急责任单位配置
// ================================
export const RESPONSIBLE_UNITS = [
  { value: '1', label: '市桥隧事务中心' },
  { value: '2', label: '市城管局' },
  { value: '3', label: '市应急和安全生产委员会' },
  { value: '4', label: '市政府' },
  { value: '5', label: '省应急管理厅' }
]

// 事件等级与应急责任单位的联动关系
export const LEVEL_UNIT_MAPPING = {
  '1': '1', // 较小事件 -> 市桥隧事务中心
  '2': '2', // 一般事件 -> 市城管局
  '3': '3', // 较大事件 -> 市应急和安全生产委员会
  '4': '3', // 重大事件 -> 市应急和安全生产委员会
  '5': '5'  // 特别重大事件 -> 省应急管理厅
}

// ================================
// 设施损坏程度配置
// ================================
export const FACILITY_DAMAGE_LEVELS = [
  { value: '1', label: '轻微' },
  { value: '2', label: '一般' },
  { value: '3', label: '严重' },
  { value: '4', label: '特别严重' }
]

// ================================
// 事态控制选项配置
// ================================
export const CONTROL_ACTIONS = [
  { value: '1', label: '解除响应' },
  { value: '2', label: '响应变更' },
  { value: '3', label: '升级响应' },
  { value: '4', label: '降级响应' }
]

// ================================
// 配置管理相关配置
// ================================

// 预案级别配置
export const PLAN_LEVELS = [
  { value: '1', label: '长沙市政府' },
  { value: '2', label: '长沙市应急和安全生产委员会' },
  { value: '3', label: '长沙市城管局' },
  { value: '4', label: '市桥隧事务中心' },
  { value: '5', label: '桥隧公司' },
  { value: '6', label: '项目部' }
]

// 单位配置（用于通讯录）
export const CONTACT_UNITS = [
  { value: '1', label: '市城管局' },
  { value: '2', label: '市桥隧事务中心' },
  { value: '3', label: '桥隧公司' },
  { value: '4', label: '市应急和安全生产委员会' },
  { value: '5', label: '交警支队' },
  { value: '6', label: '消防救援支队' },
  { value: '7', label: '医疗急救中心' },
  { value: '8', label: '供电公司' },
  { value: '9', label: '燃气公司' },
  { value: '10', label: '自来水公司' }
]

// 专家专业配置
export const EXPERT_SPECIALTIES = [
  { value: '1', label: '土木工程' },
  { value: '2', label: '桥梁工程' },
  { value: '3', label: '隧道工程' },
  { value: '4', label: '结构工程' },
  { value: '5', label: '道路工程' },
  { value: '6', label: '岩土工程' },
  { value: '7', label: '交通工程' },
  { value: '8', label: '安全工程' },
  { value: '9', label: '环境工程' },
  { value: '10', label: '水利工程' }
]

// 学历配置
export const EDUCATION_LEVELS = [
  { value: '1', label: '博士' },
  { value: '2', label: '硕士' },
  { value: '3', label: '本科' },
  { value: '4', label: '专科' },
  { value: '5', label: '其他' }
]

// 性别配置
export const GENDERS = [
  { value: '1', label: '男' },
  { value: '2', label: '女' }
]

// ================================
// 默认值配置
// ================================
export const DEFAULT_VALUES = {
  // 事件默认值
  EVENT: {
    injuredCount: 0,
    deathCount: 0,
    facilityDamage: '1', // 轻微
    sceneDescription: '未造成交通瓶颈，桥梁通行情况正常',
    eventCategory: '1',  // 自然灾害
    eventLevel: '1',     // 较小事件
    responsibleUnit: '1', // 市桥隧事务中心
    needReport: true
  },
  
  // 续报默认值
  REPORT: {
    causeAnalysis: '明应巴解制，交通已恢复',
    disposalOpinion: '明应巴解制，交通已恢复'
  },
  
  // 分页默认值
  PAGINATION: {
    current: 1,
    size: 10,
    pageSizes: [10, 20, 50, 100]
  }
}

// ================================
// 文件上传配置
// ================================
export const FILE_UPLOAD = {
  // 图片上传配置
  IMAGE: {
    accept: 'image/*',
    maxSize: 5, // MB
    formats: ['jpg', 'jpeg', 'png', 'gif']
  },
  
  // 文档上传配置
  DOCUMENT: {
    accept: '.pdf,.doc,.docx',
    maxSize: 20, // MB
    formats: ['pdf', 'doc', 'docx']
  },
  
  // Excel上传配置
  EXCEL: {
    accept: '.xlsx,.xls',
    maxSize: 5, // MB
    formats: ['xlsx', 'xls']
  }
}

// ================================
// 表单验证规则
// ================================
export const VALIDATION_RULES = {
  // 手机号验证
  PHONE: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  
  // 必填项验证
  REQUIRED: {
    required: true,
    trigger: 'blur'
  },
  
  // 选择项验证
  SELECT_REQUIRED: {
    required: true,
    trigger: 'change'
  }
}

// ================================
// API路径配置
// ================================
export const API_PATHS = {
  // 事件管理
  EVENTS: '/api/emergency/events',
  EVENT_STATS: '/api/emergency/events/stats',
  
  // 配置管理
  PROCESS: '/api/emergency/config/process',
  PLANS: '/api/emergency/config/plans',
  CONTACTS: '/api/emergency/config/contacts',
  EXPERTS: '/api/emergency/config/experts',
  EQUIPMENT_DEPARTMENTS: '/api/emergency/config/equipment/departments',
  EQUIPMENT: '/api/emergency/config/equipment',
  
  // 基础数据
  OPTIONS_EVENT_TYPES: '/api/emergency/options/event-types',
  OPTIONS_STRUCTURES: '/api/emergency/options/structures',
  OPTIONS_CONTACTS: '/api/emergency/options/contacts'
}

// ================================
// 消息提示配置
// ================================
export const MESSAGES = {
  SUCCESS: {
    CREATE: '创建成功',
    UPDATE: '更新成功',
    DELETE: '删除成功',
    SUBMIT: '提交成功',
    IMPORT: '导入成功',
    EXPORT: '导出成功'
  },
  
  ERROR: {
    CREATE: '创建失败',
    UPDATE: '更新失败',
    DELETE: '删除失败',
    SUBMIT: '提交失败',
    IMPORT: '导入失败',
    EXPORT: '导出失败',
    LOAD: '加载失败',
    NETWORK: '网络请求失败，请稍后重试'
  },
  
  CONFIRM: {
    DELETE: '确认要删除此项吗？',
    SUBMIT: '确认要提交吗？',
    CANCEL: '确认要取消吗？'
  }
}

// ================================
// 工具函数
// ================================

/**
 * 根据值获取标签
 * @param {Array} options 选项数组
 * @param {string} value 值
 * @returns {string} 标签
 */
export function getLabelByValue(options, value) {
  const option = options.find(item => item.value === value)
  return option ? option.label : value
}

/**
 * 根据事件等级获取应急责任单位
 * @param {string} level 事件等级
 * @returns {string} 应急责任单位值
 */
export function getResponsibleUnitByLevel(level) {
  return LEVEL_UNIT_MAPPING[level] || '1'
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(size) {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }
}

/**
 * 生成事件编码
 * @param {string} structureType 结构类型
 * @param {Date} date 日期
 * @param {number} sequence 序号
 * @returns {string} 事件编码
 */
export function generateEventCode(structureType, date = new Date(), sequence = 1) {
  const prefix = structureType === STRUCTURE_TYPES.BRIDGE ? 'Q' : 'S'
  const dateStr = date.getFullYear().toString() + 
                  (date.getMonth() + 1).toString().padStart(2, '0') + 
                  date.getDate().toString().padStart(2, '0')
  const seqStr = sequence.toString().padStart(3, '0')
  return `${prefix}${dateStr}${seqStr}`
}

/**
 * 验证文件类型
 * @param {File} file 文件对象
 * @param {Array} allowedTypes 允许的文件类型
 * @returns {boolean} 是否有效
 */
export function validateFileType(file, allowedTypes) {
  const fileExtension = file.name.split('.').pop().toLowerCase()
  return allowedTypes.includes(fileExtension)
}

/**
 * 验证文件大小
 * @param {File} file 文件对象
 * @param {number} maxSizeMB 最大大小（MB）
 * @returns {boolean} 是否有效
 */
export function validateFileSize(file, maxSizeMB) {
  const fileSizeMB = file.size / (1024 * 1024)
  return fileSizeMB <= maxSizeMB
}

export default {
  EVENT_STAGES,
  EVENT_STAGE_LABELS,
  STRUCTURE_TYPES,
  STRUCTURE_TYPE_LABELS,
  EVENT_TYPES,
  EVENT_CATEGORIES,
  EVENT_CATEGORY_DESCRIPTIONS,
  EVENT_LEVELS,
  EVENT_LEVEL_DESCRIPTIONS,
  RESPONSIBLE_UNITS,
  LEVEL_UNIT_MAPPING,
  FACILITY_DAMAGE_LEVELS,
  CONTROL_ACTIONS,
  PLAN_LEVELS,
  CONTACT_UNITS,
  EXPERT_SPECIALTIES,
  EDUCATION_LEVELS,
  GENDERS,
  DEFAULT_VALUES,
  FILE_UPLOAD,
  VALIDATION_RULES,
  API_PATHS,
  MESSAGES,
  getLabelByValue,
  getResponsibleUnitByLevel,
  formatFileSize,
  generateEventCode,
  validateFileType,
  validateFileSize
}





