/**
 * 应急处置模块API接口
 * 包含事件管理和配置管理相关的所有接口
 */

import request from '@/api/request'

// ================================
// 事件管理相关接口
// ================================

/**
 * 获取事件列表
 * @param {Object} params 查询参数
 * @param {string} params.stage 事件阶段: reported|pending|inprogress|finished|archived
 * @param {string} params.structureType 结构类型: bridge|tunnel
 * @param {string} params.eventType 事件类型
 * @param {string} params.eventLevel 事件等级
 * @param {string} params.eventCategory 事件分类
 * @param {string} params.responsibleUnit 应急责任单位
 * @param {Array} params.dateRange 日期范围
 * @param {number} params.page 页码
 * @param {number} params.size 页大小
 */
export function getEventList(params) {
  return request({
    url: '/api/emergency/events',
    method: 'get',
    params
  }).catch(() => {
    // API失败时返回模拟数据
    return mockGetEventList(params)
  })
}

/**
 * 获取事件详情
 * @param {number} id 事件ID
 */
export function getEventDetail(id) {
  return request({
    url: `/api/emergency/events/${id}`,
    method: 'get'
  }).catch(() => {
    return mockGetEventDetail(id)
  })
}

/**
 * 创建事件
 * @param {Object} data 事件数据
 */
export function createEvent(data) {
  return request({
    url: '/api/emergency/events',
    method: 'post',
    data
  }).catch(() => {
    return mockCreateEvent(data)
  })
}

/**
 * 事件研判
 * @param {number} id 事件ID
 * @param {Object} data 研判数据
 */
export function assessEvent(id, data) {
  return request({
    url: `/api/emergency/events/${id}/assessment`,
    method: 'put',
    data
  }).catch(() => {
    return mockAssessEvent(id, data)
  })
}

/**
 * 事件续报
 * @param {number} id 事件ID
 * @param {Object} data 续报数据
 */
export function reportEvent(id, data) {
  return request({
    url: `/api/emergency/events/${id}/reports`,
    method: 'post',
    data
  }).catch(() => {
    return mockReportEvent(id, data)
  })
}

/**
 * 事态控制
 * @param {number} id 事件ID
 * @param {Object} data 控制数据
 */
export function controlEvent(id, data) {
  return request({
    url: `/api/emergency/events/${id}/control`,
    method: 'put',
    data
  }).catch(() => {
    return mockControlEvent(id, data)
  })
}

/**
 * 获取事件阶段统计
 */
export function getEventStageStats() {
  return request({
    url: '/api/emergency/events/stats',
    method: 'get'
  }).catch(() => {
    return mockGetEventStageStats()
  })
}

/**
 * 删除事件
 * @param {number} id 事件ID
 */
export function deleteEvent(id) {
  return request({
    url: `/api/emergency/events/${id}`,
    method: 'delete'
  }).catch(() => {
    return mockDeleteEvent(id)
  })
}

// ================================
// 配置管理相关接口
// ================================

/**
 * 获取流程图列表
 */
export function getProcessList(params) {
  return request({
    url: '/api/emergency/config/process',
    method: 'get',
    params
  }).catch(() => {
    return mockGetProcessList(params)
  })
}

/**
 * 创建流程图
 */
export function createProcess(data) {
  return request({
    url: '/api/emergency/config/process',
    method: 'post',
    data
  }).catch(() => {
    return mockCreateProcess(data)
  })
}

/**
 * 更新流程图
 */
export function updateProcess(id, data) {
  return request({
    url: `/api/emergency/config/process/${id}`,
    method: 'put',
    data
  }).catch(() => {
    return mockUpdateProcess(id, data)
  })
}

/**
 * 删除流程图
 */
export function deleteProcess(id) {
  return request({
    url: `/api/emergency/config/process/${id}`,
    method: 'delete'
  }).catch(() => {
    return mockDeleteProcess(id)
  })
}

/**
 * 获取预案列表
 */
export function getPlansList(params) {
  return request({
    url: '/api/emergency/config/plans',
    method: 'get',
    params
  }).catch(() => {
    return mockGetPlansList(params)
  })
}

/**
 * 创建预案
 */
export function createPlan(data) {
  return request({
    url: '/api/emergency/config/plans',
    method: 'post',
    data
  }).catch(() => {
    return mockCreatePlan(data)
  })
}

/**
 * 获取通讯录列表
 */
export function getContactsList(params) {
  return request({
    url: '/api/emergency/config/contacts',
    method: 'get',
    params
  }).catch(() => {
    return mockGetContactsList(params)
  })
}

/**
 * 创建联系人
 */
export function createContact(data) {
  return request({
    url: '/api/emergency/config/contacts',
    method: 'post',
    data
  }).catch(() => {
    return mockCreateContact(data)
  })
}

/**
 * 获取专家列表
 */
export function getExpertsList(params) {
  return request({
    url: '/api/emergency/config/experts',
    method: 'get',
    params
  }).catch(() => {
    return mockGetExpertsList(params)
  })
}

/**
 * 创建专家
 */
export function createExpert(data) {
  return request({
    url: '/api/emergency/config/experts',
    method: 'post',
    data
  }).catch(() => {
    return mockCreateExpert(data)
  })
}

/**
 * 获取设备项目部列表
 */
export function getDepartmentList(params) {
  return request({
    url: '/api/emergency/config/equipment/departments',
    method: 'get',
    params
  }).catch(() => {
    return mockGetDepartmentList(params)
  })
}

/**
 * 获取设备列表
 */
export function getEquipmentList(params) {
  return request({
    url: '/api/emergency/config/equipment',
    method: 'get',
    params
  }).catch(() => {
    return mockGetEquipmentList(params)
  })
}

// ================================
// 基础数据接口
// ================================

/**
 * 获取事件类型选项
 */
export function getEventTypeOptions() {
  return request({
    url: '/api/emergency/options/event-types',
    method: 'get'
  }).catch(() => {
    return mockGetEventTypeOptions()
  })
}

/**
 * 获取桥梁/隧道列表
 */
export function getStructureList(type) {
  return request({
    url: '/api/emergency/options/structures',
    method: 'get',
    params: { type }
  }).catch(() => {
    return mockGetStructureList(type)
  })
}

/**
 * 获取应急通讯录（用于人员选择）
 */
export function getEmergencyContacts() {
  return request({
    url: '/api/emergency/options/contacts',
    method: 'get'
  }).catch(() => {
    return mockGetEmergencyContacts()
  })
}

// ================================
// 模拟数据方法
// ================================

/**
 * 模拟获取事件列表
 */
function mockGetEventList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = generateMockEventData(params.stage, params.structureType)
      resolve({
        code: 200,
        data: {
          records: mockData,
          total: mockData.length,
          current: params.page || 1,
          size: params.size || 10
        },
        message: '获取成功'
      })
    }, 500)
  })
}

/**
 * 模拟获取事件详情
 */
function mockGetEventDetail(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          id,
          eventName: 'XXX大桥突发事件',
          structureName: 'XXX大桥',
          location: 'XXX位置',
          triggerTime: '2025/07/15 11:27',
          eventType: '桥梁隧道结构性损坏',
          injuredCount: 0,
          deathCount: 0,
          facilityDamage: '轻微',
          sceneDescription: '未造成交通瓶颈，桥梁通行情况正常',
          createTime: '2025/07/15 11:27',
          createUser: '孙明华'
        },
        message: '获取成功'
      })
    }, 300)
  })
}

/**
 * 模拟创建事件
 */
function mockCreateEvent(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟创建事件:', data)
      resolve({
        code: 200,
        data: { id: Date.now() },
        message: '创建成功'
      })
    }, 1000)
  })
}

/**
 * 模拟事件研判
 */
function mockAssessEvent(id, data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟事件研判:', id, data)
      resolve({
        code: 200,
        data: { success: true },
        message: '研判成功'
      })
    }, 1000)
  })
}

/**
 * 模拟事件续报
 */
function mockReportEvent(id, data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟事件续报:', id, data)
      resolve({
        code: 200,
        data: { success: true },
        message: '续报成功'
      })
    }, 1000)
  })
}

/**
 * 模拟事态控制
 */
function mockControlEvent(id, data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟事态控制:', id, data)
      resolve({
        code: 200,
        data: { success: true },
        message: '控制成功'
      })
    }, 1000)
  })
}

/**
 * 模拟获取事件阶段统计
 */
function mockGetEventStageStats() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          reported: 10,
          pending: 6,
          inprogress: 3,
          finished: 2,
          archived: 8
        },
        message: '获取成功'
      })
    }, 300)
  })
}

/**
 * 模拟删除事件
 */
function mockDeleteEvent(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟删除事件:', id)
      resolve({
        code: 200,
        data: { success: true },
        message: '删除成功'
      })
    }, 500)
  })
}

// 配置管理模拟方法
function mockGetProcessList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          eventType: '桥梁隧道结构性损坏',
          fileName: '桥梁结构损坏处置流程.pdf',
          fileUrl: '/files/process1.pdf',
          operator: '张梓浩',
          operateTime: '2025-08-19 12:03:30'
        }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 500)
  })
}

function mockCreateProcess(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟创建流程:', data)
      resolve({
        code: 200,
        data: { id: Date.now() },
        message: '创建成功'
      })
    }, 1000)
  })
}

function mockUpdateProcess(id, data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟更新流程:', id, data)
      resolve({
        code: 200,
        data: { success: true },
        message: '更新成功'
      })
    }, 1000)
  })
}

function mockDeleteProcess(id) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟删除流程:', id)
      resolve({
        code: 200,
        data: { success: true },
        message: '删除成功'
      })
    }, 500)
  })
}

function mockGetPlansList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          level: '长沙市政府',
          title: '长沙市突发事件应急预案',
          fileName: '长沙市突发事件应急预案.pdf',
          fileUrl: '/files/plan1.pdf',
          operator: '张梓浩',
          operateTime: '2025-08-19 12:03:30'
        }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 500)
  })
}

function mockCreatePlan(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟创建预案:', data)
      resolve({
        code: 200,
        data: { id: Date.now() },
        message: '创建成功'
      })
    }, 1000)
  })
}

function mockGetContactsList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          name: '江轩舟',
          unit: '市城管局',
          position: '市政处处长',
          phone: '15057168395',
          remark: '',
          operator: '江轩舟',
          operateTime: '2025-08-19 12:03:30'
        }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 500)
  })
}

function mockCreateContact(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟创建联系人:', data)
      resolve({
        code: 200,
        data: { id: Date.now() },
        message: '创建成功'
      })
    }, 1000)
  })
}

function mockGetExpertsList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          name: '江轩舟',
          unitTitle: '湖南中大设计研究院',
          phone: '15057168395',
          specialty: '土木工程',
          operator: '江轩舟',
          operateTime: '2025-08-19 12:03:30'
        }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 500)
  })
}

function mockCreateExpert(data) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log('模拟创建专家:', data)
      resolve({
        code: 200,
        data: { id: Date.now() },
        message: '创建成功'
      })
    }, 1000)
  })
}

function mockGetDepartmentList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          departmentName: '应急抢险项目部',
          equipmentCount: 7,
          contactName: '江轩舟',
          contactPhone: '19922218593',
          operator: '江轩舟',
          operateTime: '2025-08-19 12:03:30'
        }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 500)
  })
}

function mockGetEquipmentList(params) {
  return new Promise(resolve => {
    setTimeout(() => {
      const mockData = [
        { id: 1, equipmentName: '发电车（355kw）', quantity: 60 },
        { id: 2, equipmentName: '发电机组（100kw）', quantity: 6 }
      ]
      resolve({
        code: 200,
        data: { records: mockData, total: mockData.length },
        message: '获取成功'
      })
    }, 300)
  })
}

// 基础数据模拟方法
function mockGetEventTypeOptions() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { value: '1', label: '桥梁隧道结构性损坏' },
          { value: '2', label: '桥梁隧道恶化老化物落' },
          { value: '3', label: '桥梁隧道灾门坍塌' },
          { value: '4', label: '桥梁隧道交通事故' }
        ],
        message: '获取成功'
      })
    }, 200)
  })
}

function mockGetStructureList(type) {
  return new Promise(resolve => {
    setTimeout(() => {
      const bridgeData = [
        { value: '1', label: 'XXX大桥' },
        { value: '2', label: 'YYY大桥' },
        { value: '3', label: 'ZZZ大桥' }
      ]
      const tunnelData = [
        { value: '1', label: 'XXX隧道' },
        { value: '2', label: 'YYY隧道' },
        { value: '3', label: 'ZZZ隧道' }
      ]
      resolve({
        code: 200,
        data: type === 'tunnel' ? tunnelData : bridgeData,
        message: '获取成功'
      })
    }, 200)
  })
}

function mockGetEmergencyContacts() {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          { id: 1, name: '刘金鑫', unit: '市城管局', position: '科长' },
          { id: 2, name: '李明宇', unit: '市桥隧事务中心', position: '主任' },
          { id: 3, name: '林文龙', unit: '桥隧公司', position: '经理' }
        ],
        message: '获取成功'
      })
    }, 200)
  })
}

/**
 * 生成模拟事件数据
 */
function generateMockEventData(stage, structureType) {
  const structureName = structureType === 'bridge' ? '大桥' : '隧道'
  const baseData = []
  
  // 事件类型选项
  const eventTypes = [
    '桥梁隧道结构性损坏',
    '桥梁隧道恶化老化物落',
    '桥梁隧道灾门坍塌',
    '桥梁隧道交通事故',
    '自然灾害影响'
  ]
  
  // 上报人选项
  const reportUsers = ['孙明华', '张梓浩', '李小红', '王大明', '陈建国']
  
  // 位置选项
  const locations = ['天心区', '岳麓区', '开福区', '雨花区', '芙蓉区']
  
  for (let i = 1; i <= 10; i++) {
    const item = {
      id: i,
      eventName: `${structureName}突发事件${i}`,
      structureName: `XX${structureName}${i}`,
      location: `${locations[i % locations.length]}XX路段`,
      triggerTime: `2025/07/${15 + (i % 10)} ${10 + i}:${20 + (i % 40)}`,
      eventType: eventTypes[i % eventTypes.length],
      createTime: `2025/07/${15 + (i % 10)} ${10 + i}:${20 + (i % 40)}`,
      createUser: reportUsers[i % reportUsers.length],
      reportUser: reportUsers[i % reportUsers.length], // 添加上报人字段
      eventCategory: '自然灾害',
      eventLevel: '较小事件',
      responsibleUnit: '市桥隧事务中心',
      remark: '',
      summaryReport: i % 2 === 0,
      operator: '张梓浩',
      operateTime: '2025-08-19 12:03:30'
    }
    baseData.push(item)
  }
  
  return baseData
}

export default {
  // 导出所有API方法
  getEventList,
  getEventDetail,
  createEvent,
  assessEvent,
  reportEvent,
  controlEvent,
  getEventStageStats,
  deleteEvent,
  getProcessList,
  createProcess,
  updateProcess,
  deleteProcess,
  getPlansList,
  createPlan,
  getContactsList,
  createContact,
  getExpertsList,
  createExpert,
  getDepartmentList,
  getEquipmentList,
  getEventTypeOptions,
  getStructureList,
  getEmergencyContacts
}
