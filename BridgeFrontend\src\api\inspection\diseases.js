import request from '@/api/request'

// 获取病害列表
export function getDiseaseList(query) {
  return request({
    url: '/api/inspection/diseases',
    method: 'get',
    params: query
  })
}

// 获取病害详情
export function getDiseaseDetail(id) {
  return request({
    url: `/api/inspection/diseases/${id}`,
    method: 'get'
  })
}

// 新增病害
export function addDisease(data) {
  return request({
    url: '/api/inspection/diseases',
    method: 'post',
    data
  })
}

// 更新病害信息
export function updateDisease(id, data) {
  return request({
    url: `/api/inspection/diseases/${id}`,
    method: 'put',
    data
  })
}

// 删除病害
export function deleteDisease(id) {
  return request({
    url: `/api/inspection/diseases/${id}`,
    method: 'delete'
  })
}

// 病害判定
export function judgeDisease(id, data) {
  return request({
    url: `/api/inspection/diseases/${id}/judge`,
    method: 'put',
    data
  })
}

// 病害复核
export function reviewDisease(id, data) {
  return request({
    url: `/api/inspection/diseases/${id}/review`,
    method: 'put',
    data
  })
}

// 批量复核病害
export function batchReviewDiseases(data) {
  return request({
    url: '/api/inspection/diseases/batch-review',
    method: 'put',
    data
  })
}

// 获取病害处置流程状态
export function getDiseaseWorkflow(id) {
  return request({
    url: `/api/inspection/diseases/${id}/workflow`,
    method: 'get'
  })
}
