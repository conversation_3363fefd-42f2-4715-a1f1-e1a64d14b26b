import request from '@/api/request'

// 获取巡检记录列表
export function getInspectionRecords(query) {
  return request({
    url: '/api/inspection/records',
    method: 'get',
    params: query
  })
}

// 获取巡检日志日历数据
export function getInspectionLogs(bridgeId, params) {
  return request({
    url: `/api/inspection/logs/${bridgeId}`,
    method: 'get',
    params
  })
}

// 获取巡检详情
export function getInspectionDetail(logId) {
  return request({
    url: `/api/inspection/detail/${logId}`,
    method: 'get'
  })
}

// 获取巡检报告列表
export function getInspectionReports(bridgeId, params) {
  return request({
    url: `/api/inspection/reports/${bridgeId}`,
    method: 'get',
    params
  })
}

// 获取巡检报告详情
export function getInspectionReportDetail(reportId) {
  return request({
    url: `/api/inspection/reports/${reportId}`,
    method: 'get'
  })
}

// 下载巡检报告
export function downloadInspectionReport(reportId) {
  return request({
    url: `/api/inspection/reports/${reportId}/download`,
    method: 'get',
    responseType: 'blob'
  })
}
