import request from '@/api/request'

// 获取巡检统计数据
export function getInspectionStatistics(query) {
  return request({
    url: '/api/inspection/statistics',
    method: 'get',
    params: query
  })
}

// 获取最近巡检记录
export function getRecentInspectionRecords(query) {
  return request({
    url: '/api/inspection/recent-records',
    method: 'get',
    params: query
  })
}

// 获取病害类型统计
export function getDamageTypeSummary(query) {
  return request({
    url: '/api/inspection/statistics/damage-type-summary',
    method: 'get',
    params: query
  })
}

// 获取桥梁病害排行
export function getBridgeDamageRanking(query) {
  return request({
    url: '/api/inspection/statistics/bridge-damage-ranking',
    method: 'get',
    params: query
  })
}

// 获取巡检趋势数据
export function getInspectionTrend(query) {
  return request({
    url: '/api/inspection/statistics/inspection-trend',
    method: 'get',
    params: query
  })
}

// 获取各区巡检完成情况
export function getRegionCompletionRate(query) {
  return request({
    url: '/api/inspection/statistics/region-completion',
    method: 'get',
    params: query
  })
}
