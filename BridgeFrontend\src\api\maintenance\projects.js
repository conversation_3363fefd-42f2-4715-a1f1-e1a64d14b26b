/**
 * 养护项目相关API接口
 */
import request from '@/api/request'
// 临时导入模拟数据用于开发调试
import { 
  getMaintenanceProjectList,
  getMaintenanceProjectDetail,
  deleteMaintenanceProject,
  getBridgesList,
  getTunnelsList,
  getDiseasesList,
  getManagementUnitsList,
  getMaintenanceUnitsList,
  getProjectManagersList,
  getProjectTypes as getMockProjectTypes
} from '@/api/mock/maintenance'

// 获取养护项目列表
export function getProjectList(params) {
  // 临时使用模拟数据，正式环境需要替换为真实API
  return getMaintenanceProjectList(params).then(response => {
    // 统一响应格式
    return {
      data: {
        list: response.rows,
        total: response.total
      }
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/projects',
    method: 'get',
    params
  })
  */
}

// 获取养护项目详情
export function getProjectDetail(id) {
  // 临时使用模拟数据
  return getMaintenanceProjectDetail(id).then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: `/maintenance/projects/${id}`,
    method: 'get'
  })
  */
}

// 创建养护项目
export function createProject(data) {
  return request({
    url: '/maintenance/projects',
    method: 'post',
    data
  })
}

// 更新养护项目
export function updateProject(id, data) {
  return request({
    url: `/maintenance/projects/${id}`,
    method: 'put',
    data
  })
}

// 删除养护项目
export function deleteProject(id) {
  // 临时使用模拟数据
  return deleteMaintenanceProject(id)
  
  /* 正式环境使用以下代码：
  return request({
    url: `/maintenance/projects/${id}`,
    method: 'delete'
  })
  */
}

// 提交项目审批
export function submitProject(id) {
  return request({
    url: `/maintenance/projects/${id}/submit`,
    method: 'post'
  })
}

// 审批项目
export function approveProject(id, data) {
  return request({
    url: `/maintenance/projects/${id}/approve`,
    method: 'post',
    data
  })
}

// 获取项目类型列表
export function getProjectTypes() {
  // 临时使用模拟数据
  return getMockProjectTypes().then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/project-types',
    method: 'get'
  })
  */
}

// 获取管理单位列表
export function getManagementUnits() {
  // 临时使用模拟数据
  return getManagementUnitsList().then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/management-units',
    method: 'get'
  })
  */
}

// 获取养护单位列表
export function getMaintenanceUnits() {
  // 临时使用模拟数据
  return getMaintenanceUnitsList().then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/maintenance-units',
    method: 'get'
  })
  */
}

// 获取项目负责人列表
export function getProjectManagers(unitId) {
  // 临时使用模拟数据
  return getProjectManagersList({ unitId }).then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/project-managers',
    method: 'get',
    params: { unitId }
  })
  */
}

// 获取桥梁列表
export function getBridgeList(params) {
  // 临时使用模拟数据
  return getBridgesList(params).then(response => {
    return {
      data: {
        rows: response.rows || [],
        total: response.total || 0
      }
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/bridges',
    method: 'get',
    params
  })
  */
}

// 获取隧道列表
export function getTunnelList(params) {
  // 临时使用模拟数据
  return getTunnelsList(params).then(response => {
    return {
      data: response.data
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/tunnels',
    method: 'get',
    params
  })
  */
}

// 获取病害列表
export function getDiseaseList(params) {
  // 临时使用模拟数据
  return getDiseasesList(params).then(response => {
    console.log('模拟API返回:', response)
    return {
      data: {
        rows: response.rows,
        total: response.total
      }
    }
  })
  
  /* 正式环境使用以下代码：
  return request({
    url: '/maintenance/diseases',
    method: 'get',
    params
  })
  */
}

// 关联桥梁到项目
export function associateBridges(projectId, bridgeIds) {
  return request({
    url: `/maintenance/projects/${projectId}/bridges`,
    method: 'post',
    data: { bridgeIds }
  })
}

// 关联病害到项目
export function associateDiseases(projectId, diseaseIds) {
  return request({
    url: `/maintenance/projects/${projectId}/diseases`,
    method: 'post',
    data: { diseaseIds }
  })
}

// 上传项目附件
export function uploadProjectFile(projectId, file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: `/maintenance/projects/${projectId}/files`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除项目附件
export function deleteProjectFile(projectId, fileId) {
  return request({
    url: `/maintenance/projects/${projectId}/files/${fileId}`,
    method: 'delete'
  })
}

// 获取项目审批历史
export function getProjectApprovalHistory(projectId) {
  return request({
    url: `/maintenance/projects/${projectId}/approval-history`,
    method: 'get'
  })
}

// 获取审批历史 (兼容性别名)
export function getAuditHistory(projectId) {
  return getProjectApprovalHistory(projectId)
}

// 获取项目任务列表
export function getProjectTasks(params) {
  return request({
    url: '/maintenance/projects/tasks',
    method: 'get',
    params
  })
}

// 获取项目病害列表
export function getProjectDiseases(params) {
  return request({
    url: '/maintenance/projects/diseases',
    method: 'get',
    params
  })
}

// 获取项目桥梁列表
export function getProjectBridges(projectId) {
  return request({
    url: `/maintenance/projects/${projectId}/bridges`,
    method: 'get'
  })
}
