/**
 * 养护运维模块API模拟服务
 * 
 * ⚠️ 注意：这是临时的API模拟服务，用于前端开发和演示
 * 当后端接口实现后，需要删除此文件并替换为真实API调用
 * 
 * 基于《长沙市智慧桥隧管理平台-养护运维前端设计文档.md》创建
 */

import {
  MAINTENANCE_PROJECTS,
  PROJECT_ITEMS,
  PROJECT_BRIDGES,
  PROJECT_DISEASES,
  BRIDGES_DATA,
  TUNNELS_DATA,
  DISEASES_DATA,
  MAINTENANCE_TASKS,
  EMERGENCY_REPAIRS,
  APPROVAL_HISTORY,
  MAINTENANCE_TEMPLATES,
  PROJECT_STATISTICS,
  TASK_COMPLETION_STATS,
  PROJECT_TYPES,
  PROJECT_STATUS,
  TASK_STATUS,
  EMERGENCY_STATUS,
  MANAGEMENT_UNITS,
  MAINTENANCE_UNITS,
  PROJECT_MANAGERS,
  MAINTENANCE_STAFF,
  mockApiDelay,
  mockPagination
} from '@/data/mock/maintenance'

// ==================== 项目管理 API ====================

/**
 * 获取养护项目列表
 * TODO: 替换为 GET /api/maintenance/projects/list
 */
export const getMaintenanceProjectList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...MAINTENANCE_PROJECTS]
  
  // 模拟筛选逻辑
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.includes(params.name)
    )
  }
  
  if (params.type) {
    filteredData = filteredData.filter(item => 
      item.type === params.type
    )
  }
  
  if (params.status) {
    filteredData = filteredData.filter(item => 
      item.status === params.status
    )
  }
  
  if (params.startDate && params.endDate) {
    filteredData = filteredData.filter(item => 
      item.startDate >= params.startDate && item.endDate <= params.endDate
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

/**
 * 获取项目详情
 * TODO: 替换为 GET /api/maintenance/projects/{id}
 */
export const getMaintenanceProjectDetail = async (id) => {
  await mockApiDelay()
  
  const project = MAINTENANCE_PROJECTS.find(item => item.id === parseInt(id))
  if (!project) {
    return {
      code: 404,
      msg: '项目不存在'
    }
  }
  
  // 组装完整的项目详情
  const projectDetail = {
    ...project,
    items: PROJECT_ITEMS[id] || [],
    bridges: (PROJECT_BRIDGES[id] || []).map(bridgeId => 
      BRIDGES_DATA.find(bridge => bridge.id === bridgeId)
    ).filter(Boolean),
    diseases: (PROJECT_DISEASES[id] || []).map(diseaseId => 
      DISEASES_DATA.find(disease => disease.id === diseaseId)
    ).filter(Boolean),
    completionStats: TASK_COMPLETION_STATS[id] || { completed: 0, total: 0 }
  }
  
  return {
    code: 200,
    msg: '操作成功',
    data: projectDetail
  }
}

/**
 * 创建养护项目
 * TODO: 替换为 POST /api/maintenance/projects
 */
export const createMaintenanceProject = async (data) => {
  await mockApiDelay()
  
  // 模拟创建逻辑
  const newProject = {
    id: Date.now(), // 使用时间戳作为临时ID
    ...data,
    status: 'draft',
    statusName: '草稿',
    createTime: new Date().toISOString().replace('T', ' ').slice(0, 19),
    updateTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
  }
  
  // 模拟添加到数据中
  MAINTENANCE_PROJECTS.unshift(newProject)
  
  return {
    code: 200,
    msg: '创建成功',
    data: newProject
  }
}

/**
 * 更新养护项目
 * TODO: 替换为 PUT /api/maintenance/projects/{id}
 */
export const updateMaintenanceProject = async (id, data) => {
  await mockApiDelay()
  
  const index = MAINTENANCE_PROJECTS.findIndex(item => item.id === parseInt(id))
  if (index === -1) {
    return {
      code: 404,
      msg: '项目不存在'
    }
  }
  
  // 模拟更新逻辑
  MAINTENANCE_PROJECTS[index] = {
    ...MAINTENANCE_PROJECTS[index],
    ...data,
    updateTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
  }
  
  return {
    code: 200,
    msg: '更新成功',
    data: MAINTENANCE_PROJECTS[index]
  }
}

/**
 * 删除养护项目
 * TODO: 替换为 DELETE /api/maintenance/projects/{id}
 */
export const deleteMaintenanceProject = async (id) => {
  await mockApiDelay()
  
  const index = MAINTENANCE_PROJECTS.findIndex(item => item.id === parseInt(id))
  if (index === -1) {
    return {
      code: 404,
      msg: '项目不存在'
    }
  }
  
  // 只能删除草稿状态的项目
  if (MAINTENANCE_PROJECTS[index].status !== 'draft') {
    return {
      code: 400,
      msg: '只能删除草稿状态的项目'
    }
  }
  
  MAINTENANCE_PROJECTS.splice(index, 1)
  
  return {
    code: 200,
    msg: '删除成功'
  }
}

/**
 * 提交项目审批
 * TODO: 替换为 POST /api/maintenance/projects/{id}/submit
 */
export const submitProjectApproval = async (id) => {
  await mockApiDelay()
  
  const index = MAINTENANCE_PROJECTS.findIndex(item => item.id === parseInt(id))
  if (index === -1) {
    return {
      code: 404,
      msg: '项目不存在'
    }
  }
  
  // 只能提交草稿状态的项目
  if (MAINTENANCE_PROJECTS[index].status !== 'draft') {
    return {
      code: 400,
      msg: '只能提交草稿状态的项目'
    }
  }
  
  MAINTENANCE_PROJECTS[index].status = 'pending'
  MAINTENANCE_PROJECTS[index].statusName = '审批中'
  MAINTENANCE_PROJECTS[index].updateTime = new Date().toISOString().replace('T', ' ').slice(0, 19)
  
  return {
    code: 200,
    msg: '提交成功'
  }
}

// ==================== 基础数据 API ====================

/**
 * 获取桥梁列表
 * TODO: 替换为 GET /api/archives/bridges/list
 */
export const getBridgesList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...BRIDGES_DATA]
  
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.includes(params.name) || item.code.includes(params.name)
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

/**
 * 获取隧道列表
 * TODO: 替换为 GET /api/archives/tunnels/list
 */
export const getTunnelsList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...TUNNELS_DATA]
  
  if (params.name) {
    filteredData = filteredData.filter(item => 
      item.name.includes(params.name) || item.code.includes(params.name)
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

/**
 * 获取病害列表
 * TODO: 替换为 GET /api/maintenance/diseases/list
 */
export const getDiseasesList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...DISEASES_DATA]
  
  if (params.bridgeName) {
    filteredData = filteredData.filter(item => 
      item.bridgeName.includes(params.bridgeName)
    )
  }
  
  if (params.type) {
    filteredData = filteredData.filter(item => 
      item.type === params.type
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

/**
 * 获取管理单位列表
 * TODO: 替换为 GET /api/system/dept/list?type=management
 */
export const getManagementUnitsList = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: MANAGEMENT_UNITS
  }
}

/**
 * 获取养护单位列表
 * TODO: 替换为 GET /api/system/dept/list?type=maintenance
 */
export const getMaintenanceUnitsList = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: MAINTENANCE_UNITS
  }
}

/**
 * 获取项目负责人列表
 * TODO: 替换为 GET /api/system/user/list?role=project_manager
 */
export const getProjectManagersList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...PROJECT_MANAGERS]
  
  if (params.unitId) {
    filteredData = filteredData.filter(item => item.unitId === parseInt(params.unitId))
  }
  
  return {
    code: 200,
    msg: '操作成功',
    data: filteredData
  }
}

/**
 * 获取养护人员列表
 * TODO: 替换为 GET /api/system/user/list?role=maintenance_staff
 */
export const getMaintenanceStaffList = async (unitId) => {
  await mockApiDelay()
  
  let filteredData = [...MAINTENANCE_STAFF]
  
  if (unitId) {
    filteredData = filteredData.filter(item => item.unitId === parseInt(unitId))
  }
  
  return {
    code: 200,
    msg: '操作成功',
    data: filteredData
  }
}

// ==================== 养护维修 API ====================

/**
 * 获取养护维修任务列表
 * TODO: 替换为 GET /api/maintenance/repairs/list
 */
export const getMaintenanceTasksList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...MAINTENANCE_TASKS]
  
  if (params.projectName) {
    filteredData = filteredData.filter(item => 
      item.projectName.includes(params.projectName)
    )
  }
  
  if (params.projectType) {
    filteredData = filteredData.filter(item => 
      item.projectType === params.projectType
    )
  }
  
  if (params.isOverdue !== undefined) {
    filteredData = filteredData.filter(item => 
      item.isOverdue === params.isOverdue
    )
  }
  
  if (params.maintenanceUnit) {
    filteredData = filteredData.filter(item => 
      item.maintenanceUnit.includes(params.maintenanceUnit)
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

/**
 * 获取应急维修列表
 * TODO: 替换为 GET /api/maintenance/emergency/list
 */
export const getEmergencyRepairsList = async (params = {}) => {
  await mockApiDelay()
  
  let filteredData = [...EMERGENCY_REPAIRS]
  
  if (params.bridgeName) {
    filteredData = filteredData.filter(item => 
      item.bridgeName.includes(params.bridgeName)
    )
  }
  
  if (params.status) {
    filteredData = filteredData.filter(item => 
      item.status === params.status
    )
  }
  
  if (params.diseaseType) {
    filteredData = filteredData.filter(item => 
      item.diseaseType.includes(params.diseaseType)
    )
  }
  
  const paginationResult = mockPagination(
    filteredData, 
    params.pageNum || 1, 
    params.pageSize || 10
  )
  
  return {
    code: 200,
    msg: '操作成功',
    rows: paginationResult.data,
    total: paginationResult.total
  }
}

// ==================== 审批相关 API ====================

/**
 * 获取审批历史
 * TODO: 替换为 GET /api/maintenance/approval/history/{targetId}
 */
export const getApprovalHistory = async (targetId, targetType = 'project') => {
  await mockApiDelay()
  
  const filteredData = APPROVAL_HISTORY.filter(item => 
    item.targetId === parseInt(targetId) && item.targetType === targetType
  )
  
  return {
    code: 200,
    msg: '操作成功',
    data: filteredData
  }
}

/**
 * 项目审批
 * TODO: 替换为 POST /api/maintenance/projects/{id}/approve
 */
export const approveProject = async (id, data) => {
  await mockApiDelay()
  
  const index = MAINTENANCE_PROJECTS.findIndex(item => item.id === parseInt(id))
  if (index === -1) {
    return {
      code: 404,
      msg: '项目不存在'
    }
  }
  
  // 更新项目状态
  if (data.result === 'approved') {
    MAINTENANCE_PROJECTS[index].status = 'approved'
    MAINTENANCE_PROJECTS[index].statusName = '审批通过'
  } else if (data.result === 'rejected') {
    MAINTENANCE_PROJECTS[index].status = 'rejected'
    MAINTENANCE_PROJECTS[index].statusName = '审批拒绝'
  }
  
  MAINTENANCE_PROJECTS[index].updateTime = new Date().toISOString().replace('T', ' ').slice(0, 19)
  
  // 添加审批记录
  const approvalRecord = {
    id: Date.now(),
    targetId: parseInt(id),
    targetType: 'project',
    step: data.step || '审批',
    processor: data.processor || '系统管理员',
    processorId: data.processorId || 1,
    status: data.result === 'approved' ? 'approved' : 'rejected',
    statusName: data.result === 'approved' ? '通过' : '拒绝',
    opinion: data.opinion || '',
    department: data.department || '桥隧中心',
    receiveTime: new Date().toISOString().replace('T', ' ').slice(0, 19),
    processTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
  }
  
  APPROVAL_HISTORY.push(approvalRecord)
  
  return {
    code: 200,
    msg: '审批成功'
  }
}


// ==================== 统计数据 API ====================

/**
 * 获取项目统计数据
 * TODO: 替换为 GET /api/maintenance/statistics
 */
export const getProjectStatistics = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: PROJECT_STATISTICS
  }
}

/**
 * 获取常用模板
 * TODO: 替换为 GET /api/maintenance/templates/list
 */
export const getMaintenanceTemplates = async (category) => {
  await mockApiDelay()
  
  let filteredData = [...MAINTENANCE_TEMPLATES]
  
  if (category) {
    filteredData = filteredData.filter(item => item.category === category)
  }
  
  return {
    code: 200,
    msg: '操作成功',
    data: filteredData
  }
}

// ==================== 字典数据 API ====================

/**
 * 获取项目类型字典
 * TODO: 替换为 GET /api/system/dict/data?dictType=project_type
 */
export const getProjectTypes = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: PROJECT_TYPES
  }
}

/**
 * 获取项目状态字典
 * TODO: 替换为 GET /api/system/dict/data?dictType=project_status
 */
export const getProjectStatus = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: PROJECT_STATUS
  }
}

/**
 * 获取任务状态字典
 * TODO: 替换为 GET /api/system/dict/data?dictType=task_status
 */
export const getTaskStatus = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: TASK_STATUS
  }
}

/**
 * 获取应急维修状态字典
 * TODO: 替换为 GET /api/system/dict/data?dictType=emergency_status
 */
export const getEmergencyStatus = async () => {
  await mockApiDelay()
  
  return {
    code: 200,
    msg: '操作成功',
    data: EMERGENCY_STATUS
  }
}

// ==================== 导出API服务 ====================

const maintenanceApi = {
  // 项目管理
  getMaintenanceProjectList,
  getMaintenanceProjectDetail,
  createMaintenanceProject,
  updateMaintenanceProject,
  deleteMaintenanceProject,
  submitProjectApproval,
  
  // 基础数据
  getBridgesList,
  getTunnelsList,
  getDiseasesList,
  getManagementUnitsList,
  getMaintenanceUnitsList,
  getProjectManagersList,
  getMaintenanceStaffList,
  
  // 养护维修
  getMaintenanceTasksList,
  getEmergencyRepairsList,
  
  // 审批相关
  getApprovalHistory,
  approveProject,
  
  
  // 统计数据
  getProjectStatistics,
  getMaintenanceTemplates,
  
  // 字典数据
  getProjectTypes,
  getProjectStatus,
  getTaskStatus,
  getEmergencyStatus
}

export default maintenanceApi
