// cover some element-ui styles
// 输入框透明
::v-deep .transparent {
  & .is-focus {
    .el-input__inner {
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 10px;
    }
  }
  .el-input__inner {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e6ebf5;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
  }
  .el-input__icon {
    color: white;
  }
}

// el-autocomplete popper-class
.popper-transparent {
  background-color: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  li {
    color: white;
    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
  &.is-loading {
    li {
      color: white;
      &:hover {
        background-color: rgba(255, 255, 255, 0);
      }
    }
  }
}

// 日期选择器
// 日期范围选择器，透明背景
::v-deep .el-date-range-t {
  background-color: transparent;
  border: 1px solid #dcdfe6;
  &.el-range-editor.is-active:hover {
    border: 1px solid #dcdfe6;
  }
  .el-input__icon {
    color: white;
  }
  .el-range-input {
    color: white;
    background-color: transparent;
  }
  .el-range-separator {
    color: white;
  }
}
// 至
::v-deep .el-range-separator {
  width: auto;
}

// 对话框
// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  &.is-fullscreen {
    .el-dialog__body {
      padding: 0;
    }
  }
}
.el-dialog-scroll {
  .el-dialog {
    max-height: calc(100vh - 12vh);
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
      height: 4px;
    }
    &::-webkit-scrollbar-thumb {
      // 滚动条里面小方块
      background-color: #ddd;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-track {
      // 滚动条里面轨道
      background-color: #fff;
    }
    &::-webkit-scrollbar-corner {
      // 边角，两个滚动条交汇处 		// height: calc(100% - 4px) !important;
      background-color: transparent;
    }
  }
}

.el-dialog-scroll2 {
  .el-dialog {
    max-height: calc(100vh - 12vh);
    .el-dialog__body {
      max-height: calc(100vh - 12vh - 54px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 4px;
      }
      &::-webkit-scrollbar-thumb {
        // 滚动条里面小方块
        background-color: #ddd;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-track {
        // 滚动条里面轨道
        background-color: #fff;
      }
      &::-webkit-scrollbar-corner {
        // 边角，两个滚动条交汇处 		// height: calc(100% - 4px) !important;
        background-color: transparent;
      }
    }
  }
}

// 消息提示
.el-message {
  top: 20px !important;
}

// 分页透明
::v-deep .pagination-t {
  .el-pagination {
    .el-pagination__total {
      color: white;
    }
    button,
    .el-pager li {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
    }
    .el-pagination__jump {
      color: white;
    }

    .el-input__inner {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      // border-radius: 10px;
    }
    .el-pagination__sizes {
      .el-input--mini {
        .el-input__inner {
          height: 22px;
          line-height: 22px;
        }

        .el-input__suffix {
          height: 100%;
          .el-input__icon {
            @include flex(center, center);
            line-height: 22px;
          }
        }
      }
    }
  }
}

.pagination-poper-t {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  .el-select-dropdown__item {
    color: white;
    &.hover,
    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 树形控件-透明
::v-deep .el-tree-t {
  background: transparent;
  color: white;

  .el-tree-node__content:hover,
  .el-tree-node:focus > .el-tree-node__content,
  .el-tree-node.is-current > .el-tree-node__content {
    background-color: rgba(255, 255, 255, 0.2);
  }
  // .el-tree-node__children {
  //   .el-tree-node__content:hover,
  //   .el-tree-node:focus > .el-tree-node__content,
  //   .el-tree-node.is-current > .el-tree-node__content {
  //     background-color: rgba(255, 255, 255, 0.2);
  //   }
  // }

  .el-tree-node__content {
    height: 50px;
    .el-tree-node__label {
      font-size: 20px;
      font-weight: 700;
    }
  }
  .el-tree-node__children {
    .el-tree-node__label {
      font-size: 16px;
      font-weight: 400;
    }
  }

  .el-tree-node__expand-icon {
    color: white;
    font-size: 16px;
    &.is-leaf {
      color: transparent;
    }
  }
}

// 6.7.4 表格组件样式（与巡检记录保持一致）- 全局覆盖
.maintenance-theme {
  // 强制覆盖Element UI表格默认样式
  .el-table {
    background: transparent !important;
    color: #f8fafc !important; // 6.7.1 主要文字颜色
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;

    // 6.7.4 无边框设计
    &::before {
      display: none !important;
    }

    // 6.7.4 表头样式
    .el-table__header-wrapper {
      background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;

      th {
        background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
        color: #FFF !important;
        font-size: 16px !important;
        font-weight: 400 !important; // 正常字重
        line-height: 140% !important; // 22.4px
        height: 44px !important;
        padding: 14px 12px !important; // 减少左右padding，给文字更多空间
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
        text-align: center !important;
        white-space: nowrap !important; // 确保表头文字不换行

        &:last-child {
          border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
        }
      }
    }

    // 6.7.4 数据行样式
    .el-table__body-wrapper {
      background: transparent !important;

      tr {
        background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;

        // 移除双数行的背景色差异
        &:nth-child(odd) {
          background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
        }

        &:nth-child(even) {
          background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
        }

        // 6.7.4 表格悬停效果
        &:hover {
          background: rgba(255, 255, 255, 0.05) !important;

          td {
            background: rgba(255, 255, 255, 0.05) !important;
          }
        }

        // 6.7.4 最后一行无底部边框
        &:last-child {
          td {
            border-bottom: none !important;
          }
        }

        td {
          background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
          color: #FFF !important;
          font-size: 14px !important;
          font-weight: 400 !important;
          line-height: 120% !important; // 16.8px
          padding: 4px 12px !important; // 减少上下padding以匹配32px行高
          border: 1px solid rgba(255, 255, 255, 0.1) !important;
          text-align: center !important;
          height: 32px !important; // 明确设置td高度

          &:last-child {
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
          }
        }
      }
    }

    // 6.7.4 空状态样式
    .el-table__empty-block {
      background: transparent !important;

      .el-table__empty-text {
        color: #94a3b8 !important; // 6.7.1 静默文字颜色
      }
    }

    // 6.7.4 操作按钮样式
    .el-button--text {
      color: #FFF !important;
      font-size: 14px !important;
      line-height: 120% !important;
      height: 20px !important;
      padding: 2px 6px !important;
      background: none !important;
      border: none !important;
      border-radius: 4px !important;
      transition: all 0.2s ease !important;

      &:hover {
        color: #E0E0E0 !important;
        background: rgba(255, 255, 255, 0.1) !important;
      }

      & + .el-button--text {
        margin-left: 8px !important;
      }
    }
  }

  // 6.7.8 下拉选择框面板样式（与巡检记录保持一致）
  .el-select-dropdown {
    background: rgba(9, 26, 75, 0.9) !important;
    border: 1px solid #374151 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;

    .el-select-dropdown__item {
      color: #e2e8f0 !important; // 6.7.1 次要文字颜色
      font-size: 16px !important;
      font-weight: normal !important;
      padding: 8px 16px !important;

      &:hover {
        background: #334155 !important;
        color: #f8fafc !important; // 6.7.1 主要文字颜色
      }

      &.selected {
        background: #5C9DFF !important; // 6.7.1 蓝色强调色
        color: #f8fafc !important;
        font-weight: 500 !important;
      }
    }
  }

  // 6.7.4 固定列样式修复 - 关键！！！
  .el-table__fixed-right {
    background: transparent !important;

    .el-table__fixed-header-wrapper {
      background: transparent !important;

      .el-table__header {
        background: transparent !important;

        th {
          background: #1e3a8a !important;
          color: #ffffff !important;
          font-weight: bold !important;
          border-bottom: 1px solid #9ca3af !important;
          border-left: 1px solid #9ca3af !important;
        }
      }
    }

    .el-table__fixed-body-wrapper {
      background: transparent !important;

      .el-table__body {
        background: transparent !important;

        tr {
          background: transparent !important;

          &:nth-child(odd) {
            background: rgba(30, 64, 175, 0.1) !important;
          }

          &:nth-child(even) {
            background: transparent !important;
          }

          &:hover {
            background: rgba(30, 64, 175, 0.2) !important;

            td {
              background: rgba(30, 64, 175, 0.2) !important;
            }
          }

          td {
            background: inherit !important;
            color: #ffffff !important;
            border-bottom: 1px solid #9ca3af !important;
            border-left: 1px solid #9ca3af !important;
          }
        }
      }
    }
  }

  // 左固定列样式修复（如果有的话）
  .el-table__fixed {
    background: transparent !important;

    .el-table__fixed-header-wrapper {
      background: transparent !important;

      .el-table__header {
        background: transparent !important;

        th {
          background: #1e3a8a !important;
          color: #ffffff !important;
          font-weight: bold !important;
          border-bottom: 1px solid #9ca3af !important;
          border-right: 1px solid #9ca3af !important;
        }
      }
    }

    .el-table__fixed-body-wrapper {
      background: transparent !important;

      .el-table__body {
        background: transparent !important;

        tr {
          background: transparent !important;

          &:nth-child(odd) {
            background: rgba(30, 64, 175, 0.1) !important;
          }

          &:nth-child(even) {
            background: transparent !important;
          }

          &:hover {
            background: rgba(30, 64, 175, 0.2) !important;

            td {
              background: rgba(30, 64, 175, 0.2) !important;
            }
          }

          td {
            background: inherit !important;
            color: #ffffff !important;
            border-bottom: 1px solid #9ca3af !important;
            border-right: 1px solid #9ca3af !important;
          }
        }
      }
    }
  }

  // 表单控件深色主题
  .el-input__inner {
    background: #374151 !important;
    border-color: #9ca3af !important;
    color: #ffffff !important;

    &::placeholder {
      color: #9ca3af !important;
    }

    &:focus {
      border-color: #3b82f6 !important;
    }
  }

  .el-select .el-input__inner {
    background: #374151 !important;
    color: #ffffff !important;
  }

  .el-date-editor {
    background: linear-gradient(135deg, #1B2A56 0%, #243066 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;
    border-radius: 8px !important;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3) !important;

    &:hover {
      border-color: rgba(255, 255, 255, 0.5) !important;
    }

    &:focus-within {
      border-color: #5C9DFF !important;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
    }

    .el-range-input {
      background: transparent !important;
      color: #FFFFFF !important;
      font-family: PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif !important;
      font-size: 16px !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }

    .el-range-separator {
      color: rgba(255, 255, 255, 0.9) !important;
      font-weight: 500 !important;
    }

    .el-input__icon {
      color: rgba(255, 255, 255, 0.7) !important;
      font-size: 16px !important;

      &:hover {
        color: #FFFFFF !important;
      }
    }
  }

  // 分页样式
  .el-pagination {
    .el-pagination__total,
    .el-pagination__jump {
      color: #9ca3af !important;
    }

    .btn-prev,
    .btn-next,
    .el-pager li {
      background: transparent !important;
      border: 1px solid #4b5563 !important;
      color: #9ca3af !important;

      &:hover {
        background: #374151 !important;
        color: #ffffff !important;
      }

      &.active {
        background: #3b82f6 !important;
        color: #ffffff !important;
        border-color: #3b82f6 !important;
      }
    }

    .el-pagination__sizes .el-select .el-input .el-input__inner {
      background: #374151 !important;
      border-color: #4b5563 !important;
      color: #ffffff !important;
    }
  }
}

// 下拉菜单深色主题 - 养护运维模块
.maintenance-theme .el-select-dropdown {
  background: #374151 !important;
  border: 1px solid #9ca3af !important;

  .el-select-dropdown__item {
    color: #ffffff !important;

    &:hover {
      background: #4b5563 !important;
    }

    &.selected {
      background: #3b82f6 !important;
      color: #ffffff !important;
    }
  }
}

// 日期选择器深色主题 - 养护运维模块
.maintenance-theme .el-picker-panel {
  background: linear-gradient(135deg, #1B2A56 0%, #243066 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4) !important;
  color: #ffffff !important;

  .el-picker-panel__header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;

    .el-picker-panel__icon-btn {
      color: rgba(255, 255, 255, 0.8) !important;

      &:hover {
        color: #FFFFFF !important;
      }
    }
  }

  .el-date-table th {
    color: rgba(255, 255, 255, 0.7) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .el-date-table td {
    color: rgba(255, 255, 255, 0.8) !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      color: #FFFFFF !important;
    }

    &.today {
      color: #5C9DFF !important;
      font-weight: 500 !important;
    }

    &.current:not(.disabled) {
      background: #5C9DFF !important;
      color: #ffffff !important;
    }

    &.in-range {
      background: rgba(92, 157, 255, 0.2) !important;
      color: #FFFFFF !important;
    }

    &.start-date,
    &.end-date {
      background: #5C9DFF !important;
      color: #FFFFFF !important;
    }
  }

  .el-picker-panel__footer {
    border-top: 1px solid rgba(255, 255, 255, 0.15) !important;

    .el-button {
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.25) !important;
      color: #FFFFFF !important;

      &:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
      }

      &.el-button--primary {
        background: #5C9DFF !important;
        border-color: #5C9DFF !important;

        &:hover {
          background: #4A90E2 !important;
          border-color: #4A90E2 !important;
        }
      }
    }
  }
}

// 下拉菜单-透明
.dropdown-menu-t {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(20px);
  &.el-dropdown-menu--medium {
    .el-dropdown-menu__item {
      padding: 0 30px;
    }
  }
  .el-dropdown-menu__item {
    color: white;
  }
  .el-dropdown-menu__item:not(.is-disabled):hover,
  .el-dropdown-menu__item:focus {
    background-color: rgba(255, 255, 255, 0.3);
  }
  ::v-deep .popper__arrow {
    top: -7px;
    border-bottom-color: rgba(255, 255, 255, 0.5);
    &::after {
      border-bottom-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// 加载loading
.el-loading-mask {
  .el-loading-spinner .path {
    stroke: white;
  }
}

// 滚动
.el-scrollbar {
  .el-scrollbar__thumb {
    background-color: rgba(255, 255, 255, 0.8);
    &:hover {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}
