// 养护模块特有样式 - 基于公共组件样式的扩展
// 只包含maintenance模块特有的样式，公共样式已迁移到maintenance目录

// 引入Maintenance模块公共样式
@import './maintenance/index.scss';

// ===========================
// Maintenance模块特有弹框样式
// ===========================

// 特定弹框样式覆盖 - 使用标准宽度的弹框
// 注意：已更新为使用 common-dialog-wide 基础类

// 项目模态框包装器 - 已迁移到公共样式，保留备用
// 注意：现在组件直接使用 common-dialog-wide 基础类







// ===========================
// Maintenance模块特有元素强制样式
// ===========================

// 特有的强制背景色应用
.project-detail-dialog {
  // 强制Element UI背景色修复
  :deep(.el-dialog__wrapper) {
    background: rgba(0, 0, 0, 0.5) !important;
  }
}

// ===========================
// Maintenance模块特有图片预览样式
// ===========================

// 养护模块特有的图片预览样式
.maintenance-image-gallery,
.maintenance-photo-grid {
  display: flex !important;
  gap: 16px !important;
  flex-wrap: wrap !important;
  margin-top: 12px !important;
  
  .image-category {
    margin-bottom: 16px !important;
    
    .category-title {
      color: var(--inspection-text-primary, #f8fafc) !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      margin-bottom: 8px !important;
    }
    
    .image-list {
      display: flex !important;
      gap: 12px !important;
      flex-wrap: wrap !important;
    }
  }
  
  .image-item,
  .photo-item {
    width: 150px !important;
    position: relative !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    cursor: pointer !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    
    img {
      width: 100% !important;
      height: 120px !important;
      object-fit: cover !important;
      display: block !important;
    }
    
    .image-overlay {
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      background: rgba(0, 0, 0, 0.5) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      opacity: 0 !important;
      transition: opacity 0.3s ease !important;
      
      i {
        color: #ffffff !important;
        font-size: 24px !important;
      }
    }
    
    &:hover .image-overlay {
      opacity: 1 !important;
    }
    
    .image-caption {
      position: absolute !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      background: rgba(0, 0, 0, 0.7) !important;
      color: #ffffff !important;
      font-size: 12px !important;
      padding: 4px 8px !important;
      text-align: center !important;
    }
  }
}

// ===========================
// Maintenance模块特有状态标签样式
// ===========================

// 养护模块特有的危险文本样式
.maintenance-danger-text {
  color: #ef4444 !important;
  
  &:hover {
    color: #dc2626 !important;
  }
}

// ===========================
// Maintenance模块特有审核信息样式
// ===========================

// 养护模块特有的审核信息展示
.maintenance-audit-info {
  .section {
    margin-bottom: 24px !important;
    
    .section-title {
      color: #ffffff !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      margin-bottom: 16px !important;
      padding-bottom: 8px !important;
      border-bottom: 1px solid #374151 !important;
      
      i {
        margin-right: 8px !important;
        color: var(--inspection-primary-light, #74a7f5) !important;
      }
    }
  }
}

// ===========================
// Maintenance模块响应式适配
// ===========================

// 平板设备适配 - 针对特有弹框
@media (max-width: 1024px) and (min-width: 769px) {
  .project-detail-dialog :deep(.el-dialog) {
    width: 75vw !important;
    min-width: 600px !important;
    max-width: 800px !important;
  }
}

// 手机设备适配 - 针对特有弹框
@media (max-width: 768px) {
  .project-detail-dialog .modal-content-wrapper {
    max-height: calc(96vh - 100px) !important;
    padding: 10px !important;
  }
}