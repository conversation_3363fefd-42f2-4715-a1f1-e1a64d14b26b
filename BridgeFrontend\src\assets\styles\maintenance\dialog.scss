// 弹框组件公共样式 - 深色主题
// 提供统一的弹框样式，支持不同尺寸和功能

// ===========================
// 基础弹框样式变量
// ===========================
$dialog-bg-primary: var(--inspection-bg-primary, #091A4B);
$dialog-text-primary: var(--inspection-text-primary, #f8fafc);
$dialog-border: rgba(255, 255, 255, 0.2);
$dialog-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);

// ===========================
// 基础弹框样式 - 深色主题
// ===========================
.common-dialog {
  :deep(.el-dialog) {
    // 弹框大小：约为浏览器的1/3，最大宽度1200px，最小宽度800px
    width: 33vw !important;
    max-width: 1200px !important;
    min-width: 800px !important;
    
    // 弹框位置：居中显示，距离顶部5%的视口高度
    margin-top: 5vh !important;
    margin-left: auto !important;
    margin-right: auto !important;
    top: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    
    // 弹框高度：最大不超过90%视口高度，确保内容不溢出
    max-height: 90vh !important;
    overflow: hidden !important;
    
    // 深色主题样式 - 强制修复Element UI默认白色背景
    background: $dialog-bg-primary !important;
    background-color: $dialog-bg-primary !important;
    border: 1px solid $dialog-border !important;
    border-radius: 8px !important;
    color: $dialog-text-primary !important;
    box-shadow: $dialog-shadow !important;

    .el-dialog__header {
      background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
      color: #ffffff !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-radius: 8px 8px 0 0 !important;
      padding: 16px 20px !important;

      .el-dialog__title {
        color: #ffffff !important;
        font-weight: 500 !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 16px !important;
      }

      .el-dialog__headerbtn {
        top: 16px !important;
        right: 20px !important;
        width: 32px !important;
        height: 32px !important;

        .el-dialog__close {
          color: rgba(255, 255, 255, 0.8) !important;
          font-size: 18px !important;
          background: transparent !important;
          border: none !important;
          width: 100% !important;
          height: 100% !important;

          &:hover {
            color: #ffffff !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border-radius: 4px !important;
          }
        }
      }
    }

    .el-dialog__body {
      // 确保弹框内容区域可以滚动，不超出弹框范围
      background: $dialog-bg-primary !important;
      color: $dialog-text-primary !important;
      padding: 20px !important;
      max-height: calc(90vh - 120px) !important; // 减去header和footer的高度
      overflow-y: auto !important;
      overflow-x: hidden !important;
      
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px !important;
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 3px !important;
      }
      
      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3) !important;
        border-radius: 3px !important;
        
        &:hover {
          background: rgba(255, 255, 255, 0.5) !important;
        }
      }
    }

    .el-dialog__footer {
      background: $dialog-bg-primary !important;
      border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
      padding: 16px 20px !important;
      text-align: right !important;

      .el-button {
        border-radius: 6px !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        
        &--primary {
          background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
          border: none !important;
          color: #ffffff !important;
          
          &:hover {
            background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%) !important;
          }
        }
        
        &--default {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          color: rgba(255, 255, 255, 0.9) !important;
          
          &:hover {
            background: rgba(255, 255, 255, 0.15) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

// ===========================
// 弹框变体样式
// ===========================

// 宽屏弹框样式 - 适用于需要更大显示空间的弹框
.common-dialog-wide {
  @extend .common-dialog;
  
  :deep(.el-dialog) {
    width: 50vw !important; // 宽屏占50%视口宽度
    max-width: 1600px !important;
    min-width: 1200px !important;
  }
}

// 带表格的弹框样式 - 减少内边距以充分利用空间
.common-dialog-with-table {
  @extend .common-dialog;
  
  :deep(.el-dialog__body) {
    padding: 10px !important; // 减少padding但保留少量间距
  }
  
  .dialog-content {
    padding: 0 !important;
    
    // 确保内容不超出弹框范围
    max-width: 100% !important;
    overflow-x: auto !important;
    
    // 表格容器样式
    .el-table {
      max-width: 100% !important;
    }
  }
}

// ===========================
// 弹框内容组件样式
// ===========================

// Tab标签样式
.common-dialog {
  .approval-tabs,
  .inspection-tabs {
    :deep(.el-tabs__header) {
      background: $dialog-bg-primary !important;
      margin-bottom: 20px !important;
      border-bottom: 1px solid var(--inspection-border, #374151) !important;
    }

    :deep(.el-tabs__nav-wrap) {
      &::after {
        display: none !important; // 隐藏默认底边线
      }
    }

    :deep(.el-tabs__item) {
      padding: 16px 20px !important;
      font-size: 14px !important;
      color: var(--inspection-text-secondary, #e2e8f0) !important;
      background: $dialog-bg-primary !important;
      border: none !important;
      border-bottom: 3px solid transparent !important;
      transition: all 0.3s ease !important;
      font-weight: 500 !important;
      line-height: 1.4 !important;

      // 悬停效果
      &:hover:not(.is-active) {
        color: var(--inspection-primary-light, #74a7f5) !important;
        background: rgba(92, 157, 255, 0.1) !important;
      }

      // 激活状态
      &.is-active {
        color: var(--inspection-primary, #5C9DFF) !important;
        background: var(--inspection-bg-secondary, rgba(9, 26, 75, 0.9)) !important;
        border-bottom-color: var(--inspection-primary, #5C9DFF) !important;
        font-weight: 600 !important;
      }

      // 图标样式
      i {
        margin-right: 6px !important;
        font-size: 14px !important;
      }
    }

    :deep(.el-tabs__active-bar) {
      display: none !important; // 隐藏Element UI默认的激活条
    }

    :deep(.el-tabs__content) {
      background: $dialog-bg-primary !important;
      color: $dialog-text-primary !important;
    }
  }
}

// 自定义关闭按钮样式
.custom-close-btn {
  position: absolute !important;
  top: 12px !important;
  right: 20px !important;
  z-index: 9999 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(255, 255, 255, 0.1) !important;

  &:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
  }

  svg {
    width: 24px !important;
    height: 24px !important;
    
    path {
      fill: #ffffff !important;
    }
  }
}

// ===========================
// 强制背景色应用
// ===========================

// 通用弹框背景色强制应用 - 确保所有弹框及其内容都使用深色主题
.common-dialog,
.common-dialog-wide,
.common-dialog-with-table {
  // 强制所有嵌套内容使用深色背景
  :deep(*) {
    // 强制重写Element UI的默认白色背景
    &.el-dialog__wrapper {
      background: rgba(0, 0, 0, 0.5) !important;
    }
    
    // 所有可能的白色背景元素
    &[style*="background"],
    &[style*="background-color"] {
      background: $dialog-bg-primary !important;
    }
    
    // 具体的背景元素
    .page-container,
    .content-wrapper,
    .main-content,
    .form-wrapper,
    .table-wrapper {
      background: $dialog-bg-primary !important;
    }
  }
}

// 紧急修复：强制覆盖Element UI的默认白色背景
.el-dialog[class*="common-dialog"] {
  background: $dialog-bg-primary !important;
  background-color: $dialog-bg-primary !important;
}

// ===========================
// 响应式适配
// ===========================

// 平板设备适配
@media (max-width: 1024px) and (min-width: 769px) {
  .common-dialog :deep(.el-dialog) {
    width: 80vw !important;
    min-width: 600px !important;
    max-width: 900px !important;
  }
  
  .common-dialog-wide :deep(.el-dialog) {
    width: 90vw !important;
    min-width: 700px !important;
    max-width: 1000px !important;
  }
}

// 手机设备适配
@media (max-width: 768px) {
  .common-dialog :deep(.el-dialog),
  .common-dialog-wide :deep(.el-dialog) {
    width: 95vw !important;
    min-width: 320px !important;
    max-width: none !important;
    margin-top: 2vh !important;
    max-height: 96vh !important;
  }
  
  .common-dialog :deep(.el-dialog__body),
  .common-dialog-wide :deep(.el-dialog__body) {
    padding: 15px !important;
    max-height: calc(96vh - 100px) !important;
  }
}
