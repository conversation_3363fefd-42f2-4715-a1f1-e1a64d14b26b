// 表单组件公共样式 - 深色主题
// 提供统一的表单样式，包括输入框、选择器、按钮等

// ===========================
// 表单样式变量
// ===========================
$form-input-bg: rgba(255, 255, 255, 0.1);
$form-input-border: rgba(255, 255, 255, 0.2);
$form-input-border-hover: rgba(255, 255, 255, 0.4);
$form-input-border-focus: var(--inspection-primary, #5C9DFF);
$form-input-text: var(--inspection-text-primary, #f8fafc);
$form-placeholder: rgba(255, 255, 255, 0.5);
$form-readonly-bg: rgba(255, 255, 255, 0.05);
$form-readonly-text: rgba(255, 255, 255, 0.7);

// ===========================
// 基础表单样式
// ===========================
.common-form {
  .el-form-item__label {
    color: $form-input-text !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    line-height: 140% !important;
    padding-bottom: 8px !important;
  }
  
  // 输入框样式
  .el-input__inner {
    background: $form-input-bg !important;
    border: 1px solid $form-input-border !important;
    color: $form-input-text !important;
    border-radius: 6px !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-size: var(--sds-typography-body-size-medium, 16px) !important;
    font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
    line-height: 140% !important;
    height: 40px !important;
    
    &::placeholder {
      color: $form-placeholder !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
    }
    
    &:hover {
      border-color: $form-input-border-hover !important;
    }
    
    &:focus {
      border-color: $form-input-border-focus !important;
      box-shadow: 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
    }
    
    &[readonly] {
      background-color: $form-readonly-bg !important;
      color: $form-readonly-text !important;
      cursor: not-allowed !important;
    }
  }
  
  // 文本域样式
  .el-textarea__inner {
    background: $form-input-bg !important;
    border: 1px solid $form-input-border !important;
    color: $form-input-text !important;
    border-radius: 6px !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-size: var(--sds-typography-body-size-medium, 16px) !important;
    font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
    line-height: 140% !important;
    
    &::placeholder {
      color: $form-placeholder !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
    }
    
    &:hover {
      border-color: $form-input-border-hover !important;
    }
    
    &:focus {
      border-color: $form-input-border-focus !important;
      box-shadow: 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
    }
    
    &[readonly] {
      background-color: $form-readonly-bg !important;
      color: $form-readonly-text !important;
      cursor: not-allowed !important;
    }
  }
  
  // 选择器样式
  .el-select {
    width: 100%;

    .el-input__inner {
      background: $form-input-bg !important;
      border: 1px solid $form-input-border !important;
      color: $form-input-text !important;
      border-radius: 8px !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
      line-height: 140% !important;
      height: 40px !important;

      &::placeholder {
        color: $form-placeholder !important;
      }

      &:hover {
        border-color: $form-input-border-hover !important;
      }

      &:focus {
        border-color: #409EFF !important;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
      }
    }

    // 下拉箭头样式
    .el-input__suffix {
      .el-input__suffix-inner {
        .el-select__caret,
        .el-input__icon {
          color: rgba(255, 255, 255, 0.7) !important;
          font-size: 14px !important;
          transition: color 0.3s ease !important;

          &:hover {
            color: rgba(255, 255, 255, 0.9) !important;
          }
        }

        // 清除按钮特殊样式
        .el-icon-circle-close {
          &:hover {
            color: #f56c6c !important;
          }
        }
      }
    }
  }
  
  // 日期选择器样式
  .el-date-editor .el-input__inner {
    background: $form-input-bg !important;
    border: 1px solid $form-input-border !important;
    color: $form-input-text !important;
  }
  
  // 单选框组样式
  .el-radio-group {
    .el-radio__label {
      color: $form-input-text !important;
    }
    
    .el-radio__input.is-checked + .el-radio__label {
      color: var(--inspection-primary, #5C9DFF) !important;
    }
  }
}

// ===========================
// 搜索表单样式
// ===========================
.search-form {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid $form-input-border !important;
  border-radius: 10px !important;
  padding: 16px 20px !important;
  margin-bottom: 24px !important;
  position: relative !important;
  
  // 左上角和右下角亮边框效果
  &::before {
    content: '' !important;
    position: absolute !important;
    top: -1px !important;
    left: -1px !important;
    width: 30px !important;
    height: 30px !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    border-right: none !important;
    border-bottom: none !important;
    border-radius: 10px 0 0 0 !important;
  }
  
  &::after {
    content: '' !important;
    position: absolute !important;
    bottom: -1px !important;
    right: -1px !important;
    width: 30px !important;
    height: 30px !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    border-left: none !important;
    border-top: none !important;
    border-radius: 0 0 10px 0 !important;
  }
  
  .el-form-item__label {
    color: #f8fafc !important;
    font-size: 14px !important;
  }
  
  .el-input__inner,
  .el-select .el-input__inner {
    background: $form-input-bg !important;
    border: 1px solid $form-input-border !important;
    color: #f8fafc !important;
    border-radius: 8px !important;
    
    &::placeholder {
      color: $form-placeholder !important;
    }
    
    &:hover {
      border-color: $form-input-border-hover !important;
    }
    
    &:focus {
      border-color: #409EFF !important;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
    }
  }
}

// ===========================
// 筛选区域样式
// ===========================
.filter-section {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid $form-input-border !important;
  border-radius: 10px !important;
  margin-top: 0px !important;
  margin-bottom: 12px !important;
  padding: 14px 20px !important;
  min-height: 52px !important;
  flex-shrink: 0 !important;
  position: relative;

  // 角落特效
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #2A3B6B;
    border-top-right-radius: 10px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    pointer-events: none;
    z-index: 2;
    background:
      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;
    background-position: top left, top left, bottom right, bottom right;
    background-repeat: no-repeat;
  }

  // 筛选标题
  .filter-title {
    margin-bottom: 20px;

    span {
      color: $form-input-text;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      font-size: var(--sds-typography-body-size-medium, 16px);
      font-weight: var(--sds-typography-body-font-weight-regular, 400);
      line-height: 140%;
    }
  }

  // 筛选内容区域
  .filter-content {
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;

    .filter-select {
      min-width: 180px;
      flex: 1;
      display: flex;
      align-items: center;

      .el-input__inner,
      .el-select__inner {
        background: $form-input-bg !important;
        border: 1px solid $form-input-border !important;
        border-radius: 8px !important;
        color: $form-input-text !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        font-size: var(--sds-typography-body-size-medium, 16px) !important;
        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
        line-height: 140% !important;
        height: 40px !important;
        box-sizing: border-box !important;
        display: flex !important;
        align-items: center !important;

        &::placeholder {
          color: $form-placeholder !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
        }

        &:hover {
          border-color: $form-input-border-hover !important;
        }

        &:focus {
          border-color: #409EFF !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        }
      }
    }

    .filter-row {
      display: flex;
      gap: 32px;
      align-items: flex-start;
      margin-bottom: 20px;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        flex-direction: column;
        min-width: 180px;

        label {
          display: block;
          color: $form-input-text !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
          line-height: 140% !important;
          margin-bottom: 8px !important;
        }

        .el-select {
          width: 100%;

          .el-input__inner {
            background: $form-input-bg !important;
            border: 1px solid $form-input-border !important;
            border-radius: 8px !important;
            color: $form-input-text !important;
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            font-size: var(--sds-typography-body-size-medium, 16px) !important;
            font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
            line-height: 140% !important;
            height: 40px !important;

            &::placeholder {
              color: $form-placeholder !important;
            }

            &:hover {
              border-color: $form-input-border-hover !important;
            }

            &:focus {
              border-color: #409EFF !important;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
            }
          }
        }
      }
    }

    // 操作按钮区域
    .filter-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// ===========================
// 按钮样式
// ===========================
.common-form,
.search-form,
.filter-section {
  .el-button {
    border-radius: 6px !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: 14px;
    font-weight: var(--sds-typography-body-font-weight-regular, 400);
    line-height: 140%;
    height: 36px;
    padding: 0 16px;

    &.el-button--primary {
      background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
      border: none !important;
      color: #FFF !important;

      &:hover {
        background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%) !important;
        transform: translateY(-1px);
      }

      &:active {
        background: linear-gradient(135deg, #337ECC 0%, #2B6CB0 100%) !important;
        transform: translateY(0);
      }
    }

    &.el-button--default {
      background: $form-input-bg !important;
      border: 1px solid rgba(255, 255, 255, 0.3) !important;
      color: rgba(255, 255, 255, 0.9) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: $form-input-border-hover !important;
        color: #FFF !important;
        transform: translateY(-1px);
      }

      &:active {
        background: rgba(255, 255, 255, 0.05) !important;
        transform: translateY(0);
      }
    }
  }
}

// ===========================
// 下拉选择器全局样式
// ===========================
.el-select-dropdown {
  background: var(--inspection-bg-secondary, rgba(9, 26, 75, 0.9));
  border: 1px solid var(--inspection-border, #374151);
  border-radius: 8px;
  box-shadow: var(--inspection-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2));

  .el-select-dropdown__item {
    color: var(--inspection-text-secondary, #e2e8f0);
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: var(--sds-typography-body-size-medium, 16px);
    font-weight: var(--sds-typography-body-font-weight-regular, 400);
    line-height: 140%;

    &:hover {
      background: var(--inspection-bg-tertiary, #334155);
      color: var(--inspection-text-primary, #f8fafc);
    }

    &.selected {
      background: var(--inspection-primary, #5C9DFF);
      color: var(--inspection-text-primary, #f8fafc);
      font-weight: 500;
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// ===========================
// 响应式设计
// ===========================
@media (max-width: 768px) {
  .filter-section {
    padding: 16px 20px !important;
    margin-top: 20px !important;

    .filter-content {
      .filter-row {
        flex-direction: column;
        gap: 16px;

        .filter-item {
          min-width: auto;
          width: 100%;
        }
      }

      .filter-actions {
        justify-content: center;
        align-items: center;
        margin-top: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .filter-section {
    margin: 16px 12px 0 12px !important;
    padding: 12px 16px !important;

    .filter-title {
      span {
        font-size: 14px;
      }
    }

    .filter-content {
      .filter-actions {
        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }

    .filter-item {
      label {
        font-size: 14px !important;
      }

      .el-select {
        .el-input__inner {
          height: 36px !important;
          font-size: 14px !important;
        }
      }
    }
  }
}

// ===========================
// 只读表单样式
// ===========================
.readonly-form {
  .maintenance-form {
    :deep(.el-form-item) {
      margin-bottom: 20px;
      
      .el-form-item__label {
        color: #e5e7eb;
        font-weight: 500;
      }
    }
    
    .readonly-input,
    .readonly-textarea {
      :deep(.el-input__inner),
      :deep(.el-textarea__inner) {
        background: #374151 !important;
        border: 1px solid #4b5563;
        color: #d1d5db;
        cursor: default;
        
        &:focus {
          border-color: #4b5563;
          box-shadow: none;
        }
      }
    }
  }
}

// ===========================
// 附件列表样式
// ===========================
.common-attachment-list {
  .attachment-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    margin-bottom: 8px;
    
    i {
      color: #60a5fa;
      margin-right: 8px;
      font-size: 16px;
    }
    
    .file-name {
      color: #d1d5db;
      flex: 1;
    }
    
    .file-size {
      color: #9ca3af;
      font-size: 12px;
      margin-left: 8px;
    }
  }
  
  .no-attachments {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background: #374151;
    border: 1px dashed #4b5563;
    border-radius: 6px;
  }
}