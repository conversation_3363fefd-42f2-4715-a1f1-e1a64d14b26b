// 导航组件公共样式 - 深色主题
// 提供统一的Tab导航、面包屑导航等样式

// ===========================
// Tab导航样式变量
// ===========================
$tab-bg-primary: #1e3a8a;
$tab-bg-secondary: #1e40af;
$tab-text-normal: #9ca3af;
$tab-text-active: #ffffff;
$tab-text-hover: #ffffff;
$tab-border-active: #3b82f6;
$tab-border-normal: transparent;

// ===========================
// 主要Tab导航样式
// ===========================
.common-tab-navigation {
  background: $tab-bg-primary;
  border-bottom: 1px solid #4b5563;
  
  .tab-container {
    display: flex;
    padding: 0 24px;
    
    .tab-item {
      padding: 12px 16px;
      color: $tab-text-normal;
      cursor: pointer;
      border-bottom: 3px solid $tab-border-normal;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 400;
      
      i {
        font-size: 14px;
        flex-shrink: 0;
      }
      
      &:hover:not(.is-active) {
        color: $tab-text-hover;
        background: rgba(59, 130, 246, 0.08);
      }
      
      &.is-active {
        color: $tab-text-active;
        border-bottom-color: $tab-border-active;
        font-weight: bold;
      }
    }
  }
}

// ===========================
// 二级Tab导航样式
// ===========================
.common-secondary-navigation {
  background: $tab-bg-secondary;
  border-bottom: 1px solid #4b5563;
  
  .sub-tab-container {
    display: flex;
    padding: 0 24px;
    
    .sub-tab-item {
      padding: 8px 16px;
      color: $tab-text-normal;
      cursor: pointer;
      border-bottom: 2px solid $tab-border-normal;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 400;
      
      &:hover:not(.is-active) {
        color: $tab-text-hover;
      }
      
      &.is-active {
        color: $tab-text-active;
        border-bottom-color: $tab-border-active;
        font-weight: bold;
      }
    }
  }
}

// ===========================
// 审核状态Tab导航样式
// ===========================
.common-status-tab-navigation {
  margin-bottom: 24px;
  
  .status-tabs {
    display: inline-flex;
    align-items: center;
    gap: 0;
    background: transparent;
    
    .tab-item {
      display: flex;
      height: 40px;
      padding: 10px 16px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      background: rgba(27, 42, 86, 0.6);
      border: 1px solid rgba(255, 255, 255, 0.1);
      margin: 0 8px 0 0;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
      gap: 6px;
      min-width: 80px;
      
      i {
        font-size: 14px;
        flex-shrink: 0;
      }
      
      &:hover:not(.is-active) {
        background: rgba(59, 130, 246, 0.08);
        color: #94a3b8;
        border-color: rgba(255, 255, 255, 0.2);
      }
      
      &.is-active {
        background: rgba(59, 130, 246, 0.15);
        color: #60a5fa;
        border-color: #60a5fa;
        box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
      }
    }
  }
}

// ===========================
// Element UI Tab组件样式覆盖
// ===========================
.common-element-tabs {
  :deep(.el-tabs__header) {
    margin: 0 0 20px 0;
    border-bottom: 1px solid #374151;
    background: transparent;
    
    .el-tabs__nav-wrap {
      &::after {
        background: #374151;
      }
    }
    
    .el-tabs__item {
      color: $tab-text-normal;
      font-size: 16px;
      font-weight: 500;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      border: none;
      
      &.is-active {
        color: $tab-border-active;
      }
      
      &:hover:not(.is-active) {
        color: #60a5fa;
      }
    }
    
    .el-tabs__active-bar {
      background: $tab-border-active;
    }
  }
  
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

// ===========================
// 页面头部样式
// ===========================
.common-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    h2 {
      color: #ffffff;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }
    
    .back-btn {
      color: #9ca3af;
      font-size: 16px;
      cursor: pointer;
      transition: color 0.3s ease;
      
      &:hover {
        color: #ffffff;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .close-btn {
      color: #9ca3af;
      font-size: 18px;
      cursor: pointer;
      transition: color 0.3s ease;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      
      &:hover {
        color: #ffffff;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// ===========================
// 响应式适配
// ===========================
@media (max-width: 768px) {
  .common-tab-navigation,
  .common-secondary-navigation {
    .tab-container,
    .sub-tab-container {
      padding: 0 16px;
      overflow-x: auto;
      white-space: nowrap;
      
      .tab-item,
      .sub-tab-item {
        flex-shrink: 0;
      }
    }
  }
  
  .common-page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .header-right {
      align-self: flex-end;
    }
  }
}
