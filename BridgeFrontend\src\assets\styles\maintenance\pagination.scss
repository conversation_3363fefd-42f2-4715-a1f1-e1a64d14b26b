// 分页组件公共样式 - 深色主题
// 提供统一的分页样式

// ===========================
// 分页样式变量
// ===========================
$pagination-bg: var(--inspection-bg-tertiary, #334155);
$pagination-text: var(--inspection-text-secondary, #e2e8f0);
$pagination-border: var(--inspection-border, #374151);
$pagination-hover-bg: var(--inspection-primary-light, #74a7f5);
$pagination-hover-text: var(--inspection-text-primary, #f8fafc);
$pagination-active-bg: var(--inspection-primary, #5C9DFF);
$pagination-active-text: var(--inspection-text-primary, #f8fafc);

// ===========================
// 基础分页样式
// ===========================
.common-pagination,
.inspection-pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  .el-pagination {
    .el-pager {
      li {
        background: $pagination-bg !important;
        color: $pagination-text !important;
        border: 1px solid $pagination-border !important;
        border-radius: 6px !important;
        margin: 0 4px !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 140%;
        transition: all 0.3s ease;

        &:hover {
          background: $pagination-hover-bg !important;
          color: $pagination-hover-text !important;
          border-color: $pagination-hover-bg !important;
          transform: translateY(-1px);
        }

        &.active {
          background: $pagination-active-bg !important;
          color: $pagination-active-text !important;
          border-color: $pagination-active-bg !important;
          font-weight: 500;
        }
      }
    }

    .btn-prev, 
    .btn-next {
      background: $pagination-bg !important;
      color: $pagination-text !important;
      border: 1px solid $pagination-border !important;
      border-radius: 6px !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 140%;
      transition: all 0.3s ease;

      &:hover {
        background: $pagination-hover-bg !important;
        color: $pagination-hover-text !important;
        border-color: $pagination-hover-bg !important;
        transform: translateY(-1px);
      }

      &:disabled {
        background: rgba(255, 255, 255, 0.05) !important;
        color: rgba(255, 255, 255, 0.3) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        cursor: not-allowed !important;
        transform: none !important;

        &:hover {
          background: rgba(255, 255, 255, 0.05) !important;
          color: rgba(255, 255, 255, 0.3) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
          transform: none !important;
        }
      }
    }

    .el-pagination__total,
    .el-pagination__jump {
      color: $pagination-text !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 140%;
    }

    .el-pagination__jump {
      .el-input__inner {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: var(--inspection-text-primary, #f8fafc) !important;
        border-radius: 4px !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 14px;
        text-align: center;

        &:hover {
          border-color: rgba(255, 255, 255, 0.4) !important;
        }

        &:focus {
          border-color: var(--inspection-primary, #5C9DFF) !important;
          box-shadow: 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
        }
      }
    }

    .el-pagination__sizes {
      .el-select {
        .el-input__inner {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          color: var(--inspection-text-primary, #f8fafc) !important;
          border-radius: 4px !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 14px;

          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important;
          }

          &:focus {
            border-color: var(--inspection-primary, #5C9DFF) !important;
            box-shadow: 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
          }
        }

        .el-input__suffix {
          .el-select__caret {
            color: rgba(255, 255, 255, 0.7) !important;

            &:hover {
              color: rgba(255, 255, 255, 0.9) !important;
            }
          }
        }
      }
    }
  }
}

// ===========================
// 分页容器样式
// ===========================
.pagination-container {
  @extend .common-pagination;
  
  // 额外的容器样式
  padding: 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 20px;
}

// ===========================
// 分页信息样式
// ===========================
.pagination-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  color: var(--inspection-text-secondary, #e2e8f0);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;

  .info-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .total-count {
      color: var(--inspection-text-primary, #f8fafc);
      font-weight: 500;

      .highlight {
        color: var(--inspection-primary, #5C9DFF);
        font-weight: 600;
      }
    }

    .selected-count {
      color: var(--inspection-warning, #f59e0b);
      font-weight: 500;

      .highlight {
        color: var(--inspection-warning, #f59e0b);
        font-weight: 600;
      }
    }
  }

  .info-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .page-size-selector {
      display: flex;
      align-items: center;
      gap: 8px;

      label {
        color: var(--inspection-text-secondary, #e2e8f0);
        font-size: 14px;
        white-space: nowrap;
      }

      .el-select {
        width: 80px;

        .el-input__inner {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          color: var(--inspection-text-primary, #f8fafc) !important;
          border-radius: 4px !important;
          font-size: 14px;
          text-align: center;

          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important;
          }

          &:focus {
            border-color: var(--inspection-primary, #5C9DFF) !important;
            box-shadow: 0 0 0 2px rgba(92, 157, 255, 0.2) !important;
          }
        }
      }
    }
  }
}

// ===========================
// 响应式设计
// ===========================
@media (max-width: 768px) {
  .common-pagination,
  .inspection-pagination {
    .el-pagination {
      .el-pager {
        li {
          margin: 0 2px !important;
          min-width: 32px;
          height: 32px;
          line-height: 30px;
          font-size: 13px;
        }
      }

      .btn-prev, 
      .btn-next {
        min-width: 32px;
        height: 32px;
        line-height: 30px;
        font-size: 13px;
      }

      .el-pagination__total,
      .el-pagination__jump {
        font-size: 13px;
      }

      .el-pagination__jump {
        .el-input__inner {
          height: 32px;
          font-size: 13px;
        }
      }

      .el-pagination__sizes {
        .el-select {
          .el-input__inner {
            height: 32px;
            font-size: 13px;
          }
        }
      }
    }
  }

  .pagination-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .info-left,
    .info-right {
      width: 100%;
      justify-content: space-between;
    }

    .info-right {
      .page-size-selector {
        .el-select {
          width: 70px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .common-pagination,
  .inspection-pagination {
    margin-top: 16px;

    .el-pagination {
      .el-pager {
        li {
          margin: 0 1px !important;
          min-width: 28px;
          height: 28px;
          line-height: 26px;
          font-size: 12px;
        }
      }

      .btn-prev, 
      .btn-next {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
        font-size: 12px;
      }

      .el-pagination__total,
      .el-pagination__jump {
        font-size: 12px;
      }

      .el-pagination__jump {
        .el-input__inner {
          height: 28px;
          font-size: 12px;
        }
      }

      .el-pagination__sizes {
        .el-select {
          .el-input__inner {
            height: 28px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .pagination-info {
    font-size: 13px;

    .info-right {
      .page-size-selector {
        label {
          font-size: 13px;
        }

        .el-select {
          width: 60px;

          .el-input__inner {
            font-size: 12px;
          }
        }
      }
    }
  }
}
