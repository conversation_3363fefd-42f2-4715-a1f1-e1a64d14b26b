// 项目配置组件专用样式
// 用于养护项目配置页面的特定样式

// ===========================
// 项目配置容器样式
// ===========================
.common-project-config {
  .add-project-btn {
    min-width: 120px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border-color: rgba(59, 130, 246, 0.7);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    i {
      font-size: 14px;
    }
  }
  
  .project-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .project-row {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(30, 58, 138, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-bottom: 16px;
    
    &:hover {
      background: rgba(30, 58, 138, 0.4);
      border-color: rgba(59, 130, 246, 0.5);
    }
    
    .project-input-container {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      
      .project-name-input {
        flex: 1;
        min-width: 200px;
        
        :deep(.el-input__inner) {
          background: #374151;
          border: 1px solid #6b7280;
          color: #ffffff;
          height: 40px;
          border-radius: 6px;
          
          &::placeholder {
            color: #9ca3af;
          }
          
          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
      
      // 保洁项目的频次输入框
      .frequency-input {
        width: 80px;
        
        :deep(.el-input__inner) {
          background: #374151;
          border-color: #6b7280;
          color: #ffffff;
          height: 40px;
          border-radius: 6px;
          text-align: center;
          
          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
      
      .frequency-unit {
        color: #e5e7eb;
        font-size: 14px;
        white-space: nowrap;
        margin-left: 4px;
      }
    }
    
    .cancel-btn {
      color: #ef4444;
      font-size: 14px;
      min-width: 60px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        color: #dc2626;
        background-color: rgba(239, 68, 68, 0.1);
      }
      
      &:focus {
        color: #dc2626;
        background-color: rgba(239, 68, 68, 0.1);
      }
    }
    
    // 确保最后一行不会有多余的margin
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  // 只读模式下的样式调整
  &.readonly {
    .project-input-container {
      .project-name-input :deep(.el-input__inner) {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
      
      .frequency-input :deep(.el-input__inner) {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
    }
  }
}

// 确保在深色主题下有良好的视觉效果
.maintenance-theme .common-project-config,
.inspection-theme .common-project-config {
  .add-project-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .project-row {
    .project-input-container {
      .project-name-input :deep(.el-input__inner),
      .frequency-input :deep(.el-input__inner) {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
