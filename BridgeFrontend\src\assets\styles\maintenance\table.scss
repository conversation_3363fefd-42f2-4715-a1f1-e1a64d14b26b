// 表格组件公共样式 - 深色主题
// 提供统一的表格样式，包括表头、表体、状态标签等

// ===========================
// 表格样式变量
// ===========================
$table-header-bg: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
$table-row-bg: linear-gradient(180deg, #243066 0%, #1C2A4E 100%);
$table-border: rgba(255, 255, 255, 0.1);
$table-text-color: #FFF;
$table-hover-bg: rgba(255, 255, 255, 0.05);

// ===========================
// 基础表格样式
// ===========================
.common-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  .el-table {
    background: transparent;
    border: none;
    border-radius: 0;
    overflow: visible;
    box-shadow: none;
    flex: 1;

    &::before,
    &::after {
      display: none;
    }

    // 移除表格边框
    .el-table__border-left-patch,
    .el-table__border-right-patch,
    .el-table__border-bottom-patch {
      display: none !important;
    }

    // 移除表格容器的边框
    .el-table__body-wrapper,
    .el-table__footer-wrapper,
    .el-table__append-wrapper {
      border: none !important;
    }

    // 表头样式
    .el-table__header-wrapper {
      .el-table__header {
        background: transparent;

        th {
          background: $table-header-bg;
          color: $table-text-color;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          border-bottom: 1px solid $table-border;
          border-left: 1px solid $table-border;
          border-right: 1px solid $table-border;
          padding: 0;
          white-space: nowrap;

          .cell {
            display: flex;
            height: 44px;
            padding: 14px 24px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            align-self: stretch;
            color: $table-text-color;
            white-space: nowrap !important;
            word-break: keep-all !important;
          }
        }
      }
    }

    // 表体样式
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          background: transparent;

          &:hover {
            background: $table-hover-bg !important;
          }

          // 移除最后一行的底部边框
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: $table-row-bg;
            border-bottom: 1px solid $table-border;
            border-left: 1px solid $table-border;
            border-right: 1px solid $table-border;
            padding: 4px 12px;
            text-align: center;
            height: 32px;

            .cell {
              color: $table-text-color;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 120%;
              justify-content: center;
              align-items: center;
              text-align: center;
            }
          }
        }
      }
    }

    // 固定列样式
    .el-table__fixed-right {
      background: transparent !important;
      height: 100% !important;
      max-height: none !important;
      min-height: auto !important;
      border: none !important;
      box-shadow: none !important;

      // 移除伪元素
      &::before,
      &::after {
        content: none !important;
        display: none !important;
      }

      // 固定列表头样式
      .el-table__header {
        background: transparent;

        th {
          background: $table-header-bg !important;
          color: $table-text-color !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          border-bottom: 1px solid $table-border !important;
          border-left: 1px solid $table-border !important;
          border-right: 1px solid $table-border !important;
          vertical-align: middle !important;
          text-align: center !important;

          .cell {
            display: flex !important;
            height: 44px !important;
            padding: 14px 16px !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 10px;
            align-self: stretch;
            color: $table-text-color !important;
            text-align: center !important;
            vertical-align: middle !important;
            line-height: 1 !important;
          }
        }
      }

      // 固定列单元格样式
      .el-table__body {
        tr {
          // 移除最后一行的底部边框
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: $table-row-bg !important;
            border-bottom: 1px solid $table-border !important;
            border-left: 1px solid $table-border !important;
            border-right: 1px solid $table-border !important;
            padding: 4px 12px;
            text-align: center;
            vertical-align: middle !important;
            height: 32px !important;

            .cell {
              color: $table-text-color !important;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 120%;
              display: flex !important;
              justify-content: center !important;
              align-items: center !important;
              text-align: center !important;
              flex-direction: row !important;
              flex-wrap: nowrap !important;
              gap: 8px !important;
              width: 100% !important;
              height: 100% !important;
              min-height: 32px !important;
              max-height: 32px !important;
              padding: 0 !important;
              margin: 0 !important;
              line-height: 32px !important;
            }
          }
        }
      }

      // 确保固定列容器高度正确
      .el-table__fixed-body-wrapper {
        height: 100% !important;
        overflow: visible !important;
        position: relative !important;
        border: none !important;
        box-shadow: none !important;
        
        &::before,
        &::after {
          display: none !important;
        }
      }

      .el-table__fixed-header-wrapper {
        height: auto !important;
        border-bottom: none !important;
        box-shadow: none !important;
        
        &::before,
        &::after {
          display: none !important;
        }
      }
    }

    // 空数据样式
    .el-table__empty-block {
      background: transparent !important;
      color: var(--inspection-text-muted, #94a3b8) !important;
    }
  }
}

// ===========================
// 操作按钮样式
// ===========================
.common-table {
  // 操作按钮通用样式
  .el-button--text,
  .action-buttons .el-button--text,
  .action-buttons .el-button {
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 120% !important;
    padding: 2px 6px !important;
    margin: 0 !important;
    border: none !important;
    background: none !important;
    height: 20px !important;
    min-height: 20px !important;
    max-height: 20px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      cursor: pointer !important;
    }

    &:focus {
      background: none !important;
    }
  }

  // 删除按钮样式 - 红色主题
  .custom-delete-btn.el-button.el-button--text,
  .danger-btn.el-button.el-button--text {
    color: #f56c6c !important;
    
    &:hover,
    &:hover:not(:disabled) {
      color: #f78989 !important;
      background: rgba(245, 108, 108, 0.1) !important;
    }
    
    &:focus,
    &:focus:not(:disabled) {
      color: #f56c6c !important;
      background: none !important;
    }
    
    &:active,
    &:active:not(:disabled) {
      color: #f56c6c !important;
      background: rgba(245, 108, 108, 0.2) !important;
    }
  }

  // 审批按钮样式 - 白色主题
  .custom-approve-btn.el-button.el-button--text {
    color: #ffffff !important;
    
    &:hover,
    &:hover:not(:disabled) {
      color: #e0e0e0 !important;
      background: rgba(255, 255, 255, 0.1) !important;
    }
    
    &:focus,
    &:focus:not(:disabled) {
      color: #ffffff !important;
      background: none !important;
    }
    
    &:active,
    &:active:not(:disabled) {
      color: #ffffff !important;
      background: rgba(255, 255, 255, 0.2) !important;
    }
  }

  // ActionButtons组件样式
  .action-buttons {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 6px !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: 32px !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 32px !important;

    .el-button {
      flex-shrink: 0 !important;
      margin: 0 !important;
      
      // 默认text类型按钮为白色
      &.el-button--text {
        color: #FFF !important;

        &:hover {
          color: #E0E0E0 !important;
          background: rgba(255, 255, 255, 0.1) !important;
        }

        &:focus {
          color: #FFF !important;
          background: none !important;
        }
      }

      // 功能按钮颜色
      &.el-button--primary {
        color: #FFF !important;
        background-color: #409EFF !important;
        border-color: #409EFF !important;

        &:hover {
          background-color: #66b1ff !important;
          border-color: #66b1ff !important;
        }
      }

      &.el-button--warning {
        color: #FFF !important;
        background-color: #e6a23c !important;
        border-color: #e6a23c !important;

        &:hover {
          background-color: #ebb563 !important;
          border-color: #ebb563 !important;
        }
      }

      &.el-button--success {
        color: #FFF !important;
        background-color: #67c23a !important;
        border-color: #67c23a !important;

        &:hover {
          background-color: #85ce61 !important;
          border-color: #85ce61 !important;
        }
      }
    }
  }
}

// ===========================
// 状态标签样式
// ===========================
.status-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;

  &.status-0 {
    background: rgba(16, 185, 129, 0.2);
    color: var(--inspection-success, #10b981);
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  &.status-1 {
    background: rgba(245, 158, 11, 0.2);
    color: var(--inspection-warning, #f59e0b);
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  &.status-2,
  &.status-3,
  &.status-high {
    background: rgba(239, 68, 68, 0.2);
    color: var(--inspection-danger, #ef4444);
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
}

// 本月缺巡次数样式
.missing-patrol-count {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  width: 26px;
  height: 22px;
  padding: 0;
  margin: 0 auto;

  // 0次 - 绿色背景
  &.count-0 {
    background: rgba(179, 255, 200, 0.82);
    color: #059669;
  }

  // 1次 - 黄色背景
  &.count-1 {
    background: rgba(248, 252, 146, 0.80);
    color: #D97706;
  }

  // 2次以上 - 橙红色背景
  &.count-2-plus {
    background: rgba(255, 203, 196, 0.80);
    color: #D97706;
  }
}

// ===========================
// 表格别名和扩展
// ===========================

// 为不同模块的表格提供别名
.audit-table,
.maintenance-table,
.inspection-data-table,
.compact-table,
.diseases-table,
.projects-table,
.approval-table,
.approval-records-table,
.tasks-table,
.inspection-table {
  @extend .common-table;
}

// 表格头部样式增强 - 防止文字换行
.table-header-wrap {
  white-space: nowrap !important;
  word-break: keep-all !important;
  display: inline-block;
}

// 确保表格底部没有白线
.common-table {
  .el-table__body-wrapper {
    border-bottom: none !important;
  }

  // 移除表格底部的所有边框样式和伪元素
  &::after,
  .el-table::after,
  .el-table__body-wrapper::after,
  .el-table__fixed-right::after {
    display: none !important;
  }

  // 修复固定列高度问题
  .el-table__fixed-right {
    height: 100% !important;
    border-bottom: none !important;
    border-right: none !important;
    
    &::before,
    &::after {
      content: none !important;
      display: none !important;
      background: none !important;
      border: none !important;
      height: 0 !important;
      width: 0 !important;
    }
  }
}
