/**
* 表格滚动条样式
* $width  纵向滚动条的宽
* $height 横向滚动条的高
* $radius 滑块圆角
* $thumbColor 滑块颜色
* $trackColor 滑块轨道颜色
*/
@mixin tableScrollBar($width: 5px, $height: 8px, $radius: 3px, $thumbColor: #ddd, $trackColor: #fff) {
  // 底线
  &::before {
    height: 0px;
  }
  // 滚动条整体样式
  &::-webkit-scrollbar {
    width: $width;
    height: $height;
  }
  &::-webkit-scrollbar-thumb {
    // 滚动条里面小方块
    background-color: $thumbColor;
    border-radius: $radius;
  }
  &::-webkit-scrollbar-track {
    // 滚动条里面轨道
    background-color: $trackColor;
  }
  &::-webkit-scrollbar-corner {
    // 边角，两个滚动条交汇处 		// height: calc(100% - 4px) !important;
    background-color: transparent;
  }

  // 滚动条整体样式
  & .el-table__body-wrapper::-webkit-scrollbar {
    width: $width;
    height: $height;
  }
  & .el-table__body-wrapper::-webkit-scrollbar-thumb {
    // 滚动条里面小方块
    background-color: $thumbColor;
    border-radius: $radius;
  }
  & .el-table__body-wrapper:-webkit-scrollbar-track {
    // 滚动条里面轨道
    background-color: $trackColor;
  }
  & .el-table__body-wrapper::-webkit-scrollbar-corner {
    // 边角，两个滚动条交汇处 		// height: calc(100% - 4px) !important;
    background-color: transparent;
  }
  // 解决表格固定列问题
  & .el-table__fixed,
  .el-table__fixed-right {
    height: calc(100% - #{$height}) !important;
    .el-table__fixed-body-wrapper {
      height: calc(100% - px(44px)) !important;
    }
    // 底线
    &::before {
      height: 0px;
    }
  }
  // 当表格没有滚动条时
  & .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed-right,
  & .el-table__body-wrapper.is-scrolling-none ~ .el-table__fixed {
    height: 100% !important;
  }
  // 当表格有纵向滚动条时(对应-webkit-scrollbar的width: 6px;)
  &.el-table--scrollable-y .el-table__fixed-right {
    right: $width !important;
  }
  // 当表格只有横向滚动条，没有纵向滚动条时
  &.el-table--scrollable-x:not(.el-table--scrollable-y) .el-table__fixed-right {
    right: 0 !important;
  }
}

/**
* 表格透明样式
*/
@mixin tableTransparent() {
  background-color: transparent;
  .background-table-header-row {
    background-color: transparent;
    & th {
      border-bottom: px(1px) solid rgba(255, 255, 255, 0.2);
    }
  }
  .background-table-header-cell {
    color: #FFF;
    background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
    border-left: px(1px) solid rgba(255, 255, 255, 0.1);
    border-right: px(1px) solid rgba(255, 255, 255, 0.1);
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 22.4px */
    
    .cell {
      display: flex;
      height: 48px;
      padding: 24px 47px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      align-self: stretch;
    }
  }

  .background-table-row {
    color: #fff;
    background-color: transparent;
    &.hover-row {
      background-color: rgba(255, 255, 255, 0.3);
    }
    & td {
      background: linear-gradient(180deg, #4A5A84 0%, #3A4A76 100%);
      border-bottom: px(1px) solid rgba(255, 255, 255, 0.2);
      border-left: px(1px) solid rgba(255, 255, 255, 0.1);
      border-right: px(1px) solid rgba(255, 255, 255, 0.1);
      text-align: center;
      
      .cell {
        justify-content: center;
        align-items: center;
        text-align: center;
      }
    }
    &.el-table__row--striped td {
      background: linear-gradient(180deg, #4A5A84 0%, #3A4A76 100%);
      text-align: center;
      
      .cell {
        justify-content: center;
        align-items: center;
        text-align: center;
      }
    }
  }

  .el-table__body tr:hover > td.el-table__cell {
    background-color: rgba(255, 255, 255, 0) !important;
  }
  .el-table__body tr.hover-row > td.el-table__cell {
    background-color: rgba(255, 255, 255, 0) !important;
  }

  .el-table__fixed-right {
    background-color: transparent;
    .background-table-row {
      & td {
        border-bottom: px(1px) solid rgba(255, 255, 255, 0);
      }
    }
    .background-table-header-row {
      & th {
        border-bottom: px(1px) solid rgba(255, 255, 255, 0);
      }
    }

    .el-table__body {
      .background-table-row {
        &.hover-row {
          background-color: rgba(0, 0, 0, 0) !important;
        }
        &.el-table__row--striped td{
          background-color: #38466E !important;
        }
      }
      tr.hover-row > td.el-table__cell {
        background-color: rgba(0, 0, 0, 0) !important;
      }
    }
  }

  .el-table__empty-text {
    color: #cacaca;
  }
  .el-loading-spinner .path {
    stroke: #fff;
  }
  .gutter {
    background-color: transparent;
  }
  
  // 本月缺巡次数样式
  .missing-patrol-count {
    display: flex;
    width: 26px;
    height: 22px;
    padding: 0;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    
    // 0次 - 绿色背景
    &.count-0 {
      background: rgba(179, 255, 200, 0.82);
      color: #059669; // 深绿色文字
    }
    
    // 1次 - 黄色背景
    &.count-1 {
      background: rgba(248, 252, 146, 0.80);
      color: #D97706; // 深黄色文字
    }
    
    // 2次以上 - 新的背景色和尺寸
    &.count-2-plus {
      background: rgba(255, 203, 196, 0.80);
      color: #D97706;
    }
  }
}
