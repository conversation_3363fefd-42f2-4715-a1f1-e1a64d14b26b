<template>
  <div class="action-buttons">
    <template v-for="button in visibleButtons">
      <el-button
        :key="button.key"
        :type="button.type"
        :size="button.size || size"
        :plain="button.plain"
        :disabled="button.disabled"
        :loading="button.loading"
        @click="handleButtonClick(button)"
      >
        <i v-if="button.icon" :class="button.icon" class="button-icon"></i>
        {{ button.text }}
      </el-button>
    </template>
  </div>
</template>

<script>
export default {
  name: 'ActionButtons',
  props: {
    // 数据记录
    record: {
      type: Object,
      required: true
    },
    // 按钮类型：disease | inspection
    type: {
      type: String,
      default: 'disease'
    },
    // 按钮大小
    size: {
      type: String,
      default: 'small'
    },
    // 用户权限
    permissions: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loadingButtons: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 病害管理按钮配置
    diseaseButtons() {
      const buttons = [
        {
          key: 'view',
          text: '查看',
          type: 'text',
          icon: 'el-icon-view',
          permission: 'inspection:disease:view',
          showForStatus: ['judging', 'planning', 'disposing', 'reviewing', 'archived', 'rejected']
        },
        {
          key: 'judge',
          text: '判定',
          type: 'primary',
          size: 'mini',
          icon: 'el-icon-edit',
          permission: 'inspection:disease:judge',
          showForStatus: ['judging']
        },
        {
          key: 'dispose',
          text: '处置',
          type: 'warning',
          size: 'mini',
          icon: 'el-icon-setting',
          permission: 'inspection:disease:dispose',
          showForStatus: ['planning']
        },
        {
          key: 'review',
          text: '复核',
          type: 'success',
          size: 'mini',
          icon: 'el-icon-check',
          permission: 'inspection:disease:review',
          showForStatus: ['reviewing']
        },
        {
          key: 'edit',
          text: '编辑',
          type: 'text',
          icon: 'el-icon-edit',
          permission: 'inspection:disease:edit',
          showForStatus: ['judging', 'planning']
        },
        {
          key: 'delete',
          text: '删除',
          type: 'text',
          icon: 'el-icon-delete',
          permission: 'inspection:disease:delete',
          showForStatus: ['judging'],
          plain: true,
          style: 'danger'
        }
      ]
      
      return buttons.filter(button => {
        // 检查状态匹配
        const statusMatch = button.showForStatus.includes(this.record.diseaseStatus || this.record.status)
        
        // 检查权限
        const hasPermission = !button.permission || this.permissions.includes(button.permission)
        
        return statusMatch && hasPermission
      })
    },
    
    // 巡检记录按钮配置
    inspectionButtons() {
      const buttons = [
        {
          key: 'log',
          text: '日志',
          type: 'text',
          icon: 'el-icon-date'
          // 移除权限要求，允许所有用户查看日志
        },
        {
          key: 'report',
          text: '报告',
          type: 'text',
          icon: 'el-icon-document'
          // 移除权限要求，允许所有用户查看报告
        }
      ]
      
      return buttons.filter(button => {
        // 检查权限
        const hasPermission = !button.permission || this.permissions.includes(button.permission)
        return hasPermission
      })
    },
    
    // 可见按钮列表
    visibleButtons() {
      const buttons = this.type === 'disease' ? this.diseaseButtons : this.inspectionButtons
      
      return buttons.map(button => ({
        ...button,
        loading: this.loadingButtons.includes(button.key),
        disabled: this.isButtonDisabled(button)
      }))
    }
  },
  methods: {
    // 检查按钮是否禁用
    isButtonDisabled(button) {
      // 如果正在加载，禁用按钮
      if (this.loadingButtons.includes(button.key)) {
        return true
      }
      
      // 根据业务逻辑禁用按钮
      if (button.key === 'delete' && this.record.diseaseStatus !== 'judging') {
        return true
      }
      
      return false
    },
    
    // 处理按钮点击
    handleButtonClick(button) {
      if (button.disabled || button.loading) {
        return
      }
      
      // 危险操作需要确认
      if (button.style === 'danger') {
        this.$confirm(`确定要${button.text}这条记录吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('button-click', {
            action: button.key,
            record: this.record,
            button: button
          })
        }).catch(() => {
          // 取消操作
        })
      } else {
        this.$emit('button-click', {
          action: button.key,
          record: this.record,
          button: button
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  
  .button-icon {
    margin-right: 4px;
  }
  
  // 确保按钮在表格中正确显示
  .el-button {
    margin-left: 0;
    margin-right: 0;
  }
  
  // 危险操作按钮样式
  .el-button--text {
    &.is-danger {
      color: #f56c6c;
      
      &:hover {
        color: #f78989;
        background-color: #fef0f0;
      }
    }
  }
}

// 响应式处理
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
    
    .el-button {
      width: 100%;
      justify-content: center;
    }
  }
}</style>
