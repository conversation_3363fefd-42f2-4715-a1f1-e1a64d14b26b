<template>
  <div class="inspection-calendar">
    <!-- 图例说明 -->
    <div class="calendar-legend">
      <span class="legend-title">图例说明</span>
      <div class="legend-items">
        <div class="legend-item">
          <span class="legend-dot daily"></span>
          <span class="legend-text">日常巡检</span>
        </div>
        <div class="legend-item">
          <span class="legend-dot regular"></span>
          <span class="legend-text">经常巡检</span>
        </div>
        <div class="legend-item">
          <span class="legend-dot center"></span>
          <span class="legend-text">中心巡检</span>
        </div>
        <div class="legend-item">
          <span class="legend-dot uninspected"></span>
          <span class="legend-text">未巡检</span>
        </div>
      </div>
    </div>
    
    <!-- 月份导航 -->
    <div class="calendar-header">
      <el-button 
        icon="el-icon-arrow-left" 
        circle 
        size="mini"
        @click="prevMonth"
      ></el-button>
      <span class="current-month">{{ currentMonthText }}</span>
      <el-button 
        icon="el-icon-arrow-right" 
        circle 
        size="mini"
        @click="nextMonth"
      ></el-button>
    </div>
    
    <!-- 日历主体 -->
    <div class="calendar-body">
      <!-- 星期标题 -->
      <div class="week-header">
        <div 
          v-for="day in weekDays" 
          :key="day"
          class="week-day"
        >
          {{ day }}
        </div>
      </div>
      
      <!-- 日历网格 -->
      <div class="calendar-grid">
        <div 
          v-for="date in calendarDates" 
          :key="date.key"
          class="calendar-cell"
          :class="{
            'other-month': !date.isCurrentMonth,
            'selected': date.isSelected,
            'today': date.isToday
          }"
          @click="handleDateClick(date)"
        >
          <div 
            class="date-number" 
            :class="[
              getDateCircleClass(date),
              { 'has-inspections': date.inspectionData && date.inspectionData.length > 0 }
            ]"
          >
            {{ date.day }}
          </div>
          <!-- 巡检次数显示在右下角 -->
          <div v-if="date.inspectionData && date.inspectionData.length > 0" class="inspection-count">
            {{ date.inspectionData.length }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 详情展示区域 -->
    <div v-if="selectedDateInfo" class="date-detail">
      <div class="detail-title">
        {{ selectedDateInfo.dateText }} - 巡检记录
      </div>
      <div class="inspection-list">
        <div 
          v-for="inspection in selectedDateInfo.inspections" 
          :key="inspection.id"
          class="inspection-item"
          @click="handleInspectionItemClick(inspection)"
        >
          <div class="inspection-header">
            <span 
              class="inspection-type-dot" 
              :class="inspection.type"
            ></span>
            <span class="inspection-type-text">{{ getInspectionTypeText(inspection.type) }}</span>
            <span class="inspection-time">{{ inspection.time }}</span>
          </div>
          <div class="inspection-inspector">巡检人员：{{ inspection.inspector }}</div>
          <div v-if="inspection.description" class="inspection-description">{{ inspection.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CalendarView',
  props: {
    // 当前月份 YYYY-MM 格式
    currentMonth: {
      type: String,
      default: () => {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        return `${year}-${month}`
      }
    },
    // 巡检日志数据
    inspectionLogs: {
      type: Array,
      default: () => []
    },
    // 选中的日期
    selectedDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      weekDays: ['日', '一', '二', '三', '四', '五', '六'],
      internalSelectedDate: this.selectedDate
    }
  },
  computed: {
    currentMonthText() {
      if (!this.currentMonth) return ''
      const [year, month] = this.currentMonth.split('-')
      return `${year}年${month}月`
    },
    
    calendarDates() {
      if (!this.currentMonth) return []
      
      const [year, month] = this.currentMonth.split('-')
      const currentDate = new Date(year, month - 1, 1)
      const today = new Date()
      const todayStr = today.toISOString().split('T')[0]
      
      // 获取当月第一天是星期几
      const firstDayWeek = currentDate.getDay()
      
      // 获取当月天数
      const daysInMonth = new Date(year, month, 0).getDate()
      
      // 获取上月天数
      const prevMonth = new Date(year, month - 1, 0)
      const daysInPrevMonth = prevMonth.getDate()
      
      const dates = []
      
      // 添加上月末尾几天
      for (let i = firstDayWeek - 1; i >= 0; i--) {
        const day = daysInPrevMonth - i
        const dateStr = `${year}-${String(month - 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        dates.push({
          day,
          dateStr,
          isCurrentMonth: false,
          isToday: dateStr === todayStr,
          isSelected: dateStr === this.internalSelectedDate,
          key: dateStr,
          inspectionData: this.getInspectionData(dateStr)
        })
      }
      
      // 添加当月天数
      for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        dates.push({
          day,
          dateStr,
          isCurrentMonth: true,
          isToday: dateStr === todayStr,
          isSelected: dateStr === this.internalSelectedDate,
          key: dateStr,
          inspectionData: this.getInspectionData(dateStr)
        })
      }
      
      // 添加下月开头几天，补齐42个格子
      const remainingCells = 42 - dates.length
      for (let day = 1; day <= remainingCells; day++) {
        const dateStr = `${year}-${String(parseInt(month) + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        dates.push({
          day,
          dateStr,
          isCurrentMonth: false,
          isToday: dateStr === todayStr,
          isSelected: dateStr === this.internalSelectedDate,
          key: dateStr,
          inspectionData: this.getInspectionData(dateStr)
        })
      }
      
      return dates
    },
    
    selectedDateInfo() {
      if (!this.internalSelectedDate) return null
      
      const inspections = this.inspectionLogs.filter(log => 
        log.date === this.internalSelectedDate
      )
      
      if (!inspections.length) return null
      
      const dateObj = new Date(this.internalSelectedDate)
      const dateText = `${dateObj.getFullYear()}年${String(dateObj.getMonth() + 1).padStart(2, '0')}月${String(dateObj.getDate()).padStart(2, '0')}日`
      
      return {
        dateText,
        inspections
      }
    }
  },
  watch: {
    selectedDate(newVal) {
      this.internalSelectedDate = newVal
    }
  },
  methods: {
    getInspectionData(dateStr) {
      const logs = this.inspectionLogs.filter(log => log.date === dateStr)
      return logs.length > 0 ? logs : null
    },
    
    getInspectionTypeText(type) {
      const typeMap = {
        'daily': '日常巡检',
        'regular': '经常巡检',
        'center': '中心巡检',
        'uninspected': '未巡检'
      }
      return typeMap[type] || type
    },
    
    getDateCircleClass(date) {
      // 非本月日期不显示圈颜色
      if (!date.isCurrentMonth) {
        return ''
      }
      
      // 没有巡检记录，显示红色（未巡检）
      if (!date.inspectionData || date.inspectionData.length === 0) {
        return 'uninspected-circle'
      }
      
      // 有巡检记录，按优先级决定颜色：日常巡检 > 经常巡检 > 中心巡检
      const types = date.inspectionData.map(item => item.type)
      
      if (types.includes('daily')) {
        return 'daily-circle'
      } else if (types.includes('regular')) {
        return 'regular-circle'
      } else if (types.includes('center')) {
        return 'center-circle'
      }
      
      // 默认返回未巡检样式
      return 'uninspected-circle'
    },

    handleDateClick(date) {
      if (date.inspectionData) {
        this.internalSelectedDate = date.dateStr
        this.$emit('date-click', date)
      }
    },

    // 处理巡检项目点击
    handleInspectionItemClick(inspection) {
      console.log('📅 [CalendarView] 巡检项目点击:', inspection)
      this.$emit('inspection-click', inspection)
      console.log('📅 [CalendarView] 已触发 inspection-click 事件')
    },
    
    prevMonth() {
      const [year, month] = this.currentMonth.split('-')
      let newYear = parseInt(year)
      let newMonth = parseInt(month) - 1
      
      if (newMonth < 1) {
        newMonth = 12
        newYear -= 1
      }
      
      const newMonthStr = `${newYear}-${String(newMonth).padStart(2, '0')}`
      this.$emit('month-change', newMonthStr)
    },
    
    nextMonth() {
      const [year, month] = this.currentMonth.split('-')
      let newYear = parseInt(year)
      let newMonth = parseInt(month) + 1
      
      if (newMonth > 12) {
        newMonth = 1
        newYear += 1
      }
      
      const newMonthStr = `${newYear}-${String(newMonth).padStart(2, '0')}`
      this.$emit('month-change', newMonthStr)
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-calendar {
  background: #091A4B;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  
  .calendar-legend {
    padding: 20px 24px;
    background: linear-gradient(135deg, #0d2861 0%, #162c5a 100%);
    border-bottom: 1px solid #2d4a87;
    
    .legend-title {
      font-size: 15px;
      font-weight: 600;
      color: #ffffff;
      margin-right: 24px;
    }
    
    .legend-items {
      display: inline-flex;
      gap: 20px;
      
        .legend-item {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: #1a3568;
          border-radius: 16px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.2s ease;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
            background: #2d4a87;
          }
        
        .legend-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          display: inline-block;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          
          &.daily { background: #67c23a; }
          &.regular { background: #409eff; }
          &.center { background: #e6a23c; }
          &.uninspected { background: #f56c6c; }
        }
        
        .legend-text {
          font-size: 13px;
          color: #e2e8f0;
          font-weight: 500;
        }
      }
    }
  }
  
  .calendar-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 24px;
    gap: 20px;
    background: #091A4B;
    
    .current-month {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      min-width: 120px;
      text-align: center;
    }
    
    .el-button {
      border-radius: 8px;
      padding: 8px;
      background: #1a3568;
      border-color: #2d4a87;
      color: #ffffff;
      
      &:hover {
        background: #2d4a87;
        border-color: #3b5998;
      }
    }
  }
  
  .calendar-body {
    padding: 0 24px 24px;
    
    .week-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2px;
      margin-bottom: 12px;
      
      .week-day {
        text-align: center;
        padding: 12px 8px;
        font-size: 14px;
        font-weight: 600;
        color: #e2e8f0;
        background: #1a3568;
        border-radius: 6px;
      }
    }
    
    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2px;
      
      .calendar-cell {
        min-height: 64px;
        padding: 8px;
        border: 2px solid transparent;
        border-radius: 12px;
        cursor: pointer;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background: #1a3568;
        
        &:hover {
          background: #2d4a87;
          border-color: #4a67c7;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(74, 103, 199, 0.3);
        }
        
        &.selected {
          background: #2d4a87;
          border-color: #4a67c7;
          box-shadow: 0 4px 12px rgba(74, 103, 199, 0.4);
        }
        
        &.today {
          background: #3b5998;
          border-color: #4a67c7;
          
          .date-number {
            background: linear-gradient(135deg, #4a67c7 0%, #667eea 100%);
            color: #fff;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(74, 103, 199, 0.4);
          }
        }
        
        &.other-month {
          color: #718096;
          pointer-events: none;
          opacity: 0.4;
          background: #0d1f3d;
        }
        
        .date-number {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          background: #2d4a87;
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
          
          // 根据巡检类型显示不同的颜色
          &.daily-circle {
            background: #67c23a;
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
          }
          
          &.regular-circle {
            background: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }
          
          &.center-circle {
            background: #e6a23c;
            box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
          }
          
          &.uninspected-circle {
            background: #f56c6c;
            box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
          }
          
          &:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }
        
        
        .inspection-count {
          background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
          color: #fff;
          font-size: 10px;
          line-height: 1;
          padding: 2px 5px;
          border-radius: 10px;
          min-width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          position: absolute;
          bottom: -2px;
          right: -2px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          border: 2px solid #091A4B;
        }
      }
    }
  }
  
  .date-detail {
    padding: 20px 24px;
    background: linear-gradient(135deg, #0d2861 0%, #162c5a 100%);
    border-top: 1px solid #2d4a87;
    margin-top: 16px;
    border-radius: 0 0 12px 12px;
    
    .detail-title {
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .inspection-list {
      display: grid;
      gap: 12px;
      
      .inspection-item {
        display: flex;
        flex-direction: column;
        padding: 16px;
        background: #1a3568;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        border-left: 4px solid transparent;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          background: #2d4a87;
        }
        
        &:nth-child(odd) {
          border-left-color: #68d391;
        }
        
        &:nth-child(even) {
          border-left-color: #63b3ed;
        }
        
        .inspection-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .inspection-type-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            
            &.daily { background: #67c23a; }
            &.regular { background: #409eff; }
            &.center { background: #e6a23c; }
            &.uninspected { background: #f56c6c; }
          }
          
          .inspection-type-text {
            font-size: 15px;
            font-weight: 600;
            color: #ffffff;
            margin-right: auto;
          }
          
          .inspection-time {
            font-size: 13px;
            color: #cbd5e0;
            background: #2d4a87;
            padding: 2px 8px;
            border-radius: 12px;
          }
        }
        
        .inspection-inspector {
          font-size: 13px;
          color: #e2e8f0;
          margin-left: 24px;
        }
        
        .inspection-description {
          font-size: 12px;
          color: #cbd5e0;
          margin-left: 24px;
          margin-top: 4px;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
