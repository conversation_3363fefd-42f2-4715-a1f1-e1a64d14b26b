<template>
  <div class="inspection-table">
    <el-table
      ref="dataTable"
      v-loading="loading"
      :data="data"
      style="width: 100%"
      v-bind="tableProps"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <!-- 多选框 -->
      <el-table-column
        v-if="selectable"
        type="selection"
        width="50"
        :selectable="selectableCallback"
      />
      
      <!-- 序号列 -->
      <el-table-column 
        v-if="showIndex"
        type="index" 
        label="序号" 
        width="60" 
        align="center" 
      />
      
      <!-- 动态列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.prop || column.key"
        v-bind="column"
      >
        <template v-if="column.slot" slot-scope="scope">
          <slot 
            :name="column.slot" 
            :row="scope.row" 
            :column="column"
            :$index="scope.$index"
          />
        </template>
        <template v-else-if="column.render" slot-scope="scope">
          <component 
            :is="column.render"
            :row="scope.row"
            :column="column"
            :index="scope.$index"
          />
        </template>
        <template v-else-if="column.formatter" slot-scope="scope">
          {{ column.formatter(scope.row, column, scope.row[column.prop], scope.$index) }}
        </template>
      </el-table-column>
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="actions && actions.length > 0"
        label="操作" 
        :width="actionWidth" 
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <slot 
            name="actions" 
            :row="scope.row" 
            :index="scope.$index"
            :actions="getRowActions(scope.row)"
          >
            <ActionButtons
              :record="scope.row"
              :type="actionType"
              :permissions="permissions"
              :loading-buttons="loadingButtons"
              @button-click="handleActionClick"
            />
          </slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div v-if="pagination && pagination.total > 0" class="pagination-wrapper inspection-pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="pageSizes"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :layout="paginationLayout"
      />
    </div>
  </div>
</template>

<script>
import { ActionButtons } from '@/components/Inspection'

export default {
  name: 'DataTable',
  components: {
    ActionButtons
  },
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 表格列配置
    columns: {
      type: Array,
      default: () => []
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 是否可选择
    selectable: {
      type: Boolean,
      default: false
    },
    // 选择回调
    selectableCallback: {
      type: Function,
      default: null
    },
    // 是否显示序号
    showIndex: {
      type: Boolean,
      default: true
    },
    // 操作配置
    actions: {
      type: Array,
      default: () => []
    },
    // 操作列宽度
    actionWidth: {
      type: String,
      default: '150'
    },
    // 操作类型（传递给ActionButtons）
    actionType: {
      type: String,
      default: 'common'
    },
    // 权限列表
    permissions: {
      type: Array,
      default: () => []
    },
    // 加载中的按钮
    loadingButtons: {
      type: Array,
      default: () => []
    },
    // 分页配置
    pagination: {
      type: Object,
      default: null
    },
    // 分页大小选项
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    // 分页布局
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 表格其他属性
    tableProps: {
      type: Object,
      default: () => ({})
    }
  },
  emits: [
    'selection-change',
    'sort-change', 
    'action-click',
    'size-change',
    'current-change'
  ],
  methods: {
    // 选择变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    
    // 排序变化
    handleSortChange(sortInfo) {
      this.$emit('sort-change', sortInfo)
    },
    
    // 操作按钮点击
    handleActionClick(event) {
      this.$emit('action-click', event)
    },
    
    // 分页大小变化
    handleSizeChange(size) {
      this.$emit('size-change', size)
    },
    
    // 页码变化
    handleCurrentChange(page) {
      this.$emit('current-change', page)
    },
    
    // 获取行操作
    getRowActions(row) {
      if (typeof this.actions === 'function') {
        return this.actions(row)
      }
      return this.actions
    },
    
    // 清除选择
    clearSelection() {
      this.$refs.dataTable.clearSelection()
    },
    
    // 切换行选择
    toggleRowSelection(row, selected) {
      this.$refs.dataTable.toggleRowSelection(row, selected)
    }
  }
}
</script>

<style lang="scss" scoped>
// 继承主题样式
@import '@/styles/inspection-theme.scss';

.inspection-table {
  // 样式已在主题文件中定义
}
</style>
