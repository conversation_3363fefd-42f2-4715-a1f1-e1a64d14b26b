<template>
  <div class="detail-layout inspection-container">
    <div class="page-container">
      <!-- 工作流步骤 -->
      <el-card v-if="showWorkflow" class="workflow-card" shadow="never">
        <WorkflowSteps
          :current-step="currentStep"
          :current-handler="currentHandler"
          :current-time="currentTime"
          :steps="workflowSteps"
        />
      </el-card>

      <!-- 详情卡片 -->
      <el-card 
        v-for="(card, index) in cards"
        :key="card.key || index"
        class="info-card" 
        shadow="never"
      >
        <div class="card-header">
          <h3>
            <i :class="card.icon || 'el-icon-info'"></i> 
            {{ card.title }}
          </h3>
          <!-- 卡片操作按钮 -->
          <div v-if="card.actions && card.actions.length > 0" class="card-actions">
            <el-button
              v-for="action in card.actions"
              :key="action.key"
              :type="action.type || 'default'"
              :size="action.size || 'small'"
              :icon="action.icon"
              :disabled="action.disabled"
              :loading="action.loading"
              @click="handleCardAction(action, card)"
            >
              {{ action.text }}
            </el-button>
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <slot 
            :name="card.slot" 
            :card="card"
            :data="cardData"
          >
            <!-- 默认信息显示 -->
            <div v-if="card.type === 'info'" class="info-section">
              <el-row :gutter="20">
                <el-col 
                  v-for="field in card.fields"
                  :key="field.prop"
                  :span="field.span || 12"
                >
                  <div class="info-item">
                    <label>{{ field.label }}</label>
                    <span v-if="field.slot">
                      <slot 
                        :name="field.slot"
                        :field="field"
                        :value="getFieldValue(field)"
                        :data="cardData"
                      />
                    </span>
                    <span v-else>
                      {{ formatFieldValue(field) || '-' }}
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 表单编辑 -->
            <div v-else-if="card.type === 'form'" class="form-section">
              <el-form
                :ref="card.formRef"
                :model="formData[card.key]"
                :rules="card.rules"
                :label-width="card.labelWidth || '140px'"
                :disabled="card.readonly"
              >
                <el-row :gutter="20">
                  <el-col 
                    v-for="field in card.fields"
                    :key="field.prop"
                    :span="field.span || 12"
                  >
                    <el-form-item 
                      :label="field.label" 
                      :prop="field.prop"
                      :required="field.required"
                    >
                      <slot 
                        :name="`form-${field.prop}`"
                        :field="field"
                        :value="formData[card.key][field.prop]"
                        :form-data="formData[card.key]"
                      >
                        <!-- 默认表单控件 -->
                        <component 
                          :is="getFormComponent(field)"
                          v-model="formData[card.key][field.prop]"
                          v-bind="field.props"
                          :placeholder="field.placeholder"
                        />
                      </slot>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </slot>
        </div>
      </el-card>

      <!-- 操作按钮区 -->
      <div v-if="actions && actions.length > 0" class="action-buttons">
        <el-button
          v-for="action in actions"
          :key="action.key"
          :type="action.type || 'default'"
          :icon="action.icon"
          :disabled="action.disabled"
          :loading="action.loading"
          @click="handleAction(action)"
        >
          {{ action.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { WorkflowSteps } from '@/components/Inspection'

export default {
  name: 'DetailLayout',
  components: {
    WorkflowSteps
  },
  props: {
    // 是否显示工作流
    showWorkflow: {
      type: Boolean,
      default: false
    },
    // 工作流步骤配置
    currentStep: {
      type: Number,
      default: 0
    },
    currentHandler: {
      type: String,
      default: ''
    },
    currentTime: {
      type: String,
      default: ''
    },
    workflowSteps: {
      type: Array,
      default: () => []
    },
    // 卡片配置
    cards: {
      type: Array,
      default: () => []
    },
    // 卡片数据
    cardData: {
      type: Object,
      default: () => ({})
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },
    // 操作按钮
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['card-action', 'action'],
  methods: {
    // 获取字段值
    getFieldValue(field) {
      const keys = field.prop.split('.')
      let value = this.cardData
      
      for (const key of keys) {
        if (value && typeof value === 'object') {
          value = value[key]
        } else {
          return null
        }
      }
      
      return value
    },
    
    // 格式化字段值
    formatFieldValue(field) {
      const value = this.getFieldValue(field)
      
      if (field.formatter && typeof field.formatter === 'function') {
        return field.formatter(value, field, this.cardData)
      }
      
      // 默认格式化规则
      if (value === null || value === undefined || value === '') {
        return '-'
      }
      
      return value
    },
    
    // 获取表单组件类型
    getFormComponent(field) {
      const componentMap = {
        input: 'el-input',
        select: 'el-select',
        'date-picker': 'el-date-picker',
        'time-picker': 'el-time-picker',
        textarea: 'el-input',
        radio: 'el-radio-group',
        checkbox: 'el-checkbox-group',
        switch: 'el-switch',
        slider: 'el-slider',
        rate: 'el-rate'
      }
      
      return componentMap[field.type] || 'el-input'
    },
    
    // 卡片操作
    handleCardAction(action, card) {
      this.$emit('card-action', { action, card })
    },
    
    // 操作按钮点击
    handleAction(action) {
      this.$emit('action', action)
    }
  }
}
</script>

<style lang="scss" scoped>
// 继承主题样式
@import '@/styles/inspection-theme.scss';

.detail-layout {
  .page-container {
    .workflow-card {
      margin-bottom: 20px;
      background: transparent;
      border: none;
      box-shadow: none;
      
      :deep(.el-card__body) {
        padding: 0;
      }
    }
    
    .info-card {
      margin-bottom: 20px;
      background: transparent;
      border: none;
      box-shadow: none;
      
      :deep(.el-card__body) {
        padding: 0;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid var(--inspection-border);
        
        h3 {
          display: flex;
          align-items: center;
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--inspection-text-primary);
          
          i {
            margin-right: 8px;
            font-size: 20px;
            color: var(--inspection-primary-light);
          }
        }
        
        .card-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .card-content {
        .info-section {
          .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
            min-height: 32px;
            
            label {
              flex-shrink: 0;
              width: 140px;
              color: var(--inspection-text-secondary);
              font-weight: 500;
              line-height: 32px;
            }
            
            span {
              flex: 1;
              color: var(--inspection-text-primary);
              line-height: 32px;
              word-break: break-all;
            }
          }
        }
        
        .form-section {
          :deep(.el-form-item__label) {
            color: var(--inspection-text-secondary);
            font-weight: 500;
          }
          
          :deep(.el-input__inner),
          :deep(.el-textarea__inner),
          :deep(.el-select .el-input__inner) {
            background: var(--inspection-bg-tertiary);
            border-color: var(--inspection-border);
            color: var(--inspection-text-primary);
            
            &:focus {
              border-color: var(--inspection-primary-light);
              box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
            }
            
            &::placeholder {
              color: var(--inspection-text-muted);
            }
          }
        }
      }
    }
    
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--inspection-border);
    }
  }
}
</style>
