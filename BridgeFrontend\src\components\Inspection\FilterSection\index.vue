<template>
  <div class="filter-section">
    <div class="filter-content">
      <!-- 筛选条件 -->
      <div class="filter-items">
        <slot name="filters" :form-data="formData" :options="options">
          <!-- 默认筛选项 -->
          <el-select
            v-if="configs.bridgeName"
            v-model="formData.bridgeName"
            :placeholder="configs.bridgeName.placeholder || '桥梁名称'"
            clearable
            filterable
            class="filter-select"
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in options.bridgeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-select
            v-if="configs.unit"
            v-model="formData.unit"
            :placeholder="configs.unit.placeholder || '单位'"
            clearable
            filterable
            class="filter-select"
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in options.unitOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-select
            v-if="configs.month"
            v-model="formData.month"
            :placeholder="configs.month.placeholder || '月份'"
            clearable
            class="filter-select"
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in options.monthOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-select
            v-if="configs.status"
            v-model="formData.status"
            :placeholder="configs.status.placeholder || '状态'"
            clearable
            class="filter-select"
            @change="handleFilterChange"
          >
            <el-option
              v-for="option in options.statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-date-picker
            v-if="configs.dateRange"
            v-model="formData.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="filter-select"
            @change="handleFilterChange"
          />
        </slot>
      </div>

      <!-- 操作按钮 -->
      <div class="filter-actions">
        <slot name="actions" :reset="handleReset" :search="handleSearch">
          <!-- 主要操作按钮 -->
          <slot name="primary-actions">
            <el-button 
              v-if="configs.addButton"
              type="primary" 
              :icon="configs.addButton.icon || 'el-icon-plus'"
              @click="handleAdd"
            >
              {{ configs.addButton.text || '新增' }}
            </el-button>
            <el-button 
              v-if="configs.batchButton"
              type="success" 
              :icon="configs.batchButton.icon || 'el-icon-check'"
              :disabled="!hasBatchSelection"
              @click="handleBatch"
            >
              {{ configs.batchButton.text || '批量操作' }}
            </el-button>
          </slot>
          
          <!-- 搜索操作按钮 -->
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterSection',
  props: {
    // 表单数据
    modelValue: {
      type: Object,
      default: () => ({})
    },
    // 筛选配置
    configs: {
      type: Object,
      default: () => ({})
    },
    // 选项数据
    options: {
      type: Object,
      default: () => ({})
    },
    // 是否有批量选择
    hasBatchSelection: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'search', 'reset', 'add', 'batch'],
  data() {
    return {
      formData: { ...this.modelValue }
    }
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.formData = { ...newVal }
      },
      deep: true
    }
  },
  methods: {
    // 筛选条件变化
    handleFilterChange() {
      this.$emit('update:modelValue', this.formData)
    },
    
    // 搜索
    handleSearch() {
      this.$emit('search', this.formData)
    },
    
    // 重置
    handleReset() {
      const resetData = {}
      Object.keys(this.formData).forEach(key => {
        resetData[key] = Array.isArray(this.formData[key]) ? [] : ''
      })
      this.formData = resetData
      this.$emit('update:modelValue', resetData)
      this.$emit('reset', resetData)
    },
    
    // 新增
    handleAdd() {
      this.$emit('add')
    },
    
    // 批量操作
    handleBatch() {
      this.$emit('batch')
    }
  }
}
</script>

<style lang="scss" scoped>
// 筛选区域继承主题样式
@import '@/styles/inspection-theme.scss';

.filter-section {
  // 样式已在主题文件中定义
  .filter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;

    .filter-items {
      display: flex;
      align-items: center;
      gap: 20px;
      flex: 1;
      flex-wrap: wrap;
      min-width: 0; // 防止flex项目过度扩张
    }

    .filter-actions {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-shrink: 0; // 防止按钮区域被压缩
    }
  }
}

// 移动端响应式
@media (max-width: 768px) {
  .filter-section {
    .filter-content {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .filter-items {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        .filter-select {
          width: 100%;
        }
      }

      .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
        
        .el-button {
          flex: 1;
          min-width: 80px;
        }
      }
    }
  }
}
</style>
