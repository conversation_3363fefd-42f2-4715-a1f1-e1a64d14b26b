<template>
  <div class="image-viewer">
    <div 
      class="image-grid"
      :class="`grid-${gridType || 4}`"
    >
      <div 
        v-for="(image, index) in images" 
        :key="index"
        class="image-item"
        @click="previewImage(index)"
      >
        <div class="image-wrapper">
          <img 
            :src="image.url" 
            :alt="image.alt || `图片${index + 1}`"
            class="image"
            @error="handleImageError"
          />
          <div class="image-overlay">
            <i class="el-icon-zoom-in"></i>
          </div>
        </div>
        <div v-if="image.alt" class="image-caption">
          {{ image.alt }}
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      width="80%"
      custom-class="image-preview-dialog"
      center
    >
      <div class="preview-container" v-if="currentPreviewImage">
        <img 
          :src="currentPreviewImage.url" 
          :alt="currentPreviewImage.alt"
          class="preview-image"
        />
        <div class="preview-info">
          <h4>{{ currentPreviewImage.alt || `图片${currentPreviewIndex + 1}` }}</h4>
          <p>{{ currentPreviewIndex + 1 }} / {{ images.length }}</p>
        </div>
      </div>
      
      <div slot="footer" class="preview-footer">
        <el-button 
          @click="prevImage" 
          :disabled="currentPreviewIndex <= 0"
          icon="el-icon-arrow-left"
        >
          上一张
        </el-button>
        <el-button 
          @click="nextImage" 
          :disabled="currentPreviewIndex >= images.length - 1"
          icon="el-icon-arrow-right"
        >
          下一张
        </el-button>
        <el-button @click="previewVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    // 图片列表
    images: {
      type: Array,
      default: () => []
    },
    // 网格类型：每行显示多少张图片
    gridType: {
      type: Number,
      default: 4
    },
    // 是否显示上传按钮
    showUpload: {
      type: Boolean,
      default: false
    },
    // 是否显示删除按钮
    showDelete: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      previewVisible: false,
      currentPreviewIndex: 0
    }
  },
  
  computed: {
    currentPreviewImage() {
      if (this.currentPreviewIndex >= 0 && this.currentPreviewIndex < this.images.length) {
        return this.images[this.currentPreviewIndex]
      }
      return null
    }
  },
  
  methods: {
    // 预览图片
    previewImage(index) {
      this.currentPreviewIndex = index
      this.previewVisible = true
    },
    
    // 上一张图片
    prevImage() {
      if (this.currentPreviewIndex > 0) {
        this.currentPreviewIndex--
      }
    },
    
    // 下一张图片
    nextImage() {
      if (this.currentPreviewIndex < this.images.length - 1) {
        this.currentPreviewIndex++
      }
    },
    
    // 图片加载错误处理
    handleImageError(event) {
      console.warn('图片加载失败:', event.target.src)
      // 可以设置默认图片
      // event.target.src = require('@/assets/images/default-image.png')
    }
  }
}
</script>

<style lang="scss" scoped>
.image-viewer {
  width: 100%;
  
  .image-grid {
    display: grid;
    gap: 16px;
    
    &.grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    &.grid-4 {
      grid-template-columns: repeat(4, 1fr);
    }
    
    &.grid-5 {
      grid-template-columns: repeat(5, 1fr);
    }
    
    // 响应式布局
    @media (max-width: 1200px) {
      &.grid-4,
      &.grid-5 {
        grid-template-columns: repeat(3, 1fr);
      }
    }
    
    @media (max-width: 768px) {
      &.grid-3,
      &.grid-4,
      &.grid-5 {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }
  
  .image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f7fa;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      
      .image-overlay {
        opacity: 1;
      }
    }
    
    .image-wrapper {
      position: relative;
      width: 100%;
      height: 200px;
      overflow: hidden;
      
      .image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        
        i {
          font-size: 24px;
          color: #fff;
        }
      }
    }
    
    .image-caption {
      padding: 12px;
      font-size: 14px;
      color: #606266;
      text-align: center;
      background: #fff;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 预览对话框样式
:deep(.image-preview-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .el-dialog__body {
    padding: 0;
  }
  
  .el-dialog__footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  
  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
  
  .preview-info {
    margin-top: 16px;
    text-align: center;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #909399;
    }
  }
}

.preview-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>