<template>
  <el-tag 
    :type="tagType" 
    :effect="effect"
    :size="size"
    :class="customClass"
  >
    <i v-if="showIcon" :class="iconClass" class="status-icon"></i>
    {{ displayText }}
  </el-tag>
</template>

<script>
export default {
  name: 'StatusTag',
  props: {
    // 状态值
    status: {
      type: String,
      required: true
    },
    // 状态类型 disease | inspection
    type: {
      type: String,
      default: 'disease'
    },
    // 标签大小
    size: {
      type: String,
      default: 'small'
    },
    // 标签效果
    effect: {
      type: String,
      default: 'light'
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    statusConfig() {
      if (this.type === 'disease') {
        return this.diseaseStatusConfig
      } else if (this.type === 'inspection') {
        return this.inspectionStatusConfig
      }
      return {}
    },
    
    diseaseStatusConfig() {
      return {
        'judging': {
          text: '判定中',
          type: 'warning',
          icon: 'el-icon-time'
        },
        'planning': {
          text: '计划中',
          type: 'info',
          icon: 'el-icon-document'
        },
        'disposing': {
          text: '处置中',
          type: 'primary',
          icon: 'el-icon-loading'
        },
        'reviewing': {
          text: '验收中',
          type: 'warning',
          icon: 'el-icon-view'
        },
        'archived': {
          text: '已归档',
          type: 'success',
          icon: 'el-icon-check'
        },
        'rejected': {
          text: '已驳回',
          type: 'danger',
          icon: 'el-icon-close'
        }
      }
    },
    
    inspectionStatusConfig() {
      return {
        'daily': {
          text: '日常巡检',
          type: 'primary',
          icon: 'el-icon-calendar'
        },
        'regular': {
          text: '经常巡检',
          type: 'success',
          icon: 'el-icon-date'
        },
        'center': {
          text: '中心巡检',
          type: 'warning',
          icon: 'el-icon-office-building'
        },
        'inspection': {
          text: '定检',
          type: 'info',
          icon: 'el-icon-document-checked'
        },
        'superior': {
          text: '上级交办',
          type: 'danger',
          icon: 'el-icon-user'
        },
        'uninspected': {
          text: '未巡检',
          type: 'info',
          icon: 'el-icon-remove-outline'
        }
      }
    },
    
    currentConfig() {
      return this.statusConfig[this.status] || {
        text: this.status,
        type: 'info',
        icon: ''
      }
    },
    
    displayText() {
      return this.currentConfig.text
    },
    
    tagType() {
      return this.currentConfig.type
    },
    
    iconClass() {
      return this.currentConfig.icon
    },
    
    customClass() {
      return [
        'status-tag',
        `status-tag--${this.type}`,
        `status-tag--${this.status}`
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.status-tag {
  .status-icon {
    margin-right: 4px;
  }
  
  // 病害状态特殊样式
  &.status-tag--disease {
    &.status-tag--judging {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }
    
    &.status-tag--planning {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #909399;
    }
    
    &.status-tag--disposing {
      background-color: #ecf5ff;
      border-color: #b3d8ff;
      color: #409eff;
    }
    
    &.status-tag--reviewing {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }
    
    &.status-tag--archived {
      background-color: #f0f9ff;
      border-color: #b3e19d;
      color: #67c23a;
    }
    
    &.status-tag--rejected {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }
  }
  
  // 巡检状态特殊样式
  &.status-tag--inspection {
    &.status-tag--daily {
      background-color: #ecf5ff;
      border-color: #b3d8ff;
      color: #409eff;
    }
    
    &.status-tag--regular {
      background-color: #f0f9ff;
      border-color: #b3e19d;
      color: #67c23a;
    }
    
    &.status-tag--center {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }
    
    &.status-tag--inspection {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #909399;
    }
    
    &.status-tag--superior {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }
    
    &.status-tag--uninspected {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      color: #c0c4cc;
    }
  }
}</style>
