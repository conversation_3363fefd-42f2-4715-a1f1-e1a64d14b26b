<template>
  <el-tabs
    v-model="currentTab"
    @tab-click="handleTabClick"
    class="inspection-tabs"
  >
    <el-tab-pane
      v-for="tab in tabs"
      :key="tab.name"
      :name="tab.name"
      :disabled="tab.disabled"
    >
      <span slot="label">
        <svg-icon 
          v-if="tab.icon" 
          :icon-class="tab.icon" 
        />
        {{ tab.label }}
      </span>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'TabSwitch',
  props: {
    // 当前激活的TAB (Vue 2 兼容)
    value: {
      type: String,
      required: true
    },
    // TAB配置
    tabs: {
      type: Array,
      required: true,
      validator(tabs) {
        return tabs.every(tab => 
          tab && 
          typeof tab.name === 'string' && 
          typeof tab.label === 'string'
        )
      }
    }
  },
  computed: {
    currentTab: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    handleTabClick(tab) {
      this.$emit('tab-click', tab)
    }
  }
}
</script>

<style lang="scss" scoped>
// 继承主题样式
@import '@/styles/inspection-theme.scss';

.inspection-tabs {
  // 样式已在主题文件中定义
}
</style>
