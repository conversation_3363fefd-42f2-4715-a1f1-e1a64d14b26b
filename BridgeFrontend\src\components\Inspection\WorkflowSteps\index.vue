<template>
  <div class="workflow-steps">
    <div class="steps-container">
      <div 
        v-for="(step, index) in stepsList" 
        :key="step.key"
        class="step-item"
        :class="getStepClass(index)"
      >
        <div class="step-icon">
          <div class="step-circle">
            <i 
              v-if="step.status === 'completed'" 
              class="el-icon-check"
            ></i>
            <span v-else>{{ index + 1 }}</span>
          </div>
        </div>
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div v-if="step.status === 'current'" class="step-info">
            <div class="handler-info">{{ step.handler }}</div>
            <div class="time-info">{{ step.time }}</div>
          </div>
        </div>
        <!-- 连接线 -->
        <div 
          v-if="index < stepsList.length - 1" 
          class="step-connector"
          :class="{ 'completed': step.status === 'completed' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkflowSteps',
  props: {
    // 当前步骤索引
    currentStep: {
      type: Number,
      default: 0
    },
    // 当前处理人
    currentHandler: {
      type: String,
      default: ''
    },
    // 当前处理时间
    currentTime: {
      type: String,
      default: ''
    },
    // 步骤配置
    steps: {
      type: Array,
      default: () => [
        { key: 'report', title: '上报' },
        { key: 'judge', title: '判定' },
        { key: 'dispose', title: '处置' },
        { key: 'review', title: '复核' },
        { key: 'archive', title: '归档' }
      ]
    }
  },
  computed: {
    stepsList() {
      return this.steps.map((step, index) => {
        let status = 'pending'
        let handler = ''
        let time = ''
        
        if (index < this.currentStep) {
          status = 'completed'
        } else if (index === this.currentStep) {
          status = 'current'
          handler = this.currentHandler
          time = this.currentTime
        }
        
        return {
          ...step,
          status,
          handler,
          time
        }
      })
    }
  },
  methods: {
    getStepClass(index) {
      const step = this.stepsList[index]
      return {
        'completed': step.status === 'completed',
        'current': step.status === 'current',
        'pending': step.status === 'pending'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-steps {
  padding: 20px;
  
  .steps-container {
    display: flex;
    align-items: flex-start;
    position: relative;
  }
  
  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    
    &:last-child {
      flex: none;
    }
    
    .step-icon {
      position: relative;
      z-index: 2;
      margin-bottom: 8px;
      
      .step-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        transition: all 0.3s;
        
        span {
          color: #fff;
        }
        
        .el-icon-check {
          color: #fff;
          font-size: 16px;
        }
      }
    }
    
    .step-content {
      text-align: center;
      
      .step-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .step-info {
        font-size: 12px;
        color: #666;
        
        .handler-info {
          margin-bottom: 2px;
        }
        
        .time-info {
          font-size: 11px;
          color: #999;
        }
      }
    }
    
    .step-connector {
      position: absolute;
      top: 16px;
      left: calc(50% + 16px);
      right: calc(-50% + 16px);
      height: 2px;
      background: #e4e7ed;
      z-index: 1;
      
      &.completed {
        background: #67c23a;
      }
    }
    
    // 完成状态样式
    &.completed {
      .step-circle {
        background: #67c23a;
        border: 2px solid #67c23a;
      }
      
      .step-title {
        color: #67c23a;
      }
    }
    
    // 当前状态样式
    &.current {
      .step-circle {
        background: #409eff;
        border: 2px solid #409eff;
      }
      
      .step-title {
        color: #409eff;
        font-weight: 600;
      }
    }
    
    // 待处理状态样式
    &.pending {
      .step-circle {
        background: #f5f7fa;
        border: 2px solid #e4e7ed;
        color: #909399;
        
        span {
          color: #909399;
        }
      }
      
      .step-title {
        color: #909399;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .workflow-steps {
    .step-content {
      .step-title {
        font-size: 12px;
      }
      
      .step-info {
        font-size: 11px;
      }
    }
    
    .step-icon {
      .step-circle {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }
  }
}
</style>
