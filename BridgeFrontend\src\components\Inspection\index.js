/**
 * 巡检中心模块组件库
 * 统一导出所有巡检相关组件
 */

// 导入组件
import WorkflowSteps from './WorkflowSteps'
import CalendarView from './CalendarView'
import StatusTag from './StatusTag'
import ActionButtons from './ActionButtons'
import ImageViewer from './ImageViewer/index.js'
import FilterSection from './FilterSection'
import DataTable from './DataTable'
import TabSwitch from './TabSwitch'
import DetailLayout from './DetailLayout'

// 组件列表
const components = [
  WorkflowSteps,
  CalendarView,
  StatusTag,
  ActionButtons,
  ImageViewer,
  FilterSection,
  DataTable,
  TabSwitch,
  DetailLayout
]

// 定义install方法，用于Vue.use()
const install = function(Vue) {
  // 判断是否已安装
  if (install.installed) return
  install.installed = true
  
  // 注册所有组件
  components.forEach(component => {
    Vue.component(component.name, component)
  })
}

// 判断是否直接引入文件，如果是，就不用调用Vue.use()
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

// 导出安装方法和所有组件
export {
  install,
  WorkflowSteps,
  CalendarView,
  StatusTag,
  ActionButtons,
  ImageViewer,
  FilterSection,
  DataTable,
  TabSwitch,
  DetailLayout
}

export default {
  install,
  WorkflowSteps,
  CalendarView,
  StatusTag,
  ActionButtons,
  ImageViewer,
  FilterSection,
  DataTable,
  TabSwitch,
  DetailLayout
}
