<template>
  <el-dialog
    title="关联桥梁"
    :visible.sync="dialogVisible"
    custom-class="bridge-selector-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <!-- 搜索表单 - 复用通用搜索表单样式 -->
      <div class="search-form">
        <el-form :model="queryParams" inline>
          <el-form-item label="桥梁名称">
            <el-input
              v-model="queryParams.name"
              placeholder="输入桥梁名称或编号"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
              @clear="handleQuery"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      
      <!-- 桥梁列表 - 复用通用表格样式 -->
      <div class="common-table">
        <el-table
          v-loading="loading"
          :data="bridgeList"
          class="maintenance-table"
          style="width: 100%"
          :row-style="{ height: '32px' }"
          size="small"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="isSelectable"
          />
          
          <el-table-column prop="project" label="养护项目" min-width="120" show-overflow-tooltip />
          
          <el-table-column prop="maintainer" label="养护人员" width="100" align="center" />
          
          <el-table-column type="index" label="序号" width="60" align="center" />
          
          <el-table-column prop="name" label="桥梁名称" min-width="120" show-overflow-tooltip />
          
          <el-table-column prop="code" label="桥梁编号" width="120" align="center" />
          
          <el-table-column prop="road" label="所在道路" min-width="120" show-overflow-tooltip />
          
          <el-table-column prop="managementUnit" label="管理单位" min-width="120" show-overflow-tooltip />
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmSelection">
        确定 ({{ selectedBridges.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getBridgeList } from '@/api/maintenance/projects'

export default {
  name: 'BridgeSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    selectedData: {
      type: Array,
      default: () => []
    },
    infrastructureType: {
      type: String,
      default: 'bridge' // bridge, tunnel
    }
  },
  data() {
    return {
      loading: false,
      bridgeList: [],
      selectedBridges: [],
      total: 0,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        infrastructureType: 'bridge',
        name: '',
        district: '',
        managementUnit: ''
      },
      
      // 选项数据
      districtOptions: [
        { label: '岳麓区', value: 'yuelu' },
        { label: '芙蓉区', value: 'furong' },
        { label: '天心区', value: 'tianxin' },
        { label: '开福区', value: 'kaifu' },
        { label: '雨花区', value: 'yuhua' },
        { label: '望城区', value: 'wangcheng' }
      ],
      
      unitOptions: [
        { label: '长沙市桥梁管理处', value: 'changsha_bridge' },
        { label: '长沙市隧道管理处', value: 'changsha_tunnel' },
        { label: '岳麓区市政局', value: 'yuelu_municipal' },
        { label: '芙蓉区市政局', value: 'furong_municipal' }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData()
        this.getList()
      }
    },
    
    infrastructureType: {
      immediate: true,
      handler(val) {
        this.queryParams.infrastructureType = val
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      this.selectedBridges = [...this.selectedData]
      this.queryParams.infrastructureType = this.infrastructureType
    },
    
    // 获取桥梁列表
    async getList() {
      try {
        this.loading = true
        const response = await getBridgeList(this.queryParams)
        if (response.data) {
          let bridgeData = response.data.rows || response.data.list || []
          
          // 为每条数据添加结构图所需的字段
          this.bridgeList = bridgeData.map((bridge, index) => ({
            ...bridge,
            project: bridge.project || 'XXXXXX大桥', // 养护项目
            maintainer: bridge.maintainer || ['黄昭言', '刘雨桐', '罗砚秋'][index % 3], // 养护人员
            code: bridge.code || `CS-B-${String(index + 1).padStart(3, '0')}`, // 桥梁编号
            road: bridge.road || '枫林一路', // 所在道路
            managementUnit: bridge.managementUnit || '桥隧中心' // 管理单位
          }))
          this.total = response.data.total || 0
        } else {
          // 如果接口失败，使用模拟数据展示结构
          this.bridgeList = [
            {
              id: 1,
              project: 'XXXXXX大桥',
              maintainer: '黄昭言',
              name: 'XXX大桥',
              code: 'CS-B-001',
              road: '枫林一路',
              managementUnit: '桥隧中心'
            },
            {
              id: 2,
              project: 'XXXXXX大桥',
              maintainer: '刘雨桐',
              name: '刘雨桐',
              code: 'CS-B-002', 
              road: '枫林一路',
              managementUnit: '桥隧中心'
            },
            {
              id: 3,
              project: 'XXXXXX大桥',
              maintainer: '罗砚秋',
              name: '罗砚秋',
              code: 'CS-B-003',
              road: '枫林一路',
              managementUnit: '桥隧中心'
            }
          ]
          this.total = 3
        }
      } catch (error) {
        console.error('获取桥梁列表失败:', error)
        this.$message.error('获取桥梁列表失败')
        
        // 错误时也显示模拟数据以展示结构
        this.bridgeList = [
          {
            id: 1,
            project: 'XXXXXX大桥',
            maintainer: '黄昭言',
            name: 'XXX大桥',
            code: 'CS-B-001',
            road: '枫林一路',
            managementUnit: '桥隧中心'
          },
          {
            id: 2,
            project: 'XXXXXX大桥',
            maintainer: '刘雨桐',
            name: '刘雨桐',
            code: 'CS-B-002',
            road: '枫林一路',
            managementUnit: '桥隧中心'
          },
          {
            id: 3,
            project: 'XXXXXX大桥',
            maintainer: '罗砚秋',
            name: '罗砚秋',
            code: 'CS-B-003',
            road: '枫林一路',
            managementUnit: '桥隧中心'
          }
        ]
        this.total = 3
        
        // 设置已选中的行
        this.$nextTick(() => {
          if (this.$refs.bridgeTable && this.selectedData && this.selectedData.length > 0) {
            this.bridgeList.forEach(bridge => {
              const isSelected = this.selectedData.some(selected => selected.id === bridge.id)
              if (isSelected) {
                this.$refs.bridgeTable.toggleRowSelection(bridge, true)
              }
            })
          }
        })
        
      } finally {
        this.loading = false
      }
    },
    
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        infrastructureType: this.infrastructureType,
        name: '',
        district: '',
        managementUnit: ''
      }
      this.getList()
    },
    
    // 选择桥梁
    selectBridge(bridge) {
      if (!this.multiple) {
        this.selectedBridges = [bridge]
      } else {
        if (!this.isSelected(bridge)) {
          this.selectedBridges.push(bridge)
        }
      }
    },
    
    // 移除选择
    removeSelected(bridge) {
      const index = this.selectedBridges.findIndex(item => item.id === bridge.id)
      if (index > -1) {
        this.selectedBridges.splice(index, 1)
      }
    },
    
    // 清空选择
    clearSelected() {
      this.selectedBridges = []
    },
    
    // 判断是否已选择
    isSelected(bridge) {
      return this.selectedBridges.some(item => item.id === bridge.id)
    },
    
    // 判断是否可选择
    isSelectable(row) {
      return true // 允许选择和取消选择
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      if (this.multiple) {
        // 直接使用当前页面的选择结果
        this.selectedBridges = selection
      }
    },
    
    // 确认选择
    confirmSelection() {
      this.$emit('confirm', this.selectedBridges)
      this.dialogVisible = false
    },
    
    // 获取养护等级类型
    getMaintenanceLevelType(level) {
      const typeMap = {
        '一级': 'danger',
        '二级': 'warning',
        '三级': 'info',
        '四级': 'success'
      }
      return typeMap[level] || 'info'
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

:deep(.bridge-selector-dialog) {
  // 弹窗样式覆盖 - 遵循设计文档6.7.7规范
  .el-dialog {
    background: #091A4B !important; // 与主要背景色一致
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
    border: none !important;
    width: 70vw !important;
    max-width: 1200px !important;
    min-width: 800px !important;
  }
  
  .el-dialog__header {
    background: #091A4B !important;
    border-bottom: 1px solid #374151 !important;
    padding: 16px 24px !important;
    height: 56px !important; // 按设计文档规范
    
    .el-dialog__title {
      color: #f8fafc !important; // 6.7.1 主要文字颜色
      font-size: 18px !important;
      font-weight: normal !important; // 正常字重
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;
    }
    
    .el-dialog__close {
      color: #94a3b8 !important;
      font-size: 18px !important;
      width: 32px !important;
      height: 32px !important;
      line-height: 32px !important;
      
      &:hover {
        color: #ffffff !important;
        background: #ef4444 !important;
        border-radius: 50% !important;
      }
    }
  }
  
  .el-dialog__body {
    background: #091A4B !important;
    padding: 0 !important; // 移除默认padding，让内容区域自行控制
    color: #f8fafc !important;
    max-height: calc(90vh - 120px) !important; // 按设计文档规范
    overflow-y: auto !important;
  }
  
  .el-dialog__footer {
    background: #091A4B !important;
    border-top: 1px solid #374151 !important;
    padding: 16px 24px !important;
    text-align: right !important;
  }
  
  .selector-content {
    padding: 24px !important; // 内容区域统一内边距
    
    // 搜索筛选区域 - 遵循6.7.8输入框组件样式
    .search-section {
      padding: 20px !important;
      background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景
      border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框
      border-radius: 8px !important;
      margin-bottom: 20px !important;
      
      // 搜索输入框样式
      .el-input {
        .el-input__inner {
          background: rgba(255, 255, 255, 0.1) !important; // 6.7.8 输入框背景
          border: 1px solid rgba(255, 255, 255, 0.2) !important; // 6.7.8 输入框边框
          color: #f8fafc !important; // 6.7.1 主要文字颜色
          border-radius: 8px !important; // 6.7.8 输入框圆角
          height: 40px !important; // 6.7.8 输入框高度
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;
          font-size: 16px !important;
          font-weight: normal !important;
          
          &::placeholder {
            color: rgba(255, 255, 255, 0.5) !important; // 6.7.8 占位符颜色
          }
          
          &:focus {
            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框
          }
          
          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important; // 6.7.8 悬停边框
          }
        }
      }
    }
    
    
    // 桥梁列表表格 - 遵循6.7.4表格组件样式
    .bridge-list {
      background: transparent !important;
      border: none !important;
      border-radius: 8px !important;
      
      // 表格样式覆盖 - 完全遵循maintenance-theme.scss的表格样式
      .el-table {
        background: transparent !important;
        color: #f8fafc !important; // 6.7.1 主要文字颜色
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;
        border: none !important;
        
        // 无边框设计
        &::before {
          display: none !important;
        }
        
        // 表头样式 - 完全按照设计文档6.7.4规范
        .el-table__header-wrapper {
          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
          
          th {
            background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
            color: #FFF !important;
            font-size: 16px !important;
            font-weight: 400 !important; // 正常字重
            line-height: 140% !important; // 22.4px
            height: 44px !important;
            padding: 14px 12px !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            text-align: center !important;
            white-space: nowrap !important;
            
            &:last-child {
              border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
            }
          }
        }
        
        // 数据行样式 - 完全按照设计文档6.7.4规范
        .el-table__body-wrapper {
          background: transparent !important;
          
          tr {
            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
            
            // 移除双数行的背景色差异
            &:nth-child(odd) {
              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
            }
            
            &:nth-child(even) {
              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
            }
            
            // 悬停效果
            &:hover {
              background: rgba(255, 255, 255, 0.05) !important;
              
              td {
                background: rgba(255, 255, 255, 0.05) !important;
              }
            }
            
            // 最后一行无底部边框
            &:last-child {
              td {
                border-bottom: none !important;
              }
            }
            
            td {
              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
              color: #FFF !important;
              font-size: 14px !important;
              font-weight: 400 !important;
              line-height: 120% !important; // 16.8px
              padding: 4px 12px !important;
              border: 1px solid rgba(255, 255, 255, 0.1) !important;
              text-align: center !important;
              height: 32px !important;
              
              &:last-child {
                border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
              }
            }
          }
        }
        
        // 空状态样式
        .el-table__empty-block {
          background: transparent !important;
          
          .el-table__empty-text {
            color: #94a3b8 !important; // 6.7.1 静默文字颜色
          }
        }
      }
      
      // 复选框样式
      .el-checkbox {
        .el-checkbox__inner {
          background: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.2) !important;
          
          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important;
          }
          
          &.is-checked {
            background: #5C9DFF !important; // 6.7.1 蓝色强调色
            border-color: #5C9DFF !important;
          }
        }
        
        .el-checkbox__label {
          color: #f8fafc !important; // 6.7.1 主要文字颜色
        }
      }
      
      .bridge-type {
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
        
        i {
          color: #5C9DFF !important; // 6.7.1 蓝色强调色
        }
      }
      
      .selected-btn {
        color: #10b981 !important; // 绿色表示已选择
        
        &:hover {
          color: #34d399 !important;
        }
      }
      
      // 分页容器样式
      .pagination-container {
        padding: 20px 0 !important;
        text-align: center !important;
        background: transparent !important;
        
        // 分页组件样式 - 遵循设计文档规范
        .el-pagination {
          .el-pagination__total,
          .el-pagination__jump {
            color: #94a3b8 !important; // 6.7.1 静默文字颜色
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;
          }
          
          .btn-prev,
          .btn-next,
          .el-pager li {
            background: transparent !important;
            border: 1px solid #4b5563 !important;
            color: #94a3b8 !important;
            border-radius: 4px !important;
            margin: 0 2px !important;
            
            &:hover {
              background: #374151 !important;
              color: #ffffff !important;
              border-color: rgba(255, 255, 255, 0.4) !important;
            }
            
            &.active {
              background: #5C9DFF !important; // 6.7.1 蓝色强调色
              color: #ffffff !important;
              border-color: #5C9DFF !important;
            }
          }
          
          .el-pagination__jump {
            .el-input__inner {
              background: rgba(255, 255, 255, 0.1) !important;
              border-color: rgba(255, 255, 255, 0.2) !important;
              color: #f8fafc !important;
              
              &:focus {
                border-color: rgba(255, 255, 255, 0.4) !important;
              }
            }
          }
        }
      }
    }
  }
  
  // 底部按钮样式 - 遵循设计文档规范
  .dialog-footer {
    text-align: right !important;
    
    .el-button {
      min-width: 80px !important;
      height: 36px !important;
      border-radius: 6px !important;
      font-size: 14px !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei" !important;
      font-weight: normal !important;
      padding: 0 16px !important;
      margin-left: 12px !important;
      
      // 默认按钮样式
      &.el-button--default {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: #f8fafc !important; // 6.7.1 主要文字颜色
        
        &:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          border-color: rgba(255, 255, 255, 0.4) !important;
          color: #ffffff !important;
        }
        
        &:focus {
          background: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.3) !important;
          color: #f8fafc !important;
        }
      }
      
      // 主要按钮样式
      &.el-button--primary {
        background: #5C9DFF !important; // 6.7.1 蓝色强调色
        border-color: #5C9DFF !important;
        color: #ffffff !important;
        
        &:hover {
          background: #74a7f5 !important; // 6.7.1 浅蓝色
          border-color: #74a7f5 !important;
          color: #ffffff !important;
        }
        
        &:focus {
          background: #5C9DFF !important;
          border-color: #5C9DFF !important;
          color: #ffffff !important;
        }
      }
    }
  }
}
</style>
