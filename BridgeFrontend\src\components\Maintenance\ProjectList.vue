<!--
  养护项目列表组件示例
  
  ⚠️ 注意：此组件使用模拟数据进行开发和演示
  当后端接口实现后，需要将API调用替换为真实接口
  
  基于《长沙市智慧桥隧管理平台-养护运维前端设计文档.md》图片1创建
-->

<template>
  <div class="project-list-container">
    <!-- 顶部导航标签 -->
    <div class="nav-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="🏗️ 桥梁养护" name="bridge"></el-tab-pane>
        <el-tab-pane label="🚇 隧道养护" name="tunnel"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :model="queryParams" :inline="true" label-width="80px">
        <el-form-item label="项目名称">
          <el-select 
            v-model="queryParams.name" 
            placeholder="请选择" 
            clearable
            filterable
            allow-create
            style="width: 200px">
            <el-option 
              v-for="project in projectNameOptions" 
              :key="project.id" 
              :label="project.name" 
              :value="project.name">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目类型">
          <el-select v-model="queryParams.type" placeholder="请选择" clearable style="width: 150px">
            <el-option 
              v-for="type in projectTypes" 
              :key="type.value" 
              :label="type.label" 
              :value="type.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="全部" clearable style="width: 120px">
            <el-option 
              v-for="status in projectStatus" 
              :key="status.value" 
              :label="status.label" 
              :value="status.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy/MM/dd"
            value-format="yyyy-MM-dd"
            style="width: 300px">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 新增按钮 -->
    <div class="toolbar">
      <el-button 
        type="primary" 
        icon="el-icon-plus" 
        @click="handleAdd"
        v-hasPermi="['maintenance:project:add']">
        新增养护项目
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table 
      :data="projectList" 
      :loading="loading"
      border
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange">
      
      <el-table-column type="index" label="序号" width="60" align="center">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="项目名称" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link type="primary" @click="handleView(scope.row)">
            {{ scope.row.name }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column prop="typeName" label="项目类型" width="120" align="center">
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120" align="center">
        <template slot-scope="scope">
          <el-tag 
            :type="getStatusTagType(scope.row.status)"
            :style="getStatusStyle(scope.row.status)"
            size="small">
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="startDate" label="开始日期" width="120" align="center">
      </el-table-column>
      
      <el-table-column prop="endDate" label="结束日期" width="120" align="center">
      </el-table-column>
      
      <el-table-column prop="maintenanceUnit" label="养护单位" min-width="150" show-overflow-tooltip>
      </el-table-column>
      
      <el-table-column prop="manager" label="负责人" width="100" align="center">
      </el-table-column>
      
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            size="small" 
            @click="handleView(scope.row)">
            查看
          </el-button>
          
          <el-button 
            v-if="scope.row.status === 'draft'"
            type="text" 
            size="small" 
            @click="handleEdit(scope.row)"
            v-hasPermi="['maintenance:project:edit']">
            修改
          </el-button>
          
          <el-button 
            v-if="scope.row.status === 'draft'"
            type="text" 
            size="small" 
            style="color: #f56c6c"
            @click="handleDelete(scope.row)"
            v-hasPermi="['maintenance:project:remove']">
            删除
          </el-button>
          
          <el-button 
            v-if="scope.row.status === 'pending'"
            type="text" 
            size="small" 
            @click="handleApprove(scope.row)"
            v-hasPermi="['maintenance:project:approve']">
            审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 项目详情弹窗 -->
    <project-detail-dialog
      v-if="detailDialogVisible"
      :visible.sync="detailDialogVisible"
      :project-id="currentProjectId"
      @refresh="getList" />

    <!-- 审批弹窗 -->
    <approval-dialog
      v-if="approvalDialogVisible"
      :visible.sync="approvalDialogVisible"
      :project-id="currentProjectId"
      @refresh="getList" />
  </div>
</template>

<script>
// TODO: 当后端接口实现后，替换为真实API导入
import maintenanceApi from '@/api/mock/maintenance'
import { apiCall, mockPermissionCheck } from '@/utils/mock-helper'
import ProjectDetailDialog from './ProjectDetailDialog.vue'
import ApprovalDialog from './ApprovalDialog.vue'

export default {
  name: 'MaintenanceProjectList',
  components: {
    ProjectDetailDialog,
    ApprovalDialog
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'bridge',
      
      // 加载状态
      loading: false,
      
      // 项目列表数据
      projectList: [],
      
      // 总数
      total: 0,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        type: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      
      // 日期范围
      dateRange: [],
      
      // 项目类型选项 - TODO: 后端实现后从API获取
      projectTypes: [],
      
      // 项目状态选项 - TODO: 后端实现后从API获取  
      projectStatus: [],
      
      // 项目名称选项（用于筛选）
      projectNameOptions: [],
      
      // 选中的行
      selectedRows: [],
      
      // 弹窗控制
      detailDialogVisible: false,
      approvalDialogVisible: false,
      currentProjectId: null
    }
  },
  created() {
    this.initData()
    this.getList()
  },
  methods: {
    /**
     * 初始化基础数据
     * TODO: 后端实现后替换为真实API调用
     */
    async initData() {
      try {
        // 获取项目类型
        const typesRes = await apiCall(
          maintenanceApi.getProjectTypes,
          null // TODO: 替换为真实API
        )
        if (typesRes.code === 200) {
          this.projectTypes = typesRes.data
        }
        
        // 获取项目状态
        const statusRes = await apiCall(
          maintenanceApi.getProjectStatus,
          null // TODO: 替换为真实API
        )
        if (statusRes.code === 200) {
          this.projectStatus = statusRes.data
        }
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('初始化数据失败')
      }
    },
    
    /**
     * 获取项目列表
     * TODO: 后端实现后替换为真实API调用
     */
    async getList() {
      this.loading = true
      try {
        // 处理日期范围参数
        if (this.dateRange && this.dateRange.length === 2) {
          this.queryParams.startDate = this.dateRange[0]
          this.queryParams.endDate = this.dateRange[1]
        } else {
          this.queryParams.startDate = ''
          this.queryParams.endDate = ''
        }
        
        const response = await apiCall(
          maintenanceApi.getMaintenanceProjectList,
          null, // TODO: 替换为真实API
          this.queryParams
        )
        
        if (response.code === 200) {
          this.projectList = response.rows
          this.total = response.total
          
          // 提取项目名称用于筛选选项
          this.projectNameOptions = response.rows.map(item => ({
            id: item.id,
            name: item.name
          }))
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.$message.error('获取项目列表失败')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 标签页切换
     */
    handleTabClick(tab) {
      console.log('切换到:', tab.name === 'bridge' ? '桥梁养护' : '隧道养护')
      // TODO: 根据标签页类型重新加载数据
      this.getList()
    },
    
    /**
     * 查询
     */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    /**
     * 重置
     */
    handleReset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: '',
        type: '',
        status: '',
        startDate: '',
        endDate: ''
      }
      this.dateRange = []
      this.getList()
    },
    
    /**
     * 新增项目
     */
    handleAdd() {
      // TODO: 跳转到项目创建页面
      this.$router.push('/maintenance/projects/create')
    },
    
    /**
     * 查看项目详情
     */
    handleView(row) {
      this.currentProjectId = row.id
      this.detailDialogVisible = true
    },
    
    /**
     * 编辑项目
     */
    handleEdit(row) {
      // TODO: 跳转到项目编辑页面
      this.$router.push(`/maintenance/project/edit/${row.id}`)
    },
    
    /**
     * 删除项目
     */
    handleDelete(row) {
      this.$confirm(`确认删除项目"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await apiCall(
            maintenanceApi.deleteMaintenanceProject,
            null, // TODO: 替换为真实API
            row.id
          )
          
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(response.msg)
          }
        } catch (error) {
          console.error('删除项目失败:', error)
          this.$message.error('删除项目失败')
        }
      })
    },
    
    /**
     * 审批项目
     */
    handleApprove(row) {
      this.currentProjectId = row.id
      this.approvalDialogVisible = true
    },
    
    /**
     * 表格选择变化
     */
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    /**
     * 获取状态标签类型
     */
    getStatusTagType(status) {
      const statusMap = {
        'draft': 'info',
        'pending': 'warning', 
        'approved': 'success',
        'rejected': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    /**
     * 获取状态样式
     * 基于设计文档的深色主题样式
     */
    getStatusStyle(status) {
      const statusStyles = {
        'draft': {
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          border: 'none'
        },
        'pending': {
          backgroundColor: '#eab308',
          color: '#1f2937',
          border: 'none'
        },
        'approved': {
          backgroundColor: '#22c55e',
          color: '#ffffff',
          border: 'none'
        },
        'rejected': {
          backgroundColor: '#ef4444',
          color: '#ffffff',
          border: 'none'
        }
      }
      return statusStyles[status] || {}
    }
  }
}
</script>

<style lang="scss" scoped>
/* 基于设计文档的深色主题样式 */
.project-list-container {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  min-height: 100vh;
  padding: 20px;
  color: #ffffff;
}

.nav-tabs {
  margin-bottom: 20px;
  
  ::v-deep .el-tabs {
    .el-tabs__header {
      background: #1e3a8a;
      border-bottom: 1px solid #4b5563;
      margin: 0;
    }
    
    .el-tabs__nav-wrap::after {
      display: none;
    }
    
    .el-tabs__item {
      color: #9ca3af;
      font-size: 14px;
      font-weight: normal;
      border: none;
      padding: 12px 16px;
      
      &:hover {
        color: #ffffff;
      }
      
      &.is-active {
        color: #ffffff;
        font-weight: bold;
        border-bottom: 3px solid #3b82f6;
      }
    }
  }
}

.filter-form {
  background: rgba(30, 64, 175, 0.1);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  ::v-deep .el-form-item__label {
    color: #ffffff;
  }
  
  ::v-deep .el-input__inner {
    background: #374151;
    border-color: #9ca3af;
    color: #ffffff;
    
    &::placeholder {
      color: #9ca3af;
    }
    
    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  ::v-deep .el-select .el-input .el-select__caret {
    color: #9ca3af;
  }
  
  ::v-deep .el-button--primary {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #2563eb;
      border-color: #2563eb;
    }
  }
  
  ::v-deep .el-button--default {
    background: transparent;
    border-color: #9ca3af;
    color: #ffffff;
    
    &:hover {
      background: #374151;
      border-color: #ffffff;
    }
  }
}

.toolbar {
  margin-bottom: 20px;
  
  ::v-deep .el-button--primary {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
    font-weight: bold;
    padding: 12px 24px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background: #2563eb;
      border-color: #2563eb;
      filter: brightness(1.1);
    }
    
    &:active {
      transform: translateY(1px);
    }
  }
}

::v-deep .el-table {
  background: transparent;
  border: 1px solid #9ca3af;
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header {
    background: #1e3a8a;
    
    th {
      background: #1e3a8a;
      color: #ffffff;
      font-weight: bold;
      border-bottom: 1px solid #9ca3af;
      padding: 12px 16px;
    }
  }
  
  .el-table__body {
    tr {
      background: rgba(30, 64, 175, 0.1);
      
      &:nth-child(even) {
        background: transparent;
      }
      
      &:hover {
        background: rgba(30, 64, 175, 0.2) !important;
      }
      
      td {
        color: #ffffff;
        border-bottom: 1px solid #9ca3af;
        padding: 12px 16px;
      }
    }
  }
  
  .el-table__empty-text {
    color: #9ca3af;
  }
  
  .el-link--primary {
    color: #3b82f6;
    
    &:hover {
      color: #2563eb;
    }
  }
  
  .el-button--text {
    color: #3b82f6;
    
    &:hover {
      color: #2563eb;
    }
  }
}

/* 分页样式 */
::v-deep .el-pagination {
  text-align: center;
  margin-top: 20px;
  
  .el-pagination__total,
  .el-pagination__jump {
    color: #9ca3af;
  }
  
  .btn-prev,
  .btn-next,
  .el-pager li {
    background: transparent;
    border: 1px solid #4b5563;
    color: #9ca3af;
    
    &:hover {
      background: #374151;
      color: #ffffff;
    }
    
    &.active {
      background: #3b82f6;
      color: #ffffff;
      border-color: #3b82f6;
    }
  }
  
  .el-pagination__sizes .el-select .el-input .el-input__inner {
    background: #374151;
    border-color: #4b5563;
    color: #ffffff;
  }
}
</style>
