<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="dialogVisible"
    class="project-modal-wrapper"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    append-to-body
    top="5vh"
    @close="handleClose"
  >
    <div class="modal-content-wrapper">
      <!-- 动态加载对应的页面组件 -->
      <component
        :is="currentComponent"
        v-bind="componentProps"
        @close="handleClose"
        @refresh="handleRefresh"
        @edit="handleEdit"
        @save="handleSave"
        @submit="handleSubmit"
      />
    </div>

    <!-- 不显示默认的footer，让内嵌的组件自己控制 -->
    <span slot="footer" class="dialog-footer" style="display: none;"></span>
  </el-dialog>
</template>

<script>
// 动态引入组件，避免全部加载
const ProjectCreate = () => import('@/views/maintenance/projects/create/index.vue')

export default {
  name: 'ProjectModalWrapper',
  components: {
    ProjectCreate
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      required: true,
      validator: value => ['create', 'edit', 'view', 'approve'].includes(value)
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    infrastructureType: {
      type: String,
      default: 'bridge'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },

    modalTitle() {
      const titles = {
        create: '新增养护项目',
        edit: '编辑养护项目',
        view: '查看养护项目',
        approve: '审批养护项目'
      }
      return titles[this.mode] || '养护项目'
    },

    // 根据模式确定要加载的组件
    currentComponent() {
      const componentMap = {
        create: 'ProjectCreate',
        edit: 'ProjectCreate',
        view: 'ProjectCreate',  // 查看模式也使用创建组件，但设置为只读
        approve: 'ProjectCreate'  // 审批模式也使用创建组件，但设置为只读并显示审批信息
      }
      return componentMap[this.mode]
    },

    // 传递给子组件的属性
    componentProps() {
      const baseProps = {
        infrastructureType: this.infrastructureType,
        isModal: true  // 标识这是在弹框中使用
      }

      if (this.mode === 'view') {
        return {
          ...baseProps,
          projectId: this.projectId,
          isView: true
        }
      } else if (this.mode === 'approve') {
        return {
          ...baseProps,
          projectId: this.projectId,
          isApproval: true
        }
      } else if (this.mode === 'edit') {
        return {
          ...baseProps,
          projectId: this.projectId,
          isEdit: true
        }
      }

      return baseProps
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
      this.$emit('update:visible', false)
    },

    handleRefresh() {
      this.$emit('refresh')
    },

    handleEdit(projectId) {
      this.$emit('edit', projectId)
    },

    handleSave() {
      this.$emit('save')
    },

    handleSubmit() {
      // 当子组件提交成功后，关闭弹框并刷新列表
      this.$emit('submit')
      this.handleClose()
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

.project-modal-wrapper {
  .modal-content-wrapper {
    // 确保弹框内容继承主题背景
    background: var(--inspection-bg-primary, #091A4B);
    min-height: 400px;

    // 修改子组件的样式，让它适应弹框显示
    :global(.maintenance-theme) {
      background: var(--inspection-bg-primary, #091A4B);

      .page-container {
        padding: 20px;
        margin: 0;
        background: var(--inspection-bg-primary, #091A4B);
        height: auto;
        min-height: auto;

        .card-container {
          background: transparent;
          border: none;
          box-shadow: none;
          margin: 0;
          padding: 0;
        }
      }
    }

    // 调整子组件中的关闭按钮样式
    :global(.page-header .close-btn) {
      display: none; // 隐藏子组件的关闭按钮，使用弹框自带的
    }
  }
}

// 项目弹框包装器使用公共样式，具体样式已在maintenance-theme.scss中定义
</style>
