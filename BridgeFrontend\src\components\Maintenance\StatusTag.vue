<template>
  <span :class="['status-tag', statusClass]">
    {{ statusText }}
  </span>
</template>

<script>
/**
 * 状态标签组件
 * 用于显示项目、任务等状态
 */
export default {
  name: 'StatusTag',
  props: {
    status: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'project', // project, task, audit, extension
      validator: value => ['project', 'task', 'audit', 'extension'].includes(value)
    }
  },
  computed: {
    statusClass() {
      const statusMap = {
        // 项目状态
        draft: 'draft',
        pending: 'pending',
        waiting: 'pending', 
        approved: 'approved',
        rejected: 'rejected',
        in_progress: 'pending',
        overdue: 'overdue',
        completed: 'approved',
        overdue_completed: 'approved',

        // 任务状态
        not_started: 'draft',
        normal: 'approved',

        // 审核状态
        primary_audit: 'pending',
        secondary_audit: 'pending',
        audit_passed: 'approved',
        audit_rejected: 'rejected',
        in_review: 'pending',

        // 延期状态
        extension_pending: 'pending',
        extension_approved: 'approved',
        extension_rejected: 'rejected',
        withdrawn: 'draft'
      }

      return statusMap[this.status] || 'draft'
    },
    
    statusText() {
      const textMap = {
        // 项目状态
        draft: '草稿',
        pending: '待启动',
        approved: '审批通过',
        rejected: '审批拒绝',
        waiting: '待启动',
        in_progress: '进行中',
        overdue: '超期',
        completed: '已完成',
        overdue_completed: '超期完成',

        // 任务状态
        not_started: '未开始',
        normal: '正常',

        // 审核状态
        primary_audit: '初审中',
        secondary_audit: '复核中',
        audit_passed: '审核通过',
        audit_rejected: '审核拒绝',
        in_review: '审核中',

        // 延期状态
        extension_pending: '延期审批中',
        extension_approved: '延期通过',
        extension_rejected: '延期拒绝',
        withdrawn: '已撤回'
      }

      return textMap[this.status] || '未知状态'
    }
  }
}
</script>

<style lang="scss" scoped>
// 6.7.9 状态标签组件样式（与巡检记录保持一致）
.status-tag {
  // 6.7.9 状态标签基础样式
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500; // 中等粗细
  min-width: 80px;
  text-align: center;
  display: inline-block;
  white-space: nowrap; // 防止换行

  // 6.7.9 状态颜色方案
  &.draft {
    background: #e0e7ff; // 草稿状态（浅蓝色背景）
    color: #3730a3; // 深蓝色文字
  }

  &.pending {
    background: #fef3c7; // 审批中状态（浅黄色背景）
    color: #92400e; // 深色文字
  }

  &.approved {
    background: #d1fae5; // 通过状态（浅绿色背景）
    color: #166534; // 深绿色文字
  }

  &.rejected {
    background: #ef4444; // 拒绝状态（红色背景）
    color: #ffffff; // 白色文字
  }

  &.overdue {
    background: #f59e0b; // 超期状态（黄色背景）
    color: #ffffff; // 白色文字
    // 6.7.9 CSS keyframes闪烁动画（1.5s无限循环）
    animation: blink 1.5s infinite;
  }

  // 6.7.9 成功/警告/危险状态
  &.success {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  &.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  &.danger {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
}

// 6.7.9 CSS keyframes闪烁动画（1.5s无限循环）
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.6; }
}
</style>
