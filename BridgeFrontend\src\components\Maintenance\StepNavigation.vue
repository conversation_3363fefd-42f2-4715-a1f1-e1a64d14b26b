<template>
  <div class="step-navigation">
    <div class="step-container">
      <div 
        v-for="(step, index) in steps" 
        :key="index"
        class="step-item"
        :class="{
          'is-active': index === currentStep,
          'is-completed': index < currentStep,
          'is-disabled': !isStepClickable(index),
          'is-clickable': isStepClickable(index)
        }"
        @click="handleStepClick(index)"
      >
        <div class="step-title">{{ step.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StepNavigation',
  props: {
    steps: {
      type: Array,
      required: true,
      validator: steps => steps.every(step => step.title)
    },
    currentStep: {
      type: Number,
      default: 0
    },
    allowClick: {
      type: Boolean,
      default: false
    },
    // 可点击的步骤索引数组
    clickableSteps: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleStepClick(index) {
      if (!this.isStepClickable(index)) return
      
      this.$emit('step-click', index)
    },
    
    // 判断步骤是否可点击
    isStepClickable(index) {
      if (!this.allowClick) return false
      
      // 如果指定了clickableSteps，则按照该数组判断
      if (this.clickableSteps.length > 0) {
        return this.clickableSteps.includes(index)
      }
      
      // 默认行为：只能点击当前步骤及之前的步骤
      return index <= this.currentStep
    }
  }
}
</script>

<style lang="scss" scoped>
.step-navigation {
  background: var(--inspection-bg-primary, #091A4B);
  border-bottom: 1px solid var(--inspection-border, #374151);
  
  .step-container {
    display: flex;
    align-items: stretch;
    max-width: 800px;
    margin: 0;
    
      .step-item {
        flex: 1;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
      
      &.is-disabled {
        cursor: not-allowed;
        opacity: 0.5;
        
        .step-title {
          color: var(--inspection-text-disabled, #6b7280) !important;
          border-bottom-color: transparent !important;
        }
      }
      
      &.is-clickable {
        cursor: pointer;
        
        &:not(.is-active):not(.is-completed):hover {
          .step-title {
            color: var(--inspection-primary-light, #74a7f5);
            background: rgba(92, 157, 255, 0.1);
          }
        }
      }
      
        .step-title {
          padding: 16px 20px;
          font-size: 14px;
          color: var(--inspection-text-secondary, #e2e8f0);
          text-align: center;
          background: var(--inspection-bg-primary, #091A4B);
          transition: all 0.3s ease;
          border-bottom: 3px solid transparent;
          line-height: 1.4;
          font-weight: 500;
        }
      
        // 激活状态 - tab样式
        &.is-active {
          .step-title {
            color: var(--inspection-primary, #5C9DFF);
            background: var(--inspection-bg-secondary, rgba(9, 26, 75, 0.9));
            border-bottom-color: var(--inspection-primary, #5C9DFF);
            font-weight: 600;
          }
        }
      
        // 已完成状态
        &.is-completed {
          .step-title {
            color: var(--inspection-success, #10b981);
            background: rgba(16, 185, 129, 0.1);
            border-bottom-color: var(--inspection-success, #10b981);
          }
        }
      
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .step-navigation {
    .step-container {
      .step-item {
        .step-title {
          font-size: 12px;
        }
        
        .step-icon {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
