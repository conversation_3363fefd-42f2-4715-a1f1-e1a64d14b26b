/**
 * 养护运维模块静态预制数据
 * 
 * ⚠️ 注意：这些是临时的静态数据，用于前端开发和演示
 * 当后端接口实现后，需要删除此文件并替换为真实API调用
 * 
 * 基于《长沙市智慧桥隧管理平台-养护运维前端设计文档.md》创建
 */

// ==================== 基础数据 ====================

/**
 * 项目类型枚举
 * 对应设计文档中的四种项目类型
 */
export const PROJECT_TYPES = [
  { value: 'monthly', label: '月度养护', code: 'MONTHLY' },
  { value: 'cleaning', label: '保洁项目', code: 'CLEANING' },
  { value: 'emergency', label: '应急养护', code: 'EMERGENCY' },
  { value: 'preventive', label: '预防养护', code: 'PREVENTIVE' }
]

/**
 * 项目状态枚举
 * 对应设计文档中的状态管理
 */
export const PROJECT_STATUS = [
  { value: 'draft', label: '草稿', color: '#3b82f6', bgColor: '#3b82f6' },
  { value: 'pending', label: '审批中', color: '#1f2937', bgColor: '#eab308' },
  { value: 'approved', label: '审批通过', color: '#ffffff', bgColor: '#22c55e' },
  { value: 'rejected', label: '审批拒绝', color: '#ffffff', bgColor: '#ef4444' }
]

/**
 * 任务状态枚举
 * 对应维修任务的执行状态
 */
export const TASK_STATUS = [
  { value: 'pending', label: '未完成', color: '#9ca3af' },
  { value: 'in_review', label: '审核中', color: '#eab308' },
  { value: 'returned', label: '退回', color: '#ef4444' },
  { value: 'in_audit', label: '复核中', color: '#3b82f6' },
  { value: 'completed', label: '已完成', color: '#22c55e' }
]

/**
 * 应急维修状态枚举
 */
export const EMERGENCY_STATUS = [
  { value: 'waiting', label: '待处理', color: '#eab308' },
  { value: 'processing', label: '处理审批中', color: '#3b82f6' },
  { value: 'returned', label: '退回', color: '#ef4444' },
  { value: 'completed', label: '已处理', color: '#22c55e' }
]

// ==================== 组织机构和人员数据 ====================

/**
 * 管理单位数据
 * TODO: 后端接口实现后替换为 /api/system/dept/list
 */
export const MANAGEMENT_UNITS = [
  { id: 1, name: '长沙市桥隧中心', code: 'CSBQ_CENTER' },
  { id: 2, name: '长沙市交通运输局', code: 'CS_TRANSPORT' },
  { id: 3, name: '长沙市城乡建设局', code: 'CS_CONSTRUCTION' }
]

/**
 * 养护单位数据
 * TODO: 后端接口实现后替换为 /api/system/dept/list?type=maintenance
 */
export const MAINTENANCE_UNITS = [
  { id: 10, name: '长沙市桥梁管理处', code: 'CS_BRIDGE_MGMT', managementUnitId: 1 },
  { id: 11, name: '长沙市隧道管理处', code: 'CS_TUNNEL_MGMT', managementUnitId: 1 },
  { id: 12, name: '长沙市路桥建设投资集团', code: 'CS_ROAD_BRIDGE_GROUP', managementUnitId: 2 },
  { id: 13, name: '长沙市政建设集团', code: 'CS_MUNICIPAL_GROUP', managementUnitId: 3 }
]

/**
 * 项目负责人数据
 * 基于养护运维业务需求和原型图中的真实姓名构造
 * 包含不同角色：项目经理、技术负责人、现场负责人、专业负责人
 * TODO: 后端接口实现后替换为 /api/system/user/list?role=project_manager
 */
export const PROJECT_MANAGERS = [
  // ==================== 长沙市桥梁管理处 (unitId: 10) ====================
  // 项目经理（高级）
  { 
    id: 100, 
    name: '吴知非', 
    phone: '13800138001', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'project_manager',
    title: '项目经理',
    specialties: ['月度养护', '应急养护'],
    experience: 8,
    level: 'senior'
  },
  { 
    id: 101, 
    name: '郭照临', 
    phone: '13800138002', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'project_manager',
    title: '高级项目经理',
    specialties: ['月度养护', '预防养护'],
    experience: 12,
    level: 'senior'
  },
  { 
    id: 102, 
    name: '李慕桔', 
    phone: '13800138003', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'project_manager',
    title: '项目经理',
    specialties: ['保洁项目', '月度养护'],
    experience: 6,
    level: 'intermediate'
  },
  { 
    id: 103, 
    name: '林文龙', 
    phone: '13800138004', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'technical_manager',
    title: '技术负责人',
    specialties: ['应急养护', '技术维修'],
    experience: 10,
    level: 'senior'
  },
  { 
    id: 104, 
    name: '高枕书', 
    phone: '13800138005', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'project_manager',
    title: '项目经理',
    specialties: ['月度养护', '保洁项目'],
    experience: 5,
    level: 'intermediate'
  },
  // 新增桥梁管理处负责人
  { 
    id: 110, 
    name: '王景深', 
    phone: '13800138011', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'site_manager',
    title: '现场负责人',
    specialties: ['现场管理', '安全监督'],
    experience: 7,
    level: 'intermediate'
  },
  { 
    id: 111, 
    name: '刘志强', 
    phone: '13800138012', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'quality_manager',
    title: '质量负责人',
    specialties: ['质量控制', '验收管理'],
    experience: 9,
    level: 'senior'
  },
  { 
    id: 112, 
    name: '赵临洲', 
    phone: '13800138013', 
    unitId: 10, 
    unitName: '长沙市桥梁管理处',
    role: 'project_manager',
    title: '副项目经理',
    specialties: ['保洁项目', '日常维护'],
    experience: 4,
    level: 'junior'
  },

  // ==================== 长沙市隧道管理处 (unitId: 11) ====================
  { 
    id: 105, 
    name: '徐桧桐', 
    phone: '13800138006', 
    unitId: 11, 
    unitName: '长沙市隧道管理处',
    role: 'project_manager',
    title: '隧道项目经理',
    specialties: ['隧道养护', '通风系统'],
    experience: 8,
    level: 'senior'
  },
  { 
    id: 106, 
    name: '何叔川', 
    phone: '13800138007', 
    unitId: 11, 
    unitName: '长沙市隧道管理处',
    role: 'project_manager',
    title: '隧道项目经理',
    specialties: ['隧道保洁', '照明系统'],
    experience: 6,
    level: 'intermediate'
  },
  // 新增隧道管理处负责人
  { 
    id: 113, 
    name: '张明华', 
    phone: '13800138014', 
    unitId: 11, 
    unitName: '长沙市隧道管理处',
    role: 'technical_manager',
    title: '隧道技术负责人',
    specialties: ['隧道结构', '应急维修'],
    experience: 11,
    level: 'senior'
  },
  { 
    id: 114, 
    name: '陈建国', 
    phone: '13800138015', 
    unitId: 11, 
    unitName: '长沙市隧道管理处',
    role: 'safety_manager',
    title: '安全负责人',
    specialties: ['安全管理', '应急预案'],
    experience: 13,
    level: 'expert'
  },

  // ==================== 长沙市路桥建设投资集团 (unitId: 12) ====================
  { 
    id: 107, 
    name: '郭云舟', 
    phone: '13800138008', 
    unitId: 12, 
    unitName: '长沙市路桥建设投资集团',
    role: 'project_manager',
    title: '集团项目经理',
    specialties: ['大型项目', '投资管理'],
    experience: 10,
    level: 'senior'
  },
  { 
    id: 108, 
    name: '黄梓航', 
    phone: '13800138009', 
    unitId: 12, 
    unitName: '长沙市路桥建设投资集团',
    role: 'project_manager',
    title: '建设项目经理',
    specialties: ['建设管理', '工程监理'],
    experience: 7,
    level: 'intermediate'
  },
  // 新增路桥集团负责人
  { 
    id: 115, 
    name: '李建军', 
    phone: '13800138016', 
    unitId: 12, 
    unitName: '长沙市路桥建设投资集团',
    role: 'investment_manager',
    title: '投资项目经理',
    specialties: ['投资决策', '项目评估'],
    experience: 15,
    level: 'expert'
  },
  { 
    id: 116, 
    name: '王中华', 
    phone: '13800138017', 
    unitId: 12, 
    unitName: '长沙市路桥建设投资集团',
    role: 'construction_manager',
    title: '建设负责人',
    specialties: ['施工管理', '进度控制'],
    experience: 12,
    level: 'senior'
  },

  // ==================== 长沙市政建设集团 (unitId: 13) ====================
  { 
    id: 109, 
    name: '赵景深', 
    phone: '13800138010', 
    unitId: 13, 
    unitName: '长沙市政建设集团',
    role: 'project_manager',
    title: '市政项目经理',
    specialties: ['市政工程', '综合养护'],
    experience: 9,
    level: 'senior'
  },
  // 新增市政集团负责人
  { 
    id: 117, 
    name: '周建华', 
    phone: '13800138018', 
    unitId: 13, 
    unitName: '长沙市政建设集团',
    role: 'municipal_manager',
    title: '市政负责人',
    specialties: ['市政管理', '城市基础设施'],
    experience: 14,
    level: 'expert'
  },
  { 
    id: 118, 
    name: '孙立明', 
    phone: '13800138019', 
    unitId: 13, 
    unitName: '长沙市政建设集团',
    role: 'project_manager',
    title: '综合项目经理',
    specialties: ['综合管理', '协调统筹'],
    experience: 8,
    level: 'intermediate'
  },

  // ==================== 桥隧中心监管人员 (unitId: 1) ====================
  // 预防养护项目专用负责人
  { 
    id: 119, 
    name: '高哲', 
    phone: '13800138020', 
    unitId: 1, 
    unitName: '长沙市桥隧中心',
    role: 'supervisor',
    title: '桥隧监管员',
    specialties: ['预防养护', '监管审批'],
    experience: 16,
    level: 'expert'
  },
  { 
    id: 120, 
    name: '刘明明', 
    phone: '13800138021', 
    unitId: 1, 
    unitName: '长沙市桥隧中心',
    role: 'supervisor',
    title: '高级监管员',
    specialties: ['审批管理', '政策制定'],
    experience: 18,
    level: 'expert'
  },
  { 
    id: 121, 
    name: '罗政权', 
    phone: '13800138022', 
    unitId: 1, 
    unitName: '长沙市桥隧中心',
    role: 'supervisor',
    title: '技术监管员',
    specialties: ['技术审查', '标准制定'],
    experience: 20,
    level: 'expert'
  }
]

/**
 * 养护人员数据
 * TODO: 后端接口实现后替换为 /api/system/user/list?role=maintenance_staff
 */
export const MAINTENANCE_STAFF = [
  { id: 200, name: '黄昭言', phone: '15820007394', unitId: 10 },
  { id: 201, name: '刘雨桐', phone: '13122238579', unitId: 10 },
  { id: 202, name: '罗砚秋', phone: '19620059483', unitId: 10 },
  { id: 203, name: '王景深', phone: '19321207394', unitId: 11 },
  { id: 204, name: '刘志强', phone: '18557189483', unitId: 11 },
  { id: 205, name: '赵临洲', phone: '19020017495', unitId: 12 }
]

// ==================== 桥梁隧道基础数据 ====================

/**
 * 桥梁基础数据
 * TODO: 后端接口实现后替换为 /api/archives/bridges/list
 */
export const BRIDGES_DATA = [
  {
    id: 1,
    name: '湘江大桥',
    code: 'CS-B-001',
    road: '枫林一路',
    type: 'bridge',
    district: 'yuelu',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 1200,
    width: 28,
    buildYear: 2010,
    structureType: '预应力混凝土连续梁桥',
    maintenanceLevel: '一级',
    maintenanceContent: '排水系统养护',
    maintenanceStaff: '黄昭言',
    maintenanceStaffId: 200
  },
  {
    id: 2,
    name: '浏阳河大桥',
    code: 'CS-B-002',
    road: '枫林一路',
    type: 'bridge',
    district: 'furong',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 800,
    width: 24,
    buildYear: 2015,
    structureType: '钢筋混凝土拱桥',
    maintenanceLevel: '二级',
    maintenanceContent: '上部结构养护',
    maintenanceStaff: '刘雨桐',
    maintenanceStaffId: 201
  },
  {
    id: 3,
    name: '捞刀河大桥',
    code: 'CS-B-003',
    road: '枫林一路',
    type: 'bridge',
    district: 'tianxin',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 600,
    width: 20,
    buildYear: 2018,
    structureType: '预制小箱梁桥',
    maintenanceLevel: '三级',
    maintenanceContent: '排水系统养护',
    maintenanceStaff: '罗砚秋',
    maintenanceStaffId: 202
  },
  {
    id: 4,
    name: '岳麓山大桥',
    code: 'CS-B-004',
    type: 'bridge',
    district: 'kaifu',
    road: '银盆南路',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 1500,
    width: 32,
    buildYear: 2012,
    structureType: '斜拉桥',
    maintenanceLevel: '一级',
    maintenanceContent: '桥面系养护',
    maintenanceStaff: '王景深',
    maintenanceStaffId: 203
  },
  {
    id: 5,
    name: '橘子洲大桥',
    code: 'CS-B-005',
    road: '西二环',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 1100,
    width: 30,
    buildYear: 2008,
    structureType: '预应力混凝土连续刚构桥',
    maintenanceContent: '伸缩缝养护',
    maintenanceStaff: '刘志强',
    maintenanceStaffId: 204
  }
]

/**
 * 隧道基础数据
 * TODO: 后端接口实现后替换为 /api/archives/tunnels/list
 */
export const TUNNELS_DATA = [
  {
    id: 101,
    name: '营盘路隧道',
    code: 'CS-T-001',
    road: '营盘路',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 2100,
    width: 14,
    buildYear: 2016,
    structureType: '双向四车道隧道',
    maintenanceContent: '通风系统养护',
    maintenanceStaff: '赵临洲',
    maintenanceStaffId: 205
  },
  {
    id: 102,
    name: '南湖路隧道',
    code: 'CS-T-002',
    road: '南湖路',
    managementUnit: '桥隧中心',
    managementUnitId: 1,
    length: 1800,
    width: 12,
    buildYear: 2019,
    structureType: '单向双车道隧道',
    maintenanceContent: '照明系统养护',
    maintenanceStaff: '黄昭言',
    maintenanceStaffId: 200
  }
]

// ==================== 病害数据 ====================

/**
 * 病害类型数据
 * TODO: 后端接口实现后替换为 /api/system/dict/data?dictType=disease_type
 */
export const DISEASE_TYPES = [
  { value: 'crack', label: '裂缝', category: 'structure' },
  { value: 'expansion_joint', label: '伸缩缝缺失', category: 'joint' },
  { value: 'lighting', label: '照明设施缺失', category: 'facility' },
  { value: 'drainage', label: '排水系统堵塞', category: 'drainage' },
  { value: 'guardrail', label: '护栏损坏', category: 'safety' },
  { value: 'pavement', label: '桥面铺装损坏', category: 'surface' }
]

/**
 * 病害部位数据
 * TODO: 后端接口实现后替换为 /api/system/dict/data?dictType=disease_location
 */
export const DISEASE_LOCATIONS = [
  { value: 'deck', label: '桥面系', category: 'bridge' },
  { value: 'expansion_joint', label: '伸缩缝', category: 'bridge' },
  { value: 'lighting_facility', label: '照明设施', category: 'facility' },
  { value: 'drainage_system', label: '排水系统', category: 'facility' },
  { value: 'guardrail', label: '护栏', category: 'safety' },
  { value: 'superstructure', label: '上部结构', category: 'structure' },
  { value: 'substructure', label: '下部结构', category: 'structure' }
]

/**
 * 病害数据
 * TODO: 后端接口实现后替换为 /api/maintenance/diseases/list
 */
export const DISEASES_DATA = [
  {
    id: 989,
    bridgeId: 1,
    bridgeName: '湘江大桥',
    diseaseCode: '989',
    location: '伸缩缝',
    type: '伸缩缝缺失',
    level: 3,
    quantity: 7,
    description: '桥梁东侧伸缩缝存在缺失，影响行车安全',
    reportPerson: '张三',
    reportPhone: '19321207394',
    reportTime: '2025-09-01 09:30:00',
    photos: ['disease_photo_1.jpg', 'disease_photo_2.jpg'],
    status: 'pending'
  },
  {
    id: 988,
    bridgeId: 1,
    bridgeName: '湘江大桥',
    diseaseCode: '988',
    location: '伸缩缝',
    type: '伸缩缝缺失',
    level: 2,
    quantity: 47,
    description: '桥梁西侧多处伸缩缝存在缺失现象',
    reportPerson: '李四',
    reportPhone: '19321207395',
    reportTime: '2025-09-02 14:20:00',
    photos: ['disease_photo_3.jpg'],
    status: 'processing'
  },
  {
    id: 987,
    bridgeId: 2,
    bridgeName: '浏阳河大桥',
    diseaseCode: '987',
    location: '照明设施',
    type: '照明设施缺失',
    level: 1,
    quantity: 42,
    description: '桥梁照明设施老化，部分路段照明不足',
    reportPerson: '王五',
    reportPhone: '19321207396',
    reportTime: '2025-09-03 16:45:00',
    photos: ['disease_photo_4.jpg', 'disease_photo_5.jpg'],
    status: 'completed'
  }
]

// ==================== 养护项目数据 ====================

/**
 * 养护项目列表数据
 * TODO: 后端接口实现后替换为 /api/maintenance/projects/list
 */
export const MAINTENANCE_PROJECTS = [
  {
    id: 1,
    name: '2025年9月湘江大桥月度养护项目',
    type: 'monthly',
    typeName: '月度养护',
    status: 'draft',
    statusName: '草稿',
    startDate: '2025-09-01',
    endDate: '2025-09-30',
    managementUnit: '长沙市桥隧中心',
    managementUnitId: 1,
    maintenanceUnit: '长沙市桥梁管理处',
    maintenanceUnitId: 10,
    supervisor: '长沙建设监理公司',
    manager: '吴知非',
    managerId: 100,
    phone: '13800138001',
    workload: 32,
    content: '对湘江大桥进行月度例行养护，包括排水系统清理、伸缩缝检查、照明设施维护等工作。',
    attachments: [],
    createBy: 100,
    createTime: '2025-09-15 10:30:00',
    updateTime: '2025-09-15 10:30:00'
  },
  {
    id: 2,
    name: '2025年9月浏阳河大桥月度养护项目',
    type: 'monthly',
    typeName: '月度养护',
    status: 'pending',
    statusName: '审批中',
    startDate: '2025-09-01',
    endDate: '2025-09-30',
    managementUnit: '长沙市桥隧中心',
    managementUnitId: 1,
    maintenanceUnit: '长沙市桥梁管理处',
    maintenanceUnitId: 10,
    supervisor: '长沙建设监理公司',
    manager: '郭照临',
    managerId: 101,
    phone: '13800138002',
    workload: 28,
    content: '对浏阳河大桥进行月度例行养护，重点关注上部结构检查和桥面清洁工作。',
    attachments: ['project_plan_2.pdf'],
    createBy: 101,
    createTime: '2025-09-14 14:20:00',
    updateTime: '2025-09-16 09:15:00'
  },
  {
    id: 3,
    name: '2025年9月捞刀河大桥保洁项目',
    type: 'cleaning',
    typeName: '保洁项目',
    status: 'approved',
    statusName: '审批通过',
    startDate: '2025-09-01',
    endDate: '2025-09-30',
    managementUnit: '长沙市桥隧中心',
    managementUnitId: 1,
    maintenanceUnit: '长沙市桥梁管理处',
    maintenanceUnitId: 10,
    supervisor: '',
    manager: '李慕桔',
    managerId: 102,
    phone: '13800138003',
    workload: 15,
    content: '对捞刀河大桥进行保洁工作，包括桥面清扫、护栏清洗、排水口清理等。',
    attachments: [],
    createBy: 102,
    createTime: '2025-09-10 11:45:00',
    updateTime: '2025-09-12 16:30:00'
  },
  {
    id: 4,
    name: '岳麓山大桥应急维修项目',
    type: 'emergency',
    typeName: '应急养护',
    status: 'approved',
    statusName: '审批通过',
    startDate: '2025-09-18',
    endDate: '2025-09-25',
    managementUnit: '长沙市桥隧中心',
    managementUnitId: 1,
    maintenanceUnit: '长沙市桥梁管理处',
    maintenanceUnitId: 10,
    supervisor: '',
    manager: '林文龙',
    managerId: 103,
    phone: '13800138004',
    workload: 8,
    content: '岳麓山大桥伸缩缝出现异常，需要紧急处理以确保行车安全。',
    attachments: ['emergency_report_4.pdf', 'site_photos_4.zip'],
    createBy: 103,
    createTime: '2025-09-18 08:30:00',
    updateTime: '2025-09-18 10:15:00'
  }
]

/**
 * 养护项目配置数据（养护项目步骤中的项目配置）
 * TODO: 后端接口实现后替换为项目详情API的一部分
 */
export const PROJECT_ITEMS = {
  1: [ // 项目ID为1的养护项目配置
    { id: 1, name: '排水系统养护', frequency: null },
    { id: 2, name: '伸缩缝检查维护', frequency: null }
  ],
  3: [ // 项目ID为3的保洁项目配置
    { id: 3, name: '桥面清扫', frequency: 7 }, // 7天/1次
    { id: 4, name: '护栏清洗', frequency: 30 } // 30天/1次
  ]
}

/**
 * 项目关联桥梁数据
 * TODO: 后端接口实现后替换为项目详情API的一部分
 */
export const PROJECT_BRIDGES = {
  1: [1, 2], // 项目1关联桥梁1和2
  2: [2], // 项目2关联桥梁2
  3: [3], // 项目3关联桥梁3
  4: [4] // 项目4关联桥梁4
}

/**
 * 项目关联病害数据
 * TODO: 后端接口实现后替换为项目详情API的一部分
 */
export const PROJECT_DISEASES = {
  1: [989, 988], // 项目1关联病害989和988
  4: [987] // 应急项目4关联病害987
}

// ==================== 养护维修数据 ====================

/**
 * 养护维修任务数据
 * TODO: 后端接口实现后替换为 /api/maintenance/repairs/list
 */
export const MAINTENANCE_TASKS = [
  {
    id: 1,
    projectId: 3,
    projectName: '2025年9月捞刀河大桥保洁项目',
    projectType: 'cleaning',
    bridgeId: 3,
    bridgeName: '捞刀河大桥',
    taskName: '桥面清扫',
    status: 'pending',
    statusName: '未完成',
    staffId: 202,
    staffName: '罗砚秋',
    phone: '19620059483',
    completedTime: null,
    maintenanceUnit: '长沙市桥梁管理处',
    manager: '李慕桔',
    isOverdue: false
  },
  {
    id: 2,
    projectId: 3,
    projectName: '2025年9月捞刀河大桥保洁项目',
    projectType: 'cleaning',
    bridgeId: 3,
    bridgeName: '捞刀河大桥',
    taskName: '护栏清洗',
    status: 'in_review',
    statusName: '审核中',
    staffId: 202,
    staffName: '罗砚秋',
    phone: '19620059483',
    completedTime: '2025-09-18 10:43:00',
    maintenanceUnit: '长沙市桥梁管理处',
    manager: '李慕桔',
    isOverdue: false
  },
  {
    id: 3,
    projectId: 4,
    projectName: '岳麓山大桥应急维修项目',
    projectType: 'emergency',
    bridgeId: 4,
    bridgeName: '岳麓山大桥',
    taskName: '伸缩缝紧急修复',
    status: 'completed',
    statusName: '已完成',
    staffId: 203,
    staffName: '王景深',
    phone: '19321207394',
    completedTime: '2025-09-19 16:20:00',
    maintenanceUnit: '长沙市桥梁管理处',
    manager: '林文龙',
    isOverdue: false
  }
]

/**
 * 应急维修数据
 * TODO: 后端接口实现后替换为 /api/maintenance/emergency/list
 */
export const EMERGENCY_REPAIRS = [
  {
    id: 1,
    bridgeName: '湘江大桥',
    bridgeId: 1,
    reportPerson: '向文革',
    reportTime: '2025-09-01 08:30:00',
    reportPhone: '13720186495',
    status: 'waiting',
    statusName: '待处理',
    diseaseCode: '00001',
    diseaseLocation: '伸缩缝部位',
    diseaseType: '伸缩缝损坏',
    diseaseQuantity: 71,
    staffName: '吴烟维',
    staffId: 200,
    maintenanceUnit: '长沙市桥梁管理处',
    description: '桥梁伸缩缝出现严重损坏，影响行车安全，需紧急处理'
  },
  {
    id: 2,
    bridgeName: '浏阳河大桥',
    bridgeId: 2,
    reportPerson: '罗子安',
    reportTime: '2025-09-01 14:20:00',
    reportPhone: '18720269485',
    status: 'completed',
    statusName: '已处理',
    diseaseCode: '00002',
    diseaseLocation: '照明设施',
    diseaseType: '照明故障',
    diseaseQuantity: 85,
    staffName: '郭昭临',
    staffId: 201,
    maintenanceUnit: '长沙市桥梁管理处',
    description: '桥梁照明设施故障，夜间照明不足'
  },
  {
    id: 3,
    bridgeName: '捞刀河大桥',
    bridgeId: 3,
    reportPerson: '江萍丹',
    reportTime: '2025-09-02 09:15:00',
    reportPhone: '15820007394',
    status: 'returned',
    statusName: '退回',
    diseaseCode: '00003',
    diseaseLocation: '护栏',
    diseaseType: '护栏损坏',
    diseaseQuantity: 73,
    staffName: '李露慧',
    staffId: 202,
    maintenanceUnit: '长沙市桥梁管理处',
    description: '桥梁护栏存在损坏，存在安全隐患'
  }
]

// ==================== 审批相关数据 ====================

/**
 * 审批历史数据
 * TODO: 后端接口实现后替换为 /api/maintenance/approval/history
 */
export const APPROVAL_HISTORY = [
  {
    id: 1,
    targetId: 2, // 项目ID
    targetType: 'project',
    step: '开始申请',
    processor: '郭照临',
    processorId: 101,
    status: 'approved',
    statusName: '通过',
    opinion: '项目资料齐全，同意提交审批',
    department: '长沙市桥梁管理处',
    receiveTime: '2025-09-14 14:20:00',
    processTime: '2025-09-14 14:25:00'
  },
  {
    id: 2,
    targetId: 2,
    targetType: 'project',
    step: '公司管理员初审',
    processor: '刘明明',
    processorId: 301,
    status: 'approved',
    statusName: '通过',
    opinion: '项目计划合理，养护内容符合要求',
    department: '长沙市桥梁管理处',
    receiveTime: '2025-09-14 14:25:00',
    processTime: '2025-09-15 09:30:00'
  },
  {
    id: 3,
    targetId: 2,
    targetType: 'project',
    step: '桥隧中心复核',
    processor: '罗政权',
    processorId: 401,
    status: 'pending',
    statusName: '待审批',
    opinion: '',
    department: '长沙市桥隧中心',
    receiveTime: '2025-09-15 09:30:00',
    processTime: null
  }
]


// ==================== 常用养护项目模板 ====================

/**
 * 常用养护项目模板
 * TODO: 后端接口实现后替换为 /api/maintenance/templates/list
 */
export const MAINTENANCE_TEMPLATES = [
  { id: 1, name: '排水系统养护', category: 'monthly', frequency: null },
  { id: 2, name: '伸缩缝检查维护', category: 'monthly', frequency: null },
  { id: 3, name: '照明设施检查', category: 'monthly', frequency: null },
  { id: 4, name: '护栏安全检查', category: 'monthly', frequency: null },
  { id: 5, name: '桥面清扫', category: 'cleaning', frequency: 7 },
  { id: 6, name: '护栏清洗', category: 'cleaning', frequency: 14 },
  { id: 7, name: '排水口清理', category: 'cleaning', frequency: 30 },
  { id: 8, name: '紧急抢修', category: 'emergency', frequency: null },
  { id: 9, name: '安全隐患处理', category: 'emergency', frequency: null }
]

// ==================== 统计数据 ====================

/**
 * 项目统计数据
 * TODO: 后端接口实现后替换为 /api/maintenance/statistics
 */
export const PROJECT_STATISTICS = {
  total: 156,
  draft: 12,
  pending: 8,
  approved: 128,
  rejected: 8,
  overdue: 15,
  completed: 98,
  inProgress: 43
}

/**
 * 任务完成统计
 * 用于项目详情页面的完成量显示
 */
export const TASK_COMPLETION_STATS = {
  1: { completed: 0, total: 32 }, // 项目1: 0/32
  2: { completed: 5, total: 28 }, // 项目2: 5/28
  3: { completed: 12, total: 15 }, // 项目3: 12/15
  4: { completed: 8, total: 8 } // 项目4: 8/8 (已完成)
}

// ==================== 导出所有数据 ====================

/**
 * 获取所有静态数据的函数
 * 方便在组件中统一导入使用
 */
export const getMockData = () => {
  return {
    // 基础枚举
    PROJECT_TYPES,
    PROJECT_STATUS,
    TASK_STATUS,
    EMERGENCY_STATUS,
    
    // 组织人员
    MANAGEMENT_UNITS,
    MAINTENANCE_UNITS,
    PROJECT_MANAGERS,
    MAINTENANCE_STAFF,
    
    // 基础设施
    BRIDGES_DATA,
    TUNNELS_DATA,
    
    // 病害相关
    DISEASE_TYPES,
    DISEASE_LOCATIONS,
    DISEASES_DATA,
    
    // 项目相关
    MAINTENANCE_PROJECTS,
    PROJECT_ITEMS,
    PROJECT_BRIDGES,
    PROJECT_DISEASES,
    
    // 维修相关
    MAINTENANCE_TASKS,
    EMERGENCY_REPAIRS,
    
    // 审批相关
    APPROVAL_HISTORY,
    
    // 模板和统计
    MAINTENANCE_TEMPLATES,
    PROJECT_STATISTICS,
    TASK_COMPLETION_STATS
  }
}

/**
 * 模拟API延迟的工具函数
 * 用于模拟真实API的响应时间
 */
export const mockApiDelay = (ms = 100) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 模拟分页数据的工具函数
 */
export const mockPagination = (data, page = 1, pageSize = 10) => {
  const start = (page - 1) * pageSize
  const end = start + pageSize
  return {
    data: data.slice(start, end),
    total: data.length,
    page,
    pageSize,
    totalPages: Math.ceil(data.length / pageSize)
  }
}

export default getMockData
