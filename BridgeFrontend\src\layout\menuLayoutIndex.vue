<template>
  <LayoutIndex>
    <div class="layoutIndex-main">
      <!-- 左侧菜单树 -->
      <Sidebar  v-if="!sidebar.hide" class="sidebar-container" />

      <transition name="fade-transform" mode="out-in">
        <div class="layoutIndex-main-r">
          <router-view />
        </div>
      </transition>
    </div>
  </LayoutIndex>
</template>

<script>
import LayoutIndex from '@/layout/index.vue'
import { AppMain, Navbar, Settings, Sidebar, TagsView } from '@/layout/components/index.js'

import { mapState } from 'vuex'
export default {
  name: 'MenuLayout',
  components: {
    LayoutIndex,
    Sidebar
  },
  data() {
    return {}
  },

  watch: {},

  computed: {
    ...mapState({
      theme: state => state.settings.theme,
      sideTheme: state => state.settings.sideTheme,
      sidebar: state => state.app.sidebar,
      device: state => state.app.device,
      needTagsView: state => state.settings.tagsView,
      fixedHeader: state => state.settings.fixedHeader
    })
  },

  created() {},

  mounted() {},

  methods: {}
}
</script>
<style lang="scss" scoped>
.layoutIndex-main {
  position: relative;
  width: 100%;
  height: calc(100% - #{$head-height});
  z-index: 10;
  // pointer-events: none;
  @include flex(space-between, flex-start);
  .layoutIndex-main-r {
    width: calc(100% - #{$base-sidebar-width} - #{$head-height-padding} - 5px);
    height: 100%;
    padding: 0 35px;
    // margin-left: $head-height-padding;
    box-sizing: border-box;
    // transition: all .28s;
    overflow-y: auto; // 为右侧内容区域添加垂直滚动
    overflow-x: hidden; // 隐藏水平滚动
  }
}
</style>
