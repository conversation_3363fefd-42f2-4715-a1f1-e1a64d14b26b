/**
 * 数据中心路由配置
 * @param {*} BaseLayout 基础布局组件
 * @param {*} Layout 菜单布局组件  
 */
export default (BaseLayout, Layout) => {
  return [
    {
      name: 'DataCenter',
      path: '/data-center',
      hidden: false,
      redirect: '/data-center/correlation-analysis',
      component: Layout,
      alwaysShow: true,
      meta: {
        title: '数据中心',
        icon: 'system',
        noCache: false,
        link: null
      },
      children: [
        {
          path: 'correlation-analysis',
          component: () => import('@/views/data-center/correlation-analysis/index'),
          name: 'CorrelationAnalysis',
          meta: { title: '关联性分析' }
        }
      ]
    }
  ]
}
