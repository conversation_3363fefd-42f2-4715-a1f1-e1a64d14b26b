export default function(BaseLayout, Layout) {
  return [
    {
      name: 'EmergencyCenter',
      path: '/emergency',
      hidden: false,
      redirect: '/emergency/events',
      component: Layout,
      alwaysShow: true,
      meta: {
        title: '应急处置',
        icon: 'emergency',
        noCache: false,
        link: null
      },
      children: [
        {
          path: 'events',
          component: () => import('@/views/emergency/events/index'),
          name: 'EmergencyEvents',
          meta: { title: '事件管理' }
        },
        {
          path: 'config/process',
          component: () => import('@/views/emergency/config/process/index'),
          name: 'EmergencyProcessConfig',
          meta: { title: '流程管理' }
        },
        {
          path: 'config/plans',
          component: () => import('@/views/emergency/config/plans/index'),
          name: 'EmergencyPlansConfig',
          meta: { title: '预案库管理' }
        },
        {
          path: 'config/contacts',
          component: () => import('@/views/emergency/config/contacts/index'),
          name: 'EmergencyContactsConfig',
          meta: { title: '通讯录管理' }
        },
        {
          path: 'config/experts',
          component: () => import('@/views/emergency/config/experts/index'),
          name: 'EmergencyExpertsConfig',
          meta: { title: '专家库管理' }
        },
        {
          path: 'config/equipment',
          component: () => import('@/views/emergency/config/equipment/index'),
          name: 'EmergencyEquipmentConfig',
          meta: { title: '设备管理' }
        }
      ]
    }
  ]
}