/**
 * 巡检中心路由配置
 * @param {*} BaseLayout 基础布局组件
 * @param {*} Layout 菜单布局组件  
 */
export default (BaseLayout, Layout) => {
  return [
    {
      name: 'InspectionCenter',
      path: '/inspection',
      hidden: false,
      redirect: '/inspection/records',
      component: Layout,
      alwaysShow: true,
      meta: {
        title: '巡检中心',
        icon: 'system',
        noCache: false,
        link: null
      },
      children: [
        {
          path: 'records',
          component: () => import('@/views/inspection/records/index'),
          name: 'InspectionRecords',
          meta: { title: '巡检记录' }
        },
        {
          path: 'diseases',
          component: () => import('@/views/inspection/diseases/index'),
          name: 'DiseasesManagement',
          meta: { title: '病害及处置' }
        },
        {
          path: 'diseases/detail/:id',
          component: () => import('@/views/inspection/diseases/detail'),
          name: 'DiseaseDetail',
          meta: { title: '病害详情', activeMenu: '/inspection/diseases' },
          hidden: true
        },
        {
          path: 'diseases/create',
          component: () => import('@/views/inspection/diseases/create'),
          name: 'DiseaseCreate',
          meta: { title: '新增病害', activeMenu: '/inspection/diseases' },
          hidden: true
        },
        {
          path: 'statistics',
          component: () => import('@/views/inspection/statistics/index'),
          name: 'InspectionStatistics',
          meta: { title: '巡检统计' }
        },
        {
          path: 'reports/detail/:id',
          component: () => import('@/views/inspection/reports/detail'),
          name: 'InspectionReportDetail',
          meta: { title: '巡检报告详情', activeMenu: '/inspection/records' },
          hidden: true
        },
      ]
    }
  ]
}
