/**
 * 养护运维模块路由配置
 * 基于设计文档功能模块结构
 */

export default (BaseLayout, Layout) => {
  return [
    {
      path: '/maintenance',
      component: Layout,
      redirect: '/maintenance/projects',
      name: 'Maintenance',
      meta: {
        title: '养护运维',
        icon: 'maintenance'
      },
      children: [
        // 养护项目模块
        {
          path: 'projects',
          component: () => import('@/views/maintenance/projects/index'),
          name: 'MaintenanceProjects',
          meta: {
            title: '养护项目',
            icon: 'project'
          }
        },
        {
          path: 'projects/create',
          component: () => import('@/views/maintenance/projects/create/index'),
          name: 'MaintenanceProjectCreate',
          hidden: true,
          meta: {
            title: '新增养护项目',
            activeMenu: '/maintenance/projects'
          }
        },
        {
          path: 'projects/edit/:id',
          component: () => import('@/views/maintenance/projects/create/index'),
          name: 'MaintenanceProjectEdit',
          hidden: true,
          meta: {
            title: '编辑养护项目',
            activeMenu: '/maintenance/projects'
          }
        },
        // 养护维修模块
        {
          path: 'repairs',
          component: () => import('@/views/maintenance/repairs/index'),
          name: 'MaintenanceRepairs',
          meta: {
            title: '养护维修',
            icon: 'repair'
          }
        },
        {
          path: 'repairs/detail/:id',
          component: () => import('@/views/maintenance/repairs/detail/index'),
          name: 'MaintenanceRepairDetail',
          hidden: true,
          meta: {
            title: '维修详情',
            activeMenu: '/maintenance/repairs'
          }
        },
        {
          path: 'repairs/emergency',
          component: () => import('@/views/maintenance/repairs/emergency/index'),
          name: 'MaintenanceEmergencyRepairs',
          hidden: true,
          meta: {
            title: '应急维修',
            activeMenu: '/maintenance/repairs'
          }
        },

        // 养护维修审核模块
        {
          path: 'audit',
          component: () => import('@/views/maintenance/audit/index'),
          name: 'MaintenanceAudit',
          meta: {
            title: '养护维修审核',
            icon: 'audit'
          }
        },
        {
          path: 'audit/detail/:id',
          component: () => import('@/views/maintenance/audit/detail/index'),
          name: 'MaintenanceAuditDetail',
          hidden: true,
          meta: {
            title: '审核详情',
            activeMenu: '/maintenance/audit'
          }
        },


      ]
    }
  ]
}
