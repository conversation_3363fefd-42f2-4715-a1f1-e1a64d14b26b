import { 
  getInspectionRecords, 
  getInspectionLogs, 
  getInspectionDetail 
} from '@/api/inspection/records'
import { 
  getDiseaseList, 
  getDiseaseDetail 
} from '@/api/inspection/diseases'
import { 
  getInspectionStatistics 
} from '@/api/inspection/statistics'
import { 
  getDefaultData, 
  handleApiError 
} from '@/utils/inspection/defaultData'

const state = {
  // 巡检记录相关状态
  inspectionRecords: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: false
  },
  
  // 当前查看的巡检日志
  currentInspectionLog: {
    bridgeId: null,
    bridgeName: '',
    month: '',
    logs: [],
    loading: false
  },
  
  // 当前巡检详情
  currentInspectionDetail: null,
  
  // 病害管理相关状态
  diseaseList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: false
  },
  
  // 当前病害详情
  currentDiseaseDetail: null,
  
  // 统计数据
  statisticsData: {
    shouldInspectCount: 0,
    actualInspectCount: 0,
    completionRate: 0,
    centerInspectCount: 0,
    totalDamageCount: 0,
    unprocessedCount: 0,
    disposingCount: 0,
    processedCount: 0,
    trendData: [],
    regionData: [],
    damageTypeData: [],
    bridgeRanking: []
  },
  
  // 筛选条件
  filters: {
    inspectionType: 'bridge', // bridge | tunnel
    bridgeName: '',
    inspectionUnit: '',
    inspectionMonth: '',
    diseaseStatus: '',
    reportAttribute: '',
    diseaseType: '',
    timeRange: 'month'
  },
  
  // 下拉选项数据
  selectOptions: {
    bridgeOptions: [],
    inspectionUnitOptions: [],
    reportAttributeOptions: [],
    diseasePartOptions: [],
    diseaseTypeOptions: [],
    diseaseStatusOptions: []
  }
}

const mutations = {
  // 设置巡检记录列表
  SET_INSPECTION_RECORDS(state, { list, total, pageNum, pageSize }) {
    state.inspectionRecords.list = list
    state.inspectionRecords.total = total
    state.inspectionRecords.pageNum = pageNum
    state.inspectionRecords.pageSize = pageSize
  },
  
  // 设置巡检记录加载状态
  SET_INSPECTION_RECORDS_LOADING(state, loading) {
    state.inspectionRecords.loading = loading
  },
  
  // 设置当前巡检日志
  SET_CURRENT_INSPECTION_LOG(state, logData) {
    state.currentInspectionLog = { ...state.currentInspectionLog, ...logData }
  },
  
  // 设置巡检日志加载状态
  SET_INSPECTION_LOG_LOADING(state, loading) {
    state.currentInspectionLog.loading = loading
  },
  
  // 设置当前巡检详情
  SET_CURRENT_INSPECTION_DETAIL(state, detail) {
    state.currentInspectionDetail = detail
  },
  
  // 设置病害列表
  SET_DISEASE_LIST(state, { list, total, pageNum, pageSize }) {
    state.diseaseList.list = list
    state.diseaseList.total = total
    state.diseaseList.pageNum = pageNum
    state.diseaseList.pageSize = pageSize
  },
  
  // 设置病害列表加载状态
  SET_DISEASE_LIST_LOADING(state, loading) {
    state.diseaseList.loading = loading
  },
  
  // 设置当前病害详情
  SET_CURRENT_DISEASE_DETAIL(state, detail) {
    state.currentDiseaseDetail = detail
  },
  
  // 设置统计数据
  SET_STATISTICS_DATA(state, data) {
    state.statisticsData = { ...state.statisticsData, ...data }
  },
  
  // 设置筛选条件
  SET_FILTERS(state, filters) {
    state.filters = { ...state.filters, ...filters }
  },
  
  // 设置下拉选项数据
  SET_SELECT_OPTIONS(state, options) {
    state.selectOptions = { ...state.selectOptions, ...options }
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.currentInspectionDetail = null
    state.currentDiseaseDetail = null
    state.currentInspectionLog = {
      bridgeId: null,
      bridgeName: '',
      month: '',
      logs: [],
      loading: false
    }
  }
}

const actions = {
  // 获取巡检记录列表
  async fetchInspectionRecords({ commit, state }, params = {}) {
    commit('SET_INSPECTION_RECORDS_LOADING', true)
    
    try {
      const query = {
        type: state.filters.inspectionType,
        bridgeName: state.filters.bridgeName,
        inspectionUnit: state.filters.inspectionUnit,
        inspectionMonth: state.filters.inspectionMonth,
        pageNum: state.inspectionRecords.pageNum,
        pageSize: state.inspectionRecords.pageSize,
        ...params
      }
      
      const response = await getInspectionRecords(query)
      commit('SET_INSPECTION_RECORDS', response.data)
    } catch (error) {
      console.error('获取巡检记录失败:', error)
      // 使用默认数据
      const defaultData = getDefaultData('inspectionRecords')
      commit('SET_INSPECTION_RECORDS', defaultData)
    } finally {
      commit('SET_INSPECTION_RECORDS_LOADING', false)
    }
  },

  // 获取最近巡检记录（用于统计页展示，默认取前N条）
  async fetchRecentInspectionRecords({ state }, { limit = 8 } = {}) {
    try {
      const query = {
        type: state.filters.inspectionType,
        pageNum: 1,
        pageSize: limit
      }
      await getInspectionRecords(query)
      // 统计页本地展示最近记录，当前不写入store
      return true
    } catch (error) {
      console.warn('获取最近巡检记录失败，使用本地默认数据')
      return false
    }
  },
  
  // 获取巡检日志
  async fetchInspectionLogs({ commit }, { bridgeId, month }) {
    commit('SET_INSPECTION_LOG_LOADING', true)
    
    try {
      const response = await getInspectionLogs(bridgeId, { month })
      commit('SET_CURRENT_INSPECTION_LOG', response.data)
    } catch (error) {
      console.error('获取巡检日志失败:', error)
      // 使用默认数据
      const defaultData = getDefaultData('inspectionCalendar')
      commit('SET_CURRENT_INSPECTION_LOG', defaultData)
    } finally {
      commit('SET_INSPECTION_LOG_LOADING', false)
    }
  },
  
  // 获取巡检详情
  async fetchInspectionDetail({ commit }, logId) {
    try {
      const response = await getInspectionDetail(logId)
      commit('SET_CURRENT_INSPECTION_DETAIL', response.data)
      return response.data
    } catch (error) {
      console.error('获取巡检详情失败:', error)
      return null
    }
  },
  
  // 获取病害列表
  async fetchDiseaseList({ commit, state }, params = {}) {
    commit('SET_DISEASE_LIST_LOADING', true)
    
    try {
      const query = {
        type: state.filters.inspectionType,
        status: state.filters.diseaseStatus,
        reportAttribute: state.filters.reportAttribute,
        diseaseType: state.filters.diseaseType,
        pageNum: state.diseaseList.pageNum,
        pageSize: state.diseaseList.pageSize,
        ...params
      }
      
      const response = await getDiseaseList(query)
      commit('SET_DISEASE_LIST', response.data)
    } catch (error) {
      console.error('获取病害列表失败:', error)
      // 使用默认数据
      const defaultData = getDefaultData('diseaseList')
      commit('SET_DISEASE_LIST', defaultData)
    } finally {
      commit('SET_DISEASE_LIST_LOADING', false)
    }
  },
  
  // 获取病害详情
  async fetchDiseaseDetail({ commit }, diseaseId) {
    try {
      const response = await getDiseaseDetail(diseaseId)
      commit('SET_CURRENT_DISEASE_DETAIL', response.data)
      return response.data
    } catch (error) {
      console.error('获取病害详情失败:', error)
      return null
    }
  },
  
  // 获取统计数据
  async fetchStatisticsData({ commit, state }, params = {}) {
    try {
      const query = {
        type: state.filters.inspectionType,
        timeRange: state.filters.timeRange,
        ...params
      }
      
      const response = await getInspectionStatistics(query)
      commit('SET_STATISTICS_DATA', response.data)
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 使用默认数据
      const defaultData = getDefaultData('statisticsData')
      commit('SET_STATISTICS_DATA', defaultData)
    }
  },
  
  // 更新筛选条件
  updateFilters({ commit }, filters) {
    commit('SET_FILTERS', filters)
  },
  
  // 更新分页信息
  updatePagination({ commit, state }, { type, pageNum, pageSize }) {
    if (type === 'inspection') {
      commit('SET_INSPECTION_RECORDS', {
        list: state.inspectionRecords.list,
        total: state.inspectionRecords.total,
        pageNum,
        pageSize
      })
    } else if (type === 'disease') {
      commit('SET_DISEASE_LIST', {
        list: state.diseaseList.list,
        total: state.diseaseList.total,
        pageNum,
        pageSize
      })
    }
  },
  
  // 初始化下拉选项数据
  initSelectOptions({ commit }) {
    const defaultOptions = getDefaultData('selectOptions')
    commit('SET_SELECT_OPTIONS', defaultOptions)
  },
  
  // 重置模块状态
  resetInspectionState({ commit }) {
    commit('RESET_STATE')
  }
}

const getters = {
  // 统计数据
  statisticsData: state => state.statisticsData,
  // 当前巡检记录列表
  inspectionRecordsList: state => state.inspectionRecords.list,
  
  // 巡检记录分页信息
  inspectionPagination: state => ({
    total: state.inspectionRecords.total,
    pageNum: state.inspectionRecords.pageNum,
    pageSize: state.inspectionRecords.pageSize
  }),
  
  // 当前病害列表
  diseaseList: state => state.diseaseList.list,
  
  // 病害分页信息
  diseasePagination: state => ({
    total: state.diseaseList.total,
    pageNum: state.diseaseList.pageNum,
    pageSize: state.diseaseList.pageSize
  }),
  
  // 当前筛选条件
  currentFilters: state => state.filters,
  
  // 下拉选项数据
  selectOptions: state => state.selectOptions,
  
  // 加载状态
  loadingStates: state => ({
    inspectionRecords: state.inspectionRecords.loading,
    diseaseList: state.diseaseList.loading,
    inspectionLog: state.currentInspectionLog.loading
  })
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
