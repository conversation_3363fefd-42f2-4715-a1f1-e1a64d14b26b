// 巡检模块弹出窗口公共样式
// 此文件包含了完整的弹出窗口样式，与巡检日志弹出窗样式保持一致
// 可以通过 @import 引入并使用 .common-dialog 类名应用样式

@import '../inspection-theme.scss';

// 基础弹出窗口样式 - 基于巡检日志弹出窗的样式
.common-dialog {
  // 确保弹框本身在最顶层
  :deep(.el-dialog__wrapper) {
    z-index: 99999 !important;
  }

  // 确保弹框遮罩层在最顶层，并设置深色遮罩
  :deep(.v-modal) {
    z-index: 99998 !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  // 弹框主体样式
  :deep(.el-dialog) {
    border-radius: 0px; // 移除圆角，与巡检日志保持一致
    overflow: hidden;
    z-index: 99999 !important;
    background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
    border: 1px solid rgba(79, 70, 229, 0.3) !important;
    box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
    color: #f1f5f9 !important;

    // 弹框头部样式
    .el-dialog__header {
      padding: 20px 24px;
      background: #091A4B !important;
      border-bottom: 1px solid #ffffff;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .el-dialog__headerbtn {
        top: 20px;
        right: 24px;

        .el-dialog__close {
          color: #ffffff;
          font-size: 20px;

          &:hover {
            color: #e2e8f0;
          }
        }
      }
    }

    // 弹框内容样式
    .el-dialog__body {
      padding: 0;
      background: #091A4B !important;
      position: relative;
    }

    // 弹框底部样式
    .el-dialog__footer {
      padding: 12px 24px;
      background: #091A4B !important;
      border-top: none;
      text-align: center;

      .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 600;
        background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%);
        border: 1px solid #1e3a8a;
        color: #ffffff;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);

        &:hover {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
          border-color: #3b82f6;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
        }
      }
    }
  }

  // 自定义关闭按钮样式
  .custom-close-btn {
    position: absolute !important;
    top: 20px !important;
    right: 24px !important;
    width: 36px !important;
    height: 36px !important;
    cursor: pointer !important;
    z-index: 100 !important;
    transition: all 0.3s ease !important;

    svg {
      width: 100% !important;
      height: 100% !important;
      transition: all 0.3s ease !important;
    }

    &:hover {
      transform: scale(1.1) !important;
      opacity: 0.8 !important;
    }

    &:active {
      transform: scale(0.95) !important;
    }
  }

  // 弹框内容区域样式
  .dialog-content {
    min-height: 400px;
    padding: 20px;
    background: #091A4B;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  // 弹框底部按钮样式
  .dialog-footer {
    text-align: center;
    padding: 12px 24px !important;

    .footer-close-btn {
      display: inline-block;
      padding: 10px 32px;
      background: #1e40af;
      color: white;
      border: 1px solid white;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      min-width: 80px;

      &:hover {
        background: #2563eb;
        border-color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
      }

      &:active {
        background: #1d4ed8;
        transform: translateY(0);
      }
    }
  }

  // 加载状态美化 - 与巡检日志弹出框保持一致
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;

    .el-loading-spinner {
      .path {
        stroke: #667eea;
      }

      .el-loading-text {
        color: #667eea;
        font-weight: 500;
      }
    }
  }
}

// 带外部关闭按钮的弹出窗口样式
.common-dialog-with-external-close {
  @extend .common-dialog;

  // 外部关闭按钮样式
  .custom-close-btn {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent; // 移除红色背景，改为透明
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 100001;
    box-shadow: none; // 移除红色阴影

    &:hover {
      background: rgba(255, 255, 255, 0.1); // 白色半透明背景
      transform: scale(1.1);
      box-shadow: none; // 移除红色阴影
    }

    &:active {
      transform: scale(0.95);
    }

    svg {
      width: 22px;
      height: 22px;
    }
  }
}

// 宽屏弹出窗口样式
.common-dialog-wide {
  @extend .common-dialog;

  :deep(.el-dialog) {
    width: 70% !important;
    min-width: 800px;
  }
}

// 紧凑弹出窗口样式
.common-dialog-compact {
  @extend .common-dialog;

  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 16px 20px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      .dialog-content {
        padding: 16px;
        min-height: 300px;
      }
    }

    .el-dialog__footer {
      padding: 10px 20px;

      .el-button {
        padding: 8px 24px;
        font-size: 13px;
      }
    }
  }
}

// 全屏弹出窗口样式
.common-dialog-fullscreen {
  @extend .common-dialog;

  :deep(.el-dialog) {
    width: 95% !important;
    height: 90vh !important;
    max-height: 90vh;
    margin-top: 5vh !important;

    .el-dialog__body {
      height: calc(90vh - 140px);
      overflow-y: auto;
    }
  }
}

// 可自定义的弹出窗口样式变量
.common-dialog-customizable {
  --dialog-bg: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%);
  --dialog-border: 1px solid rgba(79, 70, 229, 0.3);
  --dialog-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --dialog-color: #f1f5f9;
  
  --dialog-header-bg: #091A4B;
  --dialog-header-color: #ffffff;
  --dialog-header-padding: 20px 24px;
  --dialog-header-border: 1px solid #ffffff;
  
  --dialog-body-bg: #091A4B;
  --dialog-body-padding: 20px;
  
  --dialog-footer-bg: #091A4B;
  --dialog-footer-padding: 12px 24px;

  :deep(.el-dialog) {
    background: var(--dialog-bg) !important;
    border: var(--dialog-border) !important;
    box-shadow: var(--dialog-shadow) !important;
    color: var(--dialog-color) !important;

    .el-dialog__header {
      background: var(--dialog-header-bg) !important;
      border-bottom: var(--dialog-header-border);
      padding: var(--dialog-header-padding);

      .el-dialog__title {
        color: var(--dialog-header-color);
      }
    }

    .el-dialog__body {
      background: var(--dialog-body-bg) !important;

      .dialog-content {
        background: var(--dialog-body-bg);
        padding: var(--dialog-body-padding);
      }
    }

    .el-dialog__footer {
      background: var(--dialog-footer-bg) !important;
      padding: var(--dialog-footer-padding);
    }
  }
}

// 带表格的弹出窗口样式
.common-dialog-with-table {
  @extend .common-dialog;

  .dialog-content {
    padding: 0;

    // 应用表格样式
    .common-table {
      :deep(.el-table) {
        background: transparent !important;
        border: none !important;
        border-radius: 0 !important;
        overflow: visible !important;
        box-shadow: none !important;

        // 表头样式
        .el-table__header-wrapper {
          .el-table__header {
            background: transparent;

            th {
              background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
              color: #FFF;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 16px;
              font-weight: 400;
              line-height: 140%;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              border-left: 1px solid rgba(255, 255, 255, 0.1);
              border-right: 1px solid rgba(255, 255, 255, 0.1);
              padding: 0;

              .cell {
                display: flex;
                height: 44px;
                padding: 14px 24px;
                justify-content: center;
                align-items: center;
                color: #FFF;
              }
            }
          }
        }

        // 表格体样式
        .el-table__body-wrapper {
          .el-table__body {
            tr {
              &:hover {
                background: rgba(255, 255, 255, 0.05) !important;
              }

              &:last-child {
                td {
                  border-bottom: none !important;
                }
              }

              td {
                background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                border-left: 1px solid rgba(255, 255, 255, 0.1);
                border-right: 1px solid rgba(255, 255, 255, 0.1);
                padding: 4px 12px;
                text-align: center;
                height: 32px;

                .cell {
                  color: #FFF;
                  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 120%;
                  justify-content: center;
                  align-items: center;
                  text-align: center;
                }
              }
            }
          }
        }

        // 操作按钮样式
        .el-button--text {
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
          font-size: 14px !important;
          font-weight: 400 !important;
          line-height: 120% !important;
          padding: 2px 6px !important;
          color: #FFF !important;

          &:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            color: #E0E0E0 !important;
          }
        }
      }
    }
  }
}

// ===========================
// 🚀 强制样式应用工具类 - 解决Element UI样式覆盖问题
// ===========================

// 强制深色主题样式 - 使用最高优先级
.force-dark-theme {
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  color: #f1f5f9 !important;
  border-radius: 0px !important; // 移除圆角，与巡检日志保持一致
}

// 兼容巡检日志的强制样式类
.modern-dialog.dark-theme,
.force-high-zindex {
  @extend .force-dark-theme;
  z-index: 99999 !important;
}

// 响应式设计
@media (max-width: 1200px) {
  .common-dialog,
  .common-dialog-with-external-close,
  .common-dialog-wide {
    :deep(.el-dialog) {
      width: 75% !important;
    }
  }

  .common-dialog-with-external-close {
    .custom-close-btn {
      top: -12px;
      right: -12px;
    }
  }
}

// 现代深色主题弹框样式 - 基于巡检日志弹出框抽取的公共样式
.modern-dialog {
  :deep(.el-dialog) {
    border-radius: 0px !important; // 移除圆角，与巡检日志保持一致
    overflow: hidden !important;
    background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
    border: 1px solid rgba(79, 70, 229, 0.3) !important;
    box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
    color: #f1f5f9 !important;

    .el-dialog__header {
      padding: 20px 24px;
      background: #091A4B !important;
      border-bottom: 1px solid #ffffff;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 24px;
        
        .el-dialog__close {
          color: #ffffff;
          font-size: 20px;
          
          &:hover {
            color: #e2e8f0;
          }
        }
      }
    }
    
    .el-dialog__body {
      padding: 0;
      background: #091A4B !important;
      position: relative;
    }
    
    .el-dialog__footer {
      padding: 12px 24px;
      background: #091A4B !important;
      border-top: none;
      text-align: center;
      
      .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 600;
        background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%);
        border: 1px solid #1e3a8a;
        color: #ffffff;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
        
        &:hover {
          background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
          border-color: #3b82f6;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
        }
        
        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
        }
      }
    }
  }
}

// 深色主题增强样式
.dark-theme {
  @extend .modern-dialog;
  
  // 深色主题的表格样式
  .el-table {
    background-color: transparent !important;
    color: #e2e8f0 !important;
    
    th, td {
      background-color: rgba(30, 58, 138, 0.2) !important;
      color: #e2e8f0 !important;
      border-color: rgba(30, 58, 138, 0.3) !important;
    }
    
    th {
      background-color: rgba(30, 58, 138, 0.4) !important;
      color: #f1f5f9 !important;
      font-weight: 600 !important;
    }
  }
  
  // 深色主题的输入框样式
  .el-input__inner,
  .el-select .el-input__inner,
  .el-date-editor .el-input__inner {
    background-color: rgba(30, 58, 138, 0.3) !important;
    border-color: rgba(30, 58, 138, 0.4) !important;
    color: #e2e8f0 !important;
  }
  
  // 深色主题的标签样式
  .el-form-item__label {
    color: #cbd5e1 !important;
  }
}

// 强制高z-index防止被遮挡
.force-high-zindex {
  z-index: 99999 !important;
  
  :deep(.el-dialog__wrapper) {
    z-index: 99999 !important;
  }
  
  :deep(.el-dialog) {
    z-index: 99999 !important;
  }
}

// 巡检弹框基础样式 - 包含所有必要的样式特性
.inspection-dialog-base {
  @extend .modern-dialog;
  @extend .dark-theme;
  @extend .force-high-zindex;

  // 弹框内容区域 - 与巡检日志保持一致的最小高度
  .dialog-content {
    min-height: 400px; // 与巡检日志弹出框保持一致
    padding: 20px;
    background: #091A4B;
    color: #e2e8f0;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  // 自定义关闭按钮样式 - 与巡检日志弹出框完全一致
  .custom-close-btn {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent; // 移除红色背景，改为透明
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 100001;
    box-shadow: none; // 移除红色阴影
    
    &:hover {
      background: rgba(255, 255, 255, 0.1); // 白色半透明背景
      transform: scale(1.1);
      box-shadow: none; // 移除红色阴影
    }
    
    &:active {
      transform: scale(0.95);
    }
    
    svg {
      width: 22px;
      height: 22px;
    }
  }

  // 底部关闭按钮样式
  .dialog-footer {
    text-align: center;
    padding: 12px 24px !important;
    
    .footer-close-btn {
      display: inline-block;
      padding: 10px 32px;
      background: #1e40af;
      color: white;
      border: 1px solid white;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      min-width: 80px;
      
      &:hover {
        background: #2563eb;
        border-color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
      }
      
      &:active {
        background: #1d4ed8;
        transform: translateY(0);
      }
    }
  }
}

// 确保遮罩层样式统一
.modern-dialog,
.dark-theme,
.inspection-dialog-base {
  :deep(.v-modal) {
    z-index: 99998 !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
  }
}

@media (max-width: 768px) {
  .common-dialog,
  .common-dialog-with-external-close,
  .common-dialog-wide,
  .common-dialog-compact,
  .modern-dialog,
  .dark-theme,
  .inspection-dialog-base {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 0 auto;
      top: 3vh !important;

      .el-dialog__header {
        padding: 16px 20px;

        .el-dialog__title {
          font-size: 16px;
        }

        .el-dialog__headerbtn {
          top: 16px;
          right: 20px;
        }
      }
    }
  }

  .common-dialog-with-external-close {
    .custom-close-btn {
      top: -10px;
      right: -10px;
      width: 32px;
      height: 32px;

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }

  // 新公共样式的移动端适配 - 与巡检日志弹出框保持一致
  .modern-dialog,
  .dark-theme,
  .inspection-dialog-base {
    .custom-close-btn {
      top: -10px;
      right: -10px;
      width: 32px;
      height: 32px;
      
      svg {
        width: 18px;
        height: 18px;
      }
    }

    .dialog-content {
      padding: 16px;
      margin: 4px;
    }
  }

  .dialog-content {
    padding: 16px;
    margin: 4px;
  }

  .common-dialog-with-table {
    .dialog-content {
      padding: 16px;
      margin: 4px;

      .common-table {
        :deep(.el-table) {
          th .cell {
            font-size: 14px !important;
          }

          td .cell {
            font-size: 12px !important;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .common-dialog,
  .common-dialog-with-external-close,
  .common-dialog-wide,
  .common-dialog-compact {
    :deep(.el-dialog) {
      width: 95% !important;
      top: 2vh !important;

      .el-dialog__header {
        padding: 12px 16px;

        .el-dialog__title {
          font-size: 14px;
        }
      }

      .el-dialog__footer {
        padding: 8px 16px;

        .el-button {
          padding: 8px 20px;
          font-size: 13px;
        }
      }
    }
  }

  .dialog-content {
    padding: 12px;
    min-height: 300px;
  }

  .dialog-footer {
    .footer-close-btn {
      padding: 8px 24px;
      font-size: 13px;
    }
  }
}

// 样式保护混入 - 防止Element UI覆盖自定义样式
@mixin dialog-style-protection {
  // 在弹框显示时调用此混入来保护样式
  // 可以在组件的 mounted/updated 钩子中使用
}

// 强制应用深色主题的样式类
.force-dark-theme {
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  color: #f1f5f9 !important;
  border-radius: 0px !important; // 移除圆角，与巡检日志保持一致
}

// 针对不同可能的Element UI类名进行强制覆盖
.el-dialog.force-dark-theme,
.el-dialog__wrapper .force-dark-theme,
.el-overlay .force-dark-theme {
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  color: #f1f5f9 !important;
}

// 强制高z-index防止被遮挡
.force-high-zindex {
  z-index: 99999 !important;

  &.el-dialog__wrapper {
    z-index: 99999 !important;
  }

  .el-dialog {
    z-index: 99999 !important;
  }
}

// 🚀 关键修复：确保弹框从显示开始就是深色主题，避免白色闪烁
:deep(.el-dialog__wrapper .el-dialog.inspection-log-dialog),
:deep(.el-dialog__wrapper .el-dialog.modern-dialog),
:deep(.el-dialog__wrapper .el-dialog.dark-theme),
:deep(.el-dialog__wrapper .el-dialog.inspection-dialog-base) {
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  color: #f1f5f9 !important;
}

// 🌙 深色主题样式 - 与整体框架风格协调（完全同步巡检日志弹出框）
.inspection-log-dialog,
.modern-dialog.dark-theme,
.inspection-dialog-base {
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  background-color: #091A4B !important;
  color: #f1f5f9 !important;
  border-radius: 0px !important; // 移除圆角，与巡检日志保持一致
}

// 针对不同可能的Element UI类名进行强制覆盖（与InspectionLogDialog.vue完全一致）
.el-dialog.inspection-log-dialog,
.el-dialog__wrapper .inspection-log-dialog,
.el-overlay .inspection-log-dialog,
.el-dialog.modern-dialog,
.el-dialog__wrapper .modern-dialog,
.el-overlay .modern-dialog,
.el-dialog.dark-theme,
.el-dialog__wrapper .dark-theme,
.el-overlay .dark-theme,
.el-dialog.inspection-dialog-base,
.el-dialog__wrapper .inspection-dialog-base,
.el-overlay .inspection-dialog-base {
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  color: #f1f5f9 !important;
}

// 🌙 弹框内容区域深色主题样式（完全同步巡检日志弹出框）
.inspection-log-dialog,
.modern-dialog.dark-theme,
.inspection-dialog-base {
  // 🎨 Header区域深色样式
  .el-dialog__header {
    background: #091A4B !important;
    color: #f1f5f9 !important;
    border-bottom: 1px solid #ffffff !important;
    padding: 20px 24px !important;
    
    .el-dialog__title {
      color: #f1f5f9 !important;
      font-weight: 600 !important;
      font-size: 18px !important;
    }
  }
  
  // 🎨 Body区域深色样式
  .el-dialog__body {
    background: #091A4B !important;
    color: #e2e8f0 !important;
    padding: 0 !important;
    
    // 表格深色主题
    .el-table {
      background-color: transparent !important;
      color: #e2e8f0 !important;
      
      th, td {
        background-color: rgba(51, 65, 85, 0.3) !important;
        color: #e2e8f0 !important;
        border-color: rgba(79, 70, 229, 0.2) !important;
      }
      
      th {
        background-color: rgba(51, 65, 85, 0.6) !important;
        color: #f1f5f9 !important;
        font-weight: 600 !important;
      }
      
      tr:hover td {
        background-color: rgba(79, 70, 229, 0.1) !important;
      }
    }
    
    // 输入框和选择器深色主题
    .el-input__inner,
    .el-select .el-input__inner,
    .el-date-editor .el-input__inner {
      background-color: rgba(51, 65, 85, 0.5) !important;
      border-color: rgba(79, 70, 229, 0.3) !important;
      color: #e2e8f0 !important;
      
      &:focus {
        border-color: rgba(79, 70, 229, 0.6) !important;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
      }
    }
    
    // 标签文字深色主题
    .el-form-item__label {
      color: #cbd5e1 !important;
    }
  }
  
  // 🎨 Footer区域深色样式
  .el-dialog__footer {
    background: #091A4B !important;
    border-top: none !important;
    padding: 12px 24px !important;
    
    .el-button {
      padding: 12px 32px;
      border-radius: 8px;
      font-weight: 600;
      background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%);
      border: 1px solid #1e3a8a;
      color: #ffffff;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
      
      &:hover {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        border-color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
      }
    }
  }
}

// 🎨 自定义关闭按钮样式（内部定位版本，在header内）
.modern-dialog,
.dark-theme,
.inspection-dialog-base {
  .custom-close-btn {
    position: absolute !important;
    top: 20px !important;
    right: 24px !important;
    width: 36px !important;
    height: 36px !important;
    cursor: pointer !important;
    z-index: 100 !important;
    transition: all 0.3s ease !important;
    
    svg {
      width: 100% !important;
      height: 100% !important;
      transition: all 0.3s ease !important;
    }
    
    &:hover {
      transform: scale(1.1) !important;
      opacity: 0.8 !important;
    }
    
    &:active {
      transform: scale(0.95) !important;
    }
  }
}

// ===========================
// 🚀 JavaScript可调用的强制样式应用工具函数
// ===========================

/* 
在组件的JavaScript中使用以下函数来强制应用深色主题：

// 1. 导入样式强制应用函数（在组件中添加这个方法）
methods: {
  forceApplyDialogStyles() {
    const dialog = document.querySelector('.inspection-dialog-base') || 
                   document.querySelector('.inspection-report-dialog') ||
                   document.querySelector('.inspection-log-dialog')
    
    if (dialog) {
      // 应用强制样式
      const forceStyles = {
        'background': 'linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%)',
        'border': '1px solid rgba(79, 70, 229, 0.3)',
        'box-shadow': '0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        'color': '#f1f5f9',
        'border-radius': '8px'
      }
      
      Object.keys(forceStyles).forEach(property => {
        dialog.style.setProperty(property, forceStyles[property], 'important')
      })
      
      // 应用深色主题到各个区域
      this.applyDarkThemeToAllAreas()
    }
  },
  
  applyDarkThemeToAllAreas() {
    // Header区域
    const header = document.querySelector('.inspection-dialog-base .el-dialog__header, .inspection-report-dialog .el-dialog__header, .inspection-log-dialog .el-dialog__header')
    if (header) {
      const headerStyles = {
        'background': '#091A4B',
        'color': '#f1f5f9',
        'border-bottom': '1px solid rgba(79, 70, 229, 0.3)',
        'padding': '20px 24px'
      }
      Object.keys(headerStyles).forEach(property => {
        header.style.setProperty(property, headerStyles[property], 'important')
      })
      
      const title = header.querySelector('.el-dialog__title')
      if (title) {
        title.style.setProperty('color', '#f1f5f9', 'important')
        title.style.setProperty('font-weight', '600', 'important')
      }
    }
    
    // Body区域
    const body = document.querySelector('.inspection-dialog-base .el-dialog__body, .inspection-report-dialog .el-dialog__body, .inspection-log-dialog .el-dialog__body')
    if (body) {
      const bodyStyles = {
        'background': '#091A4B',
        'color': '#e2e8f0',
        'padding': '24px'
      }
      Object.keys(bodyStyles).forEach(property => {
        body.style.setProperty(property, bodyStyles[property], 'important')
      })
    }
    
    // Footer区域  
    const footer = document.querySelector('.inspection-dialog-base .el-dialog__footer, .inspection-report-dialog .el-dialog__footer, .inspection-log-dialog .el-dialog__footer')
    if (footer) {
      const footerStyles = {
        'background': '#091A4B',
        'border-top': '1px solid rgba(79, 70, 229, 0.3)',
        'padding': '16px 24px'
      }
      Object.keys(footerStyles).forEach(property => {
        footer.style.setProperty(property, footerStyles[property], 'important')
      })
    }
  }
}

// 2. 在组件的watch中调用：
watch: {
  visible(newVal) {
    if (newVal) {
      this.$nextTick(() => {
        this.forceApplyDialogStyles()
      })
    }
  }
}
*/

// 确保弹框本身在最顶层
:deep(.el-dialog__wrapper) {
  z-index: 99999 !important;
}

// 确保弹框遮罩层在最顶层，并设置深色遮罩
:deep(.v-modal) {
  z-index: 99998 !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
}
