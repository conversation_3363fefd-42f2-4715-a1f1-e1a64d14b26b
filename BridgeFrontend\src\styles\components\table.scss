// 巡检模块表格公共样式
// 此文件包含了完整的表格样式，与巡检记录页面表格样式保持一致
// 可以通过 @import 引入并使用 .common-table 类名应用样式

@import '../inspection-theme.scss';

// 表格公共样式类 - 基于巡检记录页面的表格样式
.common-table {
  flex: 1; // 占用剩余空间
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex收缩
  
  :deep(.el-table) {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    overflow: visible !important;
    box-shadow: none !important;
    flex: 1;

    &::before,
    &::after {
      display: none !important;
    }

    // 移除表格边框补丁
    .el-table__border-left-patch,
    .el-table__border-right-patch,
    .el-table__border-bottom-patch {
      display: none !important;
    }

    // 移除表格容器的边框
    .el-table__body-wrapper,
    .el-table__footer-wrapper,
    .el-table__append-wrapper {
      border: none !important;
    }

    // 表头样式 - 与主题完全一致
    .el-table__header-wrapper {
      .el-table__header {
        background: transparent;

        th {
          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
          color: #FFF;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          border-left: 1px solid rgba(255, 255, 255, 0.1);
          border-right: 1px solid rgba(255, 255, 255, 0.1);
          padding: 0;
          white-space: nowrap;

          .cell {
            display: flex;
            height: 44px;
            padding: 14px 24px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            align-self: stretch;
            color: #FFF;
          }
        }
      }
    }

    // 表格体样式 - 与主题完全一致
    .el-table__body-wrapper {
      border: none !important;
      
      .el-table__body {
        tr {
          background: transparent;

          &:hover {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 4px 12px;
            text-align: center;
            height: 32px;

            .cell {
              color: #FFF;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 120%;
              justify-content: center;
              align-items: center;
              text-align: center;
            }
          }
        }
      }
    }

    // 操作按钮通用样式 - 与主题完全一致
    .el-button--text,
    .action-buttons .el-button--text,
    .action-buttons .el-button {
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      font-size: 14px !important;
      font-weight: 400 !important;
      line-height: 120% !important;
      padding: 2px 6px !important;
      margin: 0 !important;
      border: none !important;
      background: none !important;
      height: 20px !important;
      min-height: 20px !important;
      max-height: 20px !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      vertical-align: middle !important;
      cursor: pointer !important;
      border-radius: 4px !important;
      transition: all 0.2s ease !important;
      flex-shrink: 0 !important;
      box-sizing: border-box !important;
      color: #FFF !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #E0E0E0 !important;
        cursor: pointer !important;
      }

      &:focus {
        background: none !important;
        color: #FFF !important;
      }

      // 为基础按钮也添加不同类型的样式支持
      &.el-button--text {
        color: #FFF !important;

        &:hover {
          color: #E0E0E0 !important;
          background: rgba(255, 255, 255, 0.1) !important;
        }

        &:focus {
          color: #FFF !important;
          background: none !important;
        }
      }

      &.el-button--primary {
        color: #FFF !important;
        background-color: #409EFF !important;
        border-color: #409EFF !important;

        &:hover {
          background-color: #66b1ff !important;
          border-color: #66b1ff !important;
        }
      }

      &.el-button--warning {
        color: #FFF !important;
        background-color: #e6a23c !important;
        border-color: #e6a23c !important;

        &:hover {
          background-color: #ebb563 !important;
          border-color: #ebb563 !important;
        }
      }

      &.el-button--success {
        color: #FFF !important;
        background-color: #67c23a !important;
        border-color: #67c23a !important;

        &:hover {
          background-color: #85ce61 !important;
          border-color: #85ce61 !important;
        }
      }
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;

    &.status-0 {
      background: rgba(16, 185, 129, 0.2);
      color: var(--inspection-success);
      border: 1px solid rgba(16, 185, 129, 0.3);
    }

    &.status-1 {
      background: rgba(245, 158, 11, 0.2);
      color: var(--inspection-warning);
      border: 1px solid rgba(245, 158, 11, 0.3);
    }

    &.status-2,
    &.status-3,
    &.status-high {
      background: rgba(239, 68, 68, 0.2);
      color: var(--inspection-danger);
      border: 1px solid rgba(239, 68, 68, 0.3);
    }
  }

  // 本月缺巡次数样式
  .missing-patrol-count {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    width: 26px;
    height: 22px;
    padding: 0;
    margin: 0 auto;

    // 0次 - 绿色背景
    &.count-0 {
      background: rgba(179, 255, 200, 0.82);
      color: #059669;
    }

    // 1次 - 黄色背景
    &.count-1 {
      background: rgba(248, 252, 146, 0.80);
      color: #D97706;
    }

    // 2次以上 - 橙红色背景
    &.count-2-plus {
      background: rgba(255, 203, 196, 0.80);
      color: #D97706;
    }
  }
}

// 扩展表格样式 - 支持固定列操作
.common-table-with-fixed {
  @extend .common-table;

  :deep(.el-table) {
    // 固定列（操作列）样式处理
    .el-table__fixed-right {
      background: transparent !important;
      height: 100% !important;
      max-height: none !important;
      min-height: auto !important;

      // 固定列表头样式
      .el-table__header {
        background: transparent;

        th {
          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
          color: #FFF !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
          vertical-align: middle !important;
          text-align: center !important;

          .cell {
            display: flex !important;
            height: 44px !important;
            padding: 14px 16px !important;
            justify-content: center !important;
            align-items: center !important;
            gap: 10px;
            align-self: stretch;
            color: #FFF !important;
            text-align: center !important;
            vertical-align: middle !important;
            line-height: 1 !important;
          }
        }
      }

      // 固定列单元格样式
      .el-table__body {
        tr {
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 4px 12px;
            text-align: center;
            vertical-align: middle !important;
            height: 32px !important;

            .cell {
              color: #FFF !important;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 120%;
              display: flex !important;
              justify-content: center !important;
              align-items: center !important;
              text-align: center !important;
              flex-direction: row !important;
              flex-wrap: nowrap !important;
              gap: 8px !important;
              width: 100% !important;
              height: 100% !important;
              min-height: 32px !important;
              max-height: 32px !important;
              padding: 0 !important;
              margin: 0 !important;
              line-height: 32px !important;

              // 处理ActionButtons组件
              .action-buttons {
                display: flex !important;
                flex-direction: row !important;
                justify-content: center !important;
                align-items: center !important;
                gap: 6px !important;
                flex-wrap: nowrap !important;
                width: 100% !important;
                height: 32px !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 32px !important;

                .el-button {
                  flex-shrink: 0 !important;
                  margin: 0 !important;
                  
                  // 统一所有按钮的基础样式
                  font-size: 14px !important;
                  line-height: 120% !important;
                  height: 20px !important;
                  min-height: 20px !important;
                  max-height: 20px !important;
                  padding: 2px 6px !important;
                  border-radius: 4px !important;
                  box-sizing: border-box !important;
                  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                  
                  // 默认text类型按钮为白色
                  &.el-button--text {
                    color: #FFF !important;

                    &:hover {
                      color: #E0E0E0 !important;
                      background: rgba(255, 255, 255, 0.1) !important;
                    }

                    &:focus {
                      color: #FFF !important;
                      background: none !important;
                    }
                  }

                  // 判定、处置、复核等功能按钮保持原有颜色
                  &.el-button--primary {
                    color: #FFF !important;
                    background-color: #409EFF !important;
                    border-color: #409EFF !important;

                    &:hover {
                      background-color: #66b1ff !important;
                      border-color: #66b1ff !important;
                    }
                  }

                  &.el-button--warning {
                    color: #FFF !important;
                    background-color: #e6a23c !important;
                    border-color: #e6a23c !important;

                    &:hover {
                      background-color: #ebb563 !important;
                      border-color: #ebb563 !important;
                    }
                  }

                  &.el-button--success {
                    color: #FFF !important;
                    background-color: #67c23a !important;
                    border-color: #67c23a !important;

                    &:hover {
                      background-color: #85ce61 !important;
                      border-color: #85ce61 !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 专用按钮样式 - 与巡检记录表格保持一致
  .log-button {
    color: #FFF !important;
    font-size: 14px !important;
    line-height: 120% !important;
    height: 20px !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;

    &:hover {
      color: #E0E0E0 !important;
      background: rgba(255, 255, 255, 0.1) !important;
    }

    &:focus {
      color: #FFF !important;
      background: none !important;
    }
  }

  .report-button {
    color: #FFF !important;
    font-size: 14px !important;
    line-height: 120% !important;
    height: 20px !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;

    &:hover {
      color: #E0E0E0 !important;
      background: rgba(255, 255, 255, 0.1) !important;
    }

    &:focus {
      color: #FFF !important;
      background: none !important;
    }
  }

  // 专门针对操作列（最后一列）的样式增强
  .el-table__fixed-right {
    .el-table__body {
      tr {
        td {
          &:last-child {
            .cell {
              justify-content: center !important;
              align-items: center !important;
              flex-direction: row !important;
              flex-wrap: nowrap !important;
              display: flex !important;
              visibility: visible !important;
            }
          }
        }
      }
    }
  }
}

// 紧凑表格样式 - 适用于对话框中的表格
.common-table-compact {
  @extend .common-table;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          .cell {
            height: 36px;
            padding: 10px 16px;
            font-size: 14px;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          td {
            height: 28px;
            padding: 2px 8px;

            .cell {
              font-size: 13px;
              line-height: 110%;
            }
          }
        }
      }
    }
  }
}

// 可自定义的表格样式变量
.common-table-customizable {
  --table-header-bg: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
  --table-header-color: #FFF;
  --table-header-height: 44px;
  --table-header-padding: 14px 24px;
  --table-header-font-size: 16px;
  
  --table-row-bg: linear-gradient(180deg, #243066 0%, #1C2A4E 100%);
  --table-row-color: #FFF;
  --table-row-height: 32px;
  --table-row-padding: 4px 12px;
  --table-row-font-size: 14px;
  
  --table-border-color: rgba(255, 255, 255, 0.1);
  --table-hover-bg: rgba(255, 255, 255, 0.05);

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background: var(--table-header-bg);
          color: var(--table-header-color);
          border-bottom: 1px solid var(--table-border-color);
          border-left: 1px solid var(--table-border-color);
          border-right: 1px solid var(--table-border-color);

          .cell {
            height: var(--table-header-height);
            padding: var(--table-header-padding);
            font-size: var(--table-header-font-size);
            color: var(--table-header-color);
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background: var(--table-hover-bg) !important;
          }

          td {
            background: var(--table-row-bg);
            color: var(--table-row-color);
            height: var(--table-row-height);
            padding: var(--table-row-padding);
            border-bottom: 1px solid var(--table-border-color);
            border-left: 1px solid var(--table-border-color);
            border-right: 1px solid var(--table-border-color);

            .cell {
              color: var(--table-row-color);
              font-size: var(--table-row-font-size);
            }
          }
        }
      }
    }
  }
}

// 响应式表格样式
@media (max-width: 768px) {
  .common-table,
  .common-table-with-fixed,
  .common-table-compact {
    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-table__header {
          th {
            .cell {
              font-size: 14px;
              padding: 10px 12px;
            }
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            td {
              padding: 2px 8px;
              
              .cell {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// 表格头部换行样式
.table-header-wrap {
  line-height: 1.4;
  word-break: keep-all;
  white-space: pre-line;
}

// 专用样式类 - 用于修复表格底部白线问题
.common-table-no-bottom-border {
  :deep(.el-table) {
    .el-table__body-wrapper {
      border-bottom: none !important;
      
      .el-table__body {
        tr:last-child {
          td {
            border-bottom: none !important;
            
            &.el-table-fixed-column--right {
              border-bottom: none !important;
            }
          }
        }
      }
    }

    .el-table__fixed-right {
      .el-table__body-wrapper {
        border-bottom: none !important;
        
        .el-table__body {
          tr:last-child {
            td {
              border-bottom: none !important;
            }
          }
        }
      }
    }
  }
}
