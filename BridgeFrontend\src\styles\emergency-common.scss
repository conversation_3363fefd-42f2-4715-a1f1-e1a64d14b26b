/**
 * 应急管理模块公共样式
 * Emergency Management Common Styles
 */

/* 应急管理容器基础样式 */
.emergency-container {
  // 基础样式已在主题文件中定义
  
  /* 固定筛选控件宽度，防止移除按钮后控件宽度发生变化 */
  .filter-section {
    .filter-content {
      .filter-items {
        .filter-select {
          flex: none !important; // 禁用flex自动扩展
          width: 248px !important; // 固定宽度
          min-width: 248px !important; // 最小宽度
          max-width: 248px !important; // 最大宽度
        }

        .filter-input{
          flex: none !important; // 禁用flex自动扩展
          width: 248px !important; // 固定宽度
          min-width: 248px !important; // 最小宽度
          max-width: 248px !important; // 最大宽度
          
        }

      }
    }
  }

}

/* 独立的主要操作按钮区域样式 */
.primary-actions-section {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;
  
  .el-button {
    margin-right: 12px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  min-width: 180px;
}

.operation-buttons .el-link {
  margin: 2px 4px;
  font-size: 12px;
  padding: 2px 4px;
}

/* 应急管理弹窗样式 */
.emergency-dialog {
  ::v-deep .el-dialog {
    background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 10px !important;
    
    .el-dialog__header {
      background: transparent !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
      
      .el-dialog__title {
        color: var(--inspection-text-primary) !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 18px !important;
        font-weight: 500 !important;
      }
      
      .el-dialog__headerbtn {
        .el-dialog__close {
          color: rgba(255, 255, 255, 0.7) !important;
          
          &:hover {
            color: #FFF !important;
          }
        }
      }
    }
    
    .el-dialog__body {
      background: transparent !important;
      color: var(--inspection-text-primary) !important;
    }
    
    .el-dialog__footer {
      background: transparent !important;
      //border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }
  }

  /* 表单样式 */
  ::v-deep .el-form {
    .el-form-item__label {
      color: var(--inspection-text-primary) !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      font-size: 14px !important;
      font-weight: 400 !important;
    }
    
    /* 统一的输入框样式 */
    .el-input,
    .el-select {
      .el-input__inner {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        color: var(--inspection-text-primary) !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.5) !important;
        }
        
        &:hover {
          border-color: rgba(255, 255, 255, 0.4) !important;
        }
        
        &:focus {
          border-color: #409EFF !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        }
      }
    }
    
    .el-select {
      .el-input__suffix {
        .el-input__suffix-inner {
          .el-select__caret {
            color: rgba(255, 255, 255, 0.7) !important;
          }
        }
      }

    }
    
    .el-textarea {
      .el-textarea__inner {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        color: var(--inspection-text-primary) !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.5) !important;
        }
        
        &:hover {
          border-color: rgba(255, 255, 255, 0.4) !important;
        }
        
        &:focus {
          border-color: #409EFF !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
        }
      }
    }

    .el-radio__inner {
      background-color: rgba(255, 255, 255, 0.1) !important;
      border-color: rgba(255, 255, 255, 0.3) !important;
    }

    .el-radio__label {
      color: rgba(255, 255, 255, 0.7) !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;       
     }
  }

  /* 文件上传样式 */
  .file-upload {
    width: 100%;
  }

  ::v-deep .upload-demo {
    .el-upload {
      border: 1px dashed rgba(255, 255, 255, 0.3) !important;
      border-radius: 8px !important;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 100%;
      text-align: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.05) !important;
      transition: all 0.3s ease !important;
      
      &:hover {
        border-color: #409EFF !important;
        background: rgba(255, 255, 255, 0.08) !important;
      }
      
      .el-button {
        background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
        border: none !important;
        color: #FFF !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        
        &:hover {
          background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%) !important;
          transform: translateY(-1px);
        }
        
        i {
          color: #FFF !important;
        }
      }
    }
    
    .el-upload-list {
      .el-upload-list__item {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 6px !important;
        color: var(--inspection-text-primary) !important;
        
        .el-upload-list__item-name {
          color: var(--inspection-text-primary) !important;
        }
        
        .el-icon-close {
          color: rgba(255, 255, 255, 0.7) !important;
          
          &:hover {
            color: #f56c6c !important;
          }
        }
      }
    }
  }

  /* 按钮样式 */
  .dialog-footer {
    text-align: right;
    
    .el-button {
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      border-radius: 6px !important;
      
      &--default {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        
        &:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          border-color: rgba(255, 255, 255, 0.4) !important;
          color: #FFF !important;
          transform: translateY(-1px);
        }
      }
      
      &--primary {
        background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
        border: none !important;
        color: #FFF !important;
        
        &:hover {
          background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%) !important;
          transform: translateY(-1px);
        }
        
        &.is-loading {
          background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
        }
      }
    }
  }

  /* 下拉选择器样式 */
  ::v-deep .el-select-dropdown {
    background: var(--inspection-bg-secondary) !important;
    border: 1px solid var(--inspection-border) !important;
    border-radius: 8px !important;
    box-shadow: var(--inspection-shadow-lg) !important;

    .el-select-dropdown__item {
      color: var(--inspection-text-secondary) !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      font-size: 14px !important;
      font-weight: 400 !important;

      &:hover {
        background: var(--inspection-bg-tertiary) !important;
        color: var(--inspection-text-primary) !important;
      }

      &.selected {
        background: var(--inspection-primary) !important;
        color: var(--inspection-text-primary) !important;
        font-weight: 500 !important;
      }
    }
  }
}


/* 组件特有的样式 */
.emergency-import-content {
  padding: 10px 0;
}

.emergency-import-tips {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;

  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.emergency-import-tips h4 {
  margin: 0 0 10px 0;
  color: white;
}

.emergency-import-tips ol {
  margin: 10px 0;
  padding-left: 20px;
}

.emergency-import-tips li {
  margin-bottom: 5px;
  color: white;
}