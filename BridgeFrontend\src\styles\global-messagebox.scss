/**
 * 全局 MessageBox 确认弹框样式
 * 应急管理模块删除确认弹框专用样式
 * 注意：Element UI 的 MessageBox 是动态插入到 body 中的，需要全局样式
 */

/* 应急管理删除确认弹框样式 */
.el-message-box {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;

  .el-message-box__header {
    background: transparent !important;
    border-bottom: none !important;
    padding: 20px 24px 15px 24px !important;

    .el-message-box__title {
      color: #FFFFFF !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      font-size: 18px !important;
      font-weight: 500 !important;
    }

    .el-message-box__headerbtn {
      .el-message-box__close {
        color: rgba(255, 255, 255, 0.7) !important;
        font-size: 18px !important;

        &:hover {
          color: #FFFFFF !important;
        }
      }
    }
  }

  .el-message-box__content {
    background: transparent !important;
    padding: 15px 24px !important;

    .el-message-box__container {
      .el-message-box__message {
        color: #FFFFFF !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        line-height: 1.5 !important;

        p {
          color: #FFFFFF !important;
          margin: 0 !important;
        }
      }

      .el-message-box__status {
        &.el-icon-warning {
          color: #E6A23C !important;
          font-size: 24px !important;
        }
      }
    }
  }

  .el-message-box__btns {
    background: transparent !important;
    border-top: none !important;
    padding: 15px 24px 20px 24px !important;
    text-align: right !important;

    .el-button {
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
      border-radius: 6px !important;
      font-size: 14px !important;
      padding: 10px 20px !important;
      margin-left: 12px !important;
      transition: all 0.3s ease !important;

      &--default {
        background: rgba(255, 255, 255, 0.1) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: rgba(255, 255, 255, 0.9) !important;

        &:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          border-color: rgba(255, 255, 255, 0.4) !important;
          color: #FFFFFF !important;
          transform: translateY(-1px) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }

      &--primary {
        background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%) !important;
        border: none !important;
        color: #FFFFFF !important;

        &:hover {
          background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%) !important;
          transform: translateY(-1px) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }
    }
  }
}

/* 确认弹框遮罩层样式 */
.v-modal {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
