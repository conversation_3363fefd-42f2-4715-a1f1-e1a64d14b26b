// 巡检模块深色主题变量
:root {
  // 主要颜色 - 与框架主题色保持一致
  --inspection-primary: #5C9DFF; // 与框架$blue保持一致
  --inspection-primary-light: #74a7f5; // 与框架$light-blue保持一致
  --inspection-primary-dark: #091A4B; // 与框架$themeColor保持一致

  // 背景颜色 - 与框架主题色保持一致
  --inspection-bg-primary: #091A4B; // 与$themeColor保持一致
  --inspection-bg-secondary: rgba(9, 26, 75, 0.9);

  // 设计系统字体变量
  --sds-typography-body-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --sds-typography-body-size-medium: 16px;
  --sds-typography-body-font-weight-regular: 400;

  // 侧边栏配色变量
  --Miscellaneous-Sidebar-Fill---Selected: #FFF;
  --inspection-bg-tertiary: #334155;

  // 文字颜色
  --inspection-text-primary: #f8fafc;
  --inspection-text-secondary: #e2e8f0;
  --inspection-text-muted: #94a3b8;

  // 边框颜色
  --inspection-border: #374151;
  --inspection-border-light: #4b5563;

  // 状态颜色
  --inspection-success: #10b981;
  --inspection-warning: #f59e0b;
  --inspection-danger: #ef4444;
  --inspection-info: #3b82f6;

  // 表格颜色
  --inspection-table-header: rgba(9, 26, 75, 0.95);
  --inspection-table-row: rgba(36, 48, 102, 0.8); // 更新表格行变量，使用稍微泛白的深色底色
  --inspection-table-hover: rgba(92, 157, 255, 0.15); // 稍微增加hover效果的透明度

  // 卡片颜色
  --inspection-card-bg: rgba(9, 26, 75, 0.6);
  --inspection-card-border: rgba(9, 26, 75, 0.4);

  // 阴影
  --inspection-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --inspection-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

// 巡检模块通用样式类
.inspection-container {
  background: transparent; // 使用透明背景，继承框架主题色
  min-height: 100vh;
  color: var(--inspection-text-primary);
  
  // 页面容器 - 使用更精确的高度计算
  .page-container {
    height: calc(100vh - 84px); // 减去框架header高度(64px)和底部margin(20px)
    display: flex;
    flex-direction: column;
    padding: 0 20px 20px 20px; // 左右内边距和底部内边距
    box-sizing: border-box;
  }
}

// Tab 样式 - 胶囊风格，与设计图一致
// 使用更高优先级的选择器确保样式生效
.app-main .inspection-tabs,
.inspection-tabs {
  margin-bottom: 4px !important; // 大幅减少TAB区域到筛选区域的间隔
  flex-shrink: 0; // 防止被压缩
  min-height: 40px !important; // 增加TAB区域的最小高度

  .el-tabs__header {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
    margin: 16px 0 0 0 !important; // 统一上方边距为16px，与其他区域保持一致
    min-height: 52px !important; // 统一header最小高度为52px

    .el-tabs__nav-wrap {
      &::after {
        display: none;
      }
    }

    .el-tabs__nav {
      border: none !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 0 !important; // 移除gap，因为每个item已有右边距10px
      min-height: 52px !important; // 统一nav区域的最小高度
    }

    .el-tabs__item {
      display: flex !important;
      height: 52px; // 统一高度为52px，与其他区域保持一致
      padding: 14px !important; // 统一内边距为14px
      justify-content: flex-start !important;
      align-items: center !important;
      border-radius: 10px; // 按设计图的圆角
        background: #1B2A56; // 未选中状态的深蓝色背景
      border: none !important;

      // 保持完整的圆角，使用box-shadow模拟特殊边框效果
      border: none !important;

      // 使用多个box-shadow分别绘制左、上、右、下边框，跳过角落
      box-shadow:
        // 左边框 (从上角10px开始到下角10px结束) - 降低亮度到0.15
        inset 1px 0 0 0 rgba(255, 255, 255, 0.15),
        // 上边框 (从左角10px开始到右角10px结束) - 降低亮度到0.15
        inset 0 1px 0 0 rgba(255, 255, 255, 0.15),
        // 右边框 (从上角10px开始到下角10px结束) - 降低亮度到0.15
        inset -1px 0 0 0 rgba(255, 255, 255, 0.15),
        // 下边框 (从左角10px开始到右角10px结束) - 降低亮度到0.15
        inset 0 -1px 0 0 rgba(255, 255, 255, 0.15);

      // 使用伪元素遮挡右上角和左下角的边框
      &::before {
        content: '';
        position: absolute;
        top: -1px;
        right: -1px;
        width: 12px;
        height: 12px;
        background: #1B2A56;
        border-top-right-radius: 10px;
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: -1px;
        width: 12px;
        height: 12px;
        background: #1B2A56;
        border-bottom-left-radius: 10px;
        z-index: 1;
      }
      margin: 0 10px 0 0 !important; // 右侧间距10px，按设计图要求
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
      min-width: 92px; // 按设计图的最小内容宽度
      position: relative;
      box-sizing: border-box !important;

      // 强制重置任何可能的文本缩进
      text-indent: 0 !important;

      span[slot="label"] {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important; // 图标和文字间隔 - 关键设置！
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
        width: 100%;
        height: 24px; // 增加内容高度到24px
        line-height: 24px;
        padding: 0 !important; // 完全移除内边距
        justify-content: flex-start !important; // 强制左对齐
        margin: 0 !important; // 移除任何外边距
        text-indent: 0 !important; // 移除文本缩进
        box-sizing: border-box !important;

        .svg-icon {
          width: 16px !important;
          height: 16px !important;
          fill: currentColor;
          flex-shrink: 0 !important;
          margin: 0 !important; // 完全移除所有margin，依赖父元素的gap
          padding: 0 !important;
          position: static !important; // 确保不被其他定位影响
          float: none !important; // 确保不浮动
          vertical-align: baseline !important;
          left: auto !important;
          right: auto !important;
          top: auto !important;
          bottom: auto !important;
          transform: none !important;
        }
      }

      // 特别针对第一个tab的处理
      &:first-child {
        padding-left: 10px !important; // 确保第一个tab也有左内边距

        span[slot="label"] {
          padding-left: 0 !important;
          text-indent: 0 !important;
          margin-left: 0 !important;

          .svg-icon {
            margin-left: 0 !important;
            position: static !important;
            left: 0 !important;
            transform: translateX(0) !important;
          }
        }
      }

      &:hover:not(.is-active) {
        color: rgba(255, 255, 255, 0.9);
        background: #1a2d5a; // 悬停状态稍微亮一些的蓝色

        // 悬停状态的角落遮罩
        &::before {
          background: #1a2d5a;
        }

        &::after {
          background: #1a2d5a;
        }
      }

        &.is-active {
          background: linear-gradient(135deg, #334067 0%, #2a3558 100%); // 选中状态的渐变背景
          color: #fff;
          border-radius: 10px; // 保持完整圆角
          border: none !important; // 移除简单边框，使用复杂的box-shadow边框

          // 保持完整的复杂边框样式和阴影效果
          box-shadow:
            // 左边框 (从上角10px开始到下角10px结束) - 保持与未选中状态一致的亮度
            inset 1px 0 0 0 rgba(255, 255, 255, 0.25),
            // 上边框 (从左角10px开始到右角10px结束)
            inset 0 1px 0 0 rgba(255, 255, 255, 0.25),
            // 右边框 (从上角10px开始到下角10px结束)
            inset -1px 0 0 0 rgba(255, 255, 255, 0.25),
            // 下边框 (从左角10px开始到右角10px结束)
            inset 0 -1px 0 0 rgba(255, 255, 255, 0.25),
            // 外部阴影效果
            0 2px 8px rgba(0, 123, 255, 0.2);

          // 激活状态的角落遮罩，使用渐变背景的中间色
          &::before {
            content: '';
            position: absolute;
            top: -1px;
            right: -1px;
            width: 12px;
            height: 12px;
            background: #2a3558; // 渐变的结束色
            border-top-right-radius: 10px;
            z-index: 1;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: -1px;
            width: 12px;
            height: 12px;
            background: #2a3558; // 渐变的结束色
            border-bottom-left-radius: 10px;
            z-index: 1;
          }
        }
    }

    .el-tabs__active-bar {
      display: none; // 隐藏默认的活动条
    }
  }

  .el-tabs__content {
    padding: 0;
  }
}

// 搜索卡片样式 - 与背景保持一致
.inspection-search-card {
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: none;
  margin-bottom: 24px;

  .el-card__body {
    padding: 0;
  }

  .search-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--inspection-border);

    .title-left {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--inspection-text-primary);

      i {
        margin-right: 8px;
        font-size: 18px;
        color: var(--inspection-primary-light);
      }
    }

    .search-actions {
      display: flex;
      gap: 12px;

      .el-button {
        border-radius: 8px;
        font-weight: 500;
        padding: 10px 20px;

        &--primary {
          background: var(--inspection-primary);
          border-color: var(--inspection-primary);
          box-shadow: 0 2px 4px rgba(30, 64, 175, 0.3);

          &:hover {
            background: var(--inspection-primary-light);
            border-color: var(--inspection-primary-light);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(30, 64, 175, 0.4);
          }
        }

        &:not(.el-button--primary) {
          background: transparent;
          border-color: var(--inspection-border-light);
          color: var(--inspection-text-secondary);

          &:hover {
            background: var(--inspection-bg-tertiary);
            border-color: var(--inspection-primary-light);
            color: var(--inspection-primary-light);
          }
        }
      }
    }
  }

  .search-form {
    .el-form-item {
      margin-bottom: 0;

      .el-form-item__label {
        color: var(--inspection-text-secondary);
        font-weight: 500;
        padding-bottom: 8px;
      }

      .el-select, .el-input {
        .el-input__inner {
          background: var(--inspection-bg-tertiary);
          border-color: var(--inspection-border);
          color: var(--inspection-text-primary);
          border-radius: 8px;

          &:focus {
            border-color: var(--inspection-primary-light);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }

          &::placeholder {
            color: var(--inspection-text-muted);
          }
        }

        .el-input__suffix {
          .el-icon-arrow-up {
            color: var(--inspection-text-muted);
          }
        }
      }
    }
  }
}

// 数据表格样式 - 与背景保持一致
.inspection-table,
.common-table {
  flex: 1; // 占用剩余空间
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许flex收缩
  
  .el-table {
    background: transparent;
    border: none;
    border-radius: 0;
    overflow: visible;
    box-shadow: none;
    flex: 1;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }

    // 移除表格边框
    .el-table__border-left-patch,
    .el-table__border-right-patch,
    .el-table__border-bottom-patch {
      display: none !important;
    }

    // 移除表格容器的边框
    .el-table__body-wrapper {
      border: none !important;
    }

    .el-table__footer-wrapper {
      border: none !important;
    }

    .el-table__append-wrapper {
      border: none !important;
    }

    .el-table__header-wrapper {
      .el-table__header {
        background: transparent;

        th {
          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%);
          color: #FFF;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%; /* 22.4px */
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          border-left: 1px solid rgba(255, 255, 255, 0.1);
          border-right: 1px solid rgba(255, 255, 255, 0.1);
          padding: 0;
          white-space: nowrap; // 表头不换行

          .cell {
            display: flex;
            height: 44px; // 适度增加表头高度，从36px到44px
            padding: 14px 24px; // 适度增加内边距，提供更好的视觉空间
            justify-content: center;
            align-items: center;
            gap: 10px;
            align-self: stretch;
            color: #FFF;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          background: transparent;

          &:hover {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          // 移除最后一行的底部边框
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%); // 稍微泛白一点的深蓝色渐变，保持与框架的协调性
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 4px 12px; // 进一步减少上下padding以匹配32px行高
            text-align: center;
            height: 32px; // 明确设置td高度

            .cell {
              color: #FFF;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 14px; // 减少字体大小从16px到14px
              font-style: normal;
              font-weight: 400;
              line-height: 120%; // 减少行高从140%到120%
              justify-content: center;
              align-items: center;
              text-align: center;
            }
          }
        }
      }
    }

    // 固定列（操作列）样式处理
    .el-table__fixed-right {
      background: transparent !important;
      // 强制重置高度，避免Element UI的自动计算导致的660px问题
      height: 100% !important;
      max-height: none !important;
      min-height: auto !important;

      // 固定列表头样式
      .el-table__header {
        background: transparent;

        th {
          background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
          color: #FFF !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 140%;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
          vertical-align: middle !important;
          text-align: center !important;

          .cell {
            display: flex !important;
            height: 44px !important; // 适度增加固定列表头高度，与普通列保持一致
            padding: 14px 16px !important; // 适度增加padding，提供更好的视觉空间
            justify-content: center !important;
            align-items: center !important;
            gap: 10px;
            align-self: stretch;
            color: #FFF !important;
            text-align: center !important;
            vertical-align: middle !important;
            line-height: 1 !important; // 设置行高为1，避免文字偏移
          }
        }
      }

      // 固定列单元格样式
      .el-table__body {
        tr {
          // 移除最后一行的底部边框
          &:last-child {
            td {
              border-bottom: none !important;
            }
          }

          td {
            background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important; // 固定列也使用相同的稍微泛白的深色底色
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
            padding: 4px 12px; // 进一步减少固定列上下padding以匹配32px行高
            text-align: center;
            vertical-align: middle !important;
            height: 32px !important; // 明确设置固定列td高度

              .cell {
                color: #FFF !important;
                font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
                font-size: 14px; // 减少固定列字体大小
                font-style: normal;
                font-weight: 400;
                line-height: 120%; // 减少固定列行高
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                text-align: center !important;
                flex-direction: row !important; // 确保水平排列
                flex-wrap: nowrap !important;
                gap: 8px !important; // 减少按钮间距
                width: 100% !important;
                height: 100% !important;
                min-height: 32px !important; // 操作列最小高度与行高一致
                max-height: 32px !important; // 限制操作列最大高度，确保与行高完全一致
                padding: 0 !important; // 移除cell的padding，让按钮完全居中
                margin: 0 !important; // 完全居中，无偏移
                line-height: 32px !important; // 设置行高与表格行高一致

              // 处理ActionButtons组件 - 确保与32px行高适配
              .action-buttons {
                display: flex !important;
                flex-direction: row !important;
                justify-content: center !important;
                align-items: center !important;
                gap: 6px !important; // 减小间距以适应更小的按钮和32px行高限制
                flex-wrap: nowrap !important; // 覆盖组件的wrap设置
                width: 100% !important;
                height: 32px !important; // 固定高度与表格行高一致
                margin: 0 !important;
                padding: 0 !important;
                line-height: 32px !important; // 设置行高与表格行高一致

                // 确保组件内的按钮也正确对齐
                .el-button {
                  flex-shrink: 0 !important; // 防止按钮被压缩
                  margin: 0 !important; // 移除默认margin
                }
              }
            }

            // 操作按钮通用样式 - 与表格数据单元格样式保持一致
            .el-button--text,
            .action-buttons .el-button--text,
            .action-buttons .el-button {
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
              font-size: 14px !important; // 与表格数据单元格字体大小保持一致
              font-weight: 400 !important;
              line-height: 120% !important; // 与表格数据单元格行高保持一致
              padding: 2px 6px !important; // 减小内边距以适应32px行高限制
              margin: 0 !important; // 移除margin，使用父元素的gap来控制间距
              border: none !important;
              background: none !important;
              height: 20px !important; // 固定按钮高度，确保在32px行高内
              min-height: 20px !important; // 最小高度也要限制
              max-height: 20px !important; // 最大高度限制，防止撑开行高
              display: inline-flex !important; // 改为inline-flex便于控制内容对齐
              align-items: center !important; // 垂直居中对齐
              justify-content: center !important; // 水平居中对齐
              vertical-align: middle !important;
              cursor: pointer !important; // 确保鼠标悬停时显示手型
              border-radius: 4px !important; // 添加圆角
              transition: all 0.2s ease !important; // 添加过渡动画
              flex-shrink: 0 !important; // 防止按钮被压缩
              box-sizing: border-box !important; // 确保尺寸计算正确

              &:hover {
                background: rgba(255, 255, 255, 0.1) !important; // 悬停时添加轻微背景
                cursor: pointer !important;
              }

              &:focus {
                background: none !important;
              }
            }

            // 日志按钮 - 与表格数据单元格样式保持一致
            .log-button {
              color: #FFF !important;
              font-size: 14px !important; // 与表格数据单元格字体大小保持一致
              line-height: 120% !important; // 与表格数据单元格行高保持一致
              height: 20px !important; // 固定高度
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;

              &:hover {
                color: #E0E0E0 !important;
                background: rgba(255, 255, 255, 0.1) !important; // 白色按钮悬停背景
              }

              &:focus {
                color: #FFF !important;
                background: none !important;
              }
            }

            // 报告按钮 - 与表格数据单元格样式保持一致
            .report-button {
              color: #FFF !important;
              font-size: 14px !important; // 与表格数据单元格字体大小保持一致
              line-height: 120% !important; // 与表格数据单元格行高保持一致
              height: 20px !important; // 固定高度
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;

              &:hover {
                color: #E0E0E0 !important;
                background: rgba(255, 255, 255, 0.1) !important; // 白色按钮悬停背景
              }

              &:focus {
                color: #FFF !important;
                background: none !important;
              }
            }

            // 针对ActionButtons组件中的按钮样式 - 统一基础样式确保行高一致
            .action-buttons {
              .el-button {
                // 统一所有按钮的基础样式，确保与表格数据单元格一致
                font-size: 14px !important; // 与表格数据单元格字体大小保持一致
                line-height: 120% !important; // 与表格数据单元格行高保持一致
                height: 20px !important; // 固定按钮高度，确保在32px行高内
                min-height: 20px !important;
                max-height: 20px !important;
                padding: 2px 6px !important; // 统一内边距，适应固定高度
                border-radius: 4px !important;
                box-sizing: border-box !important;
                font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                
                // 默认text类型按钮为白色
                &.el-button--text {
                  color: #FFF !important;

                  &:hover {
                    color: #E0E0E0 !important;
                    background: rgba(255, 255, 255, 0.1) !important;
                  }

                  &:focus {
                    color: #FFF !important;
                    background: none !important;
                  }
                }

                // 判定、处置、复核等功能按钮保持原有颜色
                &.el-button--primary {
                  color: #FFF !important;
                  background-color: #409EFF !important;
                  border-color: #409EFF !important;

                  &:hover {
                    background-color: #66b1ff !important;
                    border-color: #66b1ff !important;
                  }
                }

                &.el-button--warning {
                  color: #FFF !important;
                  background-color: #e6a23c !important;
                  border-color: #e6a23c !important;

                  &:hover {
                    background-color: #ebb563 !important;
                    border-color: #ebb563 !important;
                  }
                }

                &.el-button--success {
                  color: #FFF !important;
                  background-color: #67c23a !important;
                  border-color: #67c23a !important;

                  &:hover {
                    background-color: #85ce61 !important;
                    border-color: #85ce61 !important;
                  }
                }
              }
            }

            // 专门针对操作列（最后一列）的样式
            &:last-child {
              .cell {
                justify-content: center !important;
                align-items: center !important;
                flex-direction: row !important; // 确保按钮水平排列
                flex-wrap: nowrap !important; // 禁止换行
                display: flex !important; // 确保显示
                visibility: visible !important; // 确保可见

                // 操作按钮样式 - 与表格数据单元格样式完全一致
                .el-button {
                  font-size: 14px !important; // 与表格数据单元格字体大小保持一致
                  font-weight: 400 !important; // 正常字体粗细
                  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                  padding: 2px 6px !important; // 减小内边距以适应20px按钮高度
                  margin: 0 !important; // 移除margin，使用父元素的gap控制间距
                  height: 20px !important; // 固定按钮高度，确保在32px行高内
                  min-height: 20px !important;
                  max-height: 20px !important;
                  line-height: 120% !important; // 与表格数据单元格行高保持一致
                  border: none !important; // 移除边框，变为文字链接样式
                  background: none !important; // 移除背景，变为文字链接样式
                  color: #FFF !important; // 白色文字，与巡检记录保持一致
                  transition: all 0.2s ease !important;
                  cursor: pointer !important;
                  border-radius: 4px !important;
                  display: inline-flex !important; // 改为inline-flex便于控制内容对齐
                  align-items: center !important; // 垂直居中对齐
                  justify-content: center !important; // 水平居中对齐
                  vertical-align: middle !important;
                  flex-shrink: 0 !important;
                  box-sizing: border-box !important; // 确保尺寸计算正确

                  &:hover {
                    color: #E0E0E0 !important; // 悬停时变浅，与巡检记录按钮保持一致
                    background: rgba(255, 255, 255, 0.1) !important; // 轻微的白色背景
                  }

                  &:focus {
                    color: #FFF !important;
                    background: none !important;
                  }
                }
              }
            }

            // 针对上次巡检时间列（倒数第二列）的对齐
            &:nth-last-child(2) {
              text-align: center !important;
              .cell {
                justify-content: center !important;
                align-items: center !important;
              }
            }

            // 通用居中对齐样式 - 针对所有设置了align="center"的列
            &.is-center {
              text-align: center !important;
              .cell {
                justify-content: center !important;
                align-items: center !important;
              }
            }
          }

          // 移除整行悬停效果，只保留按钮悬停效果
        }
      }
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;

    &.status-0 {
      background: rgba(16, 185, 129, 0.2);
      color: var(--inspection-success);
      border: 1px solid rgba(16, 185, 129, 0.3);
    }

    &.status-1 {
      background: rgba(245, 158, 11, 0.2);
      color: var(--inspection-warning);
      border: 1px solid rgba(245, 158, 11, 0.3);
    }

    &.status-2,
    &.status-3,
    &.status-high {
      background: rgba(239, 68, 68, 0.2);
      color: var(--inspection-danger);
      border: 1px solid rgba(239, 68, 68, 0.3);
    }
  }

  // 本月缺巡次数样式
  .missing-patrol-count {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center; // 内容居中显示
    gap: 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    // 统一所有状态的尺寸
    width: 26px;
    height: 22px;
    padding: 0; // 统一使用0 padding，依赖固定宽高
    margin: 0 auto; // 在容器中水平居中

    // 0次 - 绿色背景
    &.count-0 {
      background: rgba(179, 255, 200, 0.82);
      color: #059669; // 深绿色文字
    }

    // 1次 - 黄色背景
    &.count-1 {
      background: rgba(248, 252, 146, 0.80);
      color: #D97706; // 深黄色文字
    }

    // 2次以上 - 橙红色背景
    &.count-2-plus {
      background: rgba(255, 203, 196, 0.80);
      color: #D97706;
    }
  }
}

// 分页样式
.inspection-pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;

  .el-pagination {
    .el-pager {
      li {
        background: var(--inspection-bg-tertiary);
        color: var(--inspection-text-secondary);
        border: 1px solid var(--inspection-border);
        border-radius: 6px;
        margin: 0 4px;

        &:hover {
          background: var(--inspection-primary-light);
          color: var(--inspection-text-primary);
          border-color: var(--inspection-primary-light);
        }

        &.active {
          background: var(--inspection-primary);
          color: var(--inspection-text-primary);
          border-color: var(--inspection-primary);
        }
      }
    }

    .btn-prev, .btn-next {
      background: var(--inspection-bg-tertiary);
      color: var(--inspection-text-secondary);
      border: 1px solid var(--inspection-border);
      border-radius: 6px;

      &:hover {
        background: var(--inspection-primary-light);
        color: var(--inspection-text-primary);
        border-color: var(--inspection-primary-light);
      }
    }

    .el-pagination__total,
    .el-pagination__jump {
      color: var(--inspection-text-secondary);
    }
  }
}

// 通用卡片样式重置 - 确保所有区域背景一致
.inspection-container {
  .el-card {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
}

  // 下拉选择器全局样式覆盖
  .el-select-dropdown {
    background: var(--inspection-bg-secondary);
    border: 1px solid var(--inspection-border);
    border-radius: 8px;
    box-shadow: var(--inspection-shadow-lg);

    .el-select-dropdown__item {
      color: var(--inspection-text-secondary);
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      font-size: var(--sds-typography-body-size-medium, 16px);
      font-weight: var(--sds-typography-body-font-weight-regular, 400);
      line-height: 140%;

      &:hover {
        background: var(--inspection-bg-tertiary);
        color: var(--inspection-text-primary);
      }

      &.selected {
        background: var(--inspection-primary);
        color: var(--inspection-text-primary);
        font-weight: 500;
      }
    }

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }

  // 筛选区域样式 - 与TAB按钮风格一致
  .filter-section {
    background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important; // #1B2A56 渐变背景
    border: 1px solid rgba(255, 255, 255, 0.2) !important; // 添加基础边框
    border-radius: 10px !important; // 与TAB按钮相同的圆角
    margin-top: 0px !important; // 移除筛选区域的上边距，紧贴TAB区域
    margin-bottom: 12px !important; // 减少到表格区域的间距
    padding: 14px 20px !important; // 统一内边距为14px上下，20px左右
    min-height: 52px !important; // 统一筛选区域最小高度为52px
    flex-shrink: 0 !important; // 防止被压缩

    // 使用伪元素实现左上角和右下角的亮边框效果，与TAB按钮风格一致
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      right: -1px;
      width: 12px;
      height: 12px;
      background: #2A3B6B; // 渐变的结束色
      border-top-right-radius: 10px;
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 10px;
      pointer-events: none;
      z-index: 2;
      // 只在左上角和右下角添加亮边框，与TAB按钮保持一致
      background:
        // 左上角亮边框 - 亮度0.8
        linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
        linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
        // 右下角亮边框 - 亮度0.8
        linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
        linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
      background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px; // 与TAB按钮一致的边框长度
      background-position: top left, top left, bottom right, bottom right;
      background-repeat: no-repeat;
    }

    // 筛选标题
    .filter-title {
      margin-bottom: 20px;

      span {
        color: var(--inspection-text-primary);
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-size: var(--sds-typography-body-size-medium, 16px);
        font-weight: var(--sds-typography-body-font-weight-regular, 400);
        line-height: 140%;
      }
    }

    // 筛选内容区域
    .filter-content {
      display: flex;
      align-items: stretch; // 改为stretch确保所有控件高度一致
      justify-content: space-between;
      gap: 20px;
      flex-wrap: wrap;

      .filter-select {
        min-width: 180px;
        flex: 1;
        display: flex; // 确保控件内部也使用flex布局
        align-items: center; // 垂直居中对齐

        // 统一所有筛选控件的样式
        .el-input__inner,
        .el-select__inner {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          border-radius: 8px !important;
          color: var(--inspection-text-primary) !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
          line-height: 140% !important;
          height: 40px !important;
          box-sizing: border-box !important;
          display: flex !important;
          align-items: center !important;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5) !important;
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            font-size: var(--sds-typography-body-size-medium, 16px) !important;
            font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
          }

          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important;
          }

          &:focus {
            border-color: #409EFF !important;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
          }
        }

        // 统一下拉箭头和图标样式
        .el-input__suffix {
          .el-input__suffix-inner {
            .el-select__caret,
            .el-input__icon {
              color: rgba(255, 255, 255, 0.7) !important;
              font-size: 14px !important;
              transition: color 0.3s ease !important;

              &:hover {
                color: rgba(255, 255, 255, 0.9) !important;
              }
            }

            // 清除按钮特殊样式
            .el-icon-circle-close {
              &:hover {
                color: #f56c6c !important;
              }
            }
          }
        }
      }

        .filter-row {
          display: flex;
          gap: 32px;
          align-items: flex-start;
          margin-bottom: 20px;
          flex-wrap: wrap;

          .filter-item {
            display: flex;
            flex-direction: column;
            min-width: 180px;

            label {
              display: block;
              color: var(--inspection-text-primary) !important;
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            font-size: var(--sds-typography-body-size-medium, 16px) !important;
            font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
            line-height: 140% !important;
            margin-bottom: 8px !important;
          }

          // 下拉框样式
          .el-select {
            width: 100%;

            .el-input__inner {
              background: rgba(255, 255, 255, 0.1) !important;
              border: 1px solid rgba(255, 255, 255, 0.2) !important;
              border-radius: 8px !important;
              color: var(--inspection-text-primary) !important;
              font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
              font-size: var(--sds-typography-body-size-medium, 16px) !important;
              font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
              line-height: 140% !important;
              height: 40px !important;

              &::placeholder {
                color: rgba(255, 255, 255, 0.5) !important;
              }

              &:hover {
                border-color: rgba(255, 255, 255, 0.4) !important;
              }

              &:focus {
                border-color: #409EFF !important;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
              }
            }

            // 下拉箭头样式
            .el-input__suffix {
              .el-input__suffix-inner {
                .el-select__caret {
                  color: rgba(255, 255, 255, 0.7) !important;

                  &:hover {
                    color: rgba(255, 255, 255, 0.9) !important;
                  }
                }
              }
            }

            // 清除按钮样式
            .el-input__suffix {
              .el-input__suffix-inner {
                .el-input__icon {
                  color: rgba(255, 255, 255, 0.5) !important;

                  &:hover {
                    color: rgba(255, 255, 255, 0.8) !important;
                  }
                }
              }
            }
          }
        }
      }

      // 操作按钮区域
      .filter-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        align-items: center; // 确保按钮垂直居中对齐

        .el-button {
          height: 36px;
          padding: 0 16px;
          border-radius: 6px;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          font-size: 14px;
          font-weight: var(--sds-typography-body-font-weight-regular, 400);
          line-height: 140%;

          &.el-button--primary {
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border: none;
            color: #FFF;

            &:hover {
              background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%);
              transform: translateY(-1px);
            }

            &:active {
              background: linear-gradient(135deg, #337ECC 0%, #2B6CB0 100%);
              transform: translateY(0);
            }
          }

          &.el-button--default {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.4);
              color: #FFF;
              transform: translateY(-1px);
            }

            &:active {
              background: rgba(255, 255, 255, 0.05);
              transform: translateY(0);
            }
          }
        }
      }
    }
  }

  // 兼容原有的搜索卡片样式
  .inspection-search-card {
    background: #0C1D4E !important; // 与TAB按钮相同的背景色
    border: none !important;
    border-radius: 10px !important; // 与TAB按钮相同的圆角
    margin-top: 0px !important; // 移除搜索卡片的上边距，紧贴TAB区域
    padding: 14px 20px !important; // 统一内边距为14px上下，20px左右
    box-shadow:
      // 左边框 - 与Tab按钮保持一致，降低亮度到0.15
      inset 1px 0 0 0 rgba(255, 255, 255, 0.15),
      // 上边框 - 与Tab按钮保持一致，降低亮度到0.15
      inset 0 1px 0 0 rgba(255, 255, 255, 0.15),
      // 右边框 - 与Tab按钮保持一致，降低亮度到0.15
      inset -1px 0 0 0 rgba(255, 255, 255, 0.15),
      // 下边框 - 与Tab按钮保持一致，降低亮度到0.15
      inset 0 -1px 0 0 rgba(255, 255, 255, 0.15) !important;

    // 使用伪元素遮挡右上角和左下角的边框，与TAB按钮效果一致
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      right: -1px;
      width: 12px;
      height: 12px;
      background: #1B2A56;
      border-top-right-radius: 10px;
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: -1px;
      width: 12px;
      height: 12px;
      background: #1B2A56;
      border-bottom-left-radius: 10px;
      z-index: 1;
    }

    .el-card__body {
      padding: 0 !important;
    }

    // 搜索标题区域
    .search-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .title-left {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: var(--inspection-text-primary);
          font-size: 18px;
        }

        span {
          color: var(--inspection-text-primary);
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          font-size: var(--sds-typography-body-size-medium, 16px);
          font-weight: var(--sds-typography-body-font-weight-regular, 400);
          line-height: 140%;
        }
      }

      .search-actions {
        display: flex;
        gap: 12px;

        .el-button {
          height: 36px;
          padding: 0 16px;
          border-radius: 6px;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          font-size: 14px;
          font-weight: var(--sds-typography-body-font-weight-regular, 400);
          line-height: 140%;

          &.el-button--primary {
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border: none;
            color: #FFF;

            &:hover {
              background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%);
              transform: translateY(-1px);
            }

            &:active {
              background: linear-gradient(135deg, #337ECC 0%, #2B6CB0 100%);
              transform: translateY(0);
            }
          }

          &.el-button--default {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.9);

            &:hover {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.4);
              color: #FFF;
              transform: translateY(-1px);
            }

            &:active {
              background: rgba(255, 255, 255, 0.05);
              transform: translateY(0);
            }
          }
        }
      }
    }

    // 搜索表单区域
    .search-form {
      .el-form-item {
        margin-bottom: 0;

        .el-form-item__label {
          color: var(--inspection-text-primary) !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
          line-height: 140% !important;
          padding-bottom: 8px !important;
        }
      }

      // 下拉框样式
      .el-select {
        width: 100%;

        .el-input__inner {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          border-radius: 8px !important;
          color: var(--inspection-text-primary) !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
          line-height: 140% !important;
          height: 40px !important;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5) !important;
          }

          &:hover {
            border-color: rgba(255, 255, 255, 0.4) !important;
          }

          &:focus {
            border-color: #409EFF !important;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
          }
        }

        // 下拉箭头样式
        .el-input__suffix {
          .el-input__suffix-inner {
            .el-select__caret {
              color: rgba(255, 255, 255, 0.7) !important;

              &:hover {
                color: rgba(255, 255, 255, 0.9) !important;
              }
            }
          }
        }

        // 清除按钮样式
        .el-input__suffix {
          .el-input__suffix-inner {
            .el-input__icon {
              color: rgba(255, 255, 255, 0.5) !important;

              &:hover {
                color: rgba(255, 255, 255, 0.8) !important;
              }
            }
          }
        }
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .filter-section {
    padding: 16px 20px !important;
    margin-top: 20px !important;

    .filter-content {
      .filter-row {
        flex-direction: column;
        gap: 16px;

        .filter-item {
          min-width: auto;
          width: 100%;
        }
      }

      .filter-actions {
        justify-content: center;
        align-items: center; // 确保按钮垂直居中对齐
        margin-top: 16px;
      }
    }
  }

  .inspection-search-card {
    padding: 16px 20px !important;
    margin-top: 20px !important;

    .search-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;

      .search-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .search-form {
      .el-row {
        .el-col {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .filter-section {
    margin: 16px 12px 0 12px !important;
    padding: 12px 16px !important;

    .filter-title {
      span {
        font-size: 14px;
      }
    }

    .filter-content {
      .filter-actions {
        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }

    .filter-item {
      label {
        font-size: 14px !important;
      }

      .el-select {
        .el-input__inner {
          height: 36px !important;
          font-size: 14px !important;
        }
      }
    }
  }

  .inspection-search-card {
    margin: 16px 12px 0 12px !important;
    padding: 12px 16px !important;

    .search-title {
      .title-left {
        i {
          font-size: 16px;
        }

        span {
          font-size: 14px;
        }
      }

      .search-actions {
        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }

    .search-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 14px !important;
        }
      }

      .el-select {
        .el-input__inner {
          height: 36px !important;
          font-size: 14px !important;
        }
      }
    }
  }
}

// 专门解决表格底部白线问题 - 确保最后一行完全没有边框
.inspection-table,
.common-table {
  .el-table__body-wrapper {
    // 移除表格容器的底部边框
    border-bottom: none !important;
    
    .el-table__body {
      // 最后一行完全移除边框
      tr:last-child {
        td {
          border-bottom: none !important;
          
          // 确保操作列的最后一行也没有边框
          &.el-table-fixed-column--right {
            border-bottom: none !important;
          }
        }
      }
    }
  }

  // 固定列容器也移除底部边框
  .el-table__fixed-right {
    .el-table__body-wrapper {
      border-bottom: none !important;
      
      .el-table__body {
        tr:last-child {
          td {
            border-bottom: none !important;
          }
        }
      }
    }
  }

  // 移除表格底部的所有边框样式和伪元素
  &::after,
  .el-table::after,
  .el-table__body-wrapper::after,
  .el-table__fixed-right::after {
    display: none !important;
  }

  // 修复固定列高度问题 - 确保固定列与表格主体高度同步
  .el-table__fixed-right {
    // 使用与表格主体相同的高度计算方式
    height: 100% !important; // 使用百分比高度而不是固定像素值
    // 彻底移除白线问题
    border: none !important;
    border-bottom: none !important;
    border-right: none !important;
    box-shadow: none !important;
    
    // 移除伪元素
    &::before,
    &::after {
      content: none !important;
      display: none !important;
      background: none !important;
      border: none !important;
      height: 0 !important;
      width: 0 !important;
    }
    
    // 确保固定列体容器正确计算高度
    .el-table__fixed-body-wrapper {
      height: 100% !important;
      // 确保内容可见且正确定位
      overflow: visible !important;
      position: relative !important;
      // 移除边框和阴影
      border: none !important;
      border-bottom: none !important;
      box-shadow: none !important;
      
      &::before,
      &::after {
        content: none !important;
        display: none !important;
        background: none !important;
        border: none !important;
        height: 0 !important;
        width: 0 !important;
      }
    }

    // 确保固定列头部容器高度正确
    .el-table__fixed-header-wrapper {
      height: auto !important;
      border-bottom: none !important;
      box-shadow: none !important;
      
      &::before,
      &::after {
        display: none !important;
      }
    }
  }

  // 确保所有操作列样式完全一致 - 无论是否使用ActionButtons组件
  .el-table__fixed-column--right {
    .cell {
      height: auto !important;
      min-height: 48px !important;
      line-height: normal !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 8px 4px !important;
      overflow: visible !important;

      // 统一所有操作按钮样式（包括ActionButtons和普通按钮）
      .el-button,
      .action-buttons .el-button {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 999 !important;
        height: auto !important;
        min-height: 28px !important;
        font-size: 14px !important;
        line-height: 1.2 !important;
        padding: 6px 8px !important;
        margin: 0 2px !important;
        flex-shrink: 0 !important;
        white-space: nowrap !important;
        
        // 文本按钮特殊处理
        &.el-button--text {
          color: #409EFF !important;
          background: transparent !important;
          border: none !important;
          
          &:hover {
            color: #66b1ff !important;
            background: rgba(64, 158, 255, 0.1) !important;
          }
        }
      }

      // ActionButtons组件容器样式
      .action-buttons {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 6px !important;
        height: 32px !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        flex-wrap: nowrap !important;
      }
    }
  }
}
