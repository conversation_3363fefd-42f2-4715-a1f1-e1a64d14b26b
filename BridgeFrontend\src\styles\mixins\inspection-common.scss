// 巡检模块通用样式混入
@import '../inspection-theme.scss';

// 页面容器混入
@mixin inspection-page-container {
  &.inspection-container {
    background: transparent;
    min-height: 100vh;
    color: var(--inspection-text-primary);
    
    .page-container {
      height: calc(100vh - 84px);
      display: flex;
      flex-direction: column;
      padding: 0 20px 20px 20px;
      box-sizing: border-box;
    }
  }
}

// 卡片样式混入
@mixin inspection-card {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  
  .el-card__body {
    padding: 0 !important;
  }
}

// 信息展示区域混入
@mixin inspection-info-section {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    min-height: 32px;
    
    label {
      flex-shrink: 0;
      width: 140px;
      color: var(--inspection-text-secondary);
      font-weight: 500;
      line-height: 32px;
    }
    
    span {
      flex: 1;
      color: var(--inspection-text-primary);
      line-height: 32px;
      word-break: break-all;
    }
  }
}

// 表单样式混入
@mixin inspection-form {
  .el-form-item__label {
    color: var(--inspection-text-secondary) !important;
    font-weight: 500 !important;
  }
  
  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner {
    background: var(--inspection-bg-tertiary) !important;
    border-color: var(--inspection-border) !important;
    color: var(--inspection-text-primary) !important;
    
    &:focus {
      border-color: var(--inspection-primary-light) !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
    }
    
    &::placeholder {
      color: var(--inspection-text-muted) !important;
    }
  }
  
  .el-select-dropdown {
    background: var(--inspection-bg-secondary) !important;
    border: 1px solid var(--inspection-border) !important;
    
    .el-select-dropdown__item {
      color: var(--inspection-text-secondary) !important;
      
      &:hover {
        background: var(--inspection-bg-tertiary) !important;
        color: var(--inspection-text-primary) !important;
      }
      
      &.selected {
        background: var(--inspection-primary) !important;
        color: var(--inspection-text-primary) !important;
      }
    }
  }
}

// 操作按钮区域混入
@mixin inspection-action-buttons {
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--inspection-border);
  }
}

// 响应式设计混入
@mixin inspection-responsive {
  @media (max-width: 768px) {
    .page-container {
      padding: 0 12px 20px 12px !important;
    }
    
    .info-item {
      flex-direction: column;
      align-items: flex-start !important;
      
      label {
        width: auto !important;
        margin-bottom: 4px;
      }
    }
    
    .action-buttons {
      flex-direction: column;
      align-items: stretch;
      
      .el-button {
        width: 100%;
      }
    }
  }
}

// 工作流步骤样式混入
@mixin inspection-workflow-steps {
  .workflow-card {
    @include inspection-card;
    margin-bottom: 20px;
  }
}

// 卡片标题样式混入
@mixin inspection-card-header {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--inspection-border);
    
    h3 {
      display: flex;
      align-items: center;
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--inspection-text-primary);
      
      i {
        margin-right: 8px;
        font-size: 20px;
        color: var(--inspection-primary-light);
      }
    }
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 统一导出所有混入
@mixin inspection-all {
  @include inspection-page-container;
  @include inspection-form;
  @include inspection-action-buttons;
  @include inspection-responsive;
  @include inspection-workflow-steps;
  @include inspection-card-header;
  
  .el-card {
    @include inspection-card;
  }
  
  .info-section {
    @include inspection-info-section;
  }
}
