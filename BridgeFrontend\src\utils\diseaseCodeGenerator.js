/**
 * 病害编号生成工具
 * 规则：桥梁/隧道编号+年份+5位随机码
 */

/**
 * 生成5位随机码
 * @returns {string} 5位数字随机码
 */
function generateRandomCode() {
  return Math.floor(Math.random() * 100000).toString().padStart(5, '0')
}

/**
 * 生成病害编号
 * @param {string} structureCode - 桥梁或隧道编号
 * @param {string} structureType - 结构类型 'bridge' | 'tunnel'
 * @param {Date|string} date - 日期，默认当前日期
 * @returns {string} 病害编号
 */
export function generateDiseaseCode(structureCode, structureType = 'bridge', date = new Date()) {
  // 获取年份
  const year = new Date(date).getFullYear()
  
  // 生成5位随机码
  const randomCode = generateRandomCode()
  
  // 组合编号：结构编号+年份+随机码
  return `${structureCode}${year}${randomCode}`
}

/**
 * 解析病害编号
 * @param {string} diseaseCode - 病害编号
 * @returns {object} 解析结果
 */
export function parseDiseaseCode(diseaseCode) {
  if (!diseaseCode || diseaseCode.length < 10) {
    return {
      structureCode: '',
      year: '',
      randomCode: '',
      valid: false
    }
  }
  
  // 假设结构编号长度为1-6位，年份4位，随机码5位
  const randomCode = diseaseCode.slice(-5)
  const year = diseaseCode.slice(-9, -5)
  const structureCode = diseaseCode.slice(0, -9)
  
  return {
    structureCode,
    year,
    randomCode,
    valid: /^\d{4}$/.test(year) && /^\d{5}$/.test(randomCode)
  }
}

/**
 * 验证病害编号格式
 * @param {string} diseaseCode - 病害编号
 * @returns {boolean} 是否有效
 */
export function validateDiseaseCode(diseaseCode) {
  const parsed = parseDiseaseCode(diseaseCode)
  return parsed.valid
}

/**
 * 批量生成病害编号
 * @param {string} structureCode - 桥梁或隧道编号
 * @param {string} structureType - 结构类型
 * @param {number} count - 生成数量
 * @param {Date|string} date - 日期
 * @returns {string[]} 病害编号数组
 */
export function generateBatchDiseaseCodes(structureCode, structureType = 'bridge', count = 1, date = new Date()) {
  const codes = []
  const usedCodes = new Set()
  
  while (codes.length < count) {
    const code = generateDiseaseCode(structureCode, structureType, date)
    if (!usedCodes.has(code)) {
      codes.push(code)
      usedCodes.add(code)
    }
  }
  
  return codes
}

export default {
  generateDiseaseCode,
  parseDiseaseCode,
  validateDiseaseCode,
  generateBatchDiseaseCodes
}
