/**
 * 巡检中心模块默认数据配置
 * 用于接口失败时的降级数据处理
 */

// 默认巡检记录数据（匹配设计稿）
export const defaultInspectionRecords = {
  list: [
    {
      id: '001',
      bridgeName: '三汊矶大桥',
      inspectors: '吴亮吉 徐建国',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '13580037492',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-01-15 09:30'
    },
    {
      id: '002',
      bridgeName: '三汊矶大桥',
      inspectors: '刘亮吉 徐承宇',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '15210087395',
      monthlyInspectionCount: 1,
      lastInspectionTime: '2024-01-20 14:15'
    },
    {
      id: '003',
      bridgeName: '三汊矶大桥',
      inspectors: '孙昭吉 吴海燕',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '15910018495',
      monthlyInspectionCount: 2,
      lastInspectionTime: '2024-01-22 10:45'
    },
    {
      id: '004',
      bridgeName: '三汊矶大桥',
      inspectors: '吴利非 徐知夏',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '13122238579',
      monthlyInspectionCount: 3,
      lastInspectionTime: '2024-01-25 16:20'
    },
    {
      id: '005',
      bridgeName: '三汊矶大桥',
      inspectors: '徐统桐 郭浩然',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '13720089685',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-01-28 08:30'
    },
    {
      id: '006',
      bridgeName: '三汊矶大桥',
      inspectors: '张秀英 吴建国',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '18202038579',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-02-01 11:15'
    },
    {
      id: '007',
      bridgeName: '三汊矶大桥',
      inspectors: '李昭宁 吴亮吉',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '18310049683',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-02-05 13:45'
    },
    {
      id: '008',
      bridgeName: '三汊矶大桥',
      inspectors: '张昕雪 张知夏',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '17821229583',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-02-08 15:30'
    },
    {
      id: '009',
      bridgeName: '橘子洲大桥',
      inspectors: '王敏华 李建军',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '13912345678',
      monthlyInspectionCount: 1,
      lastInspectionTime: '2024-02-10 09:15'
    },
    {
      id: '010',
      bridgeName: '银盆岭大桥',
      inspectors: '陈志刚 刘美玲',
      inspectionUnit: '长沙市桥梁管理处',
      contactNumbers: '15987654321',
      monthlyInspectionCount: 0,
      lastInspectionTime: '2024-02-12 14:45'
    }
  ],
  total: 152,
  pageNum: 1,
  pageSize: 10
}

// 默认巡检日历数据
export const defaultInspectionCalendar = {
  bridgeId: 1,
  bridgeName: '橘子洲大桥',
  month: '2023-06',
  logs: [
    {
      date: '2023-06-01',
      inspectionType: '日常巡检',
      time: '09:20',
      inspector: '李华',
      logId: 'log001'
    },
    {
      date: '2023-06-05',
      inspectionType: '经常巡检',
      time: '14:30',
      inspector: '张明',
      logId: 'log002'
    }
  ]
}

// 默认病害数据 - 匹配设计文档示例
export const defaultDiseaseList = {
  list: [
    {
      id: 1,
      bridgeName: '橘子洲大桥',
      reportAttribute: 'daily',
      diseaseCode: 'JZZ202500001',
      diseasePart: '伸缩缝',
      diseaseType: '伸缩缝失效',
      diseaseStatus: 'judging',
      diseaseLocation: 'XXX位置',
      diseaseCount: 2,
      diseaseDescription: 'XXXXXX',
      reporter: '张三',
      reportTime: '2025-07-12 12:30',
      unit: 'XXX单位',
      contactNumber: '13139103912'
    },
    {
      id: 2,
      bridgeName: '橘子洲大桥',
      reportAttribute: 'center',
      diseaseCode: 'JZZ202500002',
      diseasePart: '照明设施',
      diseaseType: '照明设施缺失',
      diseaseStatus: 'planning',
      diseaseLocation: 'XXX位置',
      diseaseCount: 1,
      diseaseDescription: 'XXXXXX',
      reporter: '张三',
      reportTime: '2025-07-12 12:30',
      unit: 'XXX单位',
      contactNumber: '13139103912'
    },
    {
      id: 3,
      bridgeName: '橘子洲大桥',
      reportAttribute: 'inspection',
      diseaseCode: 'JZZ202500003',
      diseasePart: '伸缩缝',
      diseaseType: '伸缩缝失效',
      diseaseStatus: 'disposing',
      diseaseLocation: 'XXX位置',
      diseaseCount: 1,
      diseaseDescription: 'XXXXXX',
      reporter: '张三',
      reportTime: '2025-07-12 12:30',
      unit: 'XXX单位',
      contactNumber: '13139103912'
    },
    {
      id: 4,
      bridgeName: '三汊矶大桥',
      reportAttribute: 'superior',
      diseaseCode: 'SCJ202500004',
      diseasePart: '照明设施',
      diseaseType: '照明设施缺失',
      diseaseStatus: 'reviewing',
      diseaseLocation: 'XXX位置',
      diseaseCount: 1,
      diseaseDescription: 'XXXXXX',
      reporter: '张三',
      reportTime: '2025-07-12 12:30',
      unit: 'XXX单位',
      contactNumber: '13139103912'
    },
    {
      id: 5,
      bridgeName: '三汊矶大桥',
      reportAttribute: 'daily',
      diseaseCode: 'SCJ202500005',
      diseasePart: '照明设施',
      diseaseType: '照明设施缺失',
      diseaseStatus: 'archived',
      diseaseLocation: 'XXX位置',
      diseaseCount: 1,
      diseaseDescription: 'XXXXXX',
      reporter: '张三',
      reportTime: '2025-07-12 12:30',
      unit: 'XXX单位',
      contactNumber: '13139103912'
    },
    {
      id: 6,
      bridgeName: '银盆岭大桥',
      reportAttribute: 'regular',
      diseaseCode: 'YPL202500006',
      diseasePart: '桥面铺装',
      diseaseType: '裂缝',
      diseaseStatus: 'judging',
      diseaseLocation: '主跨中部',
      diseaseCount: 3,
      diseaseDescription: '桥面出现多条纵向裂缝，长度约2-5米',
      reporter: '李四',
      reportTime: '2025-07-13 09:15',
      unit: '长沙市桥梁管理处',
      contactNumber: '13912345678'
    },
    {
      id: 7,
      bridgeName: '猴子石大桥',
      reportAttribute: 'center',
      diseaseCode: 'HZS202500007',
      diseasePart: '护栏',
      diseaseType: '护栏损坏',
      diseaseStatus: 'planning',
      diseaseLocation: '右侧人行道',
      diseaseCount: 1,
      diseaseDescription: '护栏立柱倾斜，存在安全隐患',
      reporter: '王五',
      reportTime: '2025-07-13 14:20',
      unit: '湖南省交通工程检测中心',
      contactNumber: '15987654321'
    },
    {
      id: 8,
      bridgeName: '福元路大桥',
      reportAttribute: 'inspection',
      diseaseCode: 'FYL202500008',
      diseasePart: '支座',
      diseaseType: '支座老化',
      diseaseStatus: 'disposing',
      diseaseLocation: '2#墩支座',
      diseaseCount: 2,
      diseaseDescription: '橡胶支座出现老化开裂现象',
      reporter: '赵六',
      reportTime: '2025-07-14 10:30',
      unit: '长沙市市政工程公司',
      contactNumber: '13712345678'
    },
    {
      id: 9,
      bridgeName: '湘江大桥',
      reportAttribute: 'superior',
      diseaseCode: 'XJ202500009',
      diseasePart: '桥墩',
      diseaseType: '混凝土剥落',
      diseaseStatus: 'reviewing',
      diseaseLocation: '3#桥墩',
      diseaseCount: 1,
      diseaseDescription: '桥墩表面混凝土出现剥落，露出钢筋',
      reporter: '孙七',
      reportTime: '2025-07-14 16:45',
      unit: '湖南省桥梁检测研究院',
      contactNumber: '18812345678'
    },
    {
      id: 10,
      bridgeName: '营盘路大桥',
      reportAttribute: 'daily',
      diseaseCode: 'YPL202500010',
      diseasePart: '排水系统',
      diseaseType: '排水不畅',
      diseaseStatus: 'archived',
      diseaseLocation: '桥面排水口',
      diseaseCount: 4,
      diseaseDescription: '多处排水口堵塞，影响桥面排水',
      reporter: '周八',
      reportTime: '2025-07-15 08:20',
      unit: '长沙市桥梁管理处',
      contactNumber: '13612345678'
    }
  ],
  total: 24,
  pageNum: 1,
  pageSize: 10
}

// 默认统计数据
export const defaultStatisticsData = {
  // 基础指标
  shouldInspectCount: 1234,
  actualInspectCount: 912,
  completionRate: 74,
  centerInspectCount: 517,
  
  // 病害统计
  totalDamageCount: 512,
  unprocessedCount: 40,
  disposingCount: 60,
  processedCount: 412,
  
  // 趋势数据 - 12个月的数据
  trendData: [
    { month: '1月', count: 120 },
    { month: '2月', count: 95 },
    { month: '3月', count: 140 },
    { month: '4月', count: 168 },
    { month: '5月', count: 145 },
    { month: '6月', count: 190 },
    { month: '7月', count: 210 },
    { month: '8月', count: 185 },
    { month: '9月', count: 195 },
    { month: '10月', count: 175 },
    { month: '11月', count: 155 },
    { month: '12月', count: 165 }
  ],
  
  // 地区巡检数据 - 各区计划巡检与实际巡检对比
  regionData: [
    { region: '岳麓区', planned: 140, actual: 37 },
    { region: '开福区', planned: 180, actual: 128 },
    { region: '天心区', planned: 120, actual: 94 },
    { region: '芙蓉区', planned: 120, actual: 60 },
    { region: '雨花区', planned: 180, actual: 18 }
  ],
  
  // 病害类型分布 - 更丰富的病害类型
  damageTypeData: [
    { type: '抗浮', count: 581 },
    { type: '下沉', count: 140 },
    { type: 'TOP', count: 95 },
    { type: '排包', count: 80 },
    { type: 'a581', count: 65 }
  ],
  
  // TOP10病害桥梁 - 完整的10个桥梁数据
  bridgeRanking: [
    { bridgeName: 'XXXX病害', count: 932 },
    { bridgeName: 'XXXX病害', count: 899 },
    { bridgeName: 'XXXX病害', count: 702 },
    { bridgeName: 'XXXX病害', count: 543 },
    { bridgeName: 'XXXX病害', count: 208 }
  ]
}

// 默认下拉选项数据
export const defaultSelectOptions = {
  // 桥梁名称选项
  bridgeOptions: [
    { value: '1', label: '橘子洲大桥' },
    { value: '2', label: '银盆岭大桥' },
    { value: '3', label: '猴子石大桥' },
    { value: '4', label: '三汊矶大桥' },
    { value: '5', label: '福元路大桥' }
  ],
  
  // 巡检单位选项
  inspectionUnitOptions: [
    { value: '1', label: '长沙市桥梁管理处' },
    { value: '2', label: '湖南省交通工程检测中心' },
    { value: '3', label: '长沙市市政工程公司' },
    { value: '4', label: '湖南省桥梁检测研究院' }
  ],
  
  // 上报属性选项
  reportAttributeOptions: [
    { value: 'daily', label: '日常巡检' },
    { value: 'regular', label: '经常巡检' },
    { value: 'center', label: '中心巡检' },
    { value: 'inspection', label: '定检' },
    { value: 'superior', label: '上级交办' }
  ],
  
  // 病害部位选项
  diseasePartOptions: [
    { value: 'expansion_joint', label: '伸缩缝' },
    { value: 'lighting', label: '照明设施' },
    { value: 'bridge_surface', label: '桥面板' },
    { value: 'bridge_connection', label: '桥路连接位置' },
    { value: 'drainage', label: '排水系统' }
  ],
  
  // 病害类型选项
  diseaseTypeOptions: [
    { value: 'crack', label: '裂缝' },
    { value: 'settlement', label: '沉降' },
    { value: 'expansion_failure', label: '伸缩缝失效' },
    { value: 'lighting_missing', label: '照明设施缺失' },
    { value: 'drainage_block', label: '排水堵塞' }
  ],
  
  // 病害状态选项
  diseaseStatusOptions: [
    { value: 'judging', label: '判定中' },
    { value: 'planning', label: '计划中' },
    { value: 'disposing', label: '处置中' },
    { value: 'reviewing', label: '复核中' },
    { value: 'archived', label: '已归档' }
  ],
  
  // 判定类型选项
  judgeTypeOptions: [
    { value: 'daily_maintenance', label: '日常养护及小修' },
    { value: 'emergency_repair', label: '应急维修' },
    { value: 'out_of_scope', label: '养护范围外' }
  ],
  
  // 复核结果选项
  reviewResultOptions: [
    { value: 'pass', label: '通过' },
    { value: 'reject', label: '不通过' }
  ]
}

/**
 * 获取默认数据的工具函数
 * @param {string} key 数据key
 * @param {object} fallbackData 降级数据
 */
export function getDefaultData(key, fallbackData = null) {
  const dataMap = {
    inspectionRecords: defaultInspectionRecords,
    inspectionCalendar: defaultInspectionCalendar,
    diseaseList: defaultDiseaseList,
    statisticsData: defaultStatisticsData,
    selectOptions: defaultSelectOptions
  }
  
  return dataMap[key] || fallbackData
}

/**
 * API请求失败时的数据处理
 * @param {string} apiName API名称
 * @param {object} params 请求参数
 */
export function handleApiError(apiName, params = {}) {
  console.warn(`API ${apiName} 请求失败，使用默认数据`)
  
  switch (apiName) {
    case 'getInspectionRecords':
      return Promise.resolve({ data: defaultInspectionRecords })
    case 'getInspectionLogs':
      return Promise.resolve({ data: defaultInspectionCalendar })
    case 'getDiseaseList':
      return Promise.resolve({ data: defaultDiseaseList })
    case 'getInspectionStatistics':
      return Promise.resolve({ data: defaultStatisticsData })
    default:
      return Promise.resolve({ data: {} })
  }
}
