/**
 * 模拟数据辅助工具
 * 
 * ⚠️ 注意：这是临时的模拟数据辅助工具，用于前端开发和演示
 * 当后端接口实现后，需要删除此文件
 * 
 * 提供统一的模拟数据管理和切换真实API的便利工具
 */

// 模拟数据开关 - 开发时设为true，生产时设为false
export const USE_MOCK_DATA = process.env.NODE_ENV === 'development'

/**
 * API调用包装器
 * 根据配置决定使用模拟数据还是真实API
 * 
 * @param {Function} mockApiCall - 模拟API调用函数
 * @param {Function} realApiCall - 真实API调用函数  
 * @param {any} params - API参数
 * @returns {Promise} API响应结果
 */
export const apiCall = async (mockApiCall, realApiCall, ...params) => {
  if (USE_MOCK_DATA && mockApiCall) {
    console.log('🔧 [Mock API]', mockApiCall.name, params)
    return await mockApiCall(...params)
  } else if (realApiCall) {
    console.log('🌐 [Real API]', realApiCall.name, params)
    return await realApiCall(...params)
  } else {
    throw new Error('API调用失败：未提供有效的API函数')
  }
}

/**
 * 模拟响应数据格式化
 * 统一模拟数据的响应格式，与后端API保持一致
 */
export const mockResponse = {
  success: (data, message = '操作成功') => ({
    code: 200,
    msg: message,
    data: data
  }),
  
  successWithPaging: (rows, total, message = '查询成功') => ({
    code: 200,
    msg: message,
    rows: rows,
    total: total
  }),
  
  error: (message = '操作失败', code = 500) => ({
    code: code,
    msg: message,
    data: null
  }),
  
  notFound: (message = '数据不存在') => ({
    code: 404,
    msg: message,
    data: null
  }),
  
  forbidden: (message = '权限不足') => ({
    code: 403,
    msg: message,
    data: null
  })
}

/**
 * 模拟用户权限检查
 * TODO: 后端实现后替换为真实权限验证
 */
export const mockPermissionCheck = {
  // 当前模拟用户角色
  currentUserRole: 'company_staff', // admin, bridge_supervisor, company_admin, company_staff
  
  // 检查是否有指定权限
  hasPermission: (permission) => {
    const rolePermissions = {
      admin: ['*'], // 超级管理员拥有所有权限
      bridge_supervisor: [
        'maintenance:project:list',
        'maintenance:project:query',
        'maintenance:project:approve',
        'maintenance:repair:list',
        'maintenance:repair:audit',
        'maintenance:extension:approve'
      ],
      company_admin: [
        'maintenance:project:list',
        'maintenance:project:query',
        'maintenance:project:approve',
        'maintenance:project:export',
        'maintenance:repair:list',
        'maintenance:repair:audit',
        'maintenance:extension:list'
      ],
      company_staff: [
        'maintenance:project:list',
        'maintenance:project:query',
        'maintenance:project:add',
        'maintenance:project:edit',
        'maintenance:project:remove',
        'maintenance:project:submit',
        'maintenance:repair:list',
        'maintenance:repair:execute',
        'maintenance:extension:add'
      ]
    }
    
    const userPermissions = rolePermissions[mockPermissionCheck.currentUserRole] || []
    return userPermissions.includes('*') || userPermissions.includes(permission)
  },
  
  // 检查是否有指定角色
  hasRole: (roles) => {
    if (typeof roles === 'string') {
      return mockPermissionCheck.currentUserRole === roles
    }
    return roles.includes(mockPermissionCheck.currentUserRole)
  },
  
  // 切换模拟用户角色（仅开发环境使用）
  switchRole: (role) => {
    if (process.env.NODE_ENV === 'development') {
      mockPermissionCheck.currentUserRole = role
      console.log('🔄 [Mock] 切换用户角色:', role)
    }
  }
}

/**
 * 模拟文件上传
 * TODO: 后端实现后替换为真实文件上传
 */
export const mockFileUpload = {
  // 模拟文件上传
  uploadFile: async (file) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockFileInfo = {
          id: Date.now(),
          name: file.name,
          size: file.size,
          type: file.type,
          url: URL.createObjectURL(file), // 创建临时URL用于预览
          uploadTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
        }
        resolve(mockResponse.success(mockFileInfo, '文件上传成功'))
      }, 1000) // 模拟上传延迟
    })
  },
  
  // 模拟文件删除
  deleteFile: async (fileId) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockResponse.success(null, '文件删除成功'))
      }, 300)
    })
  }
}

/**
 * 模拟数据生成器
 * 用于生成测试数据
 */
export const mockDataGenerator = {
  // 生成随机ID
  generateId: () => Date.now() + Math.floor(Math.random() * 1000),
  
  // 生成随机姓名
  generateName: () => {
    const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴']
    const names = ['明', '华', '强', '军', '丽', '娜', '敏', '静', '磊', '洋']
    return surnames[Math.floor(Math.random() * surnames.length)] + 
           names[Math.floor(Math.random() * names.length)] + 
           names[Math.floor(Math.random() * names.length)]
  },
  
  // 生成随机手机号
  generatePhone: () => {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139']
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
    const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0')
    return prefix + suffix
  },
  
  // 生成随机日期
  generateDate: (startDate = '2025-01-01', endDate = '2025-12-31') => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime())
    return new Date(randomTime).toISOString().slice(0, 10)
  },
  
  // 生成随机时间
  generateDateTime: (startDate = '2025-01-01', endDate = '2025-12-31') => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime())
    return new Date(randomTime).toISOString().replace('T', ' ').slice(0, 19)
  }
}

/**
 * 开发环境调试工具
 */
export const mockDebugTools = {
  // 在控制台显示当前模拟数据状态
  showStatus: () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 [Mock Data Status]')
      console.log('- 使用模拟数据:', USE_MOCK_DATA)
      console.log('- 当前用户角色:', mockPermissionCheck.currentUserRole)
      console.log('- 环境:', process.env.NODE_ENV)
    }
  },
  
  // 清除所有模拟数据的本地缓存
  clearCache: () => {
    if (process.env.NODE_ENV === 'development') {
      // 清除localStorage中的模拟数据
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('mock_')) {
          localStorage.removeItem(key)
        }
      })
      console.log('🧹 [Mock] 已清除模拟数据缓存')
    }
  },
  
  // 导出当前模拟数据（用于调试）
  exportData: () => {
    if (process.env.NODE_ENV === 'development') {
      import('@/data/mock/maintenance').then(mockData => {
        console.log('📤 [Mock] 当前模拟数据:', mockData.getMockData())
      })
    }
  }
}

// 开发环境自动显示状态
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 [Mock Helper] 模拟数据辅助工具已加载')
  mockDebugTools.showStatus()
}

export default {
  USE_MOCK_DATA,
  apiCall,
  mockResponse,
  mockPermissionCheck,
  mockFileUpload,
  mockDataGenerator,
  mockDebugTools
}
