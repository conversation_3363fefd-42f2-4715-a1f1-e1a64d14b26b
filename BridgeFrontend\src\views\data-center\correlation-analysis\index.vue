<template>
  <div class="correlation-analysis inspection-container">
    <div class="page-container">
      <!-- 桥梁选择 -->
      <div class="filter-section">
        <div class="bridge-selector">
          <span class="bridge-label">选择桥梁:</span>
          <el-select
            v-model="selectedBridge"
            placeholder="请选择桥梁"
            size="small"
            class="bridge-dropdown"
            @change="handleBridgeChange"
          >
            <el-option
              v-for="bridge in bridgeOptions"
              :key="bridge.value"
              :label="bridge.label"
              :value="bridge.value"
            />
          </el-select>
        </div>
      </div>

      <!-- 监测内容1 -->
      <div class="filter-section">
        <div class="section-title">监测内容1:</div>
        <div class="tab-and-selector">
          <!-- 监测类型选项卡 -->
          <div class="monitor-type-tabs">
            <el-button 
              v-for="type in monitorTypes"
              :key="type.value"
              :type="monitorPoint1.type === type.value ? 'primary' : 'default'"
              size="small"
              @click="handleMonitorTypeChange(1, type.value)"
              class="monitor-tab-btn"
            >
              {{ type.label }}
            </el-button>
          </div>
        </div>
        
        <div class="section-title">监测点位1:</div>
        <div class="tab-and-selector">
          <!-- 监测点位选择器 -->
          <div class="monitor-point-selector">
            <el-button 
              v-for="(point, index) in getVisiblePoints(1)"
              :key="point.value"
              :type="monitorPoint1.point === point.value ? 'primary' : 'default'"
              size="small"
              @click="handleMonitorPointChange(1, point.value)"
              class="point-selector-btn"
            >
              {{ point.label }}
            </el-button>
            <el-button 
              v-if="!expandedSections.point1 && monitorPoints.length > visiblePointsCount"
              size="small" 
              type="text" 
              class="more-btn"
            >
              ...
            </el-button>
            <el-button 
              v-if="monitorPoints.length > visiblePointsCount"
              size="small" 
              type="primary" 
              class="expand-btn"
              @click="toggleExpand(1)"
            >
              {{ expandedSections.point1 ? '收起' : '展开' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 监测内容2 -->
      <div class="filter-section">
        <div class="section-title">监测内容2:</div>
        <div class="tab-and-selector">
          <!-- 监测类型选项卡 -->
          <div class="monitor-type-tabs">
            <el-button 
              v-for="type in monitorTypes"
              :key="type.value"
              :type="monitorPoint2.type === type.value ? 'primary' : 'default'"
              size="small"
              @click="handleMonitorTypeChange(2, type.value)"
              class="monitor-tab-btn"
            >
              {{ type.label }}
            </el-button>
          </div>
        </div>
        
        <div class="section-title">监测点位2:</div>
        <div class="tab-and-selector">
          <!-- 监测点位选择器 -->
          <div class="monitor-point-selector">
            <el-button 
              v-for="(point, index) in getVisiblePoints(2)"
              :key="point.value"
              :type="monitorPoint2.point === point.value ? 'primary' : 'default'"
              size="small"
              @click="handleMonitorPointChange(2, point.value)"
              class="point-selector-btn"
            >
              {{ point.label }}
            </el-button>
            <el-button 
              v-if="!expandedSections.point2 && monitorPoints.length > visiblePointsCount"
              size="small" 
              type="text" 
              class="more-btn"
            >
              ...
            </el-button>
            <el-button 
              v-if="monitorPoints.length > visiblePointsCount"
              size="small" 
              type="primary" 
              class="expand-btn"
              @click="toggleExpand(2)"
            >
              {{ expandedSections.point2 ? '收起' : '展开' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 日期选择和操作按钮 -->
      <div class="filter-section">
        <div class="date-inputs">
          <div class="date-input-group">
            <span class="date-label">选择日期:</span>
            <el-date-picker
              v-model="dateRange.start"
              type="date"
              placeholder="开始日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="small"
              class="date-picker"
            />
            <span class="date-separator">-</span>
            <el-date-picker
              v-model="dateRange.end"
              type="date"
              placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="small"
              class="date-picker"
            />
          </div>
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleQuery" :loading="loading">
              查询
            </el-button>
            <el-button size="small" @click="handleReset">
              重置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 已选条件 -->
      <div class="filter-section">
        <div class="condition-title">已选条件:</div>
        <div class="condition-tags">
          <el-tag 
            v-if="monitorPoint1.type"
            size="small" 
            closable
            @close="removeCondition('point1-type')"
            class="condition-tag"
          >
            {{ getMonitorTypeName(monitorPoint1.type) }}
          </el-tag>
          <el-tag 
            v-if="monitorPoint1.point"
            size="small"
            closable
            @close="removeCondition('point1-point')" 
            class="condition-tag"
          >
            {{ getMonitorPointName(monitorPoint1.point) }}
          </el-tag>
          <el-tag 
            v-if="monitorPoint2.type"
            size="small"
            closable
            @close="removeCondition('point2-type')"
            class="condition-tag"
          >
            {{ getMonitorTypeName(monitorPoint2.type) }}
          </el-tag>
          <el-tag 
            v-if="monitorPoint2.point"
            size="small"
            closable
            @close="removeCondition('point2-point')"
            class="condition-tag"
          >
            {{ getMonitorPointName(monitorPoint2.point) }}
          </el-tag>
        </div>
      </div>

      <!-- 分析结果展示区域 -->
      <div class="analysis-result" v-if="showResult">
        <div class="result-header">
          <h3>关联性分析结果</h3>
        </div>
        <div class="result-content">
          <p>分析结果将在这里显示...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CorrelationAnalysis',
  data() {
    return {
      loading: false,
      showResult: false,
      
      // 监测点1配置
      monitorPoint1: {
        type: 'stress', // 默认选择应力应变
        point: 'BPJDQ-RSG-G03-01-01' // 默认选择第一个点
      },
      
      // 监测点2配置
      monitorPoint2: {
        type: 'stress', // 默认选择应力应变
        point: 'BPJDQ-RSG-G03-01-02' // 默认选择第二个点
      },
      
      // 选中的桥梁
      selectedBridge: '',
      
      // 桥梁选项
      bridgeOptions: [
        { label: '白坡大桥', value: 'baipo' },
        { label: '长江大桥', value: 'changjiang' },
        { label: '黄河大桥', value: 'huanghe' },
        { label: '珠江大桥', value: 'zhujiang' }
      ],
      
      // 日期范围
      dateRange: {
        start: '',
        end: ''
      },
      
      // 监测类型选项
      monitorTypes: [
        { label: '主梁变向位移', value: 'displacement' },
        { label: '应力应变', value: 'stress' },
        { label: '梁端位移', value: 'beam-displacement' }
      ],
      
      // 监测点位选项 (模拟数据)
      monitorPoints: [
        { label: 'BPJDQ-RSG-G03-01-01', value: 'BPJDQ-RSG-G03-01-01' },
        { label: 'BPJDQ-RSG-G03-01-02', value: 'BPJDQ-RSG-G03-01-02' },
        { label: 'BPJDQ-RSG-G03-01-03', value: 'BPJDQ-RSG-G03-01-03' },
        { label: 'BPJDQ-RSG-G03-01-04', value: 'BPJDQ-RSG-G03-01-04' },
        { label: 'BPJDQ-RSG-G03-01-05', value: 'BPJDQ-RSG-G03-01-05' },
        { label: 'BPJDQ-RSG-G03-02-01', value: 'BPJDQ-RSG-G03-02-01' },
        { label: 'BPJDQ-RSG-G03-02-02', value: 'BPJDQ-RSG-G03-02-02' },
        { label: 'BPJDQ-RSG-G03-02-03', value: 'BPJDQ-RSG-G03-02-03' },
        { label: 'BPJDQ-RSG-G03-02-04', value: 'BPJDQ-RSG-G03-02-04' },
        { label: 'BPJDQ-RSG-G03-02-05', value: 'BPJDQ-RSG-G03-02-05' }
      ],
      
      // 展开状态控制
      expandedSections: {
        point1: false,
        point2: false
      },
      
      // 可见监测点数量
      visiblePointsCount: 5
    }
  },
  
  methods: {
    // 监测类型选择变化
    handleMonitorTypeChange(pointIndex, typeValue) {
      if (pointIndex === 1) {
        this.monitorPoint1.type = typeValue
      } else {
        this.monitorPoint2.type = typeValue
      }
    },
    
    // 监测点位选择变化
    handleMonitorPointChange(pointIndex, pointValue) {
      if (pointIndex === 1) {
        this.monitorPoint1.point = pointValue
      } else {
        this.monitorPoint2.point = pointValue
      }
    },
    
    // 桥梁选择变化
    handleBridgeChange(value) {
      console.log('选择桥梁:', value)
      // 这里可以根据桥梁选择更新监测点位数据
    },
    
    // 切换展开状态
    toggleExpand(pointIndex) {
      if (pointIndex === 1) {
        this.expandedSections.point1 = !this.expandedSections.point1
      } else {
        this.expandedSections.point2 = !this.expandedSections.point2
      }
    },
    
    // 获取可见的监测点位
    getVisiblePoints(pointIndex) {
      const isExpanded = pointIndex === 1 ? this.expandedSections.point1 : this.expandedSections.point2
      if (isExpanded) {
        return this.monitorPoints
      }
      return this.monitorPoints.slice(0, this.visiblePointsCount)
    },
    
    // 查询操作
    async handleQuery() {
      // 验证必填项
      if (!this.monitorPoint1.type || !this.monitorPoint1.point) {
        this.$message.warning('请选择监测点容1的类型和点位')
        return
      }
      
      if (!this.monitorPoint2.type || !this.monitorPoint2.point) {
        this.$message.warning('请选择监测点容2的类型和点位')
        return
      }
      
      if (!this.dateRange.start || !this.dateRange.end) {
        this.$message.warning('请选择查询日期范围')
        return
      }
      
      this.loading = true
      
      try {
        // 模拟API调用
        await this.fetchCorrelationData()
        this.showResult = true
        this.$message.success('查询成功')
      } catch (error) {
        this.$message.error('查询失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    // 重置操作
    handleReset() {
      this.monitorPoint1 = {
        type: 'stress',
        point: 'BPJDQ-RSG-G03-01-01'
      }
      
      this.monitorPoint2 = {
        type: 'stress', 
        point: 'BPJDQ-RSG-G03-01-02'
      }
      
      this.selectedBridge = ''
      
      this.dateRange = {
        start: '',
        end: ''
      }
      
      this.expandedSections = {
        point1: false,
        point2: false
      }
      
      this.showResult = false
    },
    
    // 移除已选条件
    removeCondition(conditionType) {
      switch (conditionType) {
        case 'point1-type':
          this.monitorPoint1.type = ''
          break
        case 'point1-point':
          this.monitorPoint1.point = ''
          break
        case 'point2-type':
          this.monitorPoint2.type = ''
          break
        case 'point2-point':
          this.monitorPoint2.point = ''
          break
      }
    },
    
    // 获取监测类型名称
    getMonitorTypeName(value) {
      const type = this.monitorTypes.find(t => t.value === value)
      return type ? type.label : value
    },
    
    // 获取监测点位名称  
    getMonitorPointName(value) {
      const point = this.monitorPoints.find(p => p.value === value)
      return point ? point.label : value
    },
    
    // 模拟获取关联性分析数据
    async fetchCorrelationData() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            correlation: 0.85,
            trend: 'positive'
          })
        }, 1500)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 引入公共样式
@import '@/styles/inspection-theme.scss';
@import '@/styles/mixins/inspection-common.scss';

.correlation-analysis {
  // 使用公共样式混入
  @include inspection-page-container;
  
  // 重写筛选区域样式，取消外框，添加横线分隔
  .filter-section {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    margin-bottom: 8px !important;
    padding: 8px 0 !important;
    position: relative;
    
    // 取消伪元素
    &::before,
    &::after {
      display: none !important;
    }
    
    // 添加底部横线分隔
    &:not(:last-child) {
      border-bottom: 1px solid var(--inspection-border) !important;
      padding-bottom: 8px !important;
    }
    
    // 最后一个筛选区域不显示底部横线
    &:last-child {
      border-bottom: none !important;
      margin-bottom: 0 !important;
    }
  }
  
  // 筛选区域标题样式
  .section-title,
  .condition-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--inspection-text-primary);
    margin-bottom: 12px;
    display: inline-flex;
    align-items: center;
    margin-right: 16px;
    min-height: 32px;
  }
  
  .tab-and-selector {
    display: inline-block;
    width: calc(100% - 120px);
  }
  
  // 监测点位选择器样式
  .monitor-type-tabs {
    margin-bottom: 12px;
    
    .monitor-tab-btn {
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 16px;
      padding: 6px 16px;
      font-size: 12px;
    }
  }
  
  .monitor-point-selector {
    .point-selector-btn,
    .more-btn,
    .expand-btn {
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
    }
    
    .more-btn {
      color: var(--inspection-text-muted);
      border: none;
      background: none;
      font-size: 16px;
      font-weight: bold;
      padding: 4px 8px;
    }
    
    .expand-btn {
      background: var(--inspection-success);
      border-color: var(--inspection-success);
      color: white;
    }
  }
  
  // 桥梁选择器样式
  .bridge-selector {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .bridge-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--inspection-text-primary);
      min-width: 80px;
    }
    
    .bridge-dropdown {
      width: 200px;
    }
  }
  
  // 日期输入样式
  .date-inputs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    
    .date-input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .date-label {
        font-size: 14px;
        color: var(--inspection-text-primary);
        font-weight: 600;
      }
      
      .date-picker {
        width: 140px;
      }
      
      .date-separator {
        color: var(--inspection-text-muted);
        margin: 0 4px;
      }
    }
    
    .action-buttons {
      .el-button {
        margin-left: 8px;
      }
    }
  }
  
  // 条件标签样式
  .condition-tags {
    display: inline-block;
    
    .condition-tag {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
  
  // 分析结果样式
  .analysis-result {
    margin-top: 32px;
    padding: 24px;
    background: var(--inspection-card-bg);
    border-radius: 8px;
    border: 1px solid var(--inspection-card-border);
    
    .result-header {
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--inspection-text-primary);
      }
    }
    
    .result-content {
      p {
        margin: 0;
        color: var(--inspection-text-secondary);
        line-height: 1.6;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .correlation-analysis {
    .section-title,
    .condition-title {
      display: block;
      margin-bottom: 8px;
    }
    
    .tab-and-selector {
      display: block;
      width: 100%;
    }
    
    .date-inputs {
      flex-direction: column;
      align-items: flex-start;
      
      .date-input-group {
        width: 100%;
        
        .date-picker {
          flex: 1;
          min-width: 120px;
        }
      }
      
      .action-buttons {
        width: 100%;
        
        .el-button {
          margin-left: 0;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
