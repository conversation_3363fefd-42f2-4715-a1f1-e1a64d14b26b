<!-- 新增/编辑通讯录弹窗组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="formData" :rules="contactRules" ref="contactForm" label-width="120px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="单位" prop="unit">
        <el-select v-model="formData.unit" placeholder="请选择单位" style="width: 100%;">
          <el-option
            v-for="item in unitOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="职务" prop="position">
        <el-input v-model="formData.position" placeholder="请输入职务" style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="电话" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入电话号码" style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息">
        </el-input>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ContactFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    contactData: {
      type: Object,
      default: null
    },
    unitOptions: {
      type: Array,
      default: () => []
    },
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        name: '',
        unit: '',
        position: '',
        phone: '',
        remark: ''
      },
      
      // 表单验证规则
      contactRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请选择单位', trigger: 'change' }
        ],
        position: [
          { required: true, message: '请输入职务', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑通讯录' : '新增通讯录'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    },
    contactData: {
      handler(newVal) {
        if (newVal && this.isEdit) {
          this.initFormData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.isEdit && this.contactData) {
        this.formData = {
          name: this.contactData.name || '',
          unit: this.contactData.unitValue || '',
          position: this.contactData.position || '',
          phone: this.contactData.phone || '',
          remark: this.contactData.remark || ''
        }
      } else {
        this.resetForm()
      }
    },
    
    // 确认提交
    handleConfirm() {
      this.$refs.contactForm.validate(valid => {
        if (valid) {
          const submitData = { ...this.formData }
          if (this.isEdit && this.contactData) {
            submitData.id = this.contactData.id
          }
          this.$emit('confirm', submitData)
        }
      })
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    },
    
    // 重置表单
    resetForm() {
      this.formData = {
        name: '',
        unit: '',
        position: '',
        phone: '',
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.contactForm && this.$refs.contactForm.clearValidate()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
