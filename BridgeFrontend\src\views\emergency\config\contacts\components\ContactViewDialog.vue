<!-- 查看通讯录弹窗组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="contactForm" label-width="120px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="contactForm.name" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="单位" prop="unit">
        <el-input v-model="contactForm.unit" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="职务" prop="position">
        <el-input v-model="contactForm.position" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="电话" prop="phone">
        <el-input v-model="contactForm.phone" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input v-model="contactForm.remark" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作人" prop="operator">
        <el-input v-model="contactForm.operator" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作时间" prop="operateTime">
        <el-input v-model="contactForm.operateTime" readonly style="width: 50%;"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'ContactViewDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 查看数据
    detailData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      contactForm: {
        name: '',
        unit: '',
        position: '',
        phone: '',
        remark: '',
        operator: '',
        operateTime: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      return '查看通讯录'
    }
  },
  watch: {
    // 监听查看数据变化
    detailData: {
      handler(newData) {
        if (newData) {
          this.loadDetailData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.detailData) {
          this.loadDetailData(this.detailData)
        }
      }
    }
  },
  methods: {
    // 加载查看数据
    loadDetailData(data) {
      this.contactForm.name = data.name || ''
      this.contactForm.unit = data.unit || ''
      this.contactForm.position = data.position || ''
      this.contactForm.phone = data.phone || ''
      this.contactForm.remark = data.remark || ''
      this.contactForm.operator = data.operator || ''
      this.contactForm.operateTime = data.operateTime || ''
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>