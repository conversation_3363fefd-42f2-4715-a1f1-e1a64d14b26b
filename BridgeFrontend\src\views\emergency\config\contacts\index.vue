<!-- 通讯录管理 -->
<template>
  <div class="emergency-container inspection-container">
    <div class="page-container">
      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        style="margin-top:21px !important;">
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.unit"
            placeholder="单位"
            clearable
            class="filter-select">
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input
            v-model="formData.name"
            placeholder="请输入姓名"
            clearable
            class="filter-input">
          </el-input>
        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd">
          新增
        </el-button>
        <el-button 
          type="success" 
          icon="el-icon-download" 
          @click="handleImport">
          批量导入
        </el-button>
        <el-button 
          type="info" 
          icon="el-icon-upload2" 
          @click="handleExport">
          导出
        </el-button>
      </div>

      <!-- 通讯录列表表格 -->
      <div class="inspection-table">
        <el-table
          :data="contactsList"
          v-loading="loading"
          stripe
          border
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '32px' }"
          size="small">
          <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" width="120"></el-table-column>
          <el-table-column prop="unit" label="单位" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="position" label="职务" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phone" label="电话" width="140"></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.remark || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" width="120"></el-table-column>
          <el-table-column prop="operateTime" label="操作时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center" class-name="operation-column">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">编辑</el-link>
                <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑通讯录弹窗 -->
    <ContactFormDialog
      :visible.sync="dialogVisible"
      :is-edit="isEdit"
      :contact-data="currentContactData"
      :unit-options="unitOptions"
      :submitting="submitting"
      @confirm="handleFormConfirm"
      @close="handleDialogClose" />

    <!-- 查看通讯录弹窗 -->
    <ContactViewDialog
      :visible.sync="viewDialogVisible"
      :detail-data="currentContactData"
      @close="handleViewDialogClose" />

    <!-- 批量导入弹窗 -->
    <ContactImportDialog
      :visible.sync="importDialogVisible"
      :importing="importing"
      @download-template="handleDownloadTemplate"
      @file-selected="handleImportFileSelected"
      @file-removed="handleImportFileRemoved"
      @import-confirm="handleImportConfirm"
      @close="handleImportDialogClose" />
  </div>
</template>

<script>
import { FilterSection } from '@/components/Inspection'
import ContactViewDialog from './components/ContactViewDialog.vue'
import ContactFormDialog from './components/ContactFormDialog.vue'
import ContactImportDialog from './components/ContactImportDialog.vue'

export default {
  name: 'ContactsConfig',
  components: {
    FilterSection,
    ContactViewDialog,
    ContactFormDialog,
    ContactImportDialog
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      viewDialogVisible: false,
      importDialogVisible: false,
      submitting: false,
      importing: false,
      isEdit: false,
      currentContactData: null,
      
      // 筛选表单
      filterForm: {
        name: '',
        unit: ''
      },
      
      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        unitOptions: []
      },
      
      // 通讯录列表
      contactsList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      // 导入文件
      importFile: null,
      
      // 单位选项
      unitOptions: [
        { value: '1', label: '市城管局' },
        { value: '2', label: '市桥隧事务中心' },
        { value: '3', label: '桥隧公司' },
        { value: '4', label: '市应急和安全生产委员会' },
        { value: '5', label: '交警支队' },
        { value: '6', label: '消防救援支队' }
      ]
    }
  },
  computed: {},
  mounted() {
    this.loadContactsList()
  },
  methods: {
    // 加载通讯录列表
    async loadContactsList() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        const response = await this.mockGetContactsList(params)
        this.contactsList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载通讯录列表失败:', error)
        this.$message.error('加载通讯录列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadContactsList()
    },
    
    // 重置
    handleReset() {
      this.filterForm = {
        name: '',
        unit: ''
      }
      this.pagination.currentPage = 1
      this.loadContactsList()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadContactsList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadContactsList()
    },
    
    // 新增
    handleAdd() {
      this.isEdit = false
      this.currentContactData = null
      this.dialogVisible = true
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.currentContactData = row
      this.dialogVisible = true
    },
    
    // 查看
    handleView(row) {
      this.currentContactData = row
      this.viewDialogVisible = true
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm(`确认要删除联系人"${row.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.mockDeleteContact(row.id)
          this.$message.success('删除成功')
          this.loadContactsList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    
    // 批量导入
    handleImport() {
      this.importDialogVisible = true
      this.importFile = null
    },
    
    // 导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },
    
    // 表单确认提交
    async handleFormConfirm(formData) {
      this.submitting = true
      try {
        if (this.isEdit) {
          await this.mockUpdateContact(formData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreateContact(formData)
          this.$message.success('新增成功')
        }
        
        this.dialogVisible = false
        this.loadContactsList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.currentContactData = null
    },
    
    handleViewDialogClose() {
      this.viewDialogVisible = false
      this.currentContactData = null
    },
    
    handleImportDialogClose() {
      this.importDialogVisible = false
      this.importFile = null
    },
    
    // 下载模板文件
    handleDownloadTemplate() {
      // 创建模板数据
      const templateData = [
        ['姓名', '单位', '职务', '电话', '备注'],
        ['张三', '市城管局', '科长', '13800138001', '示例数据'],
        ['李四', '市桥隧事务中心', '主任', '13800138002', '示例数据']
      ]
      
      // 这里应该生成真实的Excel文件
      this.$message.success('模板文件下载成功')
    },
    
    // 导入文件处理
    handleImportFileSelected(file) {
      this.importFile = file
    },
    
    handleImportFileRemoved() {
      this.importFile = null
    },
    
    // 确认导入
    async handleImportConfirm(file) {
      this.importing = true
      try {
        await this.mockImportContacts(file)
        this.$message.success('导入成功')
        this.importDialogVisible = false
        this.loadContactsList()
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.importing = false
      }
    },
    
    // 模拟API方法
    async mockGetContactsList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              name: '江轩舟',
              unit: '市城管局',
              unitValue: '1',
              position: '市政处处长',
              phone: '15057168395',
              remark: '',
              operator: '江轩舟',
              operateTime: '2025-08-19 12:03:30'
            },
            {
              id: 2,
              name: '李星辰',
              unit: '市城管局',
              unitValue: '1',
              position: '督查督办处处长',
              phone: '19520278394',
              remark: '督查督办工作联系人',
              operator: '李明宇',
              operateTime: '2025-08-19 11:33:30'
            },
            {
              id: 3,
              name: '王继远',
              unit: '市城管局',
              unitValue: '1',
              position: '办公室主任',
              phone: '19557169683',
              remark: '办公室日常事务处理',
              operator: '陈洛凡',
              operateTime: '2025-08-19 11:02:30'
            }
          ]
          
          let filteredData = mockData
          if (params.name) {
            filteredData = filteredData.filter(item => 
              item.name.includes(params.name)
            )
          }
          if (params.unit) {
            filteredData = filteredData.filter(item => 
              item.unitValue === params.unit
            )
          }
          
          resolve({
            data: filteredData,
            total: filteredData.length
          })
        }, 500)
      })
    },
    
    async mockCreateContact(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建联系人:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdateContact(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新联系人:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeleteContact(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除联系人:', id)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockImportContacts(file) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('导入联系人文件:', file.name)
          resolve({ success: true, count: 10 })
        }, 2000)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

// 样式已移至各个弹窗组件中
</style>





