<!-- 项目部新增/编辑弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="formData" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="项目部名称" prop="departmentName">
        <el-input v-model="formData.departmentName" placeholder="请输入项目部名称" style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="联系人" prop="contactName">
        <el-input v-model="formData.contactName" placeholder="请输入联系人" style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="电话" prop="contactPhone">
        <el-input v-model="formData.contactPhone" placeholder="请输入电话号码" style="width: 70%;"></el-input>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DepartmentFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    departmentData: {
      type: Object,
      default: null
    },
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        departmentName: '',
        contactName: '',
        contactPhone: ''
      },
      rules: {
        departmentName: [
          { required: true, message: '请输入项目部名称', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑项目部' : '新增项目部'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    },
    departmentData: {
      handler(newVal) {
        if (newVal && this.isEdit) {
          this.initFormData()
        }
      },
      immediate: true
    }
  },
  methods: {
    initFormData() {
      if (this.isEdit && this.departmentData) {
        this.formData = {
          departmentName: this.departmentData.departmentName || '',
          contactName: this.departmentData.contactName || '',
          contactPhone: this.departmentData.contactPhone || ''
        }
      } else {
        this.resetForm()
      }
    },
    
    resetForm() {
      this.formData = {
        departmentName: '',
        contactName: '',
        contactPhone: ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const submitData = { ...this.formData }
          if (this.isEdit && this.departmentData) {
            submitData.id = this.departmentData.id
          }
          this.$emit('confirm', submitData)
        }
      })
    },
    
    handleClose() {
      this.$emit('close')
    },
    
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
