<!-- 项目部查看弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="departmentForm" label-width="120px">
      <el-form-item label="项目部名称" prop="departmentName">
        <el-input v-model="departmentForm.departmentName" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="联系人" prop="contactName">
        <el-input v-model="departmentForm.contactName" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="联系方式" prop="contactPhone">
        <el-input v-model="departmentForm.contactPhone" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="设备数量" prop="equipmentCount">
        <el-input v-model="equipmentCountDisplay" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作人" prop="operator">
        <el-input v-model="departmentForm.operator" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作时间" prop="operateTime">
        <el-input v-model="departmentForm.operateTime" readonly style="width: 70%;"></el-input>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'DepartmentViewDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 查看数据
    detailData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      departmentForm: {
        departmentName: '',
        contactName: '',
        contactPhone: '',
        equipmentCount: '',
        operator: '',
        operateTime: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      return '查看项目部信息'
    },
    equipmentCountDisplay() {
      return this.departmentForm.equipmentCount ? `${this.departmentForm.equipmentCount} 类` : ''
    }
  },
  watch: {
    // 监听查看数据变化
    detailData: {
      handler(newData) {
        if (newData) {
          this.loadDetailData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.detailData) {
          this.loadDetailData(this.detailData)
        }
      }
    }
  },
  methods: {
    // 加载查看数据
    loadDetailData(data) {
      this.departmentForm.departmentName = data.departmentName || ''
      this.departmentForm.contactName = data.contactName || ''
      this.departmentForm.contactPhone = data.contactPhone || ''
      this.departmentForm.equipmentCount = data.equipmentCount || ''
      this.departmentForm.operator = data.operator || ''
      this.departmentForm.operateTime = data.operateTime || ''
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
