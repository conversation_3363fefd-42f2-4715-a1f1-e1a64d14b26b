<!-- 设备新增/编辑弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="formData" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="formData.equipmentName" placeholder="请输入设备名称" style="width: 70%;"></el-input>
      </el-form-item>
      
      <el-form-item label="数量" prop="quantity">
          <el-input v-model="formData.quantity" placeholder="请输入数量" style="width: 70%;"></el-input>
      <!--
        <el-input-number 
          v-model="formData.quantity" 
          :min="1" 
          :max="9999"
          style="width: 70%;">
        </el-input-number>
      -->
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EquipmentFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    equipmentData: {
      type: Object,
      default: null
    },
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        equipmentName: '',
        quantity: 1
      },
      rules: {
        equipmentName: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑设备' : '新增设备'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    },
    equipmentData: {
      handler(newVal) {
        if (newVal && this.isEdit) {
          this.initFormData()
        }
      },
      immediate: true
    }
  },
  methods: {
    initFormData() {
      if (this.isEdit && this.equipmentData) {
        this.formData = {
          equipmentName: this.equipmentData.equipmentName || '',
          quantity: this.equipmentData.quantity || 1
        }
      } else {
        this.resetForm()
      }
    },
    
    resetForm() {
      this.formData = {
        equipmentName: '',
        quantity: 1
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const submitData = { ...this.formData }
          if (this.isEdit && this.equipmentData) {
            submitData.id = this.equipmentData.id
          }
          this.$emit('confirm', submitData)
        }
      })
    },
    
    handleClose() {
      this.$emit('close')
    },
    
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
