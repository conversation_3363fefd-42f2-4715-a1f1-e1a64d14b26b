<!-- 设备明细弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="450px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <div class="equipment-actions">
      <el-button type="primary" icon="el-icon-plus" @click="handleAddEquipment">
        新增设备
      </el-button>
    </div>
    
    <div class="inspection-table">
      <el-table
        :data="equipmentList"
        stripe
        border
        style="width: 100%; min-width: 400px; margin-top: 15px;"
        :row-style="{ height: '32px' }"
        size="small">
        <el-table-column prop="equipmentName" label="设备名称" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" align="center"></el-table-column>
        <el-table-column label="操作" width="150" align="center" class-name="operation-column">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-link @click="handleViewEquipmentDetail(scope.row)" type="primary" :underline="false">查看</el-link>
              <el-link @click="handleEditEquipment(scope.row)" type="primary" :underline="false">编辑</el-link>
              <el-link @click="handleDeleteEquipment(scope.row)" type="danger" :underline="false">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
    
      <!-- 设备列表分页 -->
      <div class="pagination-wrapper inspection-pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
    
    <!-- 设备查看详情弹窗 -->
    <EquipmentViewDialog
      :visible.sync="equipmentViewVisible"
      :equipment-data="currentEquipmentData"
      @close="handleEquipmentViewClose" />
  </el-dialog>
</template>

<script>
import EquipmentViewDialog from './EquipmentViewDialog.vue'

export default {
  name: 'EquipmentListDialog',
  components: {
    EquipmentViewDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    departmentData: {
      type: Object,
      default: null
    },
    equipmentList: {
      type: Array,
      default: () => []
    },
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        total: 0
      })
    }
  },
  data() {
    return {
      equipmentViewVisible: false,
      currentEquipmentData: null
    }
  },
  computed: {
    dialogTitle() {
      return this.departmentData ? this.departmentData.departmentName : '设备明细'
    }
  },
  methods: {
    handleAddEquipment() {
      this.$emit('add-equipment')
    },
    
    handleEditEquipment(row) {
      this.$emit('edit-equipment', row)
    },
    
    handleViewEquipmentDetail(row) {
      this.currentEquipmentData = row
      this.equipmentViewVisible = true
    },
    
    handleEquipmentViewClose() {
      this.equipmentViewVisible = false
      this.currentEquipmentData = null
    },
    
    handleDeleteEquipment(row) {
      this.$confirm(`确认要删除设备"${row.equipmentName}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-equipment', row)
      }).catch(() => {
        // 用户取消删除
      })
    },
    
    handleSizeChange(size) {
      this.$emit('size-change', size)
    },
    
    handlePageChange(page) {
      this.$emit('page-change', page)
    },
    
    handleClose() {
      this.$emit('close')
    },
    
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

.equipment-actions {
  text-align: left;
}

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
