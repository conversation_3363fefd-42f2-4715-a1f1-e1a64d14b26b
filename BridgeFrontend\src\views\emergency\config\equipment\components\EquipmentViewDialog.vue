<!-- 设备查看详情弹窗 -->
<template>
  <el-dialog
    title="设备详情"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="equipmentForm" label-width="120px" v-if="equipmentData">
      <!-- 设备名称 -->
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="equipmentForm.equipmentName" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 数量 -->
      <el-form-item label="数量" prop="quantity">
        <el-input v-model="equipmentForm.quantity" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 设备编号 -->
      <el-form-item label="设备编号" prop="equipmentCode" v-if="equipmentForm.equipmentCode">
        <el-input v-model="equipmentForm.equipmentCode" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 设备类型 -->
      <el-form-item label="设备类型" prop="equipmentType" v-if="equipmentForm.equipmentType">
        <el-input v-model="equipmentForm.equipmentType" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 规格型号 -->
      <el-form-item label="规格型号" prop="specification" v-if="equipmentForm.specification">
        <el-input v-model="equipmentForm.specification" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 生产厂家 -->
      <el-form-item label="生产厂家" prop="manufacturer" v-if="equipmentForm.manufacturer">
        <el-input v-model="equipmentForm.manufacturer" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 购置日期 -->
      <el-form-item label="购置日期" prop="purchaseDate" v-if="equipmentForm.purchaseDate">
        <el-input v-model="equipmentForm.purchaseDate" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 使用状态 -->
      <el-form-item label="使用状态" prop="status" v-if="equipmentForm.status">
        <div style="width: 70%;">
          <el-tag :type="getStatusType(equipmentForm.status)" size="small">
            {{ getStatusText(equipmentForm.status) }}
          </el-tag>
        </div>
      </el-form-item>
      
      <!-- 存放位置 -->
      <el-form-item label="存放位置" prop="location" v-if="equipmentForm.location">
        <div style="width: 70%;">
          <i class="el-icon-location" style="color: #409EFF; margin-right: 5px;"></i>
          <span>{{ equipmentForm.location }}</span>
        </div>
      </el-form-item>
      
      <!-- 负责人 -->
      <el-form-item label="负责人" prop="responsible" v-if="equipmentForm.responsible">
        <el-input v-model="equipmentForm.responsible" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 联系电话 -->
      <el-form-item label="联系电话" prop="phone" v-if="equipmentForm.phone">
        <el-input v-model="equipmentForm.phone" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 备注说明 -->
      <el-form-item label="备注说明" prop="remark" v-if="equipmentForm.remark">
        <el-input 
          type="textarea" 
          v-model="equipmentForm.remark" 
          readonly 
          :rows="3"
          style="width: 70%;"
        ></el-input>
      </el-form-item>
      
      <!-- 创建时间 -->
      <el-form-item label="创建时间" prop="createTime" v-if="equipmentForm.createTime">
        <el-input v-model="equipmentForm.createTime" readonly style="width: 70%;"></el-input>
      </el-form-item>
      
      <!-- 更新时间 -->
      <el-form-item label="更新时间" prop="updateTime" v-if="equipmentForm.updateTime">
        <el-input v-model="equipmentForm.updateTime" readonly style="width: 70%;"></el-input>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EquipmentViewDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    equipmentData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      equipmentForm: {
        equipmentName: '',
        quantity: '',
        equipmentCode: '',
        equipmentType: '',
        specification: '',
        manufacturer: '',
        purchaseDate: '',
        status: '',
        location: '',
        responsible: '',
        phone: '',
        remark: '',
        createTime: '',
        updateTime: ''
      }
    }
  },
  watch: {
    // 监听设备数据变化
    equipmentData: {
      handler(newData) {
        if (newData) {
          this.loadEquipmentData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.equipmentData) {
          this.loadEquipmentData(this.equipmentData)
        }
      }
    }
  },
  methods: {
    // 加载设备数据
    loadEquipmentData(data) {
      this.equipmentForm.equipmentName = data.equipmentName || '-'
      this.equipmentForm.quantity = data.quantity || '0'
      this.equipmentForm.equipmentCode = data.equipmentCode || ''
      this.equipmentForm.equipmentType = data.equipmentType || ''
      this.equipmentForm.specification = data.specification || ''
      this.equipmentForm.manufacturer = data.manufacturer || ''
      this.equipmentForm.purchaseDate = data.purchaseDate || ''
      this.equipmentForm.status = data.status || ''
      this.equipmentForm.location = data.location || ''
      this.equipmentForm.responsible = data.responsible || ''
      this.equipmentForm.phone = data.phone || ''
      this.equipmentForm.remark = data.remark || ''
      this.equipmentForm.createTime = data.createTime || ''
      this.equipmentForm.updateTime = data.updateTime || ''
    },
    
    handleClose() {
      this.$emit('close')
    },
    
    handleDialogClose() {
      this.$emit('close')
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'normal': 'success',
        'maintain': 'warning', 
        'repair': 'danger',
        'scrap': 'info'
      }
      return statusMap[status] || 'primary'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'normal': '正常',
        'maintain': '维护中',
        'repair': '维修中', 
        'scrap': '报废'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */

/* 设备状态标签样式 */
.el-tag {
  margin-left: 0;
}

/* 位置图标样式 */
.el-icon-location {
  font-size: 14px;
}
</style>
