<!-- 设备管理 -->
<template>
  <div class="emergency-container inspection-container">
    <div class="page-container">
      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        style="margin-top:21px !important;">
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.department"
            placeholder="项目部"
            clearable
            class="filter-select">
            <el-option
              v-for="item in departmentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-model="formData.contact"
            placeholder="联系人"
            clearable
            class="filter-select">
            <el-option
              v-for="item in contactOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd">
          新增项目部
        </el-button>
      </div>

      <!-- 项目部列表表格 -->
      <div class="inspection-table">
        <el-table
          :data="departmentList"
          v-loading="loading"
          stripe
          border
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '32px' }"
          size="small">
          <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="departmentCode" label="编号" width="120" align="center"></el-table-column>
          <el-table-column prop="departmentName" label="项目部" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="设备明细" width="100" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                @click="handleViewEquipment(scope.row)"
                style="color: #409EFF; font-weight: bold;">
                {{ scope.row.equipmentCount }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="contactName" label="联系人" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="contactPhone" label="联系方式" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operator" label="操作人" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operateTime" label="操作时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center" class-name="operation-column">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">编辑</el-link>
                <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑项目部弹窗 -->
    <DepartmentFormDialog
      :visible.sync="dialogVisible"
      :is-edit="isEdit"
      :department-data="currentDepartmentData"
      :submitting="submitting"
      @confirm="handleFormConfirm"
      @close="handleDialogClose" />

    <!-- 查看项目部弹窗 -->
    <DepartmentViewDialog
      :visible.sync="viewDialogVisible"
      :detail-data="currentDepartmentData"
      @close="handleViewDialogClose" />

    <!-- 设备明细弹窗 -->
    <EquipmentListDialog
      :visible.sync="equipmentDialogVisible"
      :department-data="currentDepartmentData"
      :equipment-list="equipmentList"
      :pagination="equipmentPagination"
      @add-equipment="handleAddEquipment"
      @edit-equipment="handleEditEquipment"
      @view-equipment-detail="handleViewEquipmentDetail"
      @delete-equipment="handleDeleteEquipmentConfirm"
      @size-change="handleEquipmentSizeChange"
      @page-change="handleEquipmentPageChange"
      @close="handleEquipmentDialogClose" />

    <!-- 新增/编辑设备弹窗 -->
    <EquipmentFormDialog
      :visible.sync="equipmentFormVisible"
      :is-edit="isEquipmentEdit"
      :equipment-data="currentEquipmentData"
      :submitting="equipmentSubmitting"
      @confirm="handleEquipmentFormConfirm"
      @close="handleEquipmentFormClose" />
  </div>
</template>

<script>
import { FilterSection } from '@/components/Inspection'
import DepartmentFormDialog from './components/DepartmentFormDialog.vue'
import DepartmentViewDialog from './components/DepartmentViewDialog.vue'
import EquipmentListDialog from './components/EquipmentListDialog.vue'
import EquipmentFormDialog from './components/EquipmentFormDialog.vue'

export default {
  name: 'EquipmentConfig',
  components: {
    FilterSection,
    DepartmentFormDialog,
    DepartmentViewDialog,
    EquipmentListDialog,
    EquipmentFormDialog
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      viewDialogVisible: false,
      equipmentDialogVisible: false,
      equipmentFormVisible: false,
      submitting: false,
      equipmentSubmitting: false,
      isEdit: false,
      isEquipmentEdit: false,
      currentDepartmentData: null,
      currentEquipmentData: null,
      
      // 筛选表单
      filterForm: {
        department: '',
        contact: ''
      },
      
      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        departmentOptions: [],
        contactOptions: []
      },
      
      // 项目部列表
      departmentList: [],
      
      // 设备列表
      equipmentList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      // 设备分页
      equipmentPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      
      // 项目部选项
      departmentOptions: [
        { value: '1', label: '应急抢险项目部' },
        { value: '2', label: '桥梁维护项目部' },
        { value: '3', label: '隧道维护项目部' }
      ],
      
      // 联系人选项
      contactOptions: [
        { value: '1', label: '江轩舟' },
        { value: '2', label: '李明宇' },
        { value: '3', label: '陈洛凡' }
      ]
    }
  },
  mounted() {
    this.loadDepartmentList()
  },
  methods: {
    // 加载项目部列表
    async loadDepartmentList() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        const response = await this.mockGetDepartmentList(params)
        this.departmentList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载项目部列表失败:', error)
        this.$message.error('加载项目部列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载设备列表
    async loadEquipmentList(departmentId) {
      try {
        const params = {
          departmentId,
          page: this.equipmentPagination.currentPage,
          size: this.equipmentPagination.pageSize
        }
        
        const response = await this.mockGetEquipmentList(params)
        this.equipmentList = response.data
        this.equipmentPagination.total = response.total
      } catch (error) {
        console.error('加载设备列表失败:', error)
        this.$message.error('加载设备列表失败')
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadDepartmentList()
    },
    
    // 重置
    handleReset() {
      this.filterForm = {
        department: '',
        contact: ''
      }
      this.pagination.currentPage = 1
      this.loadDepartmentList()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadDepartmentList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadDepartmentList()
    },
    
    handleEquipmentSizeChange(size) {
      this.equipmentPagination.pageSize = size
      this.equipmentPagination.currentPage = 1
      this.loadEquipmentList(this.currentDepartmentData.id)
    },
    
    handleEquipmentPageChange(page) {
      this.equipmentPagination.currentPage = page
      this.loadEquipmentList(this.currentDepartmentData.id)
    },
    
    // 新增项目部
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },
    
    // 编辑项目部
    handleEdit(row) {
      this.isEdit = true
      this.currentDepartmentData = row
      this.dialogVisible = true
      this.departmentForm = {
        departmentName: row.departmentName,
        contactName: row.contactName,
        contactPhone: row.contactPhone
      }
    },
    
    // 查看项目部
    handleView(row) {
      this.currentDepartmentData = row
      this.viewDialogVisible = true
    },
    
    // 查看设备明细
    handleViewEquipment(row) {
      this.currentDepartmentData = row
      this.equipmentDialogVisible = true
      this.equipmentPagination.currentPage = 1
      this.loadEquipmentList(row.id)
    },
    
    // 删除项目部
    handleDelete(row) {
      this.$confirm(`确认要删除项目部"${row.departmentName}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.mockDeleteDepartment(row.id)
          this.$message.success('删除成功')
          this.loadDepartmentList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    
    // 新增设备
    handleAddEquipment() {
      this.isEquipmentEdit = false
      this.equipmentFormVisible = true
      this.resetEquipmentForm()
    },
    
    // 编辑设备
    handleEditEquipment(row) {
      this.isEquipmentEdit = true
      this.currentEquipmentData = row
      this.equipmentFormVisible = true
      this.equipmentForm = {
        equipmentName: row.equipmentName,
        quantity: row.quantity
      }
    },
    
    // 查看设备详情
    handleViewEquipmentDetail(row) {
      this.$message.info('查看设备详情功能开发中...')
    },
    
    // 删除设备确认
    async handleDeleteEquipmentConfirm(row) {
      try {
        await this.mockDeleteEquipment(row.id)
        this.$message.success('删除成功')
        this.loadEquipmentList(this.currentDepartmentData.id)
      } catch (error) {
        this.$message.error('删除失败')
      }
    },
    
    // 项目部表单确认提交
    async handleFormConfirm(formData) {
      this.submitting = true
      try {
        if (this.isEdit) {
          await this.mockUpdateDepartment(formData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreateDepartment(formData)
          this.$message.success('新增成功')
        }
        
        this.dialogVisible = false
        this.loadDepartmentList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    // 设备表单确认提交
    async handleEquipmentFormConfirm(formData) {
      this.equipmentSubmitting = true
      try {
        const submitData = {
          ...formData,
          departmentId: this.currentDepartmentData.id
        }
        
        if (this.isEquipmentEdit) {
          await this.mockUpdateEquipment(submitData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreateEquipment(submitData)
          this.$message.success('新增成功')
        }
        
        this.equipmentFormVisible = false
        this.loadEquipmentList(this.currentDepartmentData.id)
        // 更新项目部列表中的设备数量
        this.loadDepartmentList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.equipmentSubmitting = false
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.currentDepartmentData = null
    },
    
    handleViewDialogClose() {
      this.viewDialogVisible = false
      this.currentDepartmentData = null
    },
    
    handleEquipmentDialogClose() {
      this.equipmentDialogVisible = false
      this.currentDepartmentData = null
      this.equipmentList = []
    },
    
    handleEquipmentFormClose() {
      this.equipmentFormVisible = false
      this.currentEquipmentData = null
    },
    
    
    // 模拟API方法
    async mockGetDepartmentList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              departmentCode: 'XMB001',
              departmentName: '应急抢险项目部',
              equipmentCount: 7,
              contactName: '江轩舟',
              contactPhone: '19922218593',
              operator: '江轩舟',
              operateTime: '2025-08-19 12:03:30'
            },
            {
              id: 2,
              departmentCode: 'XMB002',
              departmentName: '桥梁维护项目部',
              equipmentCount: 5,
              contactName: '李明宇',
              contactPhone: '15920034817',
              operator: '李明宇',
              operateTime: '2025-08-19 11:33:30'
            },
            {
              id: 3,
              departmentCode: 'XMB003',
              departmentName: '隧道维护项目部',
              equipmentCount: 8,
              contactName: '陈洛凡',
              contactPhone: '13720318495',
              operator: '陈洛凡',
              operateTime: '2025-08-19 11:02:30'
            }
          ]
          
          let filteredData = mockData
          if (params.department) {
            filteredData = filteredData.filter(item => 
              item.departmentName.includes(params.department)
            )
          }
          if (params.contact) {
            filteredData = filteredData.filter(item => 
              item.contactName.includes(params.contact)
            )
          }
          
          resolve({
            data: filteredData,
            total: filteredData.length
          })
        }, 500)
      })
    },
    
    async mockGetEquipmentList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            { id: 1, equipmentName: '发电车（355kw）', quantity: 60 },
            { id: 2, equipmentName: '发电机组（100kw）', quantity: 6 },
            { id: 3, equipmentName: '发电机组（50kw）', quantity: 10 },
            { id: 4, equipmentName: '载货车', quantity: 1 },
            { id: 5, equipmentName: '炉车', quantity: 89 },
            { id: 6, equipmentName: '推车', quantity: 62 },
            { id: 7, equipmentName: '抽水泵', quantity: 25 }
          ]
          
          resolve({
            data: mockData,
            total: mockData.length
          })
        }, 300)
      })
    },
    
    async mockCreateDepartment(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建项目部:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdateDepartment(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新项目部:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeleteDepartment(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除项目部:', id)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockCreateEquipment(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建设备:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdateEquipment(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新设备:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeleteEquipment(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除设备:', id)
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

// 样式已移至各个弹窗组件中
</style>





