<!-- 新增/编辑专家弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="600px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="expertForm" :rules="expertRules" ref="expertForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="expertForm.name" placeholder="请输入姓名" style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select v-model="expertForm.gender" placeholder="请选择" style="width: 100%;">
              <el-option label="男" value="1"></el-option>
              <el-option label="女" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出生年月" prop="birthDate">
            <el-date-picker
              v-model="expertForm.birthDate"
              type="month"
              placeholder="选择年月"
              format="yyyy年MM月"
              value-format="yyyy-MM"
              style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="expertForm.phone" placeholder="请输入电话号码" style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="工作单位及职称" prop="unitTitle">
        <el-input v-model="expertForm.unitTitle" placeholder="请输入工作单位及职称" style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专业" prop="specialty">
            <el-select v-model="expertForm.specialty" placeholder="请选择专业" style="width: 100%;">
              <el-option
                v-for="item in specialtyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-select v-model="expertForm.education" placeholder="请选择学历" style="width: 100%;">
              <el-option label="博士" value="1"></el-option>
              <el-option label="硕士" value="2"></el-option>
              <el-option label="本科" value="3"></el-option>
              <el-option label="专科" value="4"></el-option>
              <el-option label="其他" value="5"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="专业技术职务" prop="technicalTitle">
        <el-input v-model="expertForm.technicalTitle" placeholder="请输入专业技术职务" style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="主要研究领域">
        <el-input
          v-model="expertForm.researchField"
          type="textarea"
          :rows="3"
          placeholder="请输入主要研究领域">
        </el-input>
      </el-form-item>
      
      <el-form-item label="工作经历">
        <el-input
          v-model="expertForm.workExperience"
          type="textarea"
          :rows="3"
          placeholder="请输入工作经历">
        </el-input>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input
          v-model="expertForm.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息">
        </el-input>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExpertFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    expertData: {
      type: Object,
      default: null
    },
    specialtyOptions: {
      type: Array,
      default: () => []
    },
    submitting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      expertForm: {
        name: '',
        gender: '',
        birthDate: '',
        phone: '',
        unitTitle: '',
        specialty: '',
        education: '',
        technicalTitle: '',
        researchField: '',
        workExperience: '',
        remark: ''
      },
      
      // 表单验证规则
      expertRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入电话号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        unitTitle: [
          { required: true, message: '请输入工作单位及职称', trigger: 'blur' }
        ],
        specialty: [
          { required: true, message: '请选择专业', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑专家信息' : '新增专家'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData()
      } else {
        this.resetForm()
      }
    },
    expertData: {
      handler(newVal) {
        if (newVal && this.isEdit) {
          this.initFormData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.isEdit && this.expertData) {
        this.expertForm = {
          name: this.expertData.name || '',
          gender: this.expertData.gender || '',
          birthDate: this.expertData.birthDate || '',
          phone: this.expertData.phone || '',
          unitTitle: this.expertData.unitTitle || '',
          specialty: this.expertData.specialtyValue || '',
          education: this.expertData.education || '',
          technicalTitle: this.expertData.technicalTitle || '',
          researchField: this.expertData.researchField || '',
          workExperience: this.expertData.workExperience || '',
          remark: this.expertData.remark || ''
        }
      } else {
        this.resetForm()
      }
    },
    
    // 重置表单
    resetForm() {
      this.expertForm = {
        name: '',
        gender: '',
        birthDate: '',
        phone: '',
        unitTitle: '',
        specialty: '',
        education: '',
        technicalTitle: '',
        researchField: '',
        workExperience: '',
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.expertForm && this.$refs.expertForm.clearValidate()
      })
    },
    
    // 确认提交
    handleConfirm() {
      this.$refs.expertForm.validate(valid => {
        if (valid) {
          const submitData = { ...this.expertForm }
          if (this.isEdit && this.expertData) {
            submitData.id = this.expertData.id
          }
          this.$emit('confirm', submitData)
        }
      })
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
