<!-- 批量导入专家弹窗组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="600px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <div class="emergency-import-content">
      <div class="emergency-import-tips">
        <h4>导入说明：</h4>
        <ol>
          <li>请下载模板文件，按照模板格式填写数据</li>
          <li>支持 Excel (.xlsx, .xls) 格式文件</li>
          <li>单次最多导入 1000 条记录</li>
          <li>必填字段：姓名、工作单位及职称、电话、专业</li>
        </ol>
        <el-button type="text" @click="handleDownloadTemplate">
          <i class="el-icon-download"></i>
          下载模板文件
        </el-button>
      </div>
      
      <!-- <el-divider></el-divider> -->
      
      <div class="file-upload">
        <el-upload
          class="upload-demo"
          action=""
          :http-request="handleFileUpload"
          :file-list="fileList"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :limit="1"
          accept=".xlsx,.xls">
          
          <el-link type="primary" :underline="false" style="display: flex; align-items: center; justify-content: center; flex-direction: column; gap: 8px;">
              <svg-icon icon-class="emergency-upload" />
              <span style="color: white;">将文件拖拽或点击上传</span>
            </el-link>
          <div slot="tip" class="el-upload__tip">
            只能上传 Excel 文件，且不超过 5MB
          </div>
        </el-upload>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleImportConfirm" 
        :loading="importing"
        :disabled="fileList.length === 0">
        开始导入
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExpertImportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    importing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: []
    }
  },
  computed: {
    dialogTitle() {
      return '批量导入专家'
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.resetFileList()
      }
    }
  },
  methods: {
    // 下载模板文件
    handleDownloadTemplate() {
      this.$emit('download-template')
    },
    
    // 文件上传
    handleFileUpload(params) {
      const file = params.file
      this.fileList = [{
        name: file.name,
        uid: Date.now(),
        raw: file
      }]
      this.$emit('file-selected', file)
    },
    
    // 移除文件
    handleFileRemove() {
      this.fileList = []
      this.$emit('file-removed')
    },
    
    // 上传前验证
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel'
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isExcel) {
        this.$message.error('只能上传 Excel 格式的文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      return true
    },
    
    // 确认导入
    handleImportConfirm() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要导入的文件')
        return
      }
      
      const file = this.fileList[0].raw
      this.$emit('import-confirm', file)
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    },
    
    // 重置文件列表
    resetFileList() {
      this.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

.file-upload {
  text-align: center;
  padding: 20px 0;
}
</style>
