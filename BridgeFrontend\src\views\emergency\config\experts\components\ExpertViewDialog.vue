<!-- 查看专家弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="600px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="expertForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="expertForm.name" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-input v-model="expertForm.genderText" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="出生年月" prop="birthDate">
            <el-input v-model="expertForm.birthDate" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="expertForm.phone" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="工作单位及职称" prop="unitTitle">
        <el-input v-model="expertForm.unitTitle" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="专业" prop="specialty">
            <el-input v-model="expertForm.specialty" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="education">
            <el-input v-model="expertForm.educationText" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="专业技术职务" prop="technicalTitle">
        <el-input v-model="expertForm.technicalTitle" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="主要研究领域" prop="researchField">
        <el-input v-model="expertForm.researchField" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="工作经历" prop="workExperience">
        <el-input v-model="expertForm.workExperience" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input v-model="expertForm.remark" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="操作人" prop="operator">
            <el-input v-model="expertForm.operator" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="操作时间" prop="operateTime">
            <el-input v-model="expertForm.operateTime" readonly style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExpertViewDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 查看数据
    detailData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      expertForm: {
        name: '',
        genderText: '',
        birthDate: '',
        phone: '',
        unitTitle: '',
        specialty: '',
        educationText: '',
        technicalTitle: '',
        researchField: '',
        workExperience: '',
        remark: '',
        operator: '',
        operateTime: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      return '查看专家'
    }
  },
  watch: {
    // 监听查看数据变化
    detailData: {
      handler(newData) {
        if (newData) {
          this.loadDetailData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.detailData) {
          this.loadDetailData(this.detailData)
        }
      }
    }
  },
  methods: {
    // 加载查看数据
    loadDetailData(data) {
      this.expertForm.name = data.name || ''
      this.expertForm.genderText = data.genderText || ''
      this.expertForm.birthDate = data.birthDate || ''
      this.expertForm.phone = data.phone || ''
      this.expertForm.unitTitle = data.unitTitle || ''
      this.expertForm.specialty = data.specialty || ''
      this.expertForm.educationText = data.educationText || ''
      this.expertForm.technicalTitle = data.technicalTitle || ''
      this.expertForm.researchField = data.researchField || ''
      this.expertForm.workExperience = data.workExperience || ''
      this.expertForm.remark = data.remark || ''
      this.expertForm.operator = data.operator || ''
      this.expertForm.operateTime = data.operateTime || ''
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
