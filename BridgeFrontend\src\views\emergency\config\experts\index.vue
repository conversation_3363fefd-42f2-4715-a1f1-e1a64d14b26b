<!-- 专家库管理 -->
<template>
  <div class="emergency-container inspection-container">
    <div class="page-container">
      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        style="margin-top:21px !important;">
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-input
            v-model="formData.name"
            placeholder="请输入姓名"
            clearable
            class="filter-input">
          </el-input>

          <el-select
            v-model="formData.unitTitle"
            placeholder="单位及职称"
            clearable
            class="filter-select">
            <el-option
              v-for="item in unitTitleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-model="formData.specialty"
            placeholder="专业"
            clearable
            class="filter-select">
            <el-option
              v-for="item in specialtyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd">
          新增
        </el-button>
        <el-button 
          type="success" 
          icon="el-icon-download" 
          @click="handleImport">
          批量导入
        </el-button>
        <el-button 
          type="info" 
          icon="el-icon-upload2" 
          @click="handleExport">
          导出
        </el-button>
      </div>

      <!-- 专家列表表格 -->
      <div class="inspection-table">
        <el-table
          :data="expertsList"
          v-loading="loading"
          stripe
          border
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '32px' }"
          size="small">
          <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unitTitle" label="工作单位及职称" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phone" label="电话" width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="specialty" label="专业" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operator" label="操作人" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="operateTime" label="操作时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center" class-name="operation-column">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">编辑</el-link>
                <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑专家弹窗 -->
    <ExpertFormDialog
      :visible.sync="dialogVisible"
      :is-edit="isEdit"
      :expert-data="currentExpertData"
      :specialty-options="specialtyOptions"
      :submitting="submitting"
      @confirm="handleFormConfirm"
      @close="handleDialogClose" />

    <!-- 查看专家弹窗 -->
    <ExpertViewDialog
      :visible.sync="viewDialogVisible"
      :detail-data="currentExpertData"
      @close="handleViewDialogClose" />

    <!-- 批量导入弹窗 -->
    <ExpertImportDialog
      :visible.sync="importDialogVisible"
      :importing="importing"
      @download-template="handleDownloadTemplate"
      @file-selected="handleImportFileSelected"
      @file-removed="handleImportFileRemoved"
      @import-confirm="handleImportConfirm"
      @close="handleImportDialogClose" />
  </div>
</template>

<script>
import { FilterSection } from '@/components/Inspection'
import ExpertFormDialog from './components/ExpertFormDialog.vue'
import ExpertViewDialog from './components/ExpertViewDialog.vue'
import ExpertImportDialog from './components/ExpertImportDialog.vue'

export default {
  name: 'ExpertsConfig',
  components: {
    FilterSection,
    ExpertFormDialog,
    ExpertViewDialog,
    ExpertImportDialog
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      viewDialogVisible: false,
      importDialogVisible: false,
      submitting: false,
      importing: false,
      isEdit: false,
      currentExpertData: null,
      
      // 导入文件
      importFile: null,
      
      // 筛选表单
      filterForm: {
        name: '',
        unitTitle: '',
        specialty: ''
      },
      
      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        unitTitleOptions: [],
        specialtyOptions: []
      },
      
      // 专家列表
      expertsList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      
      // 单位及职称选项
      unitTitleOptions: [
        { value: '1', label: '湖南中大设计研究院' },
        { value: '2', label: '中南大学教授' },
        { value: '3', label: '湖南大学教授' },
        { value: '4', label: '长沙理工大学教授' },
        { value: '5', label: '省交通设计院' }
      ],
      
      // 专业选项
      specialtyOptions: [
        { value: '1', label: '土木工程' },
        { value: '2', label: '桥梁工程' },
        { value: '3', label: '隧道工程' },
        { value: '4', label: '结构工程' },
        { value: '5', label: '道路工程' },
        { value: '6', label: '岩土工程' },
        { value: '7', label: '交通工程' },
        { value: '8', label: '安全工程' }
      ]
    }
  },
  computed: {},
  mounted() {
    this.loadExpertsList()
  },
  methods: {
    // 加载专家列表
    async loadExpertsList() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        const response = await this.mockGetExpertsList(params)
        this.expertsList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载专家列表失败:', error)
        this.$message.error('加载专家列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadExpertsList()
    },
    
    // 重置
    handleReset() {
      this.filterForm = {
        name: '',
        unitTitle: '',
        specialty: ''
      }
      this.pagination.currentPage = 1
      this.loadExpertsList()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadExpertsList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadExpertsList()
    },
    
    // 新增
    handleAdd() {
      this.isEdit = false
      this.currentExpertData = null
      this.dialogVisible = true
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.currentExpertData = row
      this.dialogVisible = true
    },
    
    // 查看
    handleView(row) {
      this.currentExpertData = row
      this.viewDialogVisible = true
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm(`确认要删除专家"${row.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.mockDeleteExpert(row.id)
          this.$message.success('删除成功')
          this.loadExpertsList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    
    // 批量导入
    handleImport() {
      this.importDialogVisible = true
      this.importFile = null
    },
    
    // 导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },
    
    // 表单确认提交
    async handleFormConfirm(formData) {
      this.submitting = true
      try {
        if (this.isEdit) {
          await this.mockUpdateExpert(formData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreateExpert(formData)
          this.$message.success('新增成功')
        }
        
        this.dialogVisible = false
        this.loadExpertsList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.currentExpertData = null
    },
    
    handleViewDialogClose() {
      this.viewDialogVisible = false
      this.currentExpertData = null
    },

    handleImportDialogClose() {
      this.importDialogVisible = false
      this.importFile = null
    },

    // 下载模板文件
    handleDownloadTemplate() {
      // 创建模板数据
      const templateData = [
        ['姓名', '性别', '出生年月', '电话', '工作单位及职称', '专业', '学历', '技术职称', '研究领域', '工作经历', '备注'],
        ['张三', '男', '1975-08', '13800138001', '湖南中大设计研究院', '土木工程', '博士', '高级工程师', '桥梁结构设计', '从事桥梁工程设计20余年', '示例数据'],
        ['李四', '女', '1980-03', '13800138002', '中南大学教授', '桥梁工程', '博士', '教授', '桥梁动力学', '中南大学土木工程学院教授', '示例数据']
      ]
      
      // 这里应该生成真实的Excel文件
      this.$message.success('模板文件下载成功')
    },

    // 导入文件处理
    handleImportFileSelected(file) {
      this.importFile = file
    },

    handleImportFileRemoved() {
      this.importFile = null
    },

    // 确认导入
    async handleImportConfirm(file) {
      this.importing = true
      try {
        await this.mockImportExperts(file)
        this.$message.success('导入成功')
        this.importDialogVisible = false
        this.loadExpertsList()
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败')
      } finally {
        this.importing = false
      }
    },
    
    // 模拟API方法
    async mockGetExpertsList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              name: '江轩舟',
              gender: '1',
              genderText: '男',
              birthDate: '1975-08',
              phone: '15057168395',
              unitTitle: '湖南中大设计研究院',
              specialty: '土木工程',
              specialtyValue: '1',
              education: '1',
              educationText: '博士',
              technicalTitle: '高级工程师',
              researchField: '桥梁结构设计与安全评估',
              workExperience: '从事桥梁工程设计20余年',
              remark: '',
              operator: '江轩舟',
              operateTime: '2025-08-19 12:03:30'
            },
            {
              id: 2,
              name: '李星辰',
              gender: '1',
              genderText: '男',
              birthDate: '1968-03',
              phone: '19520278394',
              unitTitle: '中南大学教授',
              specialty: '桥梁工程',
              specialtyValue: '2',
              education: '1',
              educationText: '博士',
              technicalTitle: '教授',
              researchField: '桥梁动力学与抗震',
              workExperience: '中南大学土木工程学院教授，博士生导师',
              remark: '',
              operator: '李明宇',
              operateTime: '2025-08-19 11:33:30'
            },
            {
              id: 3,
              name: '王继远',
              gender: '1',
              genderText: '男',
              birthDate: '1972-11',
              phone: '19557169683',
              unitTitle: '中南大学教授',
              specialty: '桥梁工程',
              specialtyValue: '2',
              education: '1',
              educationText: '博士',
              technicalTitle: '副教授',
              researchField: '桥梁健康监测',
              workExperience: '长期从事桥梁工程教学与科研工作',
              remark: '',
              operator: '陈洛凡',
              operateTime: '2025-08-19 11:02:30'
            }
          ]
          
          let filteredData = mockData
          if (params.name) {
            filteredData = filteredData.filter(item => 
              item.name.includes(params.name)
            )
          }
          if (params.unitTitle) {
            filteredData = filteredData.filter(item => 
              item.unitTitle.includes(params.unitTitle)
            )
          }
          if (params.specialty) {
            filteredData = filteredData.filter(item => 
              item.specialtyValue === params.specialty
            )
          }
          
          resolve({
            data: filteredData,
            total: filteredData.length
          })
        }, 500)
      })
    },
    
    async mockCreateExpert(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建专家:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdateExpert(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新专家:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeleteExpert(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除专家:', id)
          resolve({ success: true })
        }, 1000)
      })
    },

    async mockImportExperts(file) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('导入专家文件:', file.name)
          resolve({ success: true, count: 15 })
        }, 2000)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

.pagination-wrapper {
  text-align: right;
  padding: 20px 0;
}
</style>





