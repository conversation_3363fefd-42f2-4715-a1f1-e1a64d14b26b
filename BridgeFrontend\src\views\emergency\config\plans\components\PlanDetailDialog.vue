<!-- 查看预案详情弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="planForm" label-width="120px">
      <el-form-item label="编号" prop="planCode">
        <el-input v-model="planForm.planCode" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="级别" prop="level">
        <el-input v-model="planForm.level" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input v-model="planForm.title" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="预案文件" prop="fileName">
        <el-input v-model="planForm.fileName" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作人" prop="operator">
        <el-input v-model="planForm.operator" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作时间" prop="operateTime">
        <el-input v-model="planForm.operateTime" readonly style="width: 50%;"></el-input>
      </el-form-item>
    </el-form>
    
  </el-dialog>
</template>

<script>
export default {
  name: 'PlanDetailDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 查看数据
    detailData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      planForm: {
        planCode: '',
        level: '',
        title: '',
        fileName: '',
        operator: '',
        operateTime: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      return '查看预案'
    }
  },
  watch: {
    // 监听查看数据变化
    detailData: {
      handler(newData) {
        if (newData) {
          this.loadDetailData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.detailData) {
          this.loadDetailData(this.detailData)
        }
      }
    }
  },
  methods: {
    // 加载查看数据
    loadDetailData(data) {
      this.planForm.planCode = data.planCode || ''
      this.planForm.level = data.level || ''
      this.planForm.title = data.title || ''
      this.planForm.fileName = data.fileName || ''
      this.planForm.operator = data.operator || ''
      this.planForm.operateTime = data.operateTime || ''
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
