<!-- 预案表单弹窗组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="600px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="planForm" :rules="planRules" ref="planForm" label-width="120px">
      <!-- 编号字段，仅编辑时显示 -->
      <el-form-item v-if="isEdit" label="编号" prop="number">
        <el-input
          v-model="planForm.number"
          placeholder="系统自动生成"
          style="width: 50%;"
          readonly>
        </el-input>
      </el-form-item>
      
      <el-form-item label="级别" prop="level">
        <el-select v-model="planForm.level" placeholder="请选择级别" style="width: 50%;">
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="planForm.title"
          placeholder="请输入标题"
          style="width: 50%;"
          >
        </el-input>
      </el-form-item>
      
      <el-form-item label="预案文件" prop="planFile">
        <div class="file-upload">
          <el-upload
            class="upload-demo"
            action=""
            :http-request="handleFileUpload"
            :file-list="fileList"
            :on-remove="handleFileRemove"
            :before-upload="beforeFileUpload"
            :limit="1"
            accept=".pdf">
            <el-link type="primary" :underline="false" style="display: flex; align-items: center; justify-content: center; flex-direction: column; gap: 8px;">
              <svg-icon icon-class="emergency-upload" />
              <span style="color: white;">将文件拖拽或点击上传</span>
            </el-link>

            <div slot="tip" class="el-upload__tip">
              支持上传 PDF格式文件，大小不超过 20MB
            </div>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleDialogClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PlanFormDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 编辑数据
    editData: {
      type: Object,
      default: null
    },
    // 级别选项
    levelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      submitting: false,
      
      // 表单数据
      planForm: {
        number: '',
        level: '',
        title: '',
        planFile: null
      },
      
      // 表单验证规则
      planRules: {
        level: [
          { required: true, message: '请选择级别', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        /*
        eventType: [
          { required: true, message: '请输入事件类型', trigger: 'blur' }
        ],
        planFile: [
          { required: true, message: '请上传预案文件', trigger: 'change' }
        ]*/
      },
      
      // 文件列表
      fileList: []
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑预案' : '新增预案'
    }
  },
  watch: {
    // 监听弹窗显示状态
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    // 监听编辑数据变化
    editData: {
      handler(newData) {
        if (newData && this.visible) {
          this.loadEditData(newData)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        this.loadEditData(this.editData)
      } else {
        this.resetForm()
      }
    },
    
    // 加载编辑数据
    loadEditData(data) {
      this.planForm.number = data.planCode || ''
      this.planForm.level = data.levelValue || ''
      this.planForm.title = data.title || ''
      
      if (data.fileName) {
        this.fileList = [{
          name: data.fileName,
          url: data.fileUrl,
          uid: Date.now()
        }]
        this.planForm.planFile = data.fileUrl
      }
    },
    
    // 文件上传
    handleFileUpload(params) {
      const file = params.file
      const reader = new FileReader()
      reader.onload = (e) => {
        this.fileList = [{
          name: file.name,
          url: e.target.result,
          uid: Date.now(),
          raw: file
        }]
        this.planForm.planFile = e.target.result
      }
      reader.readAsDataURL(file)
    },
    
    handleFileRemove() {
      this.fileList = []
      this.planForm.planFile = null
    },
    
    beforeFileUpload(file) {
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ]
      const isAllowedType = allowedTypes.includes(file.type)
      const isLt20M = file.size / 1024 / 1024 < 20
      
      if (!isAllowedType) {
        this.$message.error('只支持上传 PDF、Word 格式的文件!')
      }
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
      }
      return isAllowedType && isLt20M
    },
    
    // 确认提交
    handleConfirm() {
      this.$refs.planForm.validate(valid => {
        if (valid) {
          this.submitPlan()
        }
      })
    },
    
    async submitPlan() {
      this.submitting = true
      try {
        const submitData = {
          ...this.planForm,
          fileName: this.fileList[0]?.name,
          fileType: this.getFileType(this.fileList[0]?.name)
        }
        
        if (this.isEdit && this.editData) {
          submitData.id = this.editData.id
        }
        
        this.$emit('submit', submitData)
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    },
    
    // 重置表单
    resetForm() {
      this.planForm = {
        number: '',
        level: '',
        title: '',
        planFile: null
      }
      this.fileList = []
      this.$nextTick(() => {
        this.$refs.planForm && this.$refs.planForm.clearValidate()
      })
    },
    
    // 获取文件类型
    getFileType(fileName) {
      if (!fileName) return ''
      return fileName.split('.').pop()?.toLowerCase() || ''
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 弹窗相关样式 */
.file-upload {
  width: 100%;
}

.upload-demo .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  text-align: center;
  padding: 20px;
}

.upload-demo .el-upload:hover {
  border-color: #409EFF;
}

.dialog-footer {
  text-align: right;
}
</style>
