<!-- 预案库管理 -->
<template>
  <div class="emergency-container inspection-container">
    <div class="page-container">
      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        style="margin-top:21px !important;">
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.level"
            placeholder="级别"
            clearable
            class="filter-select">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>


          <el-input
            v-model="formData.title"
            placeholder="请输入标题"
            clearable
            class="filter-input">
          </el-input>

        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd">
          新增
        </el-button>
      </div>

      <!-- 预案列表表格 -->
      <div class="inspection-table">
        <el-table
          :data="plansList"
          v-loading="loading"
          stripe
          border
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '32px' }"
          size="small">
          <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="planCode" label="编号" width="120" align="center"></el-table-column>
          <el-table-column prop="level" label="级别" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="title" label="标题" min-width="300" show-overflow-tooltip></el-table-column>
          <el-table-column label="预案文件" width="150" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                @click="handleViewFile(scope.row)"
                style="color: #409EFF;">
                <i class="el-icon-document" style="font-size: 20px;"></i>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" width="120"></el-table-column>
          <el-table-column prop="operateTime" label="操作时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center" class-name="operation-column">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">编辑</el-link>
                <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑预案弹窗 -->
    <PlanFormDialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :edit-data="currentPlanData"
      :level-options="levelOptions"
      @submit="handleFormSubmit"
      @close="handleDialogClose"
    />

    <!-- 查看预案详情弹窗 -->
    <PlanDetailDialog
      :visible="detailVisible"
      :detail-data="currentPlanData"
      @close="handleDetailClose"
    />

  </div>
</template>

<script>
import { FilterSection } from '@/components/Inspection'
import PlanFormDialog from './components/PlanFormDialog.vue'
import PlanDetailDialog from './components/PlanDetailDialog.vue'

export default {
  name: 'PlansConfig',
  components: {
    FilterSection,
    PlanFormDialog,
    PlanDetailDialog
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      detailVisible: false,
      isEdit: false,
      currentPlanData: null,
      
      // 筛选表单
      filterForm: {
        level: '',
        title: ''
      },
      
      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        levelOptions: []
      },
      
      // 预案列表
      plansList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      
      // 级别选项
      levelOptions: [
        { value: '1', label: '长沙市政府' },
        { value: '2', label: '长沙市应急和安全生产委员会' },
        { value: '3', label: '长沙市城管局' },
        { value: '4', label: '市桥隧事务中心' },
        { value: '5', label: '桥隧公司' }
      ]
    }
  },
  mounted() {
    this.loadPlansList()
  },
  methods: {
    // 加载预案列表
    async loadPlansList() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        const response = await this.mockGetPlansList(params)
        this.plansList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载预案列表失败:', error)
        this.$message.error('加载预案列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadPlansList()
    },
    
    // 重置
    handleReset() {
      this.filterForm = {
        level: '',
        title: ''
      }
      this.pagination.currentPage = 1
      this.loadPlansList()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadPlansList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadPlansList()
    },
    
    // 新增
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.currentPlanData = row
      this.dialogVisible = true
    },
    
    // 查看
    handleView(row) {
      this.currentPlanData = row
      this.detailVisible = true
    },
    
    // 查看文件
    handleViewFile(row) {
      if (row.fileUrl) {
        // 打开PDF文件
        window.open(row.fileUrl, '_blank')
      } else {
        this.$message.warning('该预案文件不存在')
      }
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm(`确认要删除预案编号为 ${row.planCode} 的预案吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.mockDeletePlan(row.id)
          this.$message.success('删除成功')
          this.loadPlansList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    
    
    // 处理表单提交
    async handleFormSubmit(submitData) {
      try {
        if (this.isEdit) {
          await this.mockUpdatePlan(submitData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreatePlan(submitData)
          this.$message.success('新增成功')
        }
        
        this.dialogVisible = false
        this.loadPlansList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    
    // 关闭详情弹窗
    handleDetailClose() {
      this.detailVisible = false
      this.currentPlanData = null
    },
    
    // 重置表单
    resetForm() {
      this.currentPlanData = null
    },
    
    
    // 模拟API方法
    async mockGetPlansList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              planCode: 'YA001',
              level: '长沙市政府',
              levelValue: '1',
              title: '长沙市突发事件应急预案的案',
              fileName: '长沙市突发事件应急预案.pdf',
              fileUrl: '/files/test/emergency/process1.pdf',
              fileType: 'pdf',
              operator: '张梓浩',
              operateTime: '2025-08-19 12:03:30'
            },
            {
              id: 2,
              planCode: 'YA002',
              level: '长沙市应急和安全生产委员会',
              levelValue: '2',
              title: '长沙市应急和安全生产委员会公安表文书（长沙市突发事件应急预案实施细则）',
              fileName: '应急预案实施细则.pdf',
              fileUrl: '/files/test/emergency/process1.pdf',
              fileType: 'pdf',
              operator: '江荷富',
              operateTime: '2025-08-19 11:33:30'
            },
            {
              id: 3,
              planCode: 'YA003',
              level: '长沙市城管局',
              levelValue: '3',
              title: '长沙市城市桥梁应急处置预案',
              fileName: '城市桥梁应急处置预案.pdf',
              fileUrl: '/files/test/emergency/process1.pdf',
              fileType: 'pdf',
              operator: '孙明富',
              operateTime: '2025-08-19 11:02:30'
            }
          ]
          
          let filteredData = mockData
          if (params.level) {
            filteredData = filteredData.filter(item => item.levelValue === params.level)
          }
          if (params.title) {
            filteredData = filteredData.filter(item => 
              item.title.includes(params.title)
            )
          }
          
          resolve({
            data: filteredData,
            total: filteredData.length
          })
        }, 500)
      })
    },
    
    async mockCreatePlan(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建预案:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdatePlan(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新预案:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeletePlan(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除预案:', id)
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

</style>





