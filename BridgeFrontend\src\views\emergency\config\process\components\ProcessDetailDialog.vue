<!-- 查看应急流程图详情弹窗 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="processForm" label-width="120px">
      <el-form-item label="编号" prop="processCode">
        <el-input v-model="processForm.processCode" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="事件类型" prop="eventType">
        <el-input v-model="processForm.eventType" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input v-model="processForm.title" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="流程图" prop="processFile">
        <el-input v-model="processForm.fileName" readonly style="width: 100%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作人" prop="operator">
        <el-input v-model="processForm.operator" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="操作时间" prop="operateTime">
        <el-input v-model="processForm.operateTime" readonly style="width: 50%;"></el-input>
      </el-form-item>
    </el-form>
    
  </el-dialog>
</template>

<script>
export default {
  name: 'ProcessDetailDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 查看数据
    detailData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 表单数据
      processForm: {
        processCode: '',
        eventType: '',
        title: '',
        fileName: '',
        operator: '',
        operateTime: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      return '查看应急流程图'
    }
  },
  watch: {
    // 监听查看数据变化
    detailData: {
      handler(newData) {
        if (newData) {
          this.loadDetailData(newData)
        }
      },
      immediate: true
    },
    // 监听弹窗显示状态
    visible: {
      handler(newVisible) {
        if (newVisible && this.detailData) {
          this.loadDetailData(this.detailData)
        }
      }
    }
  },
  methods: {
    // 加载查看数据
    loadDetailData(data) {
      this.processForm.processCode = data.processCode || ''
      this.processForm.eventType = data.eventType || ''
      this.processForm.title = data.title || ''
      this.processForm.fileName = data.fileName || ''
      this.processForm.operator = data.operator || ''
      this.processForm.operateTime = data.operateTime || ''
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>