<!-- 流程表单弹窗组件 -->
<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="500px"
    :before-close="handleDialogClose"
    class="emergency-dialog">
    
    <el-form :model="processForm" :rules="processRules" ref="processForm" label-width="120px">
      <el-form-item label="编号" prop="processCode" v-if="isEdit">
        <el-input v-model="processForm.processCode" readonly style="width: 50%;"></el-input>
      </el-form-item>
      
      <el-form-item label="事件类型" prop="eventType">
        <el-select v-model="processForm.eventType" placeholder="请选择" style="width: 50%;">
          <el-option
            v-for="item in eventTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="标题" prop="title">
        <el-input v-model="processForm.title" placeholder="请输入标题"></el-input>
      </el-form-item>
      
      <el-form-item label="应急处置流程图" prop="processFile">
        <div class="file-upload">
          <el-upload
            class="upload-demo"
            action=""
            :http-request="handleFileUpload"
            :file-list="fileList"
            :on-remove="handleFileRemove"
            :before-upload="beforeFileUpload"
            :limit="1"
            accept=".pdf">
            <el-link type="primary" :underline="false" style="display: flex; align-items: center; justify-content: center; flex-direction: column; gap: 8px;">
              <svg-icon icon-class="emergency-upload" />
              <span style="color: white;">将文件拖拽或点击上传</span>
            </el-link>
            <div slot="tip" class="el-upload__tip">
              支持上传 PDF格式文件，大小不超过 20MB
            </div>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleDialogClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProcessFormDialog',
  props: {
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 编辑数据
    editData: {
      type: Object,
      default: null
    },
    // 事件类型选项
    eventTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      submitting: false,
      
      // 表单数据
      processForm: {
        processCode: '',
        eventType: '',
        title: '',
        processFile: null
      },
      
      // 表单验证规则
      processRules: {
        eventType: [
          { required: true, message: '请选择类型名称', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      },
      
      // 文件列表
      fileList: []
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑应急流程图' : '新增应急流程图'
    }
  },
  watch: {
    // 监听弹窗显示状态
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    },
    // 监听编辑数据变化
    editData: {
      handler(newData) {
        if (newData && this.visible) {
          this.loadEditData(newData)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      if (this.isEdit && this.editData) {
        this.loadEditData(this.editData)
      } else {
        this.resetForm()
      }
    },
    
    // 加载编辑数据
    loadEditData(data) {
      this.processForm.processCode = data.processCode || ''
      this.processForm.eventType = data.eventTypeValue
      this.processForm.title = data.title || ''
      if (data.fileName) {
        this.fileList = [{
          name: data.fileName,
          url: data.fileUrl,
          uid: Date.now()
        }]
        this.processForm.processFile = data.fileUrl
      }
    },
    
    // 文件上传
    handleFileUpload(params) {
      const file = params.file
      const reader = new FileReader()
      reader.onload = (e) => {
        this.fileList = [{
          name: file.name,
          url: e.target.result,
          uid: Date.now(),
          raw: file
        }]
        this.processForm.processFile = e.target.result
      }
      reader.readAsDataURL(file)
    },
    
    handleFileRemove() {
      this.fileList = []
      this.processForm.processFile = null
    },
    
    beforeFileUpload(file) {
      const allowedTypes = ['application/pdf']
      const isAllowedType = allowedTypes.includes(file.type)
      const isLt10M = file.size / 1024 / 1024 < 10
      
      if (!isAllowedType) {
        this.$message.error('只支持上传 PDF 格式的文件!')
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      return isAllowedType && isLt10M
    },
    
    // 确认提交
    handleConfirm() {
      this.$refs.processForm.validate(valid => {
        if (valid) {
          this.submitProcess()
        }
      })
    },
    
    async submitProcess() {
      this.submitting = true
      try {
        const submitData = {
          ...this.processForm,
          fileName: this.fileList[0]?.name,
          fileType: this.getFileType(this.fileList[0]?.name)
        }
        
        if (this.isEdit && this.editData) {
          submitData.id = this.editData.id
        }
        
        // 触发提交事件
        this.$emit('submit', submitData)
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.$emit('close')
      this.resetForm()
    },
    
    // 重置表单
    resetForm() {
      this.processForm = {
        processCode: '',
        eventType: '',
        title: '',
        processFile: null
      }
      this.fileList = []
      this.$nextTick(() => {
        this.$refs.processForm && this.$refs.processForm.clearValidate()
      })
    },
    
    // 获取文件类型
    getFileType(fileName) {
      if (!fileName) return ''
      return fileName.split('.').pop()?.toLowerCase() || ''
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

/* 组件特有的样式 - 公共样式已移至 emergency-common.scss */
</style>
