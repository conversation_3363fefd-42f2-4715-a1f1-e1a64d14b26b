<!-- 流程管理 -->
<template>
  <div class="emergency-container inspection-container">
    <div class="page-container">
      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        style="margin-top:21px !important;"
      >
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.eventType"
            placeholder="事件类型"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in eventTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd"
        >
          新增
        </el-button>
      </div>

      <!-- 流程列表表格 -->
      <div class="inspection-table">
        <el-table
          :data="processList"
          v-loading="loading"
          stripe
          border
          style="width: 100%; min-width: 1200px;"
          :row-style="{ height: '32px' }"
          size="small">
          <el-table-column prop="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="processCode" label="编号" width="120" align="center"></el-table-column>
          <el-table-column prop="eventType" label="事件类型" min-width="200"></el-table-column>
          <el-table-column prop="title" label="标题" min-width="250" show-overflow-tooltip></el-table-column>
          <el-table-column label="应急处置流程图" width="150" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                @click="handleViewProcess(scope.row)"
                style="color: #409EFF;">
                <i class="el-icon-document" style="font-size: 20px;"></i>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" width="120"></el-table-column>
          <el-table-column prop="operateTime" label="操作时间" width="180" align="center"></el-table-column>
          <el-table-column label="操作" width="150" align="center" class-name="operation-column">
            <template slot-scope="scope">
              <div class="operation-buttons">
                <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
                <el-link @click="handleEdit(scope.row)" type="primary" :underline="false">编辑</el-link>
                <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑流程图弹窗 -->
    <ProcessFormDialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :edit-data="currentProcessData"
      :event-type-options="eventTypeOptions"
      @submit="handleFormSubmit"
      @close="handleDialogClose"
    />

    <!-- 查看详情弹窗 -->
    <ProcessDetailDialog
      :visible="detailVisible"
      :detail-data="currentProcessData"
      @close="handleDetailClose"
    />
  </div>
</template>

<script>
import { FilterSection } from '@/components/Inspection'
import ProcessFormDialog from './components/ProcessFormDialog.vue'
import ProcessDetailDialog from './components/ProcessDetailDialog.vue'

export default {
  name: 'ProcessConfig',
  components: {
    FilterSection,
    ProcessFormDialog,
    ProcessDetailDialog
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      detailVisible: false,
      isEdit: false,
      currentProcessData: null,
      
      // 筛选表单
      filterForm: {
        eventType: ''
      },
      
      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        eventTypeOptions: []
      },
      
      // 流程列表
      processList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      
      // 事件类型选项
      eventTypeOptions: [
        { value: '1', label: '桥梁隧道结构性损坏' },
        { value: '2', label: '桥梁隧道恶化老化物落' },
        { value: '3', label: '桥梁隧道灾门坍塌' },
        { value: '4', label: '桥梁隧道交通事故' },
        { value: '5', label: '自然灾害影响' },
        { value: '6', label: '突发公共卫生事件' }
      ]
    }
  },
  computed: {
  },
  mounted() {
    this.loadProcessList()
  },
  methods: {
    // 加载流程列表
    async loadProcessList() {
      this.loading = true
      try {
        const params = {
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        const response = await this.mockGetProcessList(params)
        this.processList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载流程列表失败:', error)
        this.$message.error('加载流程列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadProcessList()
    },
    
    // 重置
    handleReset() {
      this.filterForm = {
        eventType: ''
      }
      this.pagination.currentPage = 1
      this.loadProcessList()
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadProcessList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadProcessList()
    },
    
    // 新增
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },
    
    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.currentProcessData = row
      this.dialogVisible = true
    },
    
    // 查看
    handleView(row) {
      if (row) {
        this.currentProcessData = row
        this.detailVisible = true
      } else {
        this.$message.warning('数据不存在')
      }
    },
    
    // 查看流程图
    handleViewProcess(row) {
      if (row.fileUrl) {
        // 打开PDF文件
        window.open(row.fileUrl, '_blank')
      } else {
        this.$message.warning('该流程图文件不存在')
      }
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm('确认要删除此流程图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await this.mockDeleteProcess(row.id)
          this.$message.success('删除成功')
          this.loadProcessList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },
    
    // 处理表单提交
    async handleFormSubmit(submitData) {
      try {
        if (this.isEdit) {
          await this.mockUpdateProcess(submitData)
          this.$message.success('更新成功')
        } else {
          await this.mockCreateProcess(submitData)
          this.$message.success('新增成功')
        }
        
        this.dialogVisible = false
        this.loadProcessList()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      }
    },
    
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.currentProcessData = null
    },
    
    // 关闭详情弹窗
    handleDetailClose() {
      this.detailVisible = false
      this.currentProcessData = null
    },
    
    // 从详情弹窗跳转到编辑
    handleDetailEdit(data) {
      this.currentProcessData = data
      this.isEdit = true
      this.dialogVisible = true
    },
    
    // 重置表单
    resetForm() {
      this.currentProcessData = null
    },
    
    // 模拟API方法
    async mockGetProcessList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = [
            {
              id: 1,
              processCode: 'LC001',
              eventType: '桥梁隧道结构性损坏',
              title: '桥梁结构性损坏应急处置标准流程',
              eventTypeValue: '1',
              fileName: '桥梁结构损坏处置流程.pdf',
              fileUrl: '/files/test/emergency/process1.pdf',
              fileType: 'pdf',
              operator: '张梓浩',
              operateTime: '2025-08-19 12:03:30'
            },
            {
              id: 2,
              processCode: 'LC002',
              eventType: '桥梁隧道恶化老化物落',
              title: '桥梁隧道老化物落应急处置流程',
              eventTypeValue: '2',
              fileName: '老化物落处置流程.pdf',
              fileUrl: '/files/test/emergency/process1.pdf',
              fileType: 'pdf',
              operator: '江荷富',
              operateTime: '2025-08-19 11:33:30'
            },
            {
              id: 3,
              processCode: 'LC003',
              eventType: '桥梁隧道灾门坍塌',
              title: '桥梁隧道坍塌事故紧急救援流程',
              eventTypeValue: '3',
              fileName: '坍塌事故处置流程.pdf',
              fileUrl: '/files/process3.pdf',
              fileType: 'pdf',
              operator: '孙明富',
              operateTime: '2025-08-19 11:02:30'
            },
            {
              id: 4,
              processCode: 'LC004',
              eventType: '桥梁隧道交通事故',
              title: '桥梁隧道交通事故应急处置预案',
              eventTypeValue: '4',
              fileName: '交通事故处置流程.pdf',
              fileUrl: '/files/process4.pdf',
              fileType: 'pdf',
              operator: '李明宇',
              operateTime: '2025-08-19 10:38:30'
            }
          ]
          
          let filteredData = mockData
          if (params.eventType) {
            filteredData = mockData.filter(item => 
              item.eventType.includes(params.eventType)
            )
          }
          
          resolve({
            data: filteredData,
            total: filteredData.length
          })
        }, 500)
      })
    },
    
    async mockCreateProcess(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('创建流程图:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockUpdateProcess(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('更新流程图:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    async mockDeleteProcess(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('删除流程图:', id)
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';

</style>





