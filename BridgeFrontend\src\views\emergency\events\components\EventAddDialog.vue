<!-- 上报事件 -->
<template>
  <el-dialog
    title="上报应急事件"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    
      <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="65px">
        <el-form-item label="事件名称" prop="eventName">
          <el-input v-model="addForm.eventName" placeholder="请输入"></el-input>
        </el-form-item>
      
      <el-form-item :label="structureLabel" prop="structureName">
        <el-select v-model="addForm.structureName" placeholder="选择桥梁" style="width: 100%;">
          <el-option
            v-for="item in structureOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="发生位置" prop="location">
        <el-input v-model="addForm.location" placeholder="选择地点">
          <template slot="prepend">
            <i class="el-icon-location" style="color: #409EFF;"></i>
          </template>
          <el-button slot="append" icon="el-icon-search" @click="handleSelectLocation"></el-button>
        </el-input>
      </el-form-item>
      
      <el-form-item label="触发时间" prop="triggerTime">
        <el-date-picker
          v-model="addForm.triggerTime"
          type="datetime"
          placeholder="选择时间"
          format="yyyy/MM/dd HH:mm"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%;">
        </el-date-picker>
      </el-form-item>
      
      <el-form-item label="事件类型" prop="eventType" class="event-type-item">
        <el-radio-group v-model="addForm.eventType">
          <el-radio label="防涝排渍">防涝排渍</el-radio>
          <el-radio label="抗冰除雪">抗冰除雪</el-radio>
          <el-radio label="突发事件">突发事件</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 突发事件子分类 -->
      <el-form-item v-if="addForm.eventType === '突发事件'" prop="eventSubType">
        <el-select 
          v-model="addForm.eventSubType" 
          placeholder="请选择事件子分类" 
          style="width: 100%;">
          <el-option
            v-for="item in emergencySubTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="受伤人数" prop="injuredCount">
        <el-select v-model="addForm.injuredCount" placeholder="请选择" style="width: 100%;">
          <el-option
            v-for="i in 21"
            :key="i-1"
            :label="i-1"
            :value="i-1">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="死亡人数" prop="deathCount">
        <el-select v-model="addForm.deathCount" placeholder="请选择" style="width: 100%;">
          <el-option
            v-for="i in 21"
            :key="i-1"
            :label="i-1"
            :value="i-1">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="设施损坏" prop="facilityDamage">
        <el-input v-model="addForm.facilityDamage" placeholder="请输入"></el-input>
      </el-form-item>
      
      <el-form-item label="现场情况">
        <el-input
          v-model="addForm.sceneDescription"
          type="textarea"
          :rows="3"
          placeholder="请描述现场情况">
        </el-input>
      </el-form-item>
      
      <el-form-item label="接警人">
        <div class="contact-selection">
          <div class="selected-contacts">
            <el-tag
              v-for="contact in selectedContacts"
              :key="contact.id"
              closable
              size="small"
              @close="handleContactRemove(contact)">
              {{ contact.name }}
            </el-tag>
          </div>
          <el-select
            v-model="tempContact"
            placeholder="选择接警人"
            @change="handleContactAdd"
            style="width: 200px;">
            <el-option
              v-for="item in availableContacts"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </el-form-item>
      
      <el-form-item label="现场照片">
        <div class="photo-upload">
          <el-upload
            class="upload-demo"
            action=""
            :http-request="handlePhotoUpload"
            :file-list="photoList"
            :on-remove="handlePhotoRemove"
            :before-upload="beforePhotoUpload"
            list-type="picture-card"
            accept="image/*"
            multiple>
            <i class="el-icon-plus"></i>
          </el-upload>
        </div>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EventAddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    structureType: {
      type: String,
      default: 'bridge' // bridge: 桥梁, tunnel: 隧道
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      submitting: false,
      tempContact: '',
      
      addForm: {
        eventName: '',
        structureName: '',
        location: '',
        triggerTime: '',
        eventType: '',
        eventSubType: '',
        injuredCount: 0,
        deathCount: 0,
        facilityDamage: '',
        sceneDescription: '未造成交通瓶颈，桥梁通行情况正常',
        contacts: [],
        photos: []
      },
      
      addRules: {
        eventName: [
          { required: true, message: '请输入事件名称', trigger: 'blur' }
        ],
        structureName: [
          { required: true, message: '请选择桥梁名称', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请选择发生位置', trigger: 'blur' }
        ],
        triggerTime: [
          { required: true, message: '请选择触发时间', trigger: 'change' }
        ],
        eventType: [
          { required: true, message: '请选择事件类型', trigger: 'change' }
        ],
        eventSubType: [
          { 
            validator: (rule, value, callback) => {
              if (this.addForm.eventType === '突发事件' && !value) {
                callback(new Error('请选择事件子分类'))
              } else {
                callback()
              }
            }, 
            trigger: 'change' 
          }
        ],
        injuredCount: [
          { required: true, message: '请选择受伤人数', trigger: 'change' }
        ],
        deathCount: [
          { required: true, message: '请选择死亡人数', trigger: 'change' }
        ],
        facilityDamage: [
          { required: true, message: '请选择设施损坏程度', trigger: 'change' }
        ]
      },
      
      selectedContacts: [],
      photoList: [],
      
      structureOptions: [
        { value: '1', label: 'XXX大桥' },
        { value: '2', label: 'YYY大桥' },
        { value: '3', label: 'ZZZ大桥' }
      ],
      
      emergencySubTypeOptions: [
        { value: '桥梁隧道结构性损坏', label: '桥梁隧道结构性损坏' },
        { value: '桥梁隧道恶化老化物落', label: '桥梁隧道恶化老化物落' },
        { value: '桥梁隧道灾门坍塌', label: '桥梁隧道灾门坍塌' },
        { value: '桥梁隧道交通事故', label: '桥梁隧道交通事故' }
      ],
      
      facilityDamageOptions: [
        { value: '1', label: '轻微' },
        { value: '2', label: '一般' },
        { value: '3', label: '严重' },
        { value: '4', label: '特别严重' }
      ],
      
      contactOptions: [
        { id: 1, name: '刘金鑫' },
        { id: 2, name: '李明宇' },
        { id: 3, name: '林文龙' },
        { id: 4, name: '徐振德' },
        { id: 5, name: '朱清国' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },
        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' }
      ]
    }
  },
  computed: {
    structureLabel() {
      return this.structureType === 'bridge' ? '桥梁名称' : '隧道名称'
    },
    availableContacts() {
      const selectedIds = this.selectedContacts.map(c => c.id)
      return this.contactOptions.filter(c => !selectedIds.includes(c.id))
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    structureType(val) {
      // 根据类型更新桥梁/隧道选项
      if (val === 'tunnel') {
        this.structureOptions = [
          { value: '1', label: 'XXX隧道' },
          { value: '2', label: 'YYY隧道' },
          { value: '3', label: 'ZZZ隧道' }
        ]
      } else {
        this.structureOptions = [
          { value: '1', label: 'XXX大桥' },
          { value: '2', label: 'YYY大桥' },
          { value: '3', label: 'ZZZ大桥' }
        ]
      }
    },
    'addForm.eventType'(newVal, oldVal) {
      // 当事件类型改变时，清除子分类选择
      if (newVal !== '突发事件') {
        this.addForm.eventSubType = ''
      }
    }
  },
  methods: {
    initForm() {
      // 初始化表单，设置默认时间为当前时间
      this.addForm.triggerTime = this.formatDateTime(new Date())
      
      // 预设一些接警人
      this.selectedContacts = [
        { id: 1, name: '刘金鑫' },
        { id: 2, name: '李明宇' },
        { id: 3, name: '林文龙' }
      ]
    },
    
    handleSelectLocation() {
      // 打开地图选择位置
      this.$message.info('地图选择位置功能开发中...')
    },
    
    handleContactAdd(contactId) {
      if (contactId) {
        const contact = this.contactOptions.find(c => c.id === contactId)
        if (contact) {
          this.selectedContacts.push(contact)
          this.tempContact = ''
        }
      }
    },
    
    handleContactRemove(contact) {
      const index = this.selectedContacts.findIndex(c => c.id === contact.id)
      if (index > -1) {
        this.selectedContacts.splice(index, 1)
      }
    },
    
    handlePhotoUpload(params) {
      // 模拟文件上传
      const file = params.file
      const reader = new FileReader()
      reader.onload = (e) => {
        this.photoList.push({
          name: file.name,
          url: e.target.result,
          uid: Date.now() + Math.random()
        })
      }
      reader.readAsDataURL(file)
    },
    
    handlePhotoRemove(file) {
      const index = this.photoList.findIndex(p => p.uid === file.uid)
      if (index > -1) {
        this.photoList.splice(index, 1)
      }
    },
    
    beforePhotoUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isJPG && isLt5M
    },
    
    handleConfirm() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          this.submitEvent()
        }
      })
    },
    
    async submitEvent() {
      this.submitting = true
      try {
        const submitData = {
          ...this.addForm,
          structureType: this.structureType,
          contacts: this.selectedContacts.map(c => c.id),
          photos: this.photoList
        }
        
        // 模拟API调用
        await this.mockSubmitEvent(submitData)
        
        this.$message.success('事件上报成功')
        this.$emit('confirm', submitData)
      } catch (error) {
        console.error('事件上报失败:', error)
        this.$message.error('事件上报失败')
      } finally {
        this.submitting = false
      }
    },
    
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
      this.resetForm()
    },
    
    resetForm() {
      this.addForm = {
        eventName: '',
        structureName: '',
        location: '',
        triggerTime: '',
        eventType: '',
        eventSubType: '',
        injuredCount: 0,
        deathCount: 0,
        facilityDamage: '',
        sceneDescription: '未造成交通瓶颈，桥梁通行情况正常',
        contacts: [],
        photos: []
      }
      this.selectedContacts = []
      this.photoList = []
      this.tempContact = ''
      
      this.$nextTick(() => {
        this.$refs.addForm && this.$refs.addForm.clearValidate()
      })
    },
    
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    
    // 模拟API
    async mockSubmitEvent(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('提交事件数据:', data)
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>

<style scoped>
/* 对话框左右外边距 */
::v-deep .el-dialog__body {
  margin-left: 50px;
  margin-right: 50px;
}

.contact-selection {
  width: 100%;
}

.selected-contacts {
  margin-bottom: 10px;
  min-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.photo-upload {
  width: 100%;
}

/* 现场照片上传区域不添加边框 */
.photo-upload ::v-deep .el-upload--picture-card {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.upload-demo .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.dialog-footer {
  text-align: right;
}

/* 为表单控件添加边框样式 */
::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-radio-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 事件类型单选框左对齐样式 */
.event-type-item ::v-deep .el-form-item__content {
  padding-left: 0 !important;
}

.event-type-item ::v-deep .el-radio-group {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

.event-type-item ::v-deep .el-radio {
  margin-left: 0 !important;
}

.event-type-item ::v-deep .el-radio:first-child {
  margin-left: 0 !important;
}
</style>
