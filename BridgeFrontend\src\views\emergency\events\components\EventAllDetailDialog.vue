<!-- 总结报告 -->
<template>
  <el-dialog
    title="处置应急事件"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose">

    <div class="report-dialog">
    <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="事件信息" name="eventInfo">
        <EventDetailPanel
          :eventData="eventData"
          :contactList="contactList"
          :photoList="photoList"
          :scrollable="true"
          :maxContactDisplay="10" />
      </el-tab-pane>

        <el-tab-pane label="应急研判" name="assessment">
          <EventAssessHistoryPanel
            :eventData="eventData"
            :readOnly="true"
            :showSectionTitles="true"
            :customEventCategoryOptions="assessCategoryOptions"
            :customEventLevelOptions="eventLevelOptions"
            :customResponsibleUnitOptions="responsibleUnitOptions"
            @data-change="handleAssessDataChange"
            @level-change="handleEventLevelChange" />
        </el-tab-pane>

        <el-tab-pane label="事件续报" name="eventReport">
          <!-- 续报记录列表 -->
          <EventReportList
            :reportList="reportList"
            :showSendRecord="false"
            @view="handleViewReport"
            @send-record="handleSendRecord" />
        </el-tab-pane>

        <el-tab-pane label="事态控制" name="stateControl">
          <!-- 事态控制记录列表 -->
          <EventStateControlList
            :stateControlList="stateControlList"
            :eventLevelOptions="eventLevelOptions"
            @view="handleViewStateControl" />
        </el-tab-pane>

        <el-tab-pane label="新闻通稿" name="newsRelease">
          <!-- 新闻通稿记录列表 -->
          <EventNewsList
            :newsList="newsList"
            :showSendRecord="false"
            @view="handleViewNews"
            @send-record="handleSendRecord"/>
        </el-tab-pane>


        <el-tab-pane label="应急总结" name="archiveSummary">
          <!-- 归档总结记录列表 -->
          <EventArchiveSummaryList
            :archiveSummaryList="archiveSummaryList"
            @view="handleViewArchiveSummary" />
        </el-tab-pane>
    </el-tabs>
    </div>


  </el-dialog>
</template>

<script>
import EventReportDetailDialog from './EventReportDetailDialog.vue'
import EventDetailPanel from './EventDetailPanel.vue'
import EventAssessHistoryPanel from './EventAssessHistoryPanel.vue'
import EventReportList from './EventReportList.vue'
import EventStateControlList from './EventStateControlList.vue'
import EventNewsList from './EventNewsList.vue'
import EventArchiveSummaryList from './EventArchiveSummaryList.vue'

export default {
  name: 'EventAllDetailDialog',
  components: {
    EventReportDetailDialog,
    EventDetailPanel,
    EventAssessHistoryPanel,
    EventReportList,
    EventStateControlList,
    EventNewsList,
    EventArchiveSummaryList
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    eventData: {
      type: Object,
      default: () => ({})
    },
    // 初始显示的页签
    initialTab: {
      type: String,
      default: 'eventInfo'
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'eventInfo',
      submitting: false,
      reportDetailVisible: false, // 控制续报详情弹窗显示
      selectedReportData: null, // 选中的续报数据

      reportList: [
        {
          id: 1,
          eventName: '测试事件AA',
          publisher: '格罗瑞',
          publishTime: '2025/07/15 11:27',
          reportTime: '2025-07-15 11:27:00',
          reporter: '格罗瑞',
          supervisor: '张晓东主任',
          discoverer: '现场工作人员',
          policeCoordinationTime: '2025-07-15 10:30:00',
          leaderArrivalTime: '2025-07-15 11:00:00',
          leaderName: '张主任',
          causeAnalysis: '经现场勘查和专家分析，初步判断为桥梁伸缩缝老化导致的轻微损坏，未影响桥梁主体结构安全。',
          disposalOpinion: '立即组织专业队伍进行应急修复，加强现场监控，确保交通安全有序。同时制定长期维护计划，防止类似问题再次发生。',
          reportUnit: '长沙市桥隧事务中心',
          recipients: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' },
            { id: 3, name: '陈经理', unit: '桥隧公司' }
          ]
        }
      ],

      // 事态控制记录列表
      stateControlList: [
        {
          id: 1,
          controlType: 'change',
          eventLevel: '2',
          controlTime: '2025-07-15 13:30:00',
          changeReason: '现场情况发生变化，需要调整应急响应等级',
          newEventLevel: '2',
          releaseReason: '',
          receivers: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' }
          ],
          scenePhotos: [
            { url: require('@/assets/images/emergency/event-detail/scene1.png'), name: '现场照片1' },
            { url: require('@/assets/images/emergency/event-detail/scene2.png'), name: '现场照片2' }
          ]
        },
        {
          id: 2,
          controlType: 'release',
          eventLevel: '1',
          controlTime: '2025-07-15 16:45:00',
          changeReason: '',
          newEventLevel: '',
          releaseReason: '事态已得到有效控制，交通已恢复正常',
          receivers: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' },
            { id: 3, name: '陈经理', unit: '桥隧公司' }
          ],
          scenePhotos: [
            { url: require('@/assets/images/emergency/event-detail/scene3.png'), name: '恢复现场照片' }
          ]
        }
      ],

      // 新闻通稿记录列表
      newsList: [
        {
          id: 1,
          eventName: '长沙大桥伸缩缝异常事件',
          publisher: '格罗瑞',
          publishTime: '2025/07/15 14:30',
          workContent: '紧急修复伸缩缝，确保桥梁安全',
          unitDepartment: '桥隧事务中心',
          departmentName: '应急管理处',
          companyName: '桥隧公司',
          trafficRecoveryTime: '2025-07-15 14:30:00',
          bridgeName: '长沙大桥',
          location: '中段伸缩缝处',
          eventType: '桥梁设施异常',
          recipients: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' }
          ]
        },
        {
          id: 2,
          eventName: '湘江隧道渗水事件',
          publisher: '李建国',
          publishTime: '2025/07/14 09:15',
          workContent: '隧道防水修复工程',
          unitDepartment: '桥隧事务中心',
          departmentName: '工程技术处',
          companyName: '市政工程公司',
          trafficRecoveryTime: '2025-07-14 16:20:00',
          bridgeName: '湘江隧道',
          location: '南段顶部',
          eventType: '隧道渗水',
          recipients: [
            { id: 1, name: '陈主任', unit: '市城管局' },
            { id: 3, name: '刘处长', unit: '应急管理部门' }
          ]
        }
      ],

      // 归档总结记录列表
      archiveSummaryList: [
        {
          id: 1,
          eventName: '长沙大桥伸缩缝异常事件',
          uploader: '张主任',
          archiveTime: '2025/07/15 16:30',
          summaryReport: '本次长沙大桥伸缩缝异常事件处置工作总结如下：\n\n一、事件概况\n事件发生于2025年7月15日上午10:30，长沙大桥中段伸缩缝出现异常，影响车辆正常通行。接报后，我中心立即启动应急预案，组织专业队伍赶赴现场。\n\n二、处置过程\n1. 10:35 应急队伍到达现场，立即设置警示标志，疏导交通\n2. 10:45 技术人员完成现场勘查，确定维修方案\n3. 11:00 开始实施紧急维修作业\n4. 14:30 维修工作完成，交通恢复正常\n\n三、处置效果\n通过快速响应和专业处置，事件得到有效控制，未造成人员伤亡，交通影响降到最低。维修质量达到设计要求，桥梁安全得到保障。\n\n四、经验总结\n1. 应急响应及时，处置措施得当\n2. 部门协调配合良好，信息沟通顺畅\n3. 技术方案科学合理，施工质量可靠\n\n五、改进建议\n1. 加强日常巡查，及时发现隐患\n2. 完善应急物资储备\n3. 提升应急队伍专业能力',
          attachments: [
            {
              name: '现场处置照片.zip',
              size: 2048576,
              type: 'application/zip',
              url: '/files/scene-photos.zip'
            },
            {
              name: '技术检测报告.pdf',
              size: 1536000,
              type: 'application/pdf',
              url: '/files/technical-report.pdf'
            }
          ]
        },
        {
          id: 2,
          eventName: '湘江隧道渗水事件',
          uploader: '李工程师',
          archiveTime: '2025/07/14 18:45',
          summaryReport: '湘江隧道渗水事件应急处置工作已圆满完成，现将相关情况总结如下：\n\n一、基本情况\n2025年7月14日凌晨5:30，湘江隧道南段顶部发现渗水现象，水量较小但持续存在。值班人员立即上报，启动应急预案。\n\n二、应急响应\n1. 5:35 应急小组到达现场，初步评估情况\n2. 6:00 实施临时封堵措施，确保行车安全\n3. 8:00 专业防水队伍进场，开始正式修复\n4. 16:20 修复工作完成，隧道恢复正常通行\n\n三、处置成效\n本次事件处置及时有效，未对交通造成重大影响。防水修复质量良好，达到设计标准，隧道安全运营得到保障。\n\n四、主要做法\n1. 建立完善的监测预警机制\n2. 制定科学的应急处置预案\n3. 配备专业的应急队伍和设备\n4. 加强部门间协调配合\n\n五、下步工作\n1. 继续加强隧道日常维护\n2. 完善防水系统\n3. 提升应急处置能力',
          attachments: [
            {
              name: '隧道检测数据.xlsx',
              size: 512000,
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              url: '/files/tunnel-data.xlsx'
            }
          ]
        }
      ],



      reportForm: {
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      },




      guideOptions: [
        { value: '1', label: '张三' },
        { value: '2', label: '李四' },
        { value: '3', label: '王五' }
      ],

      receiverOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '应急指挥中心' },
        { value: '4', label: '张三' },
        { value: '5', label: '李四' },
        { value: '6', label: '王五' }
      ],




      // 接警人数据
      contactList: [
        { id: 1, name: '李明' },
        { id: 2, name: '张伟' },
        { id: 3, name: '王强' },
        { id: 4, name: '刘洋' },
        { id: 5, name: '陈静' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },

        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' },
        { id: 10, name: '黄财一' },
        { id: 11, name: '黄财二' },
        { id: 12, name: '黄财三' },
        { id: 13, name: '张三' }

      ],

      // 现场照片数据
      photoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') }
      ],


      // 应急研判分类选项
      assessCategoryOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '3', label: '公共卫生事件' },
        { value: '4', label: '社会安全事件' }
      ],

      // 事件等级选项
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],

      // 责任单位选项
      responsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ]
    }
  },
  computed: {
    // 计算属性可以在这里添加其他需要的逻辑
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initDialog()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    initialTab: {
      handler(val) {
        if (val) {
          this.activeTab = val
        }
      },
      immediate: true
    }
  },
  methods: {
    initDialog() {
      // 只有在activeTab还没有被设置时才设置默认值
      if (!this.activeTab || this.activeTab === 'eventInfo') {
        this.activeTab = this.initialTab || 'eventInfo'
      }
      this.showReportList = true
      this.resetForm()
    },

    showAddReport() {
      this.showReportList = false
      // 如果已有续报记录，可以使用默认值
      if (this.reportList.length > 0) {
        // 可以从历史记录中获取一些默认值
      }
    },

    
    handleBackToReportList(){
      this.showReportList = true
      this.resetForm()
    },

    handleViewReport(row) {
      // 打开续报详情弹窗
      this.selectedReportData = row
      this.reportDetailVisible = true
    },


    handleViewNews(row) {
      // 打开详情弹窗
      this.selectedReportData = row
      this.reportDetailVisible = true
    },

    handleSendRecord(row) {
      this.$message.info('查看发送记录功能开发中...')
    },

    // 关闭续报详情弹窗
    handleReportDetailClose() {
      this.reportDetailVisible = false
      this.selectedReportData = null
    },


    handleReportConfirm(){
        console.log('handleReportConfirm....')

        this.$refs.reportForm.validateForm().then(() => {
          this.submitReport()
        }).catch(error => {
          console.log('表单验证失败:', error)
        })
    },




    async submitReport() {
      this.submitting = true
      try {
        const submitData = {
          eventId: this.eventData.id,
          ...this.reportForm
        }

        // 模拟API调用
        await this.mockSubmitReport(submitData)

        this.$message.success('续报提交成功')
        this.$emit('confirm', submitData)
      } catch (error) {
        console.error('续报提交失败:', error)
        this.$message.error('续报提交失败')
      } finally {
        this.submitting = false
      }
    },



    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
      this.resetForm()
    },



    resetForm() {
      this.reportForm = {
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      }



      this.$nextTick(() => {
        this.$refs.reportForm && this.$refs.reportForm.clearValidate()
      })
    },




    // 模拟API
    async mockSubmitReport(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('提交续报数据:', data)
          resolve({ success: true })
        }, 1000)
      })
    },

    // 应急研判数据变化处理
    handleAssessDataChange(data) {
      console.log('应急研判数据变化:', data)
      // 这里可以根据研判数据变化做一些业务逻辑处理
    },

    // 事件等级变化处理
    handleEventLevelChange(value) {
      console.log('事件等级变化:', value)
      // 这里可以根据等级变化做一些业务逻辑处理
    },

    // 查看事态控制详情
    handleViewStateControl(row) {
      console.log('查看事态控制详情:', row)
      // EventStateControlList 组件已经处理了详情弹窗，这里只需要记录日志
    },

    // 查看归档总结详情
    handleViewArchiveSummary(row) {
      console.log('查看归档总结详情:', row)
      // EventArchiveSummaryList 组件已经处理了详情弹窗，这里只需要记录日志
    },

  }
}
</script>

<style scoped>
.report-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.assess-info {
  padding: 10px 0;
}

/* 研判区域样式 */
.assess-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.assess-section:last-child {
  margin-bottom: 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.assess-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 兼容旧样式 */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
}

.info-item span {
  flex: 1;
  color: #666;
}

.report-list {
  padding: 10px 0;
}

.add-report-btn {
  text-align: right;
  margin-top: 15px;
}


.dialog-footer {
  text-align: right;
}

/* Element UI 标签页样式调整 */
.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}

/* 为表单控件添加边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.add-btn {
  text-align: right;
  margin-top: 15px;
}

</style>





