<!-- 归档总结详情弹窗 -->
<template>
  <el-dialog
    title="归档总结详情"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    
    <div class="archive-summary-detail">
      <div class="detail-item">
        <label>事件名称：</label>
        <span>{{ archiveSummaryData.eventName }}</span>
      </div>
      
      <div class="detail-item">
        <label>上传人：</label>
        <span>{{ archiveSummaryData.uploader }}</span>
      </div>
      
      <div class="detail-item">
        <label>归档时间：</label>
        <span>{{ archiveSummaryData.archiveTime }}</span>
      </div>
      
      <div class="detail-item summary-content">
        <label>总结报告：</label>
        <div class="summary-text">
          {{ archiveSummaryData.summaryReport }}
        </div>
      </div>
      
      <!-- 附件列表 -->
      <div class="detail-item" v-if="archiveSummaryData.attachments && archiveSummaryData.attachments.length > 0">
        <label>附件：</label>
        <div class="attachments-list">
          <div 
            v-for="(attachment, index) in archiveSummaryData.attachments" 
            :key="index"
            class="attachment-item"
          >
            <el-link 
              type="primary" 
              :underline="false"
              @click="handleDownloadAttachment(attachment)"
            >
              <i class="el-icon-paperclip"></i>
              {{ attachment.name }}
            </el-link>
            <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
          </div>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EventArchiveSummaryDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 归档总结数据
    archiveSummaryData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
    },
    
    // 下载附件
    handleDownloadAttachment(attachment) {
      console.log('下载附件:', attachment)
      // 这里可以实现实际的下载逻辑
      this.$message.info(`下载附件：${attachment.name}`)
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.archive-summary-detail {
  padding: 20px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.detail-item label {
  width: 80px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
  text-align: right;
  padding-top: 8px;
}

.detail-item span {
  flex: 1;
  color: #666;
  word-break: break-all;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

.summary-content {
  align-items: flex-start;
}

.summary-text {
  flex: 1;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  min-height: 100px;
}

.attachments-list {
  flex: 1;
}

.attachment-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-size {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}

.dialog-footer {
  text-align: center;
}
</style>
