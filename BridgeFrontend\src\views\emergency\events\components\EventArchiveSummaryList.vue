<!-- 归档总结记录列表组件 -->
<template>
  <div class="archive-summary-list">
    <el-table :data="archiveSummaryList" border style="width: 100%">
      <el-table-column prop="eventName" label="事件名称" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.eventName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="uploader" label="上传人" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.uploader }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="archiveTime" label="归档时间" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.archiveTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 归档总结详情弹窗 -->
    <EventArchiveSummaryDetailDialog
      :visible.sync="detailDialogVisible"
      :archive-summary-data="selectedArchiveSummaryData"
    />
  </div>
</template>

<script>
import EventArchiveSummaryDetailDialog from './EventArchiveSummaryDetailDialog.vue'

export default {
  name: 'EventArchiveSummaryList',
  components: {
    EventArchiveSummaryDetailDialog
  },
  props: {
    // 归档总结记录列表数据
    archiveSummaryList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      detailDialogVisible: false,
      selectedArchiveSummaryData: {}
    }
  },
  methods: {
    // 查看归档总结详情
    handleView(row) {
      this.selectedArchiveSummaryData = row
      this.detailDialogVisible = true
      // 同时保持向父组件发送事件的兼容性
      this.$emit('view', row)
    }
  }
}
</script>

<style scoped>
.archive-summary-list {
  padding: 10px 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
