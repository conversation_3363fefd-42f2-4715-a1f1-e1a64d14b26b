<!-- 研判 -->
<template>
  <el-dialog
    title="处置应急事件"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    
    <div class="assess-dialog">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="事件信息" name="eventInfo">
          <EventDetailPanel 
            :eventData="eventData"
            :contactList="contactList"
            :photoList="photoList"
            :scrollable="true"
            :maxContactDisplay="10" />
        </el-tab-pane>
        
        <el-tab-pane label="应急研判" name="assessment">
          <el-form :model="assessForm" :rules="assessRules" ref="assessForm" label-width="90px">
            <!-- 初次研判 -->
            <div class="assess-section">
              <el-form-item label="事件分类" prop="eventCategory" :class="{ 'readonly-item': (stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third' }">
                <el-select 
                  v-model="assessForm.eventCategory" 
                  placeholder="请选择事件分类"
                  :disabled="(stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third'"
                  style="width: 50%;">
                  <el-option
                    v-for="item in eventCategoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <el-tooltip placement="right" popper-class="light-blue-tooltip">
                  <div slot="content">
                    <div style="max-width: 300px; background-color: #e3f2fd; padding: 12px; border-radius: 6px;">
                      <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">突发事件分类</div>
                      <div style="line-height: 1.6; color: #333333;">
                        城市桥梁隧道突发事件是指突然发生，造成或可能造成严重危害，需要采取应急处置措施予以应对的自然灾害、事故灾难、社会安全事件。<br><br>
                        <strong style="color: #FF383C;">1.自然灾害：</strong><br>
                        主要包括极端天气导致的桥梁隧道路面结冰、积水、隧道口雨水倒灌等，地震灾害导致的桥梁墩台位移、隧道衬砌开裂等，地质灾害导致的桥梁隧道周边坍塌等。<br><br>
                        <strong style="color: #FF383C;">2.事故灾难：</strong><br>
                        主要包括桥面铺装破损、支座变形或限高设施撞毁、船舶撞击桥梁墩柱、货船倾覆堵塞通航孔、汽车自燃、拉索构件疲劳断裂、局部坍塌、桥梁/隧道关键设备故障、大面积停电等严重影响桥隧安全运行的事件。<br><br>
                        <strong style="color: #FF383C;">3.社会安全事件：</strong><br>
                        主要包括恐怖袭击、火灾爆炸、危化品泄漏、桥下违法施工、人为纵火等。
                      </div>
                    </div>
                  </div>
                  <i class="el-icon-warning-outline" style="margin-left: 5px; color: #E6A23C;"></i>
                </el-tooltip>
              </el-form-item>
              
              <el-form-item label="事件等级" prop="eventLevel" :class="{ 'readonly-item': (stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third' }">
                <el-select 
                  v-model="assessForm.eventLevel" 
                  placeholder="请选择事件等级"
                  :disabled="(stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third'"
                  @change="handleEventLevelChange"
                  style="width: 50%;">
                  <el-option
                    v-for="item in eventLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <el-tooltip placement="right" popper-class="light-blue-tooltip">
                  <div slot="content">
                    <div style="max-width: 300px; background-color: #e3f2fd; padding: 12px; border-radius: 6px; ">
                      <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">具体分级标准</div>
                      <div style="line-height: 1.6; color: #333333;">
                        根据事件性质、伤亡损失、交通影响及结构损坏程度，分为较小事件、一般事件、较大及以上事件三类，明确处置主体与信息报送路径。<br><br>
                        <strong style="color: #FF383C;">1.较小事件：</strong><br>
                        无人员伤亡，桥隧结构未受损，附属设施局部损坏；造成局部交通拥堵，经抢修3小时内可恢复；直接经济损失100万元以下。<br><br>
                        <strong style="color: #FF383C;">2.一般事件：</strong><br>
                        无人员伤亡，1000万元以下直接经济损失的事故；桥隧结构设施缺陷可能引发安全运营事件；自然灾害、突发事件导致交通拥堵或行车中断，经抢修3小时内无法恢复通车。<br><br>
                        <strong style="color: #FF383C;">3.较大及以上事件：</strong><br>
                        造成人员伤亡或3人以上重伤，或1000万元以上直接经济损失的事故；桥隧结构损坏或附属设施严重损坏，威胁人员安全；自然灾害、突发事件导致部分道路交通受较大影响，交通拥堵或行车中断，经抢修10小时内无法恢复通车。
                      </div>
                    </div>
                  </div>
                  <i class="el-icon-warning-outline" style="margin-left: 5px; color: #E6A23C;"></i>
                </el-tooltip>
              </el-form-item>
              
              <el-form-item label="应急责任单位" prop="responsibleUnit" :class="{ 'readonly-item': (stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third' }">
                <el-select 
                  v-model="assessForm.responsibleUnit" 
                  placeholder="请选择应急责任单位"
                  :disabled="(stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third'"
                  style="width: 50%;">
                  <el-option
                    v-for="item in responsibleUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="备注">
                <el-input
                  v-model="assessForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  :disabled="(stage === 'pending' && hasInitialAssess) || stage === 'second' || stage === 'third'">
                </el-input>
              </el-form-item>
            </div>
            
            <!-- 二级研判 -->
            <div v-if="stage === 'pending' || stage === 'second'" class="assess-section">
              <h4>二级研判</h4>
              
              <el-form-item label="事件等级" prop="secondEventLevel">
                <el-select 
                  v-model="assessForm.secondEventLevel" 
                  placeholder="请选择事件等级"
                  @change="handleSecondEventLevelChange"
                  style="width: 50%;">
                  <el-option
                    v-for="item in eventLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="应急责任单位" prop="secondResponsibleUnit">
                <el-select 
                  v-model="assessForm.secondResponsibleUnit" 
                  placeholder="请选择应急责任单位"
                  style="width: 50%;">
                  <el-option
                    v-for="item in responsibleUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="备注">
                <el-input
                  v-model="assessForm.secondRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入">
                </el-input>
              </el-form-item>
            </div>
            
            <!-- 二级研判历史记录（在三级研判时显示） -->
            <div v-if="stage === 'third'" class="assess-section">
              <h4>二级研判</h4>
              
              <el-form-item label="事件等级" prop="secondEventLevel">
                <el-select 
                  v-model="assessForm.secondEventLevel" 
                  placeholder="请选择事件等级"
                  disabled
                  style="width: 50%;">
                  <el-option
                    v-for="item in eventLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="应急责任单位" prop="secondResponsibleUnit">
                <el-select 
                  v-model="assessForm.secondResponsibleUnit" 
                  placeholder="请选择应急责任单位"
                  disabled
                  style="width: 50%;">
                  <el-option
                    v-for="item in responsibleUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="备注">
                <el-input
                  v-model="assessForm.secondRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  disabled>
                </el-input>
              </el-form-item>
            </div>
            
            <!-- 三级研判 -->
            <div v-if="stage === 'third'" class="assess-section">
              <h4>三级研判</h4>
              
              <el-form-item label="事件等级" prop="thirdEventLevel">
                <el-select 
                  v-model="assessForm.thirdEventLevel" 
                  placeholder="请选择事件等级"
                  @change="handleThirdEventLevelChange"
                  style="width: 50%;">
                  <el-option
                    v-for="item in eventLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="应急责任单位" prop="thirdResponsibleUnit">
                <el-select 
                  v-model="assessForm.thirdResponsibleUnit" 
                  placeholder="请选择应急责任单位"
                  style="width: 50%;">
                  <el-option
                    v-for="item in responsibleUnitOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="备注">
                <el-input
                  v-model="assessForm.thirdRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入">
                </el-input>
              </el-form-item>
            </div>
            
            <!-- 是否上报 -->
            <el-form-item v-if="stage !== 'third'" label="是否上报" prop="needReport">
              <el-radio-group v-model="assessForm.needReport">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitting">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import EventDetailPanel from './EventDetailPanel.vue'

export default {
  name: 'EventAssessDialog',
  components: {
    EventDetailPanel
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    eventData: {
      type: Object,
      default: () => ({})
    },
    stage: {
      type: String,
      default: 'reported' // reported: 初次研判, pending: 二级研判, third: 三级研判
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'eventInfo',
      submitting: false,
      hasInitialAssess: false, // 是否已有初次研判
      
      assessForm: {
        eventCategory: '',
        eventLevel: '',
        responsibleUnit: '',
        remark: '',
        secondEventLevel: '',
        secondResponsibleUnit: '',
        secondRemark: '',
        thirdEventLevel: '',
        thirdResponsibleUnit: '',
        thirdRemark: '',
        needReport: true
      },
      
      assessRules: {
        eventCategory: [
          { required: true, message: '请选择事件分类', trigger: 'change' }
        ],
        eventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        responsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ],
        secondEventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        secondResponsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ],
        thirdEventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        thirdResponsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ],
        needReport: [
          { required: true, message: '请选择是否上报', trigger: 'change' }
        ]
      },
      
      eventCategoryOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '4', label: '社会安全事件' }
      ],
      
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],
      
      responsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ],
      
      // 接警人数据
      contactList: [
        { id: 1, name: '李明' },
        { id: 2, name: '张伟' },
        { id: 3, name: '王强' },
        { id: 4, name: '刘洋' },


      ],
      
      // 现场照片数据
      photoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') }
      ]
    }
  },
  computed: {
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initForm() {
      // 如果是二级研判，需要显示初次研判的数据
      if (this.stage === 'pending' && this.eventData) {
        this.hasInitialAssess = true
        this.assessForm.eventCategory = this.eventData.eventCategory || '1'
        this.assessForm.eventLevel = this.eventData.eventLevel || '1'
        this.assessForm.responsibleUnit = this.eventData.responsibleUnit || '1'
        this.assessForm.remark = this.eventData.remark || ''
      }
      
      // 如果是三级研判，需要显示初次和二级研判的数据
      if (this.stage === 'third' && this.eventData) {
        this.hasInitialAssess = true
        // 初次研判数据
        this.assessForm.eventCategory = this.eventData.eventCategory || '1'
        this.assessForm.eventLevel = this.eventData.eventLevel || '1'
        this.assessForm.responsibleUnit = this.eventData.responsibleUnit || '1'
        this.assessForm.remark = this.eventData.remark || ''
        // 二级研判数据
        this.assessForm.secondEventLevel = this.eventData.secondEventLevel || ''
        this.assessForm.secondResponsibleUnit = this.eventData.secondResponsibleUnit || ''
        this.assessForm.secondRemark = this.eventData.secondRemark || ''
      }
      
      // 切换到研判标签页
      this.activeTab = 'assessment'
    },
    
    handleEventLevelChange(level) {
      // 事件等级联动应急责任单位
      const levelUnitMap = {
        '1': '1', // 较小事件 -> 市桥隧事务中心
        '2': '2', // 一般事件 -> 市城管局
        '3': '3', // 较大事件 -> 市应急和安全生产委员会
        '4': '3'  // 重大事件 -> 市应急和安全生产委员会
      }
      this.assessForm.responsibleUnit = levelUnitMap[level] || '1'
    },
    
    handleSecondEventLevelChange(level) {
      // 二级研判事件等级联动应急责任单位
      const levelUnitMap = {
        '1': '1', // 较小事件 -> 市桥隧事务中心
        '2': '2', // 一般事件 -> 市城管局
        '3': '3', // 较大事件 -> 市应急和安全生产委员会
        '4': '3'  // 重大事件 -> 市应急和安全生产委员会
      }
      this.assessForm.secondResponsibleUnit = levelUnitMap[level] || '1'
    },
    
    handleThirdEventLevelChange(level) {
      // 三级研判事件等级联动应急责任单位
      const levelUnitMap = {
        '1': '1', // 较小事件 -> 市桥隧事务中心
        '2': '2', // 一般事件 -> 市城管局
        '3': '3', // 较大事件 -> 市应急和安全生产委员会
        '4': '3'  // 重大事件 -> 市应急和安全生产委员会
      }
      this.assessForm.thirdResponsibleUnit = levelUnitMap[level] || '1'
    },
    
    handleConfirm() {
      this.$refs.assessForm.validate(valid => {
        if (valid) {
          this.submitAssess()
        }
      })
    },
    
    async submitAssess() {
      this.submitting = true
      try {
        // 构造提交数据
        const submitData = {
          eventId: this.eventData.id,
          stage: this.stage,
          ...this.assessForm
        }
        
        // 模拟API调用
        await this.mockSubmitAssess(submitData)
        
        this.$message.success('研判提交成功')
        this.$emit('confirm', submitData)
      } catch (error) {
        console.error('研判提交失败:', error)
        this.$message.error('研判提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
      this.resetForm()
    },
    
    resetForm() {
      this.assessForm = {
        eventCategory: '',
        eventLevel: '',
        responsibleUnit: '',
        remark: '',
        secondEventLevel: '',
        secondResponsibleUnit: '',
        secondRemark: '',
        thirdEventLevel: '',
        thirdResponsibleUnit: '',
        thirdRemark: '',
        needReport: true
      }
      this.$nextTick(() => {
        this.$refs.assessForm && this.$refs.assessForm.clearValidate()
      })
    },
    
    // 模拟API
    async mockSubmitAssess(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('提交研判数据:', data)
          resolve({ success: true })
        }, 1000)
      })
    }
  }
}
</script>

<style scoped>
.assess-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.assess-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.assess-section:last-child {
  border-bottom: none;
}

.assess-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.readonly-item {
  opacity: 0.6;
}

.readonly-item .el-form-item__content {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

/* Element UI 标签页样式调整 */
.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}

/* 为表单控件添加边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-radio-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

/* 自定义tooltip样式 - 浅蓝色主题 */
::v-deep .el-tooltip__popper[x-placement^="right"] {
  background-color: #e3f2fd !important;
  border: 1px solid #90caf9 !important;
}

::v-deep .el-tooltip__popper[x-placement^="right"] .popper__arrow {
  border-right-color: #90caf9 !important;
}

::v-deep .el-tooltip__popper[x-placement^="right"] .popper__arrow::after {
  border-right-color: #e3f2fd !important;
}

/* 通用tooltip样式覆盖 */
::v-deep .el-tooltip__popper.is-dark {
  background-color: #e3f2fd !important;
  border: 1px solid #90caf9 !important;
  color: #333333 !important;
}

::v-deep .el-tooltip__popper.is-dark .popper__arrow {
  border-top-color: #90caf9 !important;
  border-bottom-color: #90caf9 !important;
  border-left-color: #90caf9 !important;
  border-right-color: #90caf9 !important;
}

::v-deep .el-tooltip__popper.is-dark .popper__arrow::after {
  border-top-color: #e3f2fd !important;
  border-bottom-color: #e3f2fd !important;
  border-left-color: #e3f2fd !important;
  border-right-color: #e3f2fd !important;
}
</style>






