<template>
  <div class="assess-history-panel">
    <el-form :model="assessData" :rules="assessRules" ref="assessForm" label-width="90px">
      <!-- 初次研判 -->
      <div class="assess-section">
        <h4 v-if="showSectionTitles">初次研判</h4>
        
        <el-form-item label="事件分类" prop="eventCategory" :class="{ 'readonly-item': readOnly }">
          <el-select 
            v-model="assessData.eventCategory" 
            placeholder="请选择事件分类"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in eventCategoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-tooltip placement="right" popper-class="light-blue-tooltip">
            <div slot="content">
              <div style="max-width: 300px; background-color: #e3f2fd; padding: 12px; border-radius: 6px;">
                <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">突发事件分类</div>
                <div style="line-height: 1.6; color: #333333;">
                  城市桥梁隧道突发事件是指突然发生，造成或可能造成严重危害，需要采取应急处置措施予以应对的自然灾害、事故灾难、社会安全事件。<br><br>
                  <strong style="color: #FF383C;">1.自然灾害：</strong><br>
                  主要包括极端天气导致的桥梁隧道路面结冰、积水、隧道口雨水倒灌等，地震灾害导致的桥梁墩台位移、隧道衬砌开裂等，地质灾害导致的桥梁隧道周边坍塌等。<br><br>
                  <strong style="color: #FF383C;">2.事故灾难：</strong><br>
                  主要包括桥面铺装破损、支座变形或限高设施撞毁、船舶撞击桥梁墩柱、货船倾覆堵塞通航孔、汽车自燃、拉索构件疲劳断裂、局部坍塌、桥梁/隧道关键设备故障、大面积停电等严重影响桥隧安全运行的事件。<br><br>
                  <strong style="color: #FF383C;">3.社会安全事件：</strong><br>
                  主要包括恐怖袭击、火灾爆炸、危化品泄漏、桥下违法施工、人为纵火等。
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline" style="margin-left: 5px; color: #E6A23C;"></i>
          </el-tooltip>
        </el-form-item>
        
        <el-form-item label="事件等级" prop="eventLevel" :class="{ 'readonly-item': readOnly }">
          <el-select 
            v-model="assessData.eventLevel" 
            placeholder="请选择事件等级"
            :disabled="readOnly"
            @change="handleEventLevelChange"
            style="width: 50%;">
            <el-option
              v-for="item in eventLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-tooltip placement="right" popper-class="light-blue-tooltip">
            <div slot="content">
              <div style="max-width: 300px; background-color: #e3f2fd; padding: 12px; border-radius: 6px; ">
                <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">具体分级标准</div>
                <div style="line-height: 1.6; color: #333333;">
                  根据事件性质、伤亡损失、交通影响及结构损坏程度，分为较小事件、一般事件、较大及以上事件三类，明确处置主体与信息报送路径。<br><br>
                  <strong style="color: #FF383C;">1.较小事件：</strong><br>
                  无人员伤亡，桥隧结构未受损，附属设施局部损坏；造成局部交通拥堵，经抢修3小时内可恢复；直接经济损失100万元以下。<br><br>
                  <strong style="color: #FF383C;">2.一般事件：</strong><br>
                  无人员伤亡，1000万元以下直接经济损失的事故；桥隧结构设施缺陷可能引发安全运营事件；自然灾害、突发事件导致交通拥堵或行车中断，经抢修3小时内无法恢复通车。<br><br>
                  <strong style="color: #FF383C;">3.较大及以上事件：</strong><br>
                  造成人员伤亡或3人以上重伤，或1000万元以上直接经济损失的事故；桥隧结构损坏或附属设施严重损坏，威胁人员安全；自然灾害、突发事件导致部分道路交通受较大影响，交通拥堵或行车中断，经抢修10小时内无法恢复通车。
                </div>
              </div>
            </div>
            <i class="el-icon-warning-outline" style="margin-left: 5px; color: #E6A23C;"></i>
          </el-tooltip>
        </el-form-item>
        
        <el-form-item label="应急责任单位" prop="responsibleUnit" :class="{ 'readonly-item': readOnly }">
          <el-select 
            v-model="assessData.responsibleUnit" 
            placeholder="请选择应急责任单位"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in responsibleUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="assessData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入"
            :disabled="readOnly">
          </el-input>
        </el-form-item>
      </div>
      
      <!-- 二级研判历史记录 -->
      <div v-if="hasSecondAssessment" class="assess-section">
        <h4>二级研判</h4>
        
        <el-form-item label="事件等级" prop="secondEventLevel">
          <el-select 
            v-model="assessData.secondEventLevel" 
            placeholder="请选择事件等级"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in eventLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="应急责任单位" prop="secondResponsibleUnit">
          <el-select 
            v-model="assessData.secondResponsibleUnit" 
            placeholder="请选择应急责任单位"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in responsibleUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="assessData.secondRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入"
            :disabled="readOnly">
          </el-input>
        </el-form-item>
      </div>
      
      <!-- 三级研判历史记录 -->
      <div v-if="hasThirdAssessment" class="assess-section">
        <h4>三级研判</h4>
        
        <el-form-item label="事件等级" prop="thirdEventLevel">
          <el-select 
            v-model="assessData.thirdEventLevel" 
            placeholder="请选择事件等级"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in eventLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应急责任单位" prop="thirdResponsibleUnit">
          <el-select 
            v-model="assessData.thirdResponsibleUnit" 
            placeholder="请选择应急责任单位"
            :disabled="readOnly"
            style="width: 50%;">
            <el-option
              v-for="item in responsibleUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="assessData.thirdRemark"
            type="textarea"
            :rows="3"
            placeholder="请输入"
            :disabled="readOnly">
          </el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EventAssessHistoryPanel',
  props: {
    // 事件数据对象
    eventData: {
      type: Object,
      default: () => ({})
    },
    // 是否只读模式
    readOnly: {
      type: Boolean,
      default: true
    },
    // 是否显示分段标题
    showSectionTitles: {
      type: Boolean,
      default: false
    },
    // 是否启用表单验证
    enableValidation: {
      type: Boolean,
      default: false
    },
    // 自定义事件分类选项
    customEventCategoryOptions: {
      type: Array,
      default: null
    },
    // 自定义事件等级选项
    customEventLevelOptions: {
      type: Array,
      default: null
    },
    // 自定义责任单位选项
    customResponsibleUnitOptions: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      assessData: {
        eventCategory: '',
        eventLevel: '',
        responsibleUnit: '',
        remark: '',
        secondEventLevel: '',
        secondResponsibleUnit: '',
        secondRemark: '',
        thirdEventLevel: '',
        thirdResponsibleUnit: '',
        thirdRemark: ''
      },
      
      // 默认事件分类选项
      defaultEventCategoryOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '3', label: '公共卫生事件' },
        { value: '4', label: '社会安全事件' }
      ],
      
      // 默认事件等级选项
      defaultEventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],
      
      // 默认责任单位选项
      defaultResponsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ]
    }
  },
  computed: {
    // 事件分类选项
    eventCategoryOptions() {
      return this.customEventCategoryOptions || this.defaultEventCategoryOptions
    },
    
    // 事件等级选项
    eventLevelOptions() {
      return this.customEventLevelOptions || this.defaultEventLevelOptions
    },
    
    // 责任单位选项
    responsibleUnitOptions() {
      return this.customResponsibleUnitOptions || this.defaultResponsibleUnitOptions
    },
    
    // 是否有二级研判
    hasSecondAssessment() {
      return this.assessData.secondEventLevel || 
             this.assessData.secondResponsibleUnit || 
             this.assessData.secondRemark
    },
    
    // 是否有三级研判
    hasThirdAssessment() {
      return this.assessData.thirdEventLevel || 
             this.assessData.thirdResponsibleUnit || 
             this.assessData.thirdRemark
    },
    
    // 表单验证规则
    assessRules() {
      if (!this.enableValidation) {
        return {}
      }
      
      return {
        eventCategory: [
          { required: true, message: '请选择事件分类', trigger: 'change' }
        ],
        eventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        responsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ],
        secondEventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        secondResponsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ],
        thirdEventLevel: [
          { required: true, message: '请选择事件等级', trigger: 'change' }
        ],
        thirdResponsibleUnit: [
          { required: true, message: '请选择应急责任单位', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    eventData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.initAssessData()
        }
      }
    },
    
    // 监听assessData变化，向父组件发出数据变更事件
    assessData: {
      deep: true,
      handler(newVal) {
        this.$emit('data-change', newVal)
      }
    }
  },
  methods: {
    // 初始化研判数据
    initAssessData() {
      if (!this.eventData) return
      
      this.assessData = {
        eventCategory: this.eventData.eventCategory || '',
        eventLevel: this.eventData.eventLevel || '',
        responsibleUnit: this.eventData.responsibleUnit || '',
        remark: this.eventData.remark || '',
        secondEventLevel: this.eventData.secondEventLevel || '',
        secondResponsibleUnit: this.eventData.secondResponsibleUnit || '',
        secondRemark: this.eventData.secondRemark || '',
        thirdEventLevel: this.eventData.thirdEventLevel || '',
        thirdResponsibleUnit: this.eventData.thirdResponsibleUnit || '',
        thirdRemark: this.eventData.thirdRemark || ''
      }
    },
    
    // 事件等级变化处理
    handleEventLevelChange(value) {
      this.$emit('level-change', value)
    },
    
    // 获取表单数据
    getFormData() {
      return { ...this.assessData }
    },
    
    // 设置表单数据
    setFormData(data) {
      this.assessData = { ...this.assessData, ...data }
    },
    
    // 表单验证
    validate() {
      if (!this.enableValidation) {
        return Promise.resolve(true)
      }
      
      return new Promise((resolve) => {
        this.$refs.assessForm.validate((valid) => {
          resolve(valid)
        })
      })
    },
    
    // 清除验证
    clearValidate() {
      if (this.$refs.assessForm) {
        this.$refs.assessForm.clearValidate()
      }
    },
    
    // 重置表单
    resetForm() {
      this.assessData = {
        eventCategory: '',
        eventLevel: '',
        responsibleUnit: '',
        remark: '',
        secondEventLevel: '',
        secondResponsibleUnit: '',
        secondRemark: '',
        thirdEventLevel: '',
        thirdResponsibleUnit: '',
        thirdRemark: ''
      }
      this.clearValidate()
    },
    
    // 获取事件分类标签
    getEventCategoryLabel(value) {
      const option = this.eventCategoryOptions.find(item => item.value === value)
      return option ? option.label : ''
    },
    
    // 获取事件等级标签
    getEventLevelLabel(value) {
      const option = this.eventLevelOptions.find(item => item.value === value)
      return option ? option.label : ''
    },
    
    // 获取应急责任单位标签
    getResponsibleUnitLabel(value) {
      const option = this.responsibleUnitOptions.find(item => item.value === value)
      return option ? option.label : ''
    }
  }
}
</script>

<style scoped>
.assess-history-panel {
  background-color: #fff;
}

/* 研判区域样式 */
.assess-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.assess-section:last-child {
  margin-bottom: 0;
}

.assess-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

/* 只读模式样式 */
.readonly-item ::v-deep .el-form-item__label {
  color: #909399;
}

.readonly-item ::v-deep .el-select .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

.readonly-item ::v-deep .el-textarea__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

/* 表单控件边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

/* 工具提示图标样式 */
.el-icon-warning-outline {
  cursor: pointer;
  transition: color 0.3s;
}

.el-icon-warning-outline:hover {
  color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assess-section {
    padding: 12px;
  }
  
  .assess-section h4 {
    font-size: 13px;
  }
  
  ::v-deep .el-form-item__label {
    width: 80px !important;
    font-size: 13px;
  }
  
  ::v-deep .el-select,
  ::v-deep .el-input,
  ::v-deep .el-textarea {
    width: 100% !important;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .assess-section {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .assess-section h4 {
    color: #e4e7ed;
    border-bottom-color: #404040;
  }
}
</style>
