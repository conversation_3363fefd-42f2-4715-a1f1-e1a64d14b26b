<template>
  <el-dialog
    title="查看应急事件"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    
    <EventDetailPanel 
      :eventData="eventData"
      :contactList="contactList"
      :photoList="photoList"
      :showContacts="true"
      :showPhotos="true"
      :scrollable="true"
      :maxContactDisplay="15" />
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import EventDetailPanel from './EventDetailPanel.vue'

export default {
  name: 'EventDetailDialog',
  components: {
    EventDetailPanel
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    eventData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      // 接警人数据（可选，如果不传入，组件将使用默认数据）
      contactList: [
        { id: 1, name: '刘金鑫' },
        { id: 2, name: '李明宇' },
        { id: 3, name: '林文龙' },
        { id: 4, name: '徐振德' },
        { id: 5, name: '朱清国' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },
        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' },
        { id: 10, name: '黄财一' },
        { id: 11, name: '黄财二' },
        { id: 12, name: '黄财三' },
        { id: 13, name: '张三' }
      ],
      // 照片数据（可选，如果不传入，组件将使用默认数据）
      photoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') },
        { url: require('@/assets/images/emergency/event-detail/scene4.png') },
        { url: require('@/assets/images/emergency/event-detail/scene5.png') },
        { url: require('@/assets/images/emergency/event-detail/scene6.png') },
        { url: require('@/assets/images/emergency/event-detail/scene7.png') },
        { url: require('@/assets/images/emergency/event-detail/scene8.png') }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  margin-left: 50px;
  margin-right: 50px;
}

.dialog-footer {
  text-align: right;
}
</style>





