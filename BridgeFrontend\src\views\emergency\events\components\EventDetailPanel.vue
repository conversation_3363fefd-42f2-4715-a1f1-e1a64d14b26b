<template>
  <div class="event-detail-panel" v-if="eventData">
    <!-- 事件名称 -->
    <div class="detail-item">
      <label>事件名称：</label>
      <span>{{ eventData.eventName }}</span>
    </div>
    
    <!-- 桥梁/隧道名称 -->
    <div class="detail-item">
      <label>{{ structureLabel }}：</label>
      <span>{{ eventData.structureName }}</span>
    </div>
    
    <!-- 发生位置 -->
    <div class="detail-item">
      <label>发生位置：</label>
      <span>
        <i class="el-icon-location" style="color: #409EFF; margin-right: 5px;"></i>
        {{ eventData.location }}
      </span>
    </div>
    
    <!-- 触发时间 -->
    <div class="detail-item">
      <label>触发时间：</label>
      <span>{{ eventData.triggerTime }}</span>
    </div>
    
    <!-- 事件类型 -->
    <div class="detail-item">
      <label>事件类型：</label>
      <span>{{ eventData.eventType }}</span>
    </div>
    
    <!-- 受伤人数 -->
    <div class="detail-item">
      <label>受伤人数：</label>
      <span>{{ eventData.injuredCount || 0 }}</span>
    </div>
    
    <!-- 死亡人数 -->
    <div class="detail-item">
      <label>死亡人数：</label>
      <span>{{ eventData.deathCount || 0 }}</span>
    </div>
    
    <!-- 设施损坏 -->
    <div class="detail-item">
      <label>设施损坏：</label>
      <span>{{ eventData.facilityDamage || '轻微' }}</span>
    </div>
    
    <!-- 现场情况 -->
    <div class="detail-item">
      <label>现场情况：</label>
      <span>{{ eventData.sceneDescription || '未造成交通瓶颈，桥梁通行情况正常' }}</span>
    </div>
    
    <!-- 接警人 -->
    <div class="detail-item" v-if="showContacts">
      <label>接警人：</label>
      <div class="contact-container">
        <div class="contact-tags" :class="{ 'collapsed': !contactExpanded }">
          <span
            v-for="contact in displayContactList"
            :key="contact.id"
            class="contact-name">
            {{ contact.name }}
          </span>
        </div>
        <div v-if="displayContactList.length > maxContactDisplay" class="expand-btn" @click="toggleContactExpanded">
          <i :class="contactExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          {{ contactExpanded ? '收起' : '展开' }}
        </div>
      </div>
    </div>
    
    <!-- 现场照片 -->
    <div class="detail-item photo-section" v-if="showPhotos">
      <label>现场照片：</label>
      <div class="photo-grid">
        <div 
          v-for="(photo, index) in displayPhotoList"
          :key="index"
          class="photo-item"
          @click="handlePhotoPreview(photo)">
          <el-image
            :src="photo.url"
            :preview-src-list="photoPreviewList"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDetailPanel',
  props: {
    // 事件数据
    eventData: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 是否显示接警人信息
    showContacts: {
      type: Boolean,
      default: true
    },
    // 是否显示现场照片
    showPhotos: {
      type: Boolean,
      default: true
    },
    // 自定义接警人列表
    contactList: {
      type: Array,
      default: () => []
    },
    // 自定义照片列表
    photoList: {
      type: Array,
      default: () => []
    },
    // 接警人最大显示数量（超过此数量显示展开按钮）
    maxContactDisplay: {
      type: Number,
      default: 15
    },
    // 是否可滚动（当在弹窗中使用时设为true）
    scrollable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      contactExpanded: false,
      // 默认接警人数据
      defaultContactList: [
        { id: 1, name: '刘金鑫' },
        { id: 2, name: '李明宇' },
        { id: 3, name: '林文龙' },
        { id: 4, name: '徐振德' },
        { id: 5, name: '朱清国' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },
        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' },
        { id: 10, name: '黄财一' },
        { id: 11, name: '黄财二' },
        { id: 12, name: '黄财三' },
        { id: 13, name: '张三' }
      ],
      // 默认照片数据
      defaultPhotoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') },
        { url: require('@/assets/images/emergency/event-detail/scene4.png') },
        { url: require('@/assets/images/emergency/event-detail/scene5.png') },
        { url: require('@/assets/images/emergency/event-detail/scene6.png') },
        { url: require('@/assets/images/emergency/event-detail/scene7.png') },
        { url: require('@/assets/images/emergency/event-detail/scene8.png') }
      ]
    }
  },
  computed: {
    // 智能判断桥梁/隧道标签
    structureLabel() {
      return this.eventData.structureName && this.eventData.structureName.includes('隧道') ? '隧道名称' : '桥梁名称'
    },
    
    // 显示的接警人列表（优先使用传入的，否则使用默认的）
    displayContactList() {
      return this.contactList.length > 0 ? this.contactList : this.defaultContactList
    },
    
    // 显示的照片列表（优先使用传入的，否则使用默认的）
    displayPhotoList() {
      return this.photoList.length > 0 ? this.photoList : this.defaultPhotoList
    },
    
    // 照片预览列表
    photoPreviewList() {
      return this.displayPhotoList.map(photo => photo.url)
    }
  },
  methods: {
    // 切换接警人展开/收起
    toggleContactExpanded() {
      this.contactExpanded = !this.contactExpanded
    },
    
    // 处理照片预览
    handlePhotoPreview(photo) {
      // Element UI 的图片预览会自动处理
    }
  }
}
</script>

<style scoped>
.event-detail-panel {
  padding: 0;
}

.event-detail-panel.scrollable {
  max-height: 500px;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

/* 现场照片区域特殊处理 */
.detail-item.photo-section {
  border: none;
  background-color: transparent;
  padding: 8px 0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  width: 80px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
  text-align: right;
  padding-top: 8px;
}

.detail-item span {
  flex: 1;
  color: #666;
  word-break: break-all;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

/* 接警人样式 */
.contact-container {
  flex: 1;
}

.contact-tags {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
  min-height: 20px;
  justify-items: start;
  transition: max-height 0.3s ease;
}

.contact-tags.collapsed {
  max-height: 60px;
  overflow: hidden;
}

.contact-name {
  display: block;
  padding: 4px 4px;
  border: 1px solid #409EFF;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  width: 70px;
  box-sizing: border-box;
  background-color: #fff;
  color: #333;
}

.expand-btn {
  margin-top: 8px;
  text-align: center;
  cursor: pointer;
  color: #409EFF;
  font-size: 12px;
  transition: color 0.3s ease;
}

.expand-btn:hover {
  color: #66b1ff;
}

/* 照片网格样式 */
.photo-grid {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  /* 如果 gap 不支持，使用下面的 margin 方式 */
  margin: -5px; /* 抵消子元素的 margin */
}

.photo-item {
  width: 114px;
  height: 107px;
  cursor: pointer;
  margin: 5px; /* 这样每个元素间距就是 10px */
  flex-shrink: 0; /* 防止缩放 */
}

.photo-item .el-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-tags {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .photo-grid {
    gap: 8px;
    margin: -4px;
  }
  
  .photo-item {
    width: 100px;
    height: 90px;
    margin: 4px;
  }
}

@media (max-width: 480px) {
  .detail-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .detail-item label {
    width: auto;
    text-align: left;
    margin-bottom: 5px;
  }
  
  .contact-tags {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
