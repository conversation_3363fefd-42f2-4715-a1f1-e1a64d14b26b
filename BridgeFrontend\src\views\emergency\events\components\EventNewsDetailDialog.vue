<!-- 新闻通稿详情查看弹窗 -->
<template>
  <el-dialog
    title="新闻通稿详情"
    :visible="dialogVisible"
    width="600px"
    @close="handleClose"
    class="news-detail-dialog">
    
    <div class="template-content">
      <div class="template-header">
        <h3>{{ newsData.eventName || '突发事件' }}新闻通稿</h3>

      </div>
      
      <div class="template-body">
        <p class="news-content">
          {{ generateNewsTemplate() }}
        </p>
      </div>
      
      <!--
      <div class="template-actions">
        <el-button @click="handleCopy" type="primary" icon="el-icon-document-copy">
          复制内容
        </el-button>
        <el-button @click="handlePrint" type="default" icon="el-icon-printer">
          打印
        </el-button>
        <el-button @click="handleExport" type="default" icon="el-icon-download">
          导出
        </el-button>
      </div>
      -->
    </div>
    
    <!--
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
    -->
  </el-dialog>
</template>

<script>
export default {
  name: 'EventNewsDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    newsData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 生成新闻通稿模板内容
    generateNewsTemplate() {
      const data = this.newsData
      
      // 获取当前时间信息
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const day = now.getDate()
      const hour = now.getHours()
      const minute = now.getMinutes()
      
      // 构建模板内容
      const template = `${year}年${month}月${day}日${hour}左右，${data.bridgeName || 'XX桥梁/隧道'}${data.location || 'XX位置'}，发生${data.eventType || 'XX事件'}。事发后，长沙市委、市政府及相关部门高度重视，迅速启动应急预案，实施${data.workContent || 'XX工作'}。

截至${month}日${hour}点${minute}分，${data.unitDepartment || 'XX单位'}、${data.departmentName || 'XX部门'}和${data.companyName || 'XX公司'}等单位联合对桥梁/隧道检测及初步评估已完成，初步分析目前桥梁/隧道总体稳定，处于安全状态。

根据检测结果和专家组意见，${data.trafficRecoveryTime ? this.formatRecoveryTime(data.trafficRecoveryTime) : 'XX点XX分'}，${data.bridgeName || 'XX桥梁/隧道'}已开放交通。`
      
      return template
    },
    
    // 格式化交通恢复时间
    formatRecoveryTime(time) {
      if (!time) return 'XX点XX分'
      
      const date = new Date(time)
      const hour = date.getHours()
      const minute = date.getMinutes()
      
      return `${hour}点${minute}分`
    },
    
    // 复制内容到剪贴板
    async handleCopy() {
      try {
        const content = this.generateNewsTemplate()
        await navigator.clipboard.writeText(content)
        this.$message.success('内容已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动复制')
      }
    },
    
    // 打印功能
    handlePrint() {
      const content = this.generateNewsTemplate()
      const printWindow = window.open('', '_blank')
      
      printWindow.document.write(`
        <html>
          <head>
            <title>新闻通稿 - ${this.newsData.eventName || '突发事件'}</title>
            <style>
              body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.8;
                margin: 40px;
                color: #333;
              }
              .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
              }
              .content {
                font-size: 16px;
                text-indent: 2em;
                white-space: pre-line;
              }
              .meta {
                margin-top: 30px;
                text-align: right;
                color: #666;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>${this.newsData.eventName || '突发事件'}新闻通稿</h2>
            </div>
            <div class="content">${content}</div>
            <div class="meta">
              <p>发布人：${this.newsData.publisher || '-'}</p>
              <p>发布时间：${this.newsData.publishTime || '-'}</p>
            </div>
          </body>
        </html>
      `)
      
      printWindow.document.close()
      printWindow.print()
    },
    
    // 导出功能
    handleExport() {
      const content = this.generateNewsTemplate()
      const fileName = `新闻通稿-${this.newsData.eventName || '突发事件'}-${new Date().toISOString().slice(0, 10)}.txt`
      
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      this.$message.success('导出成功')
    },
    
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.news-detail-dialog {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.template-content {
  padding: 20px 0;
}

.template-header {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 30px;
  border-top: 2px solid #e4e7ed;
}

.template-header h3 {
  margin: 0 0 15px 0;
  font-size: 20px;
  color: #303133;
  font-weight: 600;
}

.meta-info {
  color: #909399;
  font-size: 14px;
}

.template-body {
  margin-left: 66px;
  margin-right: 66px;
  margin-bottom: 130px;
}

.news-content {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin: 0 0 15px 0;
  text-align: justify;
  white-space: pre-line;
}

.template-actions {
  text-align: center;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.template-actions .el-button {
  margin: 0 10px;
}

.dialog-footer {
  text-align: center;
}

/* 深层样式覆盖 */
::v-deep .el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-dialog__title {
  font-weight: 600;
  color: #303133;
}

::v-deep .el-dialog__body {
  padding: 20px 30px;
}

::v-deep .el-dialog__footer {
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
}
</style>
