<!-- 新闻通稿生成表单组件 -->
<template>
  <div class="news-form">
    <el-form :model="formData" :rules="formRules" ref="newsForm" label-width="120px">
      <el-form-item label="工作内容" prop="workContent">
        <!--
        <el-input
          v-model="formData.workContent"
          type="textarea"
          :rows="4"
          placeholder="请输入工作内容">
        </el-input>
        -->
        <el-input v-model="formData.workContent" placeholder="请输入工作内容"></el-input>
      </el-form-item>

      <el-form-item label="单位名称" prop="unitDepartment">
        <el-select v-model="formData.unitDepartment" placeholder="请选择单位部门" style="width: 50%;">
          <el-option
            v-for="item in unitDepartmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="部门名称" prop="departmentName">
        <el-select v-model="formData.departmentName" placeholder="请选择部门" style="width: 50%;">
          <el-option
            v-for="item in departmentNameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="公司名称" prop="companyName">
        <el-select v-model="formData.companyName" placeholder="请选择公司" style="width: 50%;">
          <el-option
            v-for="item in companyNameOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="交通恢复时间" prop="trafficRecoveryTime">
        <el-date-picker
          v-model="formData.trafficRecoveryTime"
          type="datetime"
          placeholder="选择时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 50%;">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="接收人" prop="receivers">
        <el-select
          v-model="formData.receivers"
          multiple
          placeholder="选择接收人"
          style="width: 100%;">
          <el-option
            v-for="item in receiverOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EventNewsForm',
  props: {
    // 表单数据值
    value: {
      type: Object,
      default: () => ({
        workContent: '',
        unitDepartment: '',
        departmentName: '',
        companyName: '',
        trafficRecoveryTime: '',
        receivers: []
      })
    },
    // 接收人选项
    receiverOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formData: { ...this.value },

      // 表单验证规则
      formRules: {
        workContent: [
          { required: true, message: '请输入工作内容', trigger: 'blur' }
        ],
        unitDepartment: [
          { required: true, message: '请选择单位部门', trigger: 'change' }
        ],
        departmentName: [
          { required: true, message: '请选择部门名称', trigger: 'change' }
        ],
        companyName: [
          { required: true, message: '请选择公司名称', trigger: 'change' }
        ],
        trafficRecoveryTime: [
          { required: true, message: '请选择交通恢复时间', trigger: 'change' }
        ],
        receivers: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      },

      // 单位部门选项
      unitDepartmentOptions: [
        { value: '1', label: '桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '应急管理部门' }
      ],

      // 部门名称选项
      departmentNameOptions: [
        { value: '1', label: '应急管理处' },
        { value: '2', label: '工程技术处' },
        { value: '3', label: '运营维护处' }
      ],

      // 公司名称选项
      companyNameOptions: [
        { value: '1', label: '桥隧公司' },
        { value: '2', label: '市政工程公司' },
        { value: '3', label: '应急抢险公司' }
      ]
    }
  },
  watch: {
    value: {
      handler(newVal) {
        // 避免死循环，只在值真正不同时更新
        if (JSON.stringify(newVal) !== JSON.stringify(this.formData)) {
          this.formData = { ...newVal }
        }
      },
      deep: true,
      immediate: true
    },
    formData: {
      handler(newVal) {
        // 避免死循环，只在值真正不同时触发事件
        if (JSON.stringify(newVal) !== JSON.stringify(this.value)) {
          this.$emit('input', newVal)
        }
      },
      deep: true
    }
  },
  methods: {
    // 验证表单
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.newsForm.validate(valid => {
          if (valid) {
            resolve(this.formData)
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },

    // 清除验证
    clearValidate() {
      this.$refs.newsForm.clearValidate()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        workContent: '',
        unitDepartment: '',
        departmentName: '',
        companyName: '',
        trafficRecoveryTime: '',
        receivers: []
      }
      this.$refs.newsForm.resetFields()
    },

    // 设置默认值
    setDefaultValues() {
      this.formData.unitDepartment = '1' // 默认桥隧事务中心
      this.formData.departmentName = '1' // 默认应急管理处
      this.formData.companyName = '1' // 默认桥隧公司
    }
  }
}
</script>

<style scoped>
.news-form {
  padding: 10px 0;
}

/* 为表单控件添加边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
