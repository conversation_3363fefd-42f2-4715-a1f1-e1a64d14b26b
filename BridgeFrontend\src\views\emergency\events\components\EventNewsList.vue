<!-- 新闻通稿记录列表组件 -->
<template>
  <div class="news-list">
    <el-table :data="newsList" border style="width: 100%">
      <el-table-column prop="eventName" label="事件名称" header-align="center"></el-table-column>
      <el-table-column prop="publisher" label="发布人" header-align="center"></el-table-column>
      <el-table-column prop="publishTime" label="发布时间" header-align="center"></el-table-column>
      <el-table-column label="操作" header-align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
            <el-link 
              v-if="showSendRecord"
              @click="handleSendRecord(scope.row)" 
              type="primary" 
              :underline="false" 
              style="margin-left: 20px;"
            >
              发送记录
            </el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 新闻通稿模板弹窗 -->
    <EventNewsDetailDialog
      :visible.sync="templateDialogVisible"
      :news-data="selectedNewsData"
    />
  </div>
</template>

<script>
import EventNewsDetailDialog from './EventNewsDetailDialog.vue'

export default {
  name: 'EventNewsList',
  components: {
    EventNewsDetailDialog
  },
  props: {
    // 新闻通稿列表数据
    newsList: {
      type: Array,
      default: () => []
    },
    // 是否显示发送记录按钮
    showSendRecord: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      templateDialogVisible: false,
      selectedNewsData: {}
    }
  },
  methods: {
    // 查看新闻通稿详情
    handleView(row) {
      this.selectedNewsData = row
      this.templateDialogVisible = true
      // 同时保持向父组件发送事件的兼容性
      this.$emit('view', row)
    },
    
    // 查看发送记录
    handleSendRecord(row) {
      this.$emit('send-record', row)
    }
  }
}
</script>

<style scoped>
.news-list {
  padding: 10px 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

</style>
