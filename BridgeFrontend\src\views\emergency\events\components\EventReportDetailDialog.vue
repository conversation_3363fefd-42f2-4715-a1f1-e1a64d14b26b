<template>
  <el-dialog
    title="续报详情"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    class="report-detail-dialog">
    
    <div class="report-detail-content" v-if="reportData">
      <!-- 报告标题 -->
      <div class="report-title">
        <h3>关于{{ displayEventName }}事件有关情况汇总</h3>
      </div>

      <!-- 续报内容 -->
      <div class="report-content">
          <p class="content-text">
            {{ formatReportContent() }}
          </p>        
      </div>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <div class="empty-content">
        <i class="el-icon-document" style="font-size: 48px; color: #C0C4CC; margin-bottom: 16px;"></i>
        <p>暂无续报数据</p>
      </div>
    </div>
      
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EventReportDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    reportData: {
      type: Object,
      default: () => ({
        eventName: '湘江大桥桥面积水事件',
        reportTime: '2024-03-15 14:30:00',
        reporter: '张三',
        supervisor: '李主任',
        policeCoordinationTime: '2024-03-15 10:45:00',
        leaderArrivalTime: '2024-03-15 11:20:00',
        leaderName: '王局长',
        discoverer: '巡查员小李',
        causeAnalysis: '经现场勘查和专家分析，初步判断积水原因为：\n1. 连续降雨导致排水系统负荷过重\n2. 部分排水口存在堵塞现象\n3. 桥面排水坡度设计需要进一步优化\n\n综合分析认为，此次积水事件属于极端天气条件下的正常现象，但暴露出排水系统维护不够及时的问题。',
        disposalOpinion: '基于现场情况和专家意见，制定如下处置措施：\n1. 立即启动应急排水设备，加快积水排除\n2. 组织人员清理堵塞的排水口，确保排水畅通\n3. 协调交警部门实施临时交通管制，保障行车安全\n4. 安排专业队伍24小时值守，实时监控积水情况\n5. 制定后续排查计划，全面检修桥梁排水系统',
        reportUnit: '长沙市桥隧事务中心',
        recipients: [
          { id: 1, name: '市应急管理局', unit: '应急管理部门' },
          { id: 2, name: '市交通运输局', unit: '交通管理部门' },
          { id: 3, name: '市公安交警支队', unit: '交通执法部门' },
          { id: 4, name: '湘江新区管委会', unit: '属地管理部门' }
        ]
      })
    },
    eventData: {
      type: Object,
      default: () => ({
        structureName: '湘江大桥',
        structureType: 'bridge',
        eventType: '桥面积水',
        triggerTime: '2024-03-15 09:30:00',
        eventLevel: '一般事件',
        location: '湘江大桥主桥面K1+200处',
        description: '受连续降雨影响，湘江大桥主桥面出现积水现象，积水深度约15cm，影响车辆正常通行。'
      })
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    structureType() {
      if (this.eventData.structureType === 'bridge') {
        return '桥梁'
      } else if (this.eventData.structureType === 'tunnel') {
        return '隧道'
      }
      return '桥梁/隧道'
    },
    // 判断是否为开发环境
    isDevelopment() {
      return process.env.NODE_ENV === 'development'
    },
    // 显示的事件名称
    displayEventName() {

      console.log(this.reportData)
      // 优先使用 reportData 中的 eventName
      if (this.reportData && this.reportData.eventName) {
        return this.reportData.eventName
      }
      
      // 其次使用 eventData 组合生成名称
      if (this.eventData && this.eventData.structureName && this.eventData.eventType) {
        return `${this.eventData.structureName}${this.eventData.eventType}`
      }
      
      // 最后使用默认名称
      return '桥隧事件'
    }
  },
  methods: {
    // 格式化续报内容
    formatReportContent() {
      if (!this.reportData || !this.eventData) {
        return '暂无续报内容'
      }

      const data = this.reportData
      const event = this.eventData
      
      // 解析时间
      const reportDate = this.parseDate(data.reportTime)
      const eventDate = this.parseDate(event.triggerTime)
      
      // 构建续报模板内容
      let content = `${reportDate.year}年${reportDate.month}月${reportDate.day}日${reportDate.time}，${event.structureName}发生${event.eventType}，在${data.supervisor || '相关领导'}科学调度与精准指导下，现已初步排除安全风险，${this.structureType}已恢复正常通行。现将相关情况汇报如下：\n\n`
      
      content += `${eventDate.month}月${eventDate.day}日${eventDate.hour}点${eventDate.minute}分，${data.discoverer || '相关人员'}发现${event.structureName}发生${event.eventType}，立即按程序进行报送，市桥隧中心接到信息后，第一时间启动应急响应，立即成立现场处置组、专家技术组及信息协调组，协调推进应急抢险工作。\n\n`
      
      if (data.policeCoordinationTime) {
        const policeTime = this.parseDate(data.policeCoordinationTime)
        content += `${policeTime.hour}点${policeTime.minute}分，协调交警部门采取交通管制，\n\n`
      }
      
      if (data.leaderArrivalTime && data.leaderName) {
        const leaderTime = this.parseDate(data.leaderArrivalTime)
        content += `${leaderTime.hour}点${leaderTime.minute}分，${data.leaderName}领导抵达现场指导应急调度工作，\n\n`
      }

      if (reportDate.causeAnalysis) {
        content += `依据目前现场情况，并结合专家意见，初步原因分析如下：\n`
        content += `${reportDate.causeAnalysis}\n`
      }
      if (reportDate.disposalOpinion) {
        content += `明确如下处置意见：\n`
        content += `${reportDate.causeAnalysis}\n`
      }
      
      content += `下一步，中心将实时报告检测、处置、评估结果，按照“安全第一、实事求是”原则，科学快速处置，全力保障桥梁/隧道安全运营，尽快有序放开交通。\n`
      content += `特此报告。`

      
      return content
    },

    // 解析日期时间
    parseDate(dateTimeStr) {
      if (!dateTimeStr) return {}
      
      const date = new Date(dateTimeStr)
      return {
        year: date.getFullYear(),
        month: String(date.getMonth() + 1).padStart(2, '0'),
        day: String(date.getDate()).padStart(2, '0'),
        hour: String(date.getHours()).padStart(2, '0'),
        minute: String(date.getMinutes()).padStart(2, '0'),
        time: `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },



    // 格式化文件名日期
    formatDateForFilename(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}`
    }

  }
}
</script>

<style scoped>
.report-detail-dialog {
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

.report-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.report-title {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 30px;
  border-top: 2px solid #e4e7ed;
}

.report-title h3 {
  font-size: 24px;
  color: #333;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.report-meta {
  display: flex;
  justify-content: center;
  gap: 30px;
  font-size: 14px;
  color: #666;
}

.meta-item i {
  margin-right: 5px;
  color: #409EFF;
}

.report-content {
  margin-left: 66px;
  margin-right: 66px;
  margin-bottom: 130px;
}

.content-text {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin: 0 0 15px 0;
  text-align: justify;
  white-space: pre-line;
}

.key-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 20px 0;
}

.info-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  text-align: center;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.analysis-content,
.opinion-content {
  margin-top: 10px;
}

.analysis-box,
.opinion-box {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  padding: 20px;
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin-top: 10px;
  min-height: 80px;
  white-space: pre-line;
}

.next-work {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 20px;
  margin-top: 10px;
}

.report-footer {
  text-align: center;
  margin: 30px 0 20px 0;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.footer-text {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  max-width: 500px;
  margin: 0 auto;
}

.signature-item {
  font-size: 14px;
  color: #666;
}

.signature-label {
  font-weight: 500;
}

.signature-value {
  color: #333;
  font-weight: bold;
}

.recipients-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.recipients-section h4 {
  font-size: 16px;
  color: #333;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.recipients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recipient-tag {
  margin: 0;
  font-size: 13px;
}

.no-recipients {
  color: #999;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-buttons {
  display: flex;
  gap: 8px;
}

.main-buttons {
  display: flex;
  gap: 10px;
}

.dialog-footer .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .key-info-grid {
    grid-template-columns: 1fr;
  }
  
  .report-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .signature-area {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

/* 滚动条样式 */
.report-detail-content::-webkit-scrollbar {
  width: 6px;
}

.report-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.report-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.report-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-content p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}
</style>
