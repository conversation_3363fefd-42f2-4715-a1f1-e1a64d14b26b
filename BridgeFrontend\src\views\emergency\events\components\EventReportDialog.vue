<!-- 事件续报 -->
<template>
  <el-dialog
    title="处置应急事件"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose">
    
    <div class="report-dialog">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="事件信息" name="eventInfo">
          <EventDetailPanel 
            :eventData="eventData"
            :contactList="contactList"
            :photoList="photoList"
            :scrollable="true"
            :maxContactDisplay="10" />
        </el-tab-pane>
        
        <el-tab-pane label="应急研判" name="assessment">
          <EventAssessHistoryPanel
            :eventData="eventData"
            :readOnly="true"
            :showSectionTitles="true"
            :customEventCategoryOptions="assessCategoryOptions"
            :customEventLevelOptions="eventLevelOptions"
            :customResponsibleUnitOptions="responsibleUnitOptions"
            @data-change="handleAssessDataChange"
            @level-change="handleEventLevelChange" />
        </el-tab-pane>
        
        <el-tab-pane label="事件续报" name="eventReport">
          <div v-if="showReportList">
            <!-- 续报记录列表 -->
            <EventReportList 
              :reportList="reportList"
              @view="handleViewReport"
              @send-record="handleSendRecord" />
            
            <div class="add-btn">
              <el-button type="primary" @click="showAddReport">新增续报</el-button>
            </div>  
          </div>
          
          <div v-else>
            <!-- 新增续报表单 -->
            <EventReportForm 
              ref="reportForm"
              v-model="reportForm"
              :guideOptions="guideOptions"
              :receiverOptions="receiverOptions" />
            <div slot="footer" class="dialog-footer">
              <el-button @click="handleBackToList">取消</el-button>
              <el-button type="primary" @click="handleReportConfirm" :loading="submitting">提交</el-button>
            </div>
          </div>
          
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import EventReportDetailDialog from './EventReportDetailDialog.vue'
import EventDetailPanel from './EventDetailPanel.vue'
import EventAssessHistoryPanel from './EventAssessHistoryPanel.vue'
import EventReportList from './EventReportList.vue'
import EventReportForm from './EventReportForm.vue'

export default {
  name: 'EventReportDialog',
  components: {
    EventReportDetailDialog,
    EventDetailPanel,
    EventAssessHistoryPanel,
    EventReportList,
    EventReportForm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    eventData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'eventInfo',
      submitting: false,
      showReportList: true, // 是否显示续报列表
      reportDetailVisible: false, // 控制续报详情弹窗显示
      selectedReportData: null, // 选中的续报数据
      
      reportList: [
        {
          id: 1,
          eventName: '测试事件AA',
          publisher: '格罗瑞',
          publishTime: '2025/07/15 11:27',
          reportTime: '2025-07-15 11:27:00',
          reporter: '格罗瑞',
          supervisor: '张晓东主任',
          discoverer: '现场工作人员',
          policeCoordinationTime: '2025-07-15 10:30:00',
          leaderArrivalTime: '2025-07-15 11:00:00',
          leaderName: '张主任',
          causeAnalysis: '经现场勘查和专家分析，初步判断为桥梁伸缩缝老化导致的轻微损坏，未影响桥梁主体结构安全。',
          disposalOpinion: '立即组织专业队伍进行应急修复，加强现场监控，确保交通安全有序。同时制定长期维护计划，防止类似问题再次发生。',
          reportUnit: '长沙市桥隧事务中心',
          recipients: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' },
            { id: 3, name: '陈经理', unit: '桥隧公司' }
          ]
        }
      ],
      
      reportForm: {
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      },
      
      

      guideOptions: [
        { value: '1', label: '张三' },
        { value: '2', label: '李四' },
        { value: '3', label: '王五' }
      ],
      
      receiverOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '应急指挥中心' },
        { value: '4', label: '张三' },
        { value: '5', label: '李四' },
        { value: '6', label: '王五' }
      ],
      
      // 接警人数据
      contactList: [
        { id: 1, name: '李明' },
        { id: 2, name: '张伟' },
        { id: 3, name: '王强' },
        { id: 4, name: '刘洋' },
        { id: 5, name: '陈静' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },
        
        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' },
        { id: 10, name: '黄财一' },
        { id: 11, name: '黄财二' },
        { id: 12, name: '黄财三' },
        { id: 13, name: '张三' }

      ],
      
      // 现场照片数据
      photoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') }
      ],
      
      // 应急研判分类选项 (区别于事件分类选项)
      assessCategoryOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '3', label: '公共卫生事件' },
        { value: '4', label: '社会安全事件' }
      ],
      
      // 事件等级选项
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],
      
      // 责任单位选项
      responsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ]
    }
  },
  computed: {
    // 预留计算属性区域
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initDialog()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initDialog() {
      this.activeTab = 'eventReport'
      this.showReportList = true
      this.resetForm()
    },
    
    showAddReport() {
      this.showReportList = false
      // 如果已有续报记录，可以使用默认值
      if (this.reportList.length > 0) {
        // 可以从历史记录中获取一些默认值
      }
    },
    
    handleBackToList() {
      this.showReportList = true
      this.resetForm()
    },
    
    handleViewReport(row) {
      // 打开续报详情弹窗
      this.selectedReportData = row
      this.reportDetailVisible = true
    },
    
    handleSendRecord(row) {
      this.$message.info('查看发送记录功能开发中...')
    },

    // 关闭续报详情弹窗
    handleReportDetailClose() {
      this.reportDetailVisible = false
      this.selectedReportData = null
    },
    
    
    handleReportConfirm() {
      this.$refs.reportForm.validateForm()
        .then(() => {
          this.submitReport()
        })
        .catch(() => {
          // 表单验证失败
        })
    },
    
    async submitReport() {
      this.submitting = true
      try {
        const submitData = {
          eventId: this.eventData.id,
          ...this.reportForm
        }
        
        // 模拟API调用
        await this.mockSubmitReport(submitData)
        
        this.$message.success('续报提交成功')
        this.$emit('confirm', submitData)
      } catch (error) {
        console.error('续报提交失败:', error)
        this.$message.error('续报提交失败')
      } finally {
        this.submitting = false
      }
    },
    
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
      this.resetForm()
    },
    
    resetForm() {
      this.reportForm = {
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      }

      
      this.$nextTick(() => {
        this.$refs.reportForm && this.$refs.reportForm.clearValidate()
      })
    },
    
    // 应急研判数据变化处理
    handleAssessDataChange(data) {
      // 这里可以处理研判数据变化的逻辑
      console.log('应急研判数据变化:', data)
    },
    
    // 模拟API
    async mockSubmitReport(data) {
      return new Promise(resolve => {
        setTimeout(() => {
          console.log('提交续报数据:', data)
          resolve({ success: true })
        }, 1000)
      })
    },
    
    // 事件等级变化处理
    handleEventLevelChange(value) {
      console.log('事件等级变化:', value)
      // 这里可以根据等级变化做一些业务逻辑处理
    }
  }
}
</script>

<style scoped>
.report-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.assess-info {
  padding: 10px 0;
}


.dialog-footer {
  text-align: right;
}

/* Element UI 标签页样式调整 */
.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}

.add-btn {
  text-align: right;
  margin-top: 15px;
}

</style>





