<template>
  <el-form
    :model="formData"
    :rules="formRules"
    ref="reportForm"
    label-width="120px">
    <el-form-item label="指导人" prop="guide">
      <el-select
        v-model="formData.guide"
        placeholder="请选择"
        style="width: 50%;">
        <el-option
          v-for="item in guideOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="协调交警时间" prop="policeTime">
      <el-date-picker
        v-model="formData.policeTime"
        type="datetime"
        placeholder="选择时间"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        style="width: 50%;">
      </el-date-picker>
    </el-form-item>

    <el-form-item label="领导到场时间" prop="leaderArrivalTime">
      <el-date-picker
        v-model="formData.leaderArrivalTime"
        type="datetime"
        placeholder="选择时间"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
        style="width: 50%;">
      </el-date-picker>
    </el-form-item>

    <el-form-item label="领导姓名" prop="leaderName">
      <el-input
        v-model="formData.leaderName"
        placeholder="请输入"
        style="width: 50%;">
      </el-input>
    </el-form-item>

    <el-form-item label="原因分析" prop="causeAnalysis">
      <el-input
        v-model="formData.causeAnalysis"
        type="textarea"
        :rows="4"
        placeholder="请输入原因分析">
      </el-input>
    </el-form-item>

    <el-form-item label="处置意见" prop="disposalOpinion">
      <el-input
        v-model="formData.disposalOpinion"
        type="textarea"
        :rows="4"
        placeholder="请输入处置意见">
      </el-input>
    </el-form-item>

    <el-form-item label="接收人" prop="receivers">
      <el-select
        v-model="formData.receivers"
        multiple
        placeholder="选择接收人"
        style="width: 100%;">
        <el-option
          v-for="item in receiverOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'EventReportForm',
  props: {
    value: {
      type: Object,
      default: () => ({
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      })
    },
    guideOptions: {
      type: Array,
      default: () => [
        { value: '1', label: '张三' },
        { value: '2', label: '李四' },
        { value: '3', label: '王五' }
      ]
    },
    receiverOptions: {
      type: Array,
      default: () => [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '应急指挥中心' },
        { value: '4', label: '张三' },
        { value: '5', label: '李四' },
        { value: '6', label: '王五' }
      ]
    }
  },
  data() {
    return {
      formData: { ...this.value },
      formRules: {
        guide: [
          { required: true, message: '请选择指导人', trigger: 'change' }
        ],
        policeTime: [
          { required: true, message: '请选择协调交警时间', trigger: 'change' }
        ],
        leaderArrivalTime: [
          { required: true, message: '请选择领导到场时间', trigger: 'change' }
        ],
        leaderName: [
          { required: true, message: '请输入领导姓名', trigger: 'blur' }
        ],
        causeAnalysis: [
          { required: true, message: '请输入原因分析', trigger: 'blur' }
        ],
        disposalOpinion: [
          { required: true, message: '请输入处置意见', trigger: 'blur' }
        ],
        receivers: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        // 避免死循环，只在值真正不同时更新
        if (JSON.stringify(newVal) !== JSON.stringify(this.formData)) {
          this.formData = { ...newVal }
        }
      },
      deep: true,
      immediate: true
    },
    formData: {
      handler(newVal) {
        // 避免死循环，只在值真正不同时触发事件
        if (JSON.stringify(newVal) !== JSON.stringify(this.value)) {
          this.$emit('input', newVal)
        }
      },
      deep: true
    }
  },
  methods: {
    // 验证表单
    validate() {
      return this.$refs.reportForm.validate()
    },

    // 验证表单（返回Promise）
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.reportForm.validate((valid) => {
          if (valid) {
            resolve(this.formData)
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },

    // 清除验证
    clearValidate() {
      this.$refs.reportForm.clearValidate()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        guide: '',
        policeTime: '',
        leaderArrivalTime: '',
        leaderName: '',
        causeAnalysis: '明应巴解制，交通已恢复',
        disposalOpinion: '明应巴解制，交通已恢复',
        receivers: []
      }
      this.$nextTick(() => {
        this.clearValidate()
      })
    }
  }
}
</script>

<style scoped>
/* 为表单控件添加边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
