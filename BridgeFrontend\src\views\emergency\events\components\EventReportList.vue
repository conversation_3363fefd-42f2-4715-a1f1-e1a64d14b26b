<template>
  <div class="report-list">
    <el-table :data="reportList" border style="width: 100%">
      <el-table-column prop="eventName" label="事件名称" header-align="center"></el-table-column>
      <el-table-column prop="publisher" label="续报人" header-align="center"></el-table-column>
      <el-table-column prop="publishTime" label="续报时间" header-align="center"></el-table-column>
      <el-table-column label="操作" header-align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
            <el-link 
              v-if="showSendRecord"
              @click="handleSendRecord(scope.row)" 
              type="primary" 
              :underline="false" 
              style="margin-left: 20px;">发送记录</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 续报详情弹窗 -->
    <EventReportDetailDialog
      :visible.sync="dialogVisible"
      :report-data="selectedReport"
    />
  </div>
</template>

<script>
import EventReportDetailDialog from './EventReportDetailDialog.vue'

export default {
  name: 'EventReportList',
  components: {
    EventReportDetailDialog
  },
  props: {
    reportList: {
      type: Array,
      default: () => []
    },
    showSendRecord: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedReport: {}
    }
  },
  methods: {
    handleView(row) {
      this.selectedReport = row
      this.dialogVisible = true
      this.$emit('view', row)
    },
    
    handleSendRecord(row) {
      this.$emit('send-record', row)
    }
    
  }
}
</script>

<style scoped>
/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.report-list {
  padding: 10px 0;
}

</style>
