<template>
  <div class="state-control-form">
    <el-form :model="form" :rules="formRules" ref="stateControlForm" label-width="120px">
      <el-form-item label="事态控制" prop="controlType">
        <el-select 
          v-model="form.controlType" 
          placeholder="请选择事态控制方式"
          @change="handleControlTypeChange"
          style="width: 50%;">
          <el-option
            v-for="item in controlTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <!-- 解除响应 -->
      <div v-if="form.controlType === 'release'">
        <el-form-item label="解除原因" prop="releaseReason">
          <el-input v-model="form.releaseReason" placeholder="请输入"></el-input>
        </el-form-item>
        
        <el-form-item label="接收人" prop="receivers">
          <el-select
            v-model="form.receivers"
            multiple
            placeholder="选择接收人"
            style="width: 50%;">
            <el-option
              v-for="item in receiverOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="现场照片" prop="scenePhotos" required>
          <el-upload
            ref="scenePhotoUpload"
            action="#"
            list-type="picture-card"
            :file-list="form.scenePhotos"
            :on-preview="handlePhotoPreview"
            :on-remove="handlePhotoRemove"
            :before-upload="handleBeforeUpload"
            :auto-upload="false"
            multiple>
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="upload-tip">支持上传 jpg、png 格式图片，单张不超过 5MB，至少上传1张</div>
        </el-form-item>
      </div>
      
      <!-- 响应变更 -->
      <div v-if="form.controlType === 'change'">
        <el-form-item label="事件等级" prop="newEventLevel">
          <el-select 
            v-model="form.newEventLevel" 
            placeholder="请选择新的事件等级"
            style="width: 50%;">
            <el-option
              v-for="item in eventLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EventStateControl',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    receiverOptions: {
      type: Array,
      default: () => []
    },
    eventLevelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        controlType: '',
        releaseReason: '明应巴解制，交通已恢复',
        changeReason: '',
        newEventLevel: '',
        receivers: [],
        scenePhotos: []
      },
      
      // 表单验证规则
      formRules: {
        controlType: [
          { required: true, message: '请选择事态控制方式', trigger: 'change' }
        ],
        releaseReason: [
          { required: true, message: '请输入解除原因', trigger: 'blur' }
        ],
        changeReason: [
          { required: true, message: '请输入变更原因', trigger: 'blur' }
        ],
        newEventLevel: [
          { required: true, message: '请选择新的事件等级', trigger: 'change' }
        ],
        receivers: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      },
      
      // 事态控制类型选项
      controlTypeOptions: [
        { value: 'release', label: '解除响应' },
        { value: 'change', label: '响应变更' }
      ]
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && JSON.stringify(newVal) !== JSON.stringify(this.form)) {
          this.form = { ...this.form, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    form: {
      handler(newVal) {
        // 避免死循环，只在值真正不同时触发事件
        if (JSON.stringify(newVal) !== JSON.stringify(this.value)) {
          this.$emit('input', newVal)
        }
      },
      deep: true
    }
  },
  methods: {
    // 表单验证
    validateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.stateControlForm.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },
    
    // 清除验证
    clearValidate() {
      this.$refs.stateControlForm && this.$refs.stateControlForm.clearValidate()
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        controlType: '',
        releaseReason: '明应巴解制，交通已恢复',
        changeReason: '',
        newEventLevel: '',
        receivers: [],
        scenePhotos: []
      }
      this.clearValidate()
    },
    
    // 事态控制类型变化处理
    handleControlTypeChange(value) {
      console.log('事态控制类型变化:', value)
      // 清空其他字段
      this.form.releaseReason = value === 'release' ? '明应巴解制，交通已恢复' : ''
      this.form.changeReason = ''
      this.form.newEventLevel = ''
      this.form.receivers = []
      this.form.scenePhotos = []
      
      // 清除验证错误
      this.$nextTick(() => {
        this.clearValidate()
      })
      
      // 通知父组件类型变化
      this.$emit('control-type-change', value)
    },
    
    // 照片上传前的校验
    handleBeforeUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isJPGOrPNG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      
      // 创建本地预览URL
      const url = URL.createObjectURL(file)
      const photoItem = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: url,
        file: file
      }
      
      this.form.scenePhotos.push(photoItem)
      return false // 阻止自动上传
    },
    
    // 删除照片
    handlePhotoRemove(file) {
      const index = this.form.scenePhotos.findIndex(item => item.uid === file.uid)
      if (index > -1) {
        // 释放预览URL
        if (this.form.scenePhotos[index].url) {
          URL.revokeObjectURL(this.form.scenePhotos[index].url)
        }
        this.form.scenePhotos.splice(index, 1)
      }
    },
    
    // 照片预览
    handlePhotoPreview(file) {
      if (file.url) {
        // 创建预览窗口
        const image = new Image()
        image.src = file.url
        const imgWindow = window.open(file.url)
        imgWindow.document.write(image.outerHTML)
      }
    }
  }
}
</script>

<style scoped>
.state-control-form {
  padding: 10px 0;
}

/* 照片上传样式 */
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  line-height: 1.4;
}

::v-deep .el-upload--picture-card {
  width: 104px;
  height: 104px;
  line-height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: #fbfdff;
  border-color: #c0ccda;
  color: #8c939d;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

::v-deep .el-upload--picture-card:hover {
  border-color: #409EFF;
  color: #409EFF;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 104px;
  height: 104px;
  margin: 0 8px 8px 0;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover {
  opacity: 1;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions span {
  display: none;
  cursor: pointer;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-actions:hover span {
  display: inline-block;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-preview,
::v-deep .el-upload-list--picture-card .el-upload-list__item-delete {
  color: #fff;
  margin: 0 5px;
}

::v-deep .el-upload-list--picture-card .el-upload-list__item-delete:hover {
  color: #f56c6c;
}
</style>
