<!-- 事态控制详情弹窗 -->
<template>
  <el-dialog
    title="事态控制详情"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose">
    
    <div class="state-control-detail">
      <div class="detail-item">
        <label>事件控制类型：</label>
        <span>{{ getControlTypeLabel(stateControlData.controlType) }}</span>
      </div>
      
      <div class="detail-item">
        <label>事件等级：</label>
        <span>{{ getEventLevelLabel(stateControlData.eventLevel) }}</span>
      </div>
      
      <div class="detail-item">
        <label>控制时间：</label>
        <span>{{ stateControlData.controlTime }}</span>
      </div>
      
      <div class="detail-item">
        <label>操作人：</label>
        <span>{{ stateControlData.operator }}</span>
      </div>
      
      <div class="detail-item" v-if="stateControlData.controlType === 'release'">
        <label>解除原因：</label>
        <span>{{ stateControlData.releaseReason }}</span>
      </div>
      
      <div class="detail-item" v-if="stateControlData.controlType === 'change'">
        <label>变更原因：</label>
        <span>{{ stateControlData.changeReason }}</span>
      </div>
      
      <div class="detail-item" v-if="stateControlData.controlType === 'change'">
        <label>新事件等级：</label>
        <span>{{ getEventLevelLabel(stateControlData.newEventLevel) }}</span>
      </div>
      
      <div class="detail-item" v-if="stateControlData.receivers && stateControlData.receivers.length > 0">
        <label>接收人：</label>
        <span>{{ getReceiversText(stateControlData.receivers) }}</span>
      </div>
      
      <div class="detail-item" v-if="stateControlData.scenePhotos && stateControlData.scenePhotos.length > 0">
        <label>现场照片：</label>
        <div class="photos-container">
          <div 
            v-for="(photo, index) in stateControlData.scenePhotos" 
            :key="index" 
            class="photo-item"
            @click="previewPhoto(photo.url)">
            <img :src="photo.url" :alt="photo.name" />
          </div>
        </div>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'EventStateControlDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    stateControlData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      // 事态控制类型选项
      controlTypeOptions: [
        { value: 'release', label: '解除响应' },
        { value: 'change', label: '响应变更' }
      ],
      // 事件等级选项
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],
      // 接收人选项
      receiverOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '应急指挥中心' },
        { value: '4', label: '张三' },
        { value: '5', label: '李四' },
        { value: '6', label: '王五' }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    },
    
    // 获取控制类型标签
    getControlTypeLabel(value) {
      const option = this.controlTypeOptions.find(item => item.value === value)
      return option ? option.label : value
    },
    
    // 获取事件等级标签
    getEventLevelLabel(value) {
      const option = this.eventLevelOptions.find(item => item.value === value)
      return option ? option.label : value
    },
    
    // 获取接收人文本
    getReceiversText(receivers) {
      if (!receivers || !Array.isArray(receivers)) return ''
      
      return receivers.map(receiverId => {
        const option = this.receiverOptions.find(item => item.value === receiverId)
        return option ? option.label : receiverId
      }).join('、')
    },
    
    // 预览照片
    previewPhoto(url) {
      if (url) {
        const image = new Image()
        image.src = url
        const imgWindow = window.open(url)
        imgWindow.document.write(image.outerHTML)
      }
    }
  }
}
</script>

<style scoped>
.state-control-detail {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
  text-align: right;
  padding-top: 8px;
}

.detail-item span {
  flex: 1;
  color: #666;
  word-break: break-all;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
}

.photos-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.photo-item {
  width: 80px;
  height: 80px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.photo-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.photo-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dialog-footer {
  text-align: right;
}
</style>
