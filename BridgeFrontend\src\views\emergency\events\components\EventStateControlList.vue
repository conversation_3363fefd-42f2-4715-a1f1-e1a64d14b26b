<!-- 事态控制记录列表组件 -->
<template>
  <div class="state-control-list">
    <el-table :data="stateControlList" border style="width: 100%">
      <el-table-column prop="controlType" label="事件控制类型" header-align="center">
        <template slot-scope="scope">
          <span>{{ getControlTypeLabel(scope.row.controlType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="eventLevel" label="事件等级" header-align="center">
        <template slot-scope="scope">
          <span>{{ getEventLevelLabel(scope.row.eventLevel) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="controlTime" label="事态控制时间" header-align="center"></el-table-column>
      <el-table-column label="操作" header-align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 事态控制详情弹窗 -->
    <EventStateControlDetailDialog
      :visible.sync="detailDialogVisible"
      :state-control-data="selectedStateControlData"
    />
  </div>
</template>

<script>
import EventStateControlDetailDialog from './EventStateControlDetailDialog.vue'

export default {
  name: 'EventStateControlList',
  components: {
    EventStateControlDetailDialog
  },
  props: {
    // 事态控制记录列表数据
    stateControlList: {
      type: Array,
      default: () => []
    },
    // 事件等级选项
    eventLevelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      detailDialogVisible: false,
      selectedStateControlData: {},
      // 事态控制类型选项
      controlTypeOptions: [
        { value: 'release', label: '解除响应' },
        { value: 'change', label: '响应变更' }
      ]
    }
  },
  methods: {
    // 查看事态控制详情
    handleView(row) {
      this.selectedStateControlData = row
      this.detailDialogVisible = true
      // 同时保持向父组件发送事件的兼容性
      this.$emit('view', row)
    },
    
    // 获取控制类型标签
    getControlTypeLabel(value) {
      const option = this.controlTypeOptions.find(item => item.value === value)
      return option ? option.label : value
    },
    
    // 获取事件等级标签
    getEventLevelLabel(value) {
      const option = this.eventLevelOptions.find(item => item.value === value)
      return option ? option.label : value
    }
  }
}
</script>

<style scoped>
.state-control-list {
  padding: 10px 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
