<!-- 总结报告 -->
<template>
  <el-dialog
    title="处置应急事件"
    :visible.sync="dialogVisible"
    width="700px"
    :before-close="handleClose">

    <div class="report-dialog">
    <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="事件信息" name="eventInfo">
        <EventDetailPanel
          :eventData="eventData"
          :contactList="contactList"
          :photoList="photoList"
          :scrollable="true"
          :maxContactDisplay="10" />
      </el-tab-pane>

        <el-tab-pane label="应急研判" name="assessment">
          <EventAssessHistoryPanel
            :eventData="eventData"
            :readOnly="true"
            :showSectionTitles="true"
            :customEventCategoryOptions="assessCategoryOptions"
            :customEventLevelOptions="eventLevelOptions"
            :customResponsibleUnitOptions="responsibleUnitOptions"
            @data-change="handleAssessDataChange"
            @level-change="handleEventLevelChange" />
        </el-tab-pane>

        <el-tab-pane label="事件续报" name="eventReport">
          <!-- 续报记录列表 -->
          <EventReportList
            :reportList="reportList"
            :showSendRecord="false"
            @view="handleViewReport"
            @send-record="handleSendRecord" />
      </el-tab-pane>

        <el-tab-pane label="事态控制" name="stateControl">
          <!-- 事态控制记录列表 -->
          <EventStateControlList
            :stateControlList="stateControlList"
            :eventLevelOptions="eventLevelOptions"
            @view="handleViewStateControl" />
        </el-tab-pane>

        <el-tab-pane label="新闻通稿" name="newsRelease">
          <!-- 新闻通稿记录列表 -->
          <EventNewsList
            :newsList="newsList"
            :showSendRecord="false"
            @view="handleViewNews"
            @send-record="handleSendRecord"/>
        </el-tab-pane>

        <el-tab-pane label="应急总结" name="emergencySummary">
          <!-- 应急总结表单 -->
          <div class="summary-form">
            <el-form ref="summaryForm" :model="summaryForm" :rules="summaryRules" label-width="100px">
              <el-form-item label="总结报告" prop="summaryReport">
                <el-input
                  v-model="summaryForm.summaryReport"
                  type="textarea"
                  :rows="8"
                  placeholder="请输入应急总结报告内容"
                  maxlength="2000"
                  show-word-limit>
                </el-input>
              </el-form-item>
              
              <el-form-item label="附件">
                <el-upload
                  ref="summaryUpload"
                  class="summary-upload"
                  drag
                  action="#"
                  :auto-upload="false"
                  :file-list="summaryForm.attachments"
                  :on-change="handleSummaryFileChange"
                  :on-remove="handleSummaryFileRemove"
                  multiple>
                  <i class="el-icon-paperclip"></i>
                  <div class="el-upload__text">点击上传或拖拽文件至此上传</div>
                  <div class="el-upload__tip" slot="tip">支持上传多个文件，单个文件不超过10MB</div>
                </el-upload>
              </el-form-item>
            </el-form>
            
            <div class="dialog-footer">
              <el-button @click="handleClose">取消</el-button>
              <el-button type="primary" @click="handleSummaryConfirm" :loading="submitting">提交</el-button>
            </div>
          </div>
        </el-tab-pane>
    </el-tabs>
    </div>


  </el-dialog>
</template>

<script>
import EventReportDetailDialog from './EventReportDetailDialog.vue'
import EventDetailPanel from './EventDetailPanel.vue'
import EventAssessHistoryPanel from './EventAssessHistoryPanel.vue'
import EventReportList from './EventReportList.vue'
import EventStateControlList from './EventStateControlList.vue'
import EventNewsList from './EventNewsList.vue'

export default {
  name: 'EventReportDialog',
  components: {
    EventReportDetailDialog,
    EventDetailPanel,
    EventAssessHistoryPanel,
    EventReportList,
    EventStateControlList,
    EventNewsList
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    eventData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeTab: 'stateControl',
      submitting: false,
      reportDetailVisible: false, // 控制续报详情弹窗显示
      selectedReportData: null, // 选中的续报数据

      reportList: [
        {
          id: 1,
          eventName: '测试事件AA',
          publisher: '格罗瑞',
          publishTime: '2025/07/15 11:27',
          reportTime: '2025-07-15 11:27:00',
          reporter: '格罗瑞',
          supervisor: '张晓东主任',
          discoverer: '现场工作人员',
          policeCoordinationTime: '2025-07-15 10:30:00',
          leaderArrivalTime: '2025-07-15 11:00:00',
          leaderName: '张主任',
          causeAnalysis: '经现场勘查和专家分析，初步判断为桥梁伸缩缝老化导致的轻微损坏，未影响桥梁主体结构安全。',
          disposalOpinion: '立即组织专业队伍进行应急修复，加强现场监控，确保交通安全有序。同时制定长期维护计划，防止类似问题再次发生。',
          reportUnit: '长沙市桥隧事务中心',
          recipients: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' },
            { id: 3, name: '陈经理', unit: '桥隧公司' }
          ]
        }
      ],

      // 事态控制记录列表
      stateControlList: [
        {
          id: 1,
          controlType: 'change',
          eventLevel: '2',
          controlTime: '2025-07-15 13:30:00',
          changeReason: '现场情况发生变化，需要调整应急响应等级',
          newEventLevel: '2',
          releaseReason: '',
          receivers: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' }
          ],
          scenePhotos: [
            { url: require('@/assets/images/emergency/event-detail/scene1.png'), name: '现场照片1' },
            { url: require('@/assets/images/emergency/event-detail/scene2.png'), name: '现场照片2' }
          ]
        },
        {
          id: 2,
          controlType: 'release',
          eventLevel: '1',
          controlTime: '2025-07-15 16:45:00',
          changeReason: '',
          newEventLevel: '',
          releaseReason: '事态已得到有效控制，交通已恢复正常',
          receivers: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' },
            { id: 3, name: '陈经理', unit: '桥隧公司' }
          ],
          scenePhotos: [
            { url: require('@/assets/images/emergency/event-detail/scene3.png'), name: '恢复现场照片' }
          ]
        }
      ],

      // 新闻通稿记录列表
      newsList: [
        {
          id: 1,
          eventName: '长沙大桥伸缩缝异常事件',
          publisher: '格罗瑞',
          publishTime: '2025/07/15 14:30',
          workContent: '紧急修复伸缩缝，确保桥梁安全',
          unitDepartment: '桥隧事务中心',
          departmentName: '应急管理处',
          companyName: '桥隧公司',
          trafficRecoveryTime: '2025-07-15 14:30:00',
          bridgeName: '长沙大桥',
          location: '中段伸缩缝处',
          eventType: '桥梁设施异常',
          recipients: [
            { id: 1, name: '李主任', unit: '市城管局' },
            { id: 2, name: '王处长', unit: '应急管理部门' }
          ]
        },
        {
          id: 2,
          eventName: '湘江隧道渗水事件',
          publisher: '李建国',
          publishTime: '2025/07/14 09:15',
          workContent: '隧道防水修复工程',
          unitDepartment: '桥隧事务中心',
          departmentName: '工程技术处',
          companyName: '市政工程公司',
          trafficRecoveryTime: '2025-07-14 16:20:00',
          bridgeName: '湘江隧道',
          location: '南段顶部',
          eventType: '隧道渗水',
          recipients: [
            { id: 1, name: '陈主任', unit: '市城管局' },
            { id: 3, name: '刘处长', unit: '应急管理部门' }
          ]
        }
      ],


      // 应急总结表单
      summaryForm: {
        summaryReport: '',
        attachments: []
      },

      // 应急总结表单验证规则
      summaryRules: {
        summaryReport: [
          { required: true, message: '请输入总结报告内容', trigger: 'blur' },
          { min: 10, message: '总结报告至少需要10个字符', trigger: 'blur' }
        ]
      },






      // 接警人数据
      contactList: [
        { id: 1, name: '李明' },
        { id: 2, name: '张伟' },
        { id: 3, name: '王强' },
        { id: 4, name: '刘洋' },
        { id: 5, name: '陈静' },
        { id: 6, name: '孙明茵' },
        { id: 7, name: '周宇军' },

        { id: 8, name: '徐振辉' },
        { id: 9, name: '黄财安' },
        { id: 10, name: '黄财一' },
        { id: 11, name: '黄财二' },
        { id: 12, name: '黄财三' },
        { id: 13, name: '张三' }

      ],

      // 现场照片数据
      photoList: [
        { url: require('@/assets/images/emergency/event-detail/scene1.png') },
        { url: require('@/assets/images/emergency/event-detail/scene2.png') },
        { url: require('@/assets/images/emergency/event-detail/scene3.png') }
      ],

      // 事件分类选项（事态控制页签使用）
      eventCategoryOptions: [
        { value: '1', label: '防涝排渍' },
        { value: '2', label: '抗冰除雪' },
        { value: '3', label: '突发事件' }
      ],

      // 应急研判分类选项
      assessCategoryOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '3', label: '公共卫生事件' },
        { value: '4', label: '社会安全事件' }
      ],

      // 事件等级选项
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],

      // 责任单位选项
      responsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ]
    }
  },
  computed: {
    // 计算属性可以在这里添加其他需要的逻辑
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initDialog()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initDialog() {
      this.activeTab = 'emergencySummary'
      this.resetForm()
    },
    

    handleViewReport(row) {
      // 打开续报详情弹窗
      this.selectedReportData = row
      this.reportDetailVisible = true
    },


    handleViewNews(row) {
      // 打开详情弹窗
      this.selectedReportData = row
      this.reportDetailVisible = true
    },

    handleViewStateControl(row) {
      // 处理查看事态控制详情
      console.log('查看事态控制详情:', row)
      // EventStateControlList 组件已经处理了详情弹窗，这里只需要记录日志
    },

    handleSendRecord(row) {
      this.$message.info('查看发送记录功能开发中...')
    },

    // 关闭续报详情弹窗
    handleReportDetailClose() {
      this.reportDetailVisible = false
      this.selectedReportData = null
    },







    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
      this.resetForm()
    },


    resetForm() {
      this.summaryForm = {
        summaryReport: '',
        attachments: []
      }

      this.$nextTick(() => {
        this.$refs.summaryForm && this.$refs.summaryForm.clearValidate()
      })
    },




    // 应急研判数据变化处理
    handleAssessDataChange(data) {
      console.log('应急研判数据变化:', data)
      // 这里可以根据研判数据变化做一些业务逻辑处理
    },

    // 事件等级变化处理
    handleEventLevelChange(value) {
      console.log('事件等级变化:', value)
      // 这里可以根据等级变化做一些业务逻辑处理
    },



    // 应急总结文件上传处理
    handleSummaryFileChange(file, fileList) {
      this.summaryForm.attachments = fileList
    },

    // 应急总结文件删除处理
    handleSummaryFileRemove(file, fileList) {
      this.summaryForm.attachments = fileList
    },

  }
}
</script>

<style scoped>
.report-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.assess-info {
  padding: 10px 0;
}

/* 研判区域样式 */
.assess-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.assess-section:last-child {
  margin-bottom: 0;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.assess-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 兼容旧样式 */
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  color: #333;
}

.info-item span {
  flex: 1;
  color: #666;
}

.report-list {
  padding: 10px 0;
}

.add-report-btn {
  text-align: right;
  margin-top: 15px;
}


.dialog-footer {
  text-align: right;
}

/* Element UI 标签页样式调整 */
.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}

/* 为表单控件添加边框样式 */
::v-deep .el-select .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-date-editor.el-input {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-input__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

::v-deep .el-textarea__inner {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.add-btn {
  text-align: right;
  margin-top: 15px;
}
</style>





