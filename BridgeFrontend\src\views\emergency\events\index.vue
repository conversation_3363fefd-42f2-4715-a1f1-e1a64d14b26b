<template>
  <div class="emergency-container inspection-container" :data-stage="currentStage">
    <div class="page-container">
      <!-- 桥梁/隧道切换标签 -->
      <TabSwitch
        v-model="activeStructureTab"
        :tabs="tabOptions"
        @tab-click="handleStructureTabClick"
      />

      <!-- 筛选条件 -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
      >
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.bridgeName"
            placeholder="桥梁名称"
            clearable
            filterable
            class="filter-select"
          >
            <el-option
              v-for="item in bridgeNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-model="formData.eventType"
            placeholder="事件类型"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in eventTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-if="currentStage === 'pending'"
            v-model="formData.eventClassification"
            placeholder="事件分类"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in eventClassificationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-if="currentStage !== 'reported'"
            v-model="formData.eventLevel"
            placeholder="事件等级"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in eventLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-if="currentStage === 'inprogress'"
            v-model="formData.eventCategory"
            placeholder="事件分类"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in eventCategoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select
            v-if="currentStage !== 'reported' && currentStage !== 'archived'"
            v-model="formData.responsibleUnit"
            placeholder="应急负责单位"
            clearable
            class="filter-select"
          >
            <el-option
              v-for="item in responsibleUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <div class="custom-date-range-selector filter-select">
            <div 
              class="date-range-display"
              :class="{ 'is-focused': isDatePickerFocused }"
              @click="toggleDatePicker"
              @blur="handleDatePickerBlur"
              @keydown.enter="toggleDatePicker"
              @keydown.space.prevent="toggleDatePicker"
              tabindex="0"
            >
              <span class="date-range-text">
                {{ dateRangeDisplayText }}
              </span>
              <span class="el-input__suffix">
                <!-- 清空按钮 - 当有值时显示，替换下拉箭头 -->
                <i 
                  v-if="filterForm.dateRange && filterForm.dateRange.length === 2"
                  class="el-icon-circle-close el-input__icon clear-icon"
                  @click.stop="clearDateRange"
                ></i>
                <!-- 下拉箭头 - 当没有值时显示 -->
                <i 
                  v-else
                  class="el-icon-arrow-down el-input__icon dropdown-icon"
                ></i>
              </span>
            </div>
            
            <!-- 隐藏的日期选择器 -->
            <el-date-picker
              ref="hiddenDatePicker"
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="position: absolute; opacity: 0; pointer-events: none; z-index: -1;"
              @change="handleDateRangeChange"
            />
          </div>
        </template>
      </FilterSection>

      <!-- 独立的主要操作按钮区域 -->
      <div class="primary-actions-section">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAddEvent"
        >
          上报事件
        </el-button>
      </div>

      <!-- 阶段状态标签 -->
      <div class="stage-tabs">
        <div class="stage-tab-item" 
             v-for="stage in stageList" 
             :key="stage.key"
             :class="{ active: currentStage === stage.key }"
             @click="handleStageChange(stage.key)">
          {{ stage.label }}({{ stage.count }})
        </div>
      </div>

      <!-- 事件列表表格 -->
      <div class="inspection-table">
      <el-table
        :data="eventList"
        v-loading="loading"
        style="width: 100%; min-width: 1200px;"
        :key="tableKey"
        :row-style="{ height: '32px' }"
        size="small">
        <el-table-column type="index" label="序号" width="60" :index="getTableIndex"></el-table-column>
        <el-table-column prop="eventCode" label="事件编码" width="120"></el-table-column>
        <el-table-column prop="eventSource" label="事件来源" width="120"></el-table-column>
        <el-table-column prop="eventName" label="事件名称" width="120" show-overflow-tooltip></el-table-column>
        
        <el-table-column prop="structureName" :label="activeStructureTab === 'bridge' ? '桥梁名称' : '隧道名称'" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="location" label="位置" width="120"></el-table-column>
        <el-table-column prop="triggerTime" label="事件触发时间" width="130" class-name="trigger-time-column">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.triggerTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="eventType" label="事件类型" width="120"></el-table-column>

        <el-table-column v-if="currentStage === 'reported'" prop="reportUser" label="上报人" width="110"></el-table-column>
        <el-table-column v-if="currentStage === 'reported'" prop="reportTime" label="上报时间" width="130">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.reportTime) }}
          </template>
        </el-table-column>

        <el-table-column v-if="currentStage !== 'reported'" prop="eventCategory" label="事件分类" width="120"></el-table-column>
        <el-table-column v-if="currentStage !== 'reported'" prop="eventLevel" label="事件等级" width="120">
          <template slot-scope="scope">
            <span :class="getEventLevelClass(scope.row.eventLevel)" class="event-level-tag">
              {{ scope.row.eventLevel || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column v-if="currentStage !== 'reported'" prop="responsibleUnit" label="应急负责单位" width="160" ></el-table-column>
        
        <el-table-column v-if="currentStage === 'pending'" prop="remark" label="备注" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.remark || '-' }}
          </template>
        </el-table-column>

        <el-table-column v-if="currentStage === 'archived'" label="总结报告" width="120" >
          <template slot-scope="scope">
            <i 
              v-if="scope.row.summaryReport" 
              class="el-icon-document summary-report-icon" 
              style="font-size: 20px; color: #409EFF; cursor: pointer;" 
              @click="handleViewSummaryReport(scope.row)"
              title="查看总结报告"></i>
            <span v-else>-</span>
          </template>
        </el-table-column>  

        <el-table-column label="操作" :width="operationColumnWidth" align="center" class-name="operation-column">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-link @click="handleView(scope.row)" type="primary" :underline="false">查看</el-link>
              <el-link v-if="currentStage === 'reported'" @click="handleReject(scope.row)" type="primary" :underline="false">驳回</el-link>
              <el-link v-if="currentStage === 'reported' || currentStage === 'pending'" @click="handleAssess(scope.row)" type="primary" :underline="false">{{ getAssessButtonText(scope.row) }}</el-link>
              <el-link v-if="currentStage === 'inprogress'" @click="handleReport(scope.row)" type="primary" :underline="false">事件续报</el-link>
              <el-link v-if="currentStage === 'inprogress'" @click="handleControl(scope.row)" type="primary" :underline="false">事态控制</el-link>
              <el-link v-if="currentStage === 'finished'" @click="handleNews(scope.row)" type="primary" :underline="false">新闻通稿</el-link>
              <el-link v-if="currentStage === 'finished'" @click="handleSummary(scope.row)" type="primary" :underline="false">总结报告</el-link>
              <el-link @click="handleDelete(scope.row)" type="danger" :underline="false">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper inspection-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total">
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 弹窗组件 -->
    <EventDetailDialog
      :visible.sync="detailDialogVisible"
      :event-data="currentEventData"
      @close="detailDialogVisible = false"
    />
    
    <EventAssessDialog
      :visible.sync="assessDialogVisible"
      :event-data="currentEventData"
      :stage="currentAssessmentStage"
      @close="assessDialogVisible = false"
      @confirm="handleAssessConfirm"
    />
    
    <EventReportDialog
      :visible.sync="reportDialogVisible"
      :event-data="currentEventData"
      @close="reportDialogVisible = false"
      @confirm="handleReportConfirm"
    />
    
    <EventAddDialog
      :visible.sync="addDialogVisible"
      :structure-type="activeStructureTab"
      @close="addDialogVisible = false"
      @confirm="handleAddConfirm"
    />
    
    <EventControlDialog
      :visible.sync="controlDialogVisible"
      :event-data="currentEventData"
      @close="controlDialogVisible = false"
      @control-success="handleControlConfirm"
    />
    
    <EventNewsDialog
      :visible.sync="newsDialogVisible"
      :event-data="currentEventData"
      @close="newsDialogVisible = false"
      @confirm="handleNewsConfirm"
    />
    
    <EventSummaryDialog
      :visible.sync="summaryDialogVisible"
      :event-data="currentEventData"
      @close="summaryDialogVisible = false"
      @confirm="handleSummaryConfirm"
    />
    
    <EventAllDetailDialog
      :visible.sync="allDetailDialogVisible"
      :event-data="currentEventData"
      :initial-tab="allDetailInitialTab"
      @close="handleAllDetailDialogClose"
    />
    
  </div>
</template>

<script>
import { TabSwitch, FilterSection } from '@/components/Inspection'
import EventDetailDialog from './components/EventDetailDialog'
import EventAssessDialog from './components/EventAssessDialog'
import EventReportDialog from './components/EventReportDialog'
import EventAddDialog from './components/EventAddDialog'
import EventControlDialog from './components/EventControlDialog'
import EventNewsDialog from './components/EventNewsDialog'
import EventSummaryDialog from './components/EventSummaryDialog'
import EventAllDetailDialog from './components/EventAllDetailDialog'

export default {
  name: 'EmergencyEvents',
  components: {
    TabSwitch,
    FilterSection,
    EventDetailDialog,
    EventAssessDialog,
    EventReportDialog,
    EventAddDialog,
    EventControlDialog,
    EventNewsDialog,
    EventSummaryDialog,
    EventAllDetailDialog
  },
  data() {
    return {
      loading: false,
      activeStructureTab: 'bridge', // 桥梁/隧道切换
      currentStage: 'reported', // 当前查看阶段
      tableKey: Date.now(), // 表格刷新key，用于解决切换tab表格列渲染错乱问题
      
      // 筛选表单
      filterForm: {
        bridgeName: '',
        eventType: '',
        eventLevel: '',
        eventCategory: '',
        eventClassification: '',
        responsibleUnit: '',
        dateRange: []
      },
      
      // 日期选择器焦点状态
      isDatePickerFocused: false,
      
      // TAB配置
      tabOptions: [
        {
          name: 'bridge',
          label: '桥梁突发应急事件',
          icon: 'bridge'
        },
        {
          name: 'tunnel',
          label: '隧道突发应急事件',
          icon: 'tunnel'
        }
      ],

      // 筛选配置
      filterConfigs: {
      },

      // 选项数据
      selectOptions: {
        bridgeOptions: [],
        eventTypeOptions: [],
        eventLevelOptions: [],
        eventCategoryOptions: [],
        eventClassificationOptions: [],
        responsibleUnitOptions: []
      },
      
      // 阶段列表
      stageList: [
        { key: 'reported', label: '已上报', count: 10 },
        { key: 'pending', label: '待启动', count: 6 },
        { key: 'inprogress', label: '进行中', count: 0 },
        { key: 'finished', label: '已结束', count: 0 },
        { key: 'archived', label: '已归档', count: 0 }
      ],
      
      // 事件列表
      eventList: [],
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      
      // 弹窗控制
      detailDialogVisible: false,
      assessDialogVisible: false,
      reportDialogVisible: false,
      addDialogVisible: false,
      controlDialogVisible: false,
      newsDialogVisible: false,
      summaryDialogVisible: false,
      allDetailDialogVisible: false,
      allDetailInitialTab: 'eventInfo', // EventAllDetailDialog的初始页签
      currentEventData: null,
      currentAssessmentStage: 'reported', // 当前研判阶段
      
      // 下拉选项
      eventTypeOptions: [
        { value: '1', label: '防涝排渍' },
        { value: '2', label: '抗冰除雪' },
        { value: '3', label: '突发事件' }
      ],
      bridgeNameOptions: [
        { value: '1', label: '长江大桥' },
        { value: '2', label: '黄河大桥' },
        { value: '3', label: '钱塘江大桥' },
        { value: '4', label: '珠江大桥' },
        { value: '5', label: '松花江大桥' },
        { value: '6', label: '湘江大桥' },
        { value: '7', label: '赣江大桥' },
        { value: '8', label: '汉江大桥' }
      ],
      eventLevelOptions: [
        { value: '1', label: '较小事件' },
        { value: '2', label: '一般事件' },
        { value: '3', label: '较大及以上事件' }
      ],
      eventCategoryOptions: [
        { value: '1', label: '防涝排渍' },
        { value: '2', label: '抗冰除雪' },
        { value: '3', label: '突发事件' }
      ],
      eventClassificationOptions: [
        { value: '1', label: '自然灾害' },
        { value: '2', label: '事故灾难' },
        { value: '3', label: '社会安全事件' }
      ],
      responsibleUnitOptions: [
        { value: '1', label: '市桥隧事务中心' },
        { value: '2', label: '市城管局' },
        { value: '3', label: '市应急和安全生产委员会' }
      ]
    }
  },
  computed: {
    // 日期范围显示文本
    dateRangeDisplayText() {
      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        return `${this.filterForm.dateRange[0]} - ${this.filterForm.dateRange[1]}`
      }
      return '事件触发时间'
    },
    
    // 根据当前阶段动态设置操作列宽度
    operationColumnWidth() {
      const widthMap = {
        'reported': 180,    // 已上报：查看 + 驳回 + 研判 + 删除
        'pending': 150,     // 待启动：查看 + 研判 + 删除
        'inprogress': 220,  // 进行中：查看 + 事件续报 + 事态控制 + 删除
        'finished': 220,    // 已结束：查看 + 新闻通稿 + 总结报告 + 删除
        'archived': 120     // 已归档：查看 + 编辑 + 删除
      }
      return widthMap[this.currentStage] || 160
    }
  },
  mounted() {
    this.loadEventList()
    this.loadStageStatistics()
  },
  watch: {
    // 监控 activeStructureTab 变化，处理Tab切换
    activeStructureTab(newVal, oldVal) {
      if (newVal !== oldVal) {
        console.log('activeStructureTab changed:', { from: oldVal, to: newVal })
        this.handleStructureTabChange()
      }
    }
  },
  methods: {
    // 加载事件列表
    async loadEventList() {
      this.loading = true
      try {
        // 模拟API请求
        const params = {
          stage: this.currentStage,
          structureType: this.activeStructureTab,
          ...this.filterForm,
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        
        // 这里应该调用真实API，目前使用模拟数据
        const response = await this.mockGetEventList(params)
        this.eventList = response.data.map((item, index) => ({
          ...item,
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
        }))
        this.pagination.total = response.total
      } catch (error) {
        console.error('加载事件列表失败:', error)
        this.$message.error('加载事件列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载阶段统计
    async loadStageStatistics() {
      try {
        // 模拟API请求获取各阶段统计数据
        const stats = await this.mockGetStageStats()
        this.stageList.forEach(stage => {
          stage.count = stats[stage.key] || 0
        })
      } catch (error) {
        console.error('加载阶段统计失败:', error)
      }
    },
    
    // 桥梁/隧道切换处理（通过 watch 触发）
    async handleStructureTabChange() {
      // 更新表格key，解决切换tab表格列渲染错乱问题
      this.tableKey = Date.now()
      // 重置筛选条件
      this.resetFilterForm()
      // 重置分页到第一页
      this.pagination.currentPage = 1
      // 并行重新加载事件列表和统计数据
      await Promise.all([
        this.loadEventList(),
        this.loadStageStatistics()
      ])
    },

    // 桥梁/隧道切换点击事件（通过 @tab-click 触发）
    handleStructureTabClick(tab) {
      // 什么都不做，因为 v-model 已经更新了 activeStructureTab
      // 实际的处理逻辑在 watch 中的 handleStructureTabChange 方法中
      console.log('Tab clicked:', tab.name)
    },
    
    // 阶段切换
    async handleStageChange(stage) {
      this.currentStage = stage
      this.pagination.currentPage = 1
      // 并行重新加载事件列表和统计数据
      await Promise.all([
        this.loadEventList(),
        this.loadStageStatistics()
      ])
    },

    handleTabsClick(tab, event) {
      // 每次切换tab的时候，改变tableKey的值
      this.tableKey = new Date()+'' // 解决切换tab表格列渲染错乱问题
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadEventList()
    },
    
    // 重置
    handleReset() {
      this.resetFilterForm()
      this.pagination.currentPage = 1
      this.loadEventList()
    },
    
    // 重置筛选表单数据
    resetFilterForm() {
      this.filterForm = {
        bridgeName: '',
        eventType: '',
        eventLevel: '',
        eventCategory: '',
        eventClassification: '',
        responsibleUnit: '',
        dateRange: []
      }
    },
    
    // 分页
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadEventList()
    },
    
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadEventList()
    },
    
    // 操作按钮事件
    handleView(row) {
      console.log('handleView called, currentStage:', this.currentStage)
      console.log('row data:', row)
      this.currentEventData = row
      if (this.currentStage === 'archived') {
        console.log('Opening allDetailDialogVisible')
        this.allDetailInitialTab = 'eventInfo' // 默认显示事件信息页签
        this.allDetailDialogVisible = true
      } else {
        console.log('Opening detailDialogVisible')
        this.detailDialogVisible = true
      }
    },
    
    // 查看总结报告
    handleViewSummaryReport(row) {
      console.log('handleViewSummaryReport called:', row)
      console.log('Setting allDetailInitialTab to: archiveSummary')
      this.currentEventData = row
      this.allDetailInitialTab = 'archiveSummary' // 直接显示应急总结页签
      this.allDetailDialogVisible = true
      console.log('Dialog visible set to:', this.allDetailDialogVisible)
      console.log('Initial tab set to:', this.allDetailInitialTab)
    },
    
    // 关闭全详情弹窗
    handleAllDetailDialogClose() {
      this.allDetailDialogVisible = false
      this.allDetailInitialTab = 'eventInfo' // 重置为默认页签
    },
    
    handleReject(row) {
      this.$confirm('确认要驳回此事件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用驳回API
        this.$message.success('驳回成功')
        this.loadEventList()
      })
    },
    
    handleAssess(row) {
      this.currentEventData = row
      // 设置研判阶段，优先使用数据中的 assessmentStage 字段
      this.currentAssessmentStage = row.assessmentStage || this.currentStage
      this.assessDialogVisible = true
    },
    
    handleReport(row) {
      this.currentEventData = row
      this.reportDialogVisible = true
    },
    
    handleControl(row) {
      this.currentEventData = row
      this.controlDialogVisible = true
    },
    
    handleNews(row) {
      this.currentEventData = row
      this.newsDialogVisible = true
    },
    
    handleSummary(row) {
      this.currentEventData = row
      this.summaryDialogVisible = true
    },
    
    
    handleDelete(row) {
      this.$confirm(`确认要删除事件编号为"${row.eventCode}"的事件吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API
        this.$message.success('删除成功')
        this.loadEventList()
      })
    },
    
    handleAddEvent() {
      this.addDialogVisible = true
    },
    
    // 弹窗确认事件
    handleAssessConfirm() {
      this.assessDialogVisible = false
      this.loadEventList()
      this.loadStageStatistics()
    },
    
    handleReportConfirm() {
      this.reportDialogVisible = false
      this.loadEventList()
    },
    
    handleAddConfirm() {
      this.addDialogVisible = false
      this.loadEventList()
      this.loadStageStatistics()
    },

    handleControlConfirm() {
      this.controlDialogVisible = false
      this.loadEventList()
      this.loadStageStatistics()
    },

    handleNewsConfirm() {
      this.newsDialogVisible = false
      this.loadEventList()
    },

    handleSummaryConfirm() {
      this.summaryDialogVisible = false
      this.loadEventList()
      this.loadStageStatistics()
    },
    
    // 模拟API方法
    async mockGetEventList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const allMockData = this.generateMockEventData(params.stage, params.structureType)
          // 模拟分页
          const start = (params.page - 1) * params.size
          const end = start + params.size
          const paginatedData = allMockData.slice(start, end)
          
          resolve({
            data: paginatedData,
            total: allMockData.length
          })
        }, 500)
      })
    },
    
    async mockGetStageStats() {
      return new Promise(resolve => {
        setTimeout(() => {
          // 根据当前结构类型返回不同的统计数据
          const bridgeStats = {
            reported: 10,
            pending: 6,
            inprogress: 3,
            finished: 2,
            archived: 8
          }
          const tunnelStats = {
            reported: 8,
            pending: 4,
            inprogress: 2,
            finished: 3,
            archived: 5
          }
          resolve(this.activeStructureTab === 'bridge' ? bridgeStats : tunnelStats)
        }, 300)
      })
    },
    
    generateMockEventData(stage, structureType) {
      const structureNames = structureType === 'bridge' 
        ? ['湘江大桥', '橘子洲大桥', '银盆岭大桥', '福元路大桥', '三汊矶大桥', '月亮岛大桥', '黑石铺大桥', '猴子石大桥', '营盘路大桥', '南湖路大桥']
        : ['营盘路隧道', '湘江隧道', '南湖路隧道', '万家丽隧道', '芙蓉路隧道', '解放路隧道', '五一路隧道', '韶山路隧道', '劳动路隧道', '人民路隧道']
      
      const locations = ['天心区', '岳麓区', '开福区', '雨花区', '芙蓉区', '望城区', '长沙县', '浏阳市', '宁乡市']
      const eventTypes = ['防涝排渍', '抗冰除雪', '突发事件']
      const reportUsers = ['孙明华', '张梓浩', '李小红', '王大明', '陈建国', '刘晓芳', '赵永强', '周美丽', '吴建军', '郑小燕']
      const triggerTimes = [
        '2025-09-20 08:30:15',
        '2025-09-19 14:22:33',
        '2025-09-18 16:45:22',
        '2025-09-17 09:15:44',
        '2025-09-16 11:33:18',
        '2025-09-15 13:20:55',
        '2025-09-14 15:40:12',
        '2025-09-13 07:25:30',
        '2025-09-12 10:18:45',
        '2025-09-11 12:55:20'
      ]
      const reportTimes = [
        '2025-09-20 09:15:30',
        '2025-09-19 15:10:45',
        '2025-09-18 17:20:15',
        '2025-09-17 10:05:22',
        '2025-09-16 12:18:35',
        '2025-09-15 14:08:40',
        '2025-09-14 16:25:18',
        '2025-09-13 08:12:45',
        '2025-09-12 11:05:30',
        '2025-09-11 13:40:55'
      ]
      
      // 根据不同阶段生成不同数量的数据
      let dataCount = 25
      switch (stage) {
        case 'reported':
          dataCount = 10
          break
        case 'pending':
          dataCount = 6
          break
        case 'inprogress':
          dataCount = 3
          break
        case 'finished':
          dataCount = 2
          break
        case 'archived':
          dataCount = 8
          break
      }
      
      const baseData = []
      
      for (let i = 1; i <= dataCount; i++) {
        const item = {
          id: stage + '_' + i, // 使用阶段前缀确保每个阶段的数据不同
          eventCode: `EV${new Date().getFullYear()}${String(i).padStart(4, '0')}`,
          eventSource: ['在线检测', '巡检上报', '市民上报', '第三方上报'][i % 4], 
          eventName: `${structureNames[(i-1) % structureNames.length]}突发事件${i}`,
          structureName: structureNames[(i-1) % structureNames.length],
          location: locations[(i-1) % locations.length],
          triggerTime: triggerTimes[(i-1) % triggerTimes.length],
          eventType: eventTypes[i % eventTypes.length],
          reportUser: reportUsers[(i-1) % reportUsers.length],
          reportTime: reportTimes[(i-1) % reportTimes.length],

          // 根据不同阶段设置不同的数据
          eventCategory: stage === 'reported' ? null : ['自然灾害', '事故灾难', '社会安全事件'][i % 3],
          eventLevel: stage === 'reported' ? null : ['较小事件', '一般事件', '较大及以上事件'][i % 3],
          responsibleUnit: stage === 'reported' ? null : ['市桥隧事务中心', '市城管局', '市应急和安全生产委员会'][i % 3],
          remark: stage === 'pending'&& i%4 != 0 ? `待启动事件${i}的备注信息` : '',
          summaryReport: stage === 'archived' ? true : false,
          // 研判阶段标识：待启动列表的第2行数据设置为三级研判
          assessmentStage: (stage === 'pending' && i === 2) ? 'third' : 
                          (stage === 'pending') ? 'second' : 
                          (stage === 'reported') ? 'reported' : stage,
          // 如果是三级研判的测试数据，添加二级研判的历史数据
          secondEventLevel: (stage === 'pending' && i === 2) ? '2' : null,
          secondResponsibleUnit: (stage === 'pending' && i === 2) ? '2' : null,
          secondRemark: (stage === 'pending' && i === 2) ? '二级研判已完成，需要进行三级研判' : null
        }
        baseData.push(item)
      }
      
      console.log(baseData)
      return baseData
    },
    
    // 格式化时间显示为 YYYY-MM-DD hh:mm
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      
      // 如果是字符串格式，直接处理
      if (typeof dateTime === 'string') {
        // 提取日期和时间部分，格式化为 YYYY-MM-DD hh:mm
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return dateTime // 如果无法解析，返回原值
        
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        
        return `${year}-${month}-${day} ${hours}:${minutes}`
      }
      
      return dateTime
    },
    
    // 切换日期选择器显示
    toggleDatePicker() {
      this.isDatePickerFocused = true
      this.$nextTick(() => {
        this.$refs.hiddenDatePicker.focus()
      })
    },

    // 处理日期选择器失焦
    handleDatePickerBlur() {
      // 延迟执行，确保点击操作能正常完成
      setTimeout(() => {
        this.isDatePickerFocused = false
      }, 200)
    },

    // 处理日期范围变化
    handleDateRangeChange(value) {
      this.filterForm.dateRange = value
      // 日期选择完成后移除焦点状态
      this.isDatePickerFocused = false
    },

    // 清空日期范围
    clearDateRange() {
      this.filterForm.dateRange = []
    },
    
    // 获取事件等级的样式类
    getEventLevelClass(eventLevel) {
      if (!eventLevel) return 'event-level-default'
      
      switch (eventLevel) {
        case '较大及以上事件':
          return 'event-level-major'
        case '一般事件':
          return 'event-level-general'
        case '较小事件':
          return 'event-level-minor'
        default:
          return 'event-level-default'
      }
    },
    
    // 获取研判按钮文本
    getAssessButtonText(row) {
      const assessmentStage = row.assessmentStage || this.currentStage
      switch (assessmentStage) {
        case 'third':
          return '研判'
        case 'pending':
          return '研判'
        case 'reported':
          return '研判'
        default:
          return '研判'
      }
    },
    
    // 计算表格序号（考虑分页）
    getTableIndex(index) {
      return (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入巡检主题样式
@import '@/styles/inspection-theme.scss';
// 导入应急管理公共样式
@import '@/styles/emergency-common.scss';


/* 阶段标签样式 */
.stage-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.stage-tab-item {
  padding: 10px 20px;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: none;
  margin-right: 5px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--inspection-text-secondary);
  transition: all 0.3s;
  border-radius: 4px 4px 0 0;

  &:hover {
    background: rgba(64, 158, 255, 0.1);
    color: var(--inspection-primary-light);
  }

  &.active {
    background: var(--inspection-primary);
    color: white;
    border-color: var(--inspection-primary);
  }
}

/* 事件等级标签样式 */
.event-level-tag {
  display: inline-block;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 4px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 较大及以上事件 - 红色 */
.event-level-major {
  color: #f56c6c;
  border-color: #f56c6c;
}

/* 一般事件 - 橙色 */
.event-level-general {
  color: #e6a23c;
  border-color: #e6a23c;
}

/* 较小事件 - 浅蓝色 */
.event-level-minor {
  color: #409eff;
  border-color: #409eff;
}

/* 其他情况 - 白色（实际为灰色文字） */
.event-level-default {
  color: #909399;
  border-color: #909399;
}

// 自定义日期范围选择器样式，完全匹配filter-select样式
.custom-date-range-selector {
  position: relative;
  width: 100%;
  min-width: 180px;
  flex: 1;
  display: flex; // 确保与其他filter-select对齐
  align-items: center; // 垂直居中对齐

  .date-range-display {
    position: relative;
    box-sizing: border-box;
    display: inline-flex; // 改为inline-flex确保更好的对齐
    align-items: center; // 垂直居中对齐
    width: 100%;
    height: 40px !important;
    padding: 0 30px 0 15px;
    // 使用与filter-select完全相同的样式
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    color: #f8fafc !important;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-size: var(--sds-typography-body-size-medium, 16px) !important;
    font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
    line-height: 140% !important;
    cursor: pointer;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }

    &:hover {
      border-color: rgba(255, 255, 255, 0.4) !important;
    }

    &:focus,
    &.is-focused {
      outline: none;
      border-color: #409EFF !important;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
    }

    .date-range-text {
      display: block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // 移除line-height，使用父容器的flex对齐
      color: #f8fafc !important; // 直接使用与病害类型相同的颜色值
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
      
      &:empty::before {
        content: '事件触发时间';
        color: rgba(255, 255, 255, 0.5) !important;
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        font-size: var(--sds-typography-body-size-medium, 16px) !important;
        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
      }
    }

    .el-input__suffix {
      position: absolute;
      top: 0;
      right: 15px;
      height: 100%;
      display: flex;
      align-items: center;
      pointer-events: none;

      .el-input__icon {
        color: rgba(255, 255, 255, 0.7) !important;
        font-size: 14px !important;
        transition: color 0.3s ease !important;

        &:hover {
          color: rgba(255, 255, 255, 0.9) !important;
        }

        &.clear-icon {
          pointer-events: auto;
          cursor: pointer;
          
          &:hover {
            color: #f56c6c !important;
          }
        }
        
        &.dropdown-icon {
          pointer-events: none;
        }
      }
    }
  }
}
</style>
