<template>
  <el-dialog
    title="批量复核处置"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-review-form">
      <el-form 
        ref="reviewForm"
        :model="formData" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="复核结果" prop="reviewResult" required>
          <el-select 
            v-model="formData.reviewResult" 
            placeholder="请选择复核结果"
            style="width: 100%"
          >
            <el-option
              v-for="option in reviewResultOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="复核意见" prop="reviewComment" required>
          <el-input
            v-model="formData.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入复核意见"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="复核人">
          <el-input 
            v-model="formData.reviewer" 
            placeholder="系统自动获取"
            :disabled="true"
          />
        </el-form-item>
        
        <el-form-item label="复核时间">
          <el-input 
            v-model="formData.reviewTime" 
            placeholder="系统自动获取"
            :disabled="true"
          />
        </el-form-item>
      </el-form>
      
      <!-- 选中记录预览 -->
      <div class="selected-records-preview">
        <div class="preview-title">
          <i class="el-icon-info"></i>
          <span>本次将复核 {{ selectedRows.length }} 条病害记录</span>
        </div>
        <div class="preview-list">
          <div 
            v-for="(record, index) in selectedRows.slice(0, 5)" 
            :key="record.id"
            class="preview-item"
          >
            <span class="item-index">{{ index + 1 }}.</span>
            <span class="item-name">{{ record.bridgeName }}</span>
            <span class="item-disease">{{ record.diseaseType }}</span>
            <span class="item-code">{{ record.diseaseCode }}</span>
          </div>
          <div v-if="selectedRows.length > 5" class="more-indicator">
            还有 {{ selectedRows.length - 5 }} 条记录...
          </div>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        :loading="submitLoading"
        @click="handleSubmit"
      >
        提交复核
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDefaultData } from '@/utils/inspection/defaultData'

export default {
  name: 'BatchReviewDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedRows: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      submitLoading: false,
      
      // 表单数据
      formData: {
        reviewResult: '',
        reviewComment: '',
        reviewer: '',
        reviewTime: ''
      },
      
      // 表单校验规则
      formRules: {
        reviewResult: [
          { required: true, message: '请选择复核结果', trigger: 'change' }
        ],
        reviewComment: [
          { required: true, message: '请输入复核意见', trigger: 'blur' },
          { min: 5, max: 500, message: '复核意见长度应在5-500个字符之间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    // 复核结果选项
    reviewResultOptions() {
      const selectOptions = getDefaultData('selectOptions')
      return selectOptions.reviewResultOptions || [
        { value: 'pass', label: '通过' },
        { value: 'reject', label: '不通过' }
      ]
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm()
      }
    }
  },
  methods: {
    // 初始化表单
    initForm() {
      // 获取当前用户信息（实际应该从用户状态获取）
      const currentUser = this.$store.state.user?.name || '系统管理员'
      const currentTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
      
      this.formData = {
        reviewResult: '',
        reviewComment: '',
        reviewer: currentUser,
        reviewTime: currentTime
      }
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.reviewForm) {
          this.$refs.reviewForm.clearValidate()
        }
      })
    },
    
    // 提交复核
    async handleSubmit() {
      try {
        // 表单验证
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }
        
        this.submitLoading = true
        
        // 准备提交数据
        const submitData = {
          diseaseIds: this.selectedRows.map(row => row.id),
          reviewResult: this.formData.reviewResult,
          reviewComment: this.formData.reviewComment,
          reviewer: this.formData.reviewer,
          reviewTime: this.formData.reviewTime
        }
        
        // 触发提交事件
        this.$emit('submit', submitData)
        
      } catch (error) {
        console.error('批量复核提交失败:', error)
        this.$message.error('提交失败，请重试')
      } finally {
        this.submitLoading = false
      }
    },
    
    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.reviewForm.validate((valid) => {
          resolve(valid)
        })
      })
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.initForm()
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-review-form {
  .selected-records-preview {
    margin-top: 24px;
    padding: 16px;
    background: var(--inspection-card-bg);
    border-radius: 6px;
    border: 1px solid #e9ecef;
    
    .preview-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      i {
        color: #409eff;
        margin-right: 6px;
      }
      
      span {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .preview-list {
      .preview-item {
        display: flex;
        align-items: center;
        padding: 6px 0;
        font-size: 13px;
        color: #606266;
        
        .item-index {
          width: 24px;
          color: #909399;
        }
        
        .item-name {
          flex: 1;
          font-weight: 500;
          color: #303133;
          margin-right: 12px;
        }
        
        .item-disease {
          padding: 2px 8px;
          background: var(--inspection-success);
          color: #67c23a;
          border-radius: 4px;
          font-size: 12px;
          margin-right: 8px;
        }
        
        .item-code {
          color: #909399;
          font-family: monospace;
        }
      }
      
      .more-indicator {
        text-align: center;
        padding: 8px 0;
        color: #909399;
        font-size: 12px;
        border-top: 1px dashed #e4e7ed;
        margin-top: 8px;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .batch-review-form {
    .selected-records-preview {
      .preview-list {
        .preview-item {
          flex-direction: column;
          align-items: flex-start;
          padding: 8px 0;
          
          .item-name {
            margin: 4px 0;
          }
          
          .item-disease,
          .item-code {
            margin: 2px 0;
          }
        }
      }
    }
  }
}</style>
