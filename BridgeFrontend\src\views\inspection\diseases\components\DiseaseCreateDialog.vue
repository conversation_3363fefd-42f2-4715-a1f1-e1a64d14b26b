<template>
  <el-dialog
    title="新增病害信息"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="60%"
    top="5vh"
    custom-class="inspection-log-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :show-close="false"
    :modal="true"
    :modal-append-to-body="true"
    append-to-body
    destroy-on-close
    @close="handleClose"
  >
    <!-- 自定义关闭按钮 -->
    <div class="custom-close-btn" @click="handleClose">
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z" fill="white"/>
      </svg>
    </div>
    
    <div class="dialog-content">
      <el-form
        ref="diseaseForm"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        class="disease-form"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <h4 class="section-title">基础信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="桥梁/隧道名称" prop="bridgeId" required>
                <el-select
                  v-model="formData.bridgeId"
                  placeholder="选择桥梁"
                  style="width: 100%"
                  filterable
                  clearable
                  @change="handleBridgeChange"
                >
                  <el-option
                    v-for="option in bridgeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报属性" prop="reportAttribute" required>
                <el-select
                  v-model="formData.reportAttribute"
                  placeholder="上报交办"
                  style="width: 100%"
                  @change="handleReportAttributeChange"
                >
                  <el-option
                    v-for="option in reportAttributeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="病害部位" prop="diseasePart" required>
                <el-select
                  v-model="formData.diseasePart"
                  placeholder="照明设施"
                  style="width: 100%"
                  @change="handleDiseasePartChange"
                >
                  <el-option
                    v-for="option in diseasePartOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病害类型" prop="diseaseType" required>
                <el-select
                  v-model="formData.diseaseType"
                  placeholder="选择类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in filteredDiseaseTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="病害数量" prop="diseaseCount" required>
                <el-input-number
                  v-model="formData.diseaseCount"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  style="width: 100%"
                  placeholder="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病害位置" prop="diseaseLocation" required>
                <el-input
                  v-model="formData.diseaseLocation"
                  placeholder="xxxx位置"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="病害描述" prop="diseaseDescription" required>
            <el-input
              v-model="formData.diseaseDescription"
              type="textarea"
              :rows="4"
              placeholder="请详细描述病害情况"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 上级交办特有字段 -->
        <div v-if="formData.reportAttribute === 'superior'" class="form-section">
          <h4 class="section-title">交办信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="整改期限" prop="rectificationDeadline">
                <el-select
                  v-model="formData.rectificationDeadline"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option label="7天内" value="7days" />
                  <el-option label="15天内" value="15days" />
                  <el-option label="30天内" value="30days" />
                  <el-option label="3个月内" value="3months" />
                  <el-option label="6个月内" value="6months" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交办领导" prop="assignedLeader">
                <el-input
                  v-model="formData.assignedLeader"
                  placeholder="请输入交办领导姓名"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 附件上传 -->
        <div class="form-section">
          <h4 class="section-title">附件上传</h4>
          
          <!-- 病害图片上传 -->
          <el-form-item label="病害图片" prop="diseaseImages">
            <div class="image-upload-section">
              <ImageViewer
                v-if="hasImageViewer"
                :images="formData.diseaseImages"
                :grid-type="4"
                :show-upload="true"
                :show-delete="true"
                @upload="handleImageUpload"
                @delete="handleImageDelete"
              />
              <div v-else class="simple-image-upload">
                <el-upload
                  ref="imageUploader"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :data="uploadData"
                  :accept="'.jpg,.jpeg,.png,.gif'"
                  :limit="10"
                  :multiple="true"
                  :auto-upload="false"
                  :on-change="handleImageFileChange"
                  :before-upload="beforeImageUpload"
                  list-type="picture-card"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
              </div>
              <div class="upload-tip">
                <p>建议上传病害现场照片，支持 jpg、png 格式，单张图片不超过 2MB</p>
              </div>
            </div>
          </el-form-item>

          <!-- 附件上传 -->
          <el-form-item label="附件">
            <el-upload
              ref="fileUpload"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="uploadData"
              :file-list="formData.attachments"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :before-upload="beforeFileUpload"
              multiple
            >
              <el-button slot="trigger" icon="el-icon-folder-add">点击上传</el-button>
              <div class="el-upload__tip" slot="tip">
                支持常见文档格式，单个文件不超过10MB
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 弹框底部 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        :loading="saveLoading"
        @click="handleSave"
      >
        保存
      </el-button>
      <el-button
        type="primary"
        icon="el-icon-check"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        提交
      </el-button>
    </div>

    <!-- 图片上传弹窗 -->
    <el-dialog
      title="上传病害图片"
      :visible.sync="imageUploadVisible"
      width="600px"
      custom-class="inspection-dialog-base"
      append-to-body
    >
      <el-upload
        ref="imageUploader"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData"
        :accept="'.jpg,.jpeg,.png,.gif'"
        :limit="10"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleImageFileChange"
        :before-upload="beforeImageUpload"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          最多可上传10张图片，每张不超过2MB，支持jpg、png格式
        </div>
      </el-upload>

      <div slot="footer">
        <el-button @click="imageUploadVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmImageUpload">确定上传</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { addDisease } from '@/api/inspection/diseases'

export default {
  name: 'DiseaseCreateDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      saveLoading: false,
      submitLoading: false,
      styleObserver: null,
      styleProtectionInterval: null,
      imageUploadVisible: false,
      hasImageViewer: false, // 标识是否有ImageViewer组件

      // 表单数据
      formData: {
        bridgeId: '',
        reportAttribute: '',
        diseasePart: '',
        diseaseType: '',
        diseaseCount: 1,
        diseaseLocation: '',
        diseaseDescription: '',
        rectificationDeadline: '',
        assignedLeader: '',
        diseaseImages: [],
        attachments: []
      },

      // 表单校验规则
      formRules: {
        bridgeId: [
          { required: true, message: '请选择桥梁/隧道', trigger: 'change' }
        ],
        reportAttribute: [
          { required: true, message: '请选择上报属性', trigger: 'change' }
        ],
        diseasePart: [
          { required: true, message: '请选择病害部位', trigger: 'change' }
        ],
        diseaseType: [
          { required: true, message: '请选择病害类型', trigger: 'change' }
        ],
        diseaseCount: [
          { required: true, message: '请输入病害数量', trigger: 'blur' },
          { type: 'number', min: 1, message: '病害数量至少为1', trigger: 'blur' }
        ],
        diseaseLocation: [
          { required: true, message: '请输入病害位置', trigger: 'blur' },
          { min: 5, max: 200, message: '病害位置长度在5-200个字符之间', trigger: 'blur' }
        ],
        diseaseDescription: [
          { required: true, message: '请输入病害描述', trigger: 'blur' },
          { min: 10, max: 1000, message: '病害描述长度在10-1000个字符之间', trigger: 'blur' }
        ]
      },

      // 上传配置
      uploadAction: '/api/upload/files',
      uploadHeaders: {},
      uploadData: {}
    }
  },
  computed: {
    ...mapGetters('inspection', ['selectOptions']),

    currentUser() {
      return this.$store.state.user?.name || '系统管理员'
    },

    bridgeOptions() {
      return this.selectOptions.bridgeOptions || [
        { value: 'bridge1', label: '示例桥梁1' },
        { value: 'bridge2', label: '示例桥梁2' }
      ]
    },

    reportAttributeOptions() {
      return this.selectOptions.reportAttributeOptions || [
        { value: 'routine', label: '日常巡检' },
        { value: 'superior', label: '上级交办' },
        { value: 'emergency', label: '应急处置' }
      ]
    },

    diseasePartOptions() {
      return this.selectOptions.diseasePartOptions || [
        { value: 'lighting', label: '照明设施' },
        { value: 'guardrail', label: '护栏设施' },
        { value: 'surface', label: '路面' },
        { value: 'drainage', label: '排水设施' }
      ]
    },

    // 根据病害部位筛选病害类型
    filteredDiseaseTypeOptions() {
      const allTypes = this.selectOptions.diseaseTypeOptions || [
        { value: 'crack', label: '裂缝' },
        { value: 'pothole', label: '坑洞' },
        { value: 'damage', label: '损坏' },
        { value: 'corrosion', label: '腐蚀' }
      ]
      return allTypes
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.initDialog()
          
          // 立即应用深色主题样式，避免白色闪烁
          this.$nextTick(() => {
            this.forceApplyDialogStyles()
            // 额外强制修复底部颜色
            this.forceFixFooterColor()
          })
        } else {
          // 清理样式保护资源
          if (this.styleObserver) {
            this.styleObserver.disconnect()
            this.styleObserver = null
          }
          if (this.styleProtectionInterval) {
            clearInterval(this.styleProtectionInterval)
            this.styleProtectionInterval = null
          }
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  async created() {
    await this.initPageData()
    this.checkImageViewerComponent()
  },
  methods: {
    ...mapActions('inspection', ['initSelectOptions']),

    // 初始化页面数据
    async initPageData() {
      try {
        await this.initSelectOptions()
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    },

    // 检查ImageViewer组件是否可用
    checkImageViewerComponent() {
      try {
        // 尝试动态导入ImageViewer组件
        const ImageViewer = () => import('@/components/Inspection/ImageViewer')
        if (ImageViewer) {
          this.hasImageViewer = true
          this.$options.components.ImageViewer = ImageViewer
        }
      } catch (error) {
        console.log('ImageViewer组件不可用，使用简单上传方式')
        this.hasImageViewer = false
      }
    },

    // 初始化弹框
    initDialog() {
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        bridgeId: '',
        reportAttribute: '',
        diseasePart: '',
        diseaseType: '',
        diseaseCount: 1,
        diseaseLocation: '',
        diseaseDescription: '',
        rectificationDeadline: '',
        assignedLeader: '',
        diseaseImages: [],
        attachments: []
      }
      
      this.$nextTick(() => {
        if (this.$refs.diseaseForm) {
          this.$refs.diseaseForm.clearValidate()
        }
      })
    },

    // 桥梁选择变化
    handleBridgeChange(bridgeId) {
      console.log('选择桥梁:', bridgeId)
    },

    // 上报属性变化
    handleReportAttributeChange(attribute) {
      // 根据上报属性调整表单校验规则
      if (attribute === 'superior') {
        // 上级交办需要额外字段
        this.formRules.rectificationDeadline = [
          { required: true, message: '请选择整改期限', trigger: 'change' }
        ]
        this.formRules.assignedLeader = [
          { required: true, message: '请输入交办领导', trigger: 'blur' }
        ]
      } else {
        // 移除上级交办的校验规则
        delete this.formRules.rectificationDeadline
        delete this.formRules.assignedLeader

        // 清空相关字段
        this.formData.rectificationDeadline = ''
        this.formData.assignedLeader = ''
      }
    },

    // 病害部位变化
    handleDiseasePartChange(part) {
      // 重置病害类型选择
      this.formData.diseaseType = ''
      console.log('选择病害部位:', part)
    },

    // 图片上传
    handleImageUpload() {
      this.imageUploadVisible = true
    },

    // 图片删除
    handleImageDelete(index) {
      this.formData.diseaseImages.splice(index, 1)
    },

    // 图片文件选择变化
    handleImageFileChange(file, fileList) {
      console.log('图片文件选择:', file, fileList)
    },

    // 图片上传前检查
    beforeImageUpload(file) {
      const isImage = /\.(jpg|jpeg|png|gif)$/i.test(file.name)
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    // 确认图片上传
    confirmImageUpload() {
      // 模拟上传成功，添加图片
      const mockImages = [
        { url: '/static/images/disease1.jpg', alt: '病害图片1' },
        { url: '/static/images/disease2.jpg', alt: '病害图片2' }
      ]

      this.formData.diseaseImages = [
        ...this.formData.diseaseImages,
        ...mockImages
      ]

      this.imageUploadVisible = false
      this.$message.success('图片上传成功')
    },

    // 附件选择变化
    handleFileChange(file, fileList) {
      this.formData.attachments = fileList
    },

    // 附件移除
    handleFileRemove(file, fileList) {
      this.formData.attachments = fileList
    },

    // 附件上传前检查
    beforeFileUpload(file) {
      const isValidSize = file.size / 1024 / 1024 < 10

      if (!isValidSize) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 保存
    async handleSave() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }

        this.saveLoading = true

        const saveData = this.formatSubmitData()
        // 调用保存API
        // await saveDiseaseData(saveData)

        this.$message.success('保存成功')
        this.$emit('success', saveData)

      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saveLoading = false
      }
    },

    // 提交
    async handleSubmit() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }

        // 检查必要的附件
        if (!this.checkRequiredData()) {
          return
        }

        const result = await this.$confirm(
          '病害信息提交后将进入判定流程，确定要提交吗？',
          '提交确认',
          {
            confirmButtonText: '确定提交',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => false)

        if (!result) {
          return
        }

        this.submitLoading = true

        const submitData = this.formatSubmitData()

        // 调用提交API
        await addDisease(submitData)

        this.$message.success('提交成功')
        this.$emit('success', submitData)
        this.handleClose()

      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitLoading = false
      }
    },

    // 检查必要数据
    checkRequiredData() {
      if (this.formData.diseaseImages.length === 0) {
        this.$message.warning('建议至少上传一张病害现场图片')
      }
      return true
    },

    // 格式化提交数据
    formatSubmitData() {
      const currentTime = new Date().toISOString()

      return {
        ...this.formData,
        reporter: this.currentUser,
        reportTime: currentTime,
        diseaseStatus: 'judging',
        diseaseCode: this.generateDiseaseCode()
      }
    },

    // 生成病害编号
    generateDiseaseCode() {
      const timestamp = Date.now().toString().slice(-6)
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `D${timestamp}${random}`
    },

    // 强制应用弹出框样式 - 与巡检日志弹出框完全一致
    forceApplyDialogStyles() {
      const dialog = document.querySelector('.inspection-log-dialog')
      if (dialog) {
        // 🌙 设置深色主题样式 - 与整体框架风格协调（与巡检日志弹出框完全一致）
        const forceStyles = {
          border: '1px solid rgba(79, 70, 229, 0.3) !important',
          borderColor: 'rgba(79, 70, 229, 0.3) !important',
          borderWidth: '1px !important',
          borderStyle: 'solid !important',
          boxShadow: '0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important',
          background: 'linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important',
          backgroundColor: '#091A4B !important',
          borderRadius: '8px !important',
          color: '#f1f5f9 !important'
        }
        
        Object.keys(forceStyles).forEach(property => {
          dialog.style.setProperty(property, forceStyles[property], 'important')
        })
        
        // 🔄 设置观察器，防止Element UI重新覆盖样式
        this.setupStyleProtection(dialog, forceStyles)
        
        // 🌙 应用深色主题到各个区域
        this.applyDarkThemeToAllAreas()
        
        // 🚀 额外的样式强制措施，立即再次应用一遍
        setTimeout(() => {
          Object.keys(forceStyles).forEach(property => {
            dialog.style.setProperty(property, forceStyles[property], 'important')
          })
        }, 100)
        
        // 确保遮罩层也是深色的
        const modalEl = document.querySelector('.v-modal')
        if (modalEl) {
          modalEl.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
        }
      } else {
        console.warn('❌ [强制样式] 未找到弹框元素')
      }
    },

    // 🛡️ 设置样式保护，防止被覆盖
    setupStyleProtection(dialog, targetStyles) {
      // 使用MutationObserver监听样式变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
            // 重新应用我们的样式
            Object.keys(targetStyles).forEach(property => {
              dialog.style.setProperty(property, targetStyles[property], 'important')
            })
          }
        })
      })
      
      observer.observe(dialog, {
        attributes: true,
        attributeFilter: ['style', 'class']
      })
      
      // 保存观察器引用以便后续清理
      this.styleObserver = observer
    },

    // 🌙 应用深色主题到所有区域
    applyDarkThemeToAllAreas() {
      this.$nextTick(() => {
        // Header区域
        const header = document.querySelector('.inspection-log-dialog .el-dialog__header')
        if (header) {
          header.style.setProperty('background', '#091A4B', 'important')
          header.style.setProperty('color', '#f1f5f9', 'important')
          header.style.setProperty('border-bottom', '1px solid #ffffff', 'important')
          header.style.setProperty('padding', '20px 24px', 'important')
          
          const title = header.querySelector('.el-dialog__title')
          if (title) {
            title.style.setProperty('color', '#f1f5f9', 'important')
            title.style.setProperty('font-weight', '600', 'important')
            title.style.setProperty('font-size', '18px', 'important')
          }
        }
        
        // Body区域
        const body = document.querySelector('.inspection-log-dialog .el-dialog__body')
        if (body) {
          body.style.setProperty('background', '#091A4B', 'important')
          body.style.setProperty('color', '#f1f5f9', 'important')
          body.style.setProperty('padding', '0', 'important')
        }
        
        // 内容区域
        const content = document.querySelector('.inspection-log-dialog .dialog-content')
        if (content) {
          content.style.setProperty('background', '#091A4B', 'important')
          content.style.setProperty('color', '#f1f5f9', 'important')
        }
        
        // 强制应用标题和标签为白色
        const sectionTitles = document.querySelectorAll('.inspection-log-dialog .section-title')
        sectionTitles.forEach(title => {
          title.style.setProperty('color', '#ffffff', 'important')
        })
        
        const formLabels = document.querySelectorAll('.inspection-log-dialog .el-form-item__label')
        formLabels.forEach(label => {
          label.style.setProperty('color', '#ffffff', 'important')
          label.style.setProperty('opacity', '1', 'important')
        })
        
        // 上传区域提示文字
        const uploadTips = document.querySelectorAll('.inspection-log-dialog .upload-tip p, .inspection-log-dialog .el-upload__tip')
        uploadTips.forEach(tip => {
          tip.style.setProperty('color', '#ffffff', 'important')
          tip.style.setProperty('opacity', '0.8', 'important')
        })
        
        // 🔧 强制修复底部背景颜色 - 多重保护
        this.forceFixFooterColor()
      })
    },

    // 🔧 强制修复底部颜色 - 运行时强制设置
    forceFixFooterColor() {
      // 使用定时器确保DOM已经完全渲染
      setTimeout(() => {
        // 方法1: 通过class查找
        const footerSelectors = [
          '.inspection-log-dialog .el-dialog__footer',
          '.el-dialog__footer',
          '.dialog-footer',
          '.el-overlay .el-dialog .el-dialog__footer'
        ]
        
        footerSelectors.forEach(selector => {
          const footers = document.querySelectorAll(selector)
          footers.forEach(footer => {
            if (footer) {
              footer.style.setProperty('background', '#091A4B', 'important')
              footer.style.setProperty('background-color', '#091A4B', 'important')
              footer.style.setProperty('border-top', 'none', 'important')
              console.log(`🔧 强制修复底部颜色: ${selector}`)
            }
          })
        })
        
        // 方法2: 通过父元素查找
        const dialogs = document.querySelectorAll('.inspection-log-dialog .el-dialog')
        dialogs.forEach(dialog => {
          const footer = dialog.querySelector('.el-dialog__footer')
          if (footer) {
            footer.style.setProperty('background', '#091A4B', 'important')
            footer.style.setProperty('background-color', '#091A4B', 'important')
            footer.style.setProperty('border-top', 'none', 'important')
            console.log('🔧 通过父元素强制修复底部颜色')
          }
        })
      }, 100)
      
      // 双重保险 - 稍后再次执行
      setTimeout(() => {
        const allFooters = document.querySelectorAll('.el-dialog__footer')
        allFooters.forEach((footer, index) => {
          // 检查是否在当前弹出框内
          const dialog = footer.closest('.inspection-log-dialog')
          if (dialog) {
            footer.style.setProperty('background', '#091A4B', 'important')
            footer.style.setProperty('background-color', '#091A4B', 'important')
            footer.style.setProperty('border-top', 'none', 'important')
            console.log(`🔧 双重保险修复底部颜色 ${index + 1}`)
          }
        })
      }, 300)
    },

    // 取消
    handleCancel() {
      this.handleClose()
    },

    // 关闭弹框
    handleClose() {
      // 清理样式观察器
      if (this.styleObserver) {
        this.styleObserver.disconnect()
        this.styleObserver = null
      }
      
      // 清理遮罩层样式，防止页面被遮挡
      const modalEl = document.querySelector('.v-modal')
      if (modalEl) {
        modalEl.style.display = 'none'
        modalEl.remove()
      }
      
      this.dialogVisible = false
      this.$emit('close')
      
      // 确保body滚动恢复正常
      this.$nextTick(() => {
        document.body.style.overflow = ''
        document.body.style.paddingRight = ''
      })
    },

    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.diseaseForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善必填信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 引用公共弹出框样式
@import '@/styles/components/dialog.scss';

// 应用与巡检日志弹出框完全一致的样式
.inspection-log-dialog {
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  background-color: #091A4B !important;
  color: #f1f5f9 !important;
  border-radius: 8px !important;
}

// 确保弹框样式完全生效 - 与巡检日志弹出框完全一致
:deep(.el-dialog) {
  border: 1px solid rgba(79, 70, 229, 0.3) !important;
  border-color: rgba(79, 70, 229, 0.3) !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
  color: #f1f5f9 !important;
  border-radius: 8px !important;
  
  .el-dialog__header {
    background: #091A4B !important;
    border-bottom: 1px solid #ffffff !important;
    color: #f1f5f9 !important;
    padding: 20px 24px !important;
    
    .el-dialog__title {
      color: #f1f5f9 !important;
      font-weight: 600 !important;
      font-size: 18px !important;
    }
  }
  
  .el-dialog__body {
    background: #091A4B !important;
    color: #f1f5f9 !important;
    padding: 0 !important;
  }
  
  .el-dialog__footer {
    background: #091A4B !important;
    background-color: #091A4B !important;
    border-top: none !important;
    padding: 20px 24px !important;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
  }
}

.dialog-content {
  .disease-form {
    .form-section {
      margin-bottom: 30px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff !important; // 确保区域标题为纯白色
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; // 确保边框颜色显示
        display: flex;
        align-items: center;
        
        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }
    
    // 表单项样式
    :deep(.el-form-item) {
      margin-bottom: 18px;
      
      .el-form-item__label {
        color: #ffffff !important; // 确保字段标签颜色为纯白色
        font-weight: 600 !important; // 增加字重
        font-size: 14px !important; // 确保字体大小
        opacity: 1 !important; // 确保不会被半透明化
      }
      
      .el-input__inner,
      .el-textarea__inner,
      .el-input-number__decrease,
      .el-input-number__increase {
        background-color: rgba(30, 58, 138, 0.4) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: #ffffff !important;
        border-radius: 6px !important;
        
        &:focus {
          border-color: rgba(79, 70, 229, 0.8) !important;
          box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
        }
        
        &:hover {
          border-color: rgba(255, 255, 255, 0.3) !important;
        }
        
        &::placeholder {
          color: rgba(255, 255, 255, 0.6) !important;
        }
      }
      
      .el-input-number {
        .el-input__inner {
          text-align: left !important;
        }
      }
      
      .el-select {
        .el-input__inner {
          cursor: pointer;
          background-color: rgba(30, 58, 138, 0.4) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          color: #ffffff !important;
          border-radius: 6px !important;
          
          &:focus {
            border-color: rgba(79, 70, 229, 0.8) !important;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2) !important;
          }
          
          &:hover {
            border-color: rgba(255, 255, 255, 0.3) !important;
          }
        }
      }
      
      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
          min-height: 100px;
        }
      }
    }
    
    // 上传组件样式
    .image-upload-section {
      .simple-image-upload {
        :deep(.el-upload--picture-card) {
          background-color: rgba(30, 58, 138, 0.3);
          border-color: rgba(30, 58, 138, 0.4);
          color: #e2e8f0;
          
          &:hover {
            border-color: rgba(79, 70, 229, 0.6);
          }
        }
      }
      
      .upload-tip {
        margin-top: 8px;
        
        p {
          font-size: 12px;
          color: #ffffff !important; // 确保提示文字为白色
          opacity: 0.8 !important; // 稍微透明一些便于阅读
          margin: 0;
          line-height: 1.4;
        }
      }
    }
    
    :deep(.el-upload) {
      .el-button {
        background-color: rgba(30, 58, 138, 0.5);
        border-color: rgba(30, 58, 138, 0.6);
        color: #e2e8f0;
        
        &:hover {
          background-color: rgba(79, 70, 229, 0.6);
          border-color: rgba(79, 70, 229, 0.7);
        }
      }
      
      .el-upload__tip {
        color: #ffffff !important; // 确保上传提示文字为白色
        opacity: 0.8 !important; // 稍微透明一些便于阅读
        font-size: 12px !important;
      }
    }
  }
}

// 底部按钮样式 - 强制覆盖Element UI默认样式
:deep(.el-dialog__footer),
:deep(.dialog-footer) {
  background: #091A4B !important;
  background-color: #091A4B !important;
  border-top: none !important;
  padding: 20px 24px !important;
  border-bottom-left-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
  
  .el-button {
    padding: 12px 32px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
    border: 1px solid #1e3a8a !important;
    color: #ffffff !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
    
    &:hover {
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
      border-color: #3b82f6 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3) !important;
    }
    
    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2) !important;
    }
  }
}

// 强制Footer样式 - 使用最高优先级确保覆盖全局样式
.inspection-log-dialog {
  :deep(.el-dialog) {
    .el-dialog__footer {
      background: #091A4B !important;
      background-color: #091A4B !important;
      border-top: none !important;
    }
  }
  
  // 额外保护层
  :deep(.el-dialog__wrapper) {
    .el-dialog {
      .el-dialog__footer {
        background: #091A4B !important;
        background-color: #091A4B !important;
        border-top: none !important;
      }
    }
  }
}

// 全局强制覆盖Element UI样式
:deep(.el-overlay) {
  .el-dialog {
    .el-dialog__footer {
      background: #091A4B !important;
      background-color: #091A4B !important;
      border-top: none !important;
    }
  }
}

// 🚨 超级强力CSS覆盖 - 最高特异性
.inspection-log-dialog.inspection-log-dialog.inspection-log-dialog {
  :deep(.el-dialog) {
    .el-dialog__footer {
      background: #091A4B !important;
      background-color: #091A4B !important;
      border-top: none !important;
    }
  }
  
  :deep(.el-dialog__footer) {
    background: #091A4B !important;
    background-color: #091A4B !important;
    border-top: none !important;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
    top: 3vh !important;
  }
  
  .dialog-content {
    .disease-form {
      .form-section {
        margin-bottom: 20px;
        
        .section-title {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
