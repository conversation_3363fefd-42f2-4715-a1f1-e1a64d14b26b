<template>
  <div class="disease-dispose-form">
    <!-- 显示判定信息（只读） -->
    <div v-if="showJudgeInfo" class="judge-summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-item">
            <label>判定类型</label>
            <StatusTag 
              :status="formData.judgeType"
              type="inspection"
            />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <label>判定人</label>
            <span>{{ formData.judger || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <label>要求完成时间</label>
            <span>{{ formData.requiredFinishTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="summary-item">
            <label>判定说明</label>
            <span>{{ formData.judgeComment || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 处置信息表单 -->
    <div class="dispose-section">
      <h4><i class="el-icon-setting"></i> 处置信息</h4>
      
      <el-form 
        ref="disposeForm"
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
        :disabled="readonly"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处置方式" prop="disposeType" required>
              <el-input 
                v-model="disposeTypeText" 
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处置人" prop="processor" required>
              <el-input 
                v-model="formData.processor" 
                placeholder="请输入处置人姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="联系方式" prop="processorContact" required>
          <el-input 
            v-model="formData.processorContact" 
            placeholder="请输入联系方式"
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="提交时间">
          <el-input 
            v-model="formData.submitTime" 
            placeholder="系统自动获取"
            :disabled="true"
          />
        </el-form-item>

        <!-- 专项大中修项目显示 -->
        <el-form-item 
          v-if="formData.judgeType === 'emergency_repair'" 
          label="专项大中修项目"
        >
          <el-input 
            v-model="formData.specialProject" 
            :disabled="true"
          />
        </el-form-item>

        <el-form-item label="处置说明" prop="disposeComment" required>
          <el-input
            v-model="formData.disposeComment"
            type="textarea"
            :rows="4"
            placeholder="请详细描述处置过程和结果"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <!-- 处置照片上传 -->
        <div class="dispose-images-section">
          <h5>处置照片</h5>
          <el-row :gutter="12">
            <el-col :span="4">
              <div class="image-category">
                <h6>现场照片</h6>
                <ImageViewer
                  :images="formData.sceneImages || []"
                  :grid-type="1"
                  :show-upload="!readonly"
                  :show-delete="!readonly"
                  @upload="handleImageUpload('scene')"
                  @delete="handleImageDelete('scene', $event)"
                />
              </div>
            </el-col>
            <el-col :span="4">
              <div class="image-category">
                <h6>人车照片</h6>
                <ImageViewer
                  :images="formData.personnelImages || []"
                  :grid-type="1"
                  :show-upload="!readonly"
                  :show-delete="!readonly"
                  @upload="handleImageUpload('personnel')"
                  @delete="handleImageDelete('personnel', $event)"
                />
              </div>
            </el-col>
            <el-col :span="4">
              <div class="image-category">
                <h6>{{ disposeBeforeLabel }}</h6>
                <ImageViewer
                  :images="formData.beforeImages || []"
                  :grid-type="1"
                  :show-upload="!readonly"
                  :show-delete="!readonly"
                  @upload="handleImageUpload('before')"
                  @delete="handleImageDelete('before', $event)"
                />
              </div>
            </el-col>
            <el-col :span="4">
              <div class="image-category">
                <h6>{{ disposeProcessLabel }}</h6>
                <ImageViewer
                  :images="formData.processImages || []"
                  :grid-type="1"
                  :show-upload="!readonly"
                  :show-delete="!readonly"
                  @upload="handleImageUpload('process')"
                  @delete="handleImageDelete('process', $event)"
                />
              </div>
            </el-col>
            <el-col :span="4">
              <div class="image-category">
                <h6>{{ disposeAfterLabel }}</h6>
                <ImageViewer
                  :images="formData.afterImages || []"
                  :grid-type="1"
                  :show-upload="!readonly"
                  :show-delete="!readonly"
                  @upload="handleImageUpload('after')"
                  @delete="handleImageDelete('after', $event)"
                />
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!readonly" class="form-actions">
          <el-button @click="handleCancel">返回</el-button>
          <el-button 
            :loading="saveLoading"
            @click="handleSave"
          >
            保存
          </el-button>
          <el-button 
            type="primary" 
            icon="el-icon-check"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            提交
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 图片上传弹窗 -->
    <el-dialog
      title="上传图片"
      :visible.sync="uploadDialogVisible"
      width="600px"
    >
      <el-upload
        ref="imageUpload"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData"
        :accept="'.jpg,.jpeg,.png,.gif'"
        :limit="5"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB</div>
      </el-upload>
      
      <div slot="footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmUpload">确定上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { StatusTag, ImageViewer } from '@/components/Inspection'

export default {
  name: 'DiseaseDisposeForm',
  components: {
    StatusTag,
    ImageViewer
  },
  props: {
    diseaseDetail: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      saveLoading: false,
      submitLoading: false,
      uploadDialogVisible: false,
      currentUploadCategory: '',
      
      // 表单数据
      formData: {
        // 判定信息（只读）
        judgeType: '',
        judger: '',
        judgeComment: '',
        requiredFinishTime: '',
        specialProject: '',
        
        // 处置信息
        disposeType: '',
        processor: '',
        processorContact: '',
        submitTime: '',
        disposeComment: '',
        
        // 处置照片
        sceneImages: [],
        personnelImages: [],
        beforeImages: [],
        processImages: [],
        afterImages: []
      },
      
      // 表单校验规则
      formRules: {
        processor: [
          { required: true, message: '请输入处置人姓名', trigger: 'blur' }
        ],
        processorContact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        disposeComment: [
          { required: true, message: '请输入处置说明', trigger: 'blur' },
          { min: 10, max: 1000, message: '处置说明长度应在10-1000个字符之间', trigger: 'blur' }
        ]
      },
      
      // 上传配置
      uploadAction: '/api/upload/images',
      uploadHeaders: {},
      uploadData: {}
    }
  },
  computed: {
    showJudgeInfo() {
      return this.formData.judger || this.formData.judgeComment
    },
    
    disposeTypeText() {
      const typeMap = {
        'daily_maintenance': '日常养护',
        'emergency_repair': '应急维修',
        'out_of_scope': '养护范围外'
      }
      return typeMap[this.formData.judgeType] || this.formData.judgeType
    },
    
    // 根据判定类型显示不同的照片标签
    disposeBeforeLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修前' : '处置前'
    },
    
    disposeProcessLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修中' : '处置中'
    },
    
    disposeAfterLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修后' : '处置后'
    }
  },
  watch: {
    diseaseDetail: {
      handler(newDetail) {
        this.initFormData(newDetail)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData(diseaseDetail) {
      const currentUser = this.$store.state.user?.name || '系统管理员'
      const currentTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
      
      this.formData = {
        // 判定信息
        judgeType: diseaseDetail.judgeType || '',
        judger: diseaseDetail.judger || '',
        judgeComment: diseaseDetail.judgeComment || '',
        requiredFinishTime: diseaseDetail.requiredFinishTime || '',
        specialProject: diseaseDetail.specialProject || '',
        
        // 处置信息
        disposeType: diseaseDetail.disposeType || diseaseDetail.judgeType,
        processor: diseaseDetail.processor || currentUser,
        processorContact: diseaseDetail.processorContact || '',
        submitTime: diseaseDetail.submitTime || currentTime,
        disposeComment: diseaseDetail.disposeComment || '',
        
        // 处置照片
        sceneImages: diseaseDetail.sceneImages || [],
        personnelImages: diseaseDetail.personnelImages || [],
        beforeImages: diseaseDetail.beforeImages || [],
        processImages: diseaseDetail.processImages || [],
        afterImages: diseaseDetail.afterImages || []
      }
    },
    
    // 图片上传处理
    handleImageUpload(category) {
      this.currentUploadCategory = category
      this.uploadDialogVisible = true
    },
    
    // 图片删除处理
    handleImageDelete(category, index) {
      const categoryKey = `${category}Images`
      if (this.formData[categoryKey] && this.formData[categoryKey][index]) {
        this.formData[categoryKey].splice(index, 1)
      }
    },
    
    // 文件选择变化
    handleFileChange(file, fileList) {
      console.log('文件选择:', file, fileList)
    },
    
    // 上传前检查
    beforeUpload(file) {
      const isImage = /\.(jpg|jpeg|png|gif)$/i.test(file.name)
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    // 确认上传
    handleConfirmUpload() {
      // 这里实现实际的图片上传逻辑
      // 上传成功后将图片URL添加到对应的图片数组中
      
      const categoryKey = `${this.currentUploadCategory}Images`
      
      // 模拟上传成功，添加图片到对应分类
      const mockImages = [
        { url: '/static/images/dispose1.jpg', alt: '处置图片1' },
        { url: '/static/images/dispose2.jpg', alt: '处置图片2' }
      ]
      
      this.formData[categoryKey] = [
        ...this.formData[categoryKey],
        ...mockImages
      ]
      
      this.uploadDialogVisible = false
      this.$message.success('图片上传成功')
    },
    
    // 保存
    async handleSave() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }
        
        this.saveLoading = true
        
        this.$emit('save', { ...this.formData })
        
      } catch (error) {
        console.error('保存处置信息失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saveLoading = false
      }
    },
    
    // 提交
    async handleSubmit() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }
        
        // 检查必要的照片是否已上传
        if (!this.checkRequiredImages()) {
          return
        }
        
        const result = await this.$confirm(
          '处置信息提交后将无法修改，确定要提交吗？',
          '提交确认',
          {
            confirmButtonText: '确定提交',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => false)
        
        if (!result) {
          return
        }
        
        this.submitLoading = true
        
        this.$emit('submit', { ...this.formData })
        
      } catch (error) {
        console.error('提交处置信息失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitLoading = false
      }
    },
    
    // 检查必要的图片是否已上传
    checkRequiredImages() {
      const requiredCategories = ['scene', 'before', 'after']
      const missingCategories = []
      
      requiredCategories.forEach(category => {
        const categoryKey = `${category}Images`
        if (!this.formData[categoryKey] || this.formData[categoryKey].length === 0) {
          missingCategories.push(category)
        }
      })
      
      if (missingCategories.length > 0) {
        const categoryNames = {
          scene: '现场照片',
          before: this.disposeBeforeLabel,
          after: this.disposeAfterLabel
        }
        
        const missingNames = missingCategories.map(cat => categoryNames[cat]).join('、')
        this.$message.error(`请上传必要的处置照片：${missingNames}`)
        return false
      }
      
      return true
    },
    
    // 取消
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.disposeForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善必填信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.disease-dispose-form {
  .judge-summary {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--inspection-card-bg);
    border-radius: 6px;
    border: 1px solid #e9ecef;
    
    .summary-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
        align-items: flex-start;
      }
      
      label {
        width: 100px;
        font-weight: 500;
        color: #606266;
        flex-shrink: 0;
      }
      
      span {
        color: #303133;
        word-break: break-all;
      }
    }
  }
  
  .dispose-section {
    h4 {
      display: flex;
      align-items: center;
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
    
    .dispose-images-section {
      margin-top: 24px;
      
      h5 {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
      
      .image-category {
        text-align: center;
        
        h6 {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: #606266;
          font-weight: 500;
        }
      }
    }
    
    .form-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #e4e7ed;
      margin-top: 20px;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .disease-dispose-form {
    .dispose-images-section {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .disease-dispose-form {
    .judge-summary {
      .summary-item {
        flex-direction: column;
        align-items: flex-start;
        
        label {
          width: auto;
          margin-bottom: 4px;
        }
      }
    }
    
    .form-actions {
      .el-button {
        width: 80px;
        margin: 4px !important;
      }
    }
  }
}</style>
