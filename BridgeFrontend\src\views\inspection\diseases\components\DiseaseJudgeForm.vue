<template>
  <div class="disease-judge-form">
    <el-form 
      ref="judgeForm"
      :model="formData" 
      :rules="formRules" 
      label-width="120px"
      :disabled="readonly"
    >
      <el-form-item label="判定类型" prop="judgeType" required>
        <el-radio-group v-model="formData.judgeType" :disabled="readonly">
          <el-radio label="daily_maintenance">日常养护及小修</el-radio>
          <el-radio label="emergency_repair">应急维修</el-radio>
          <el-radio label="out_of_scope">养护范围外</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="判定说明" prop="judgeComment" required>
            <el-input
              v-model="formData.judgeComment"
              type="textarea"
              :rows="4"
              placeholder="请输入判定说明"
              maxlength="500"
              show-word-limit
              :disabled="readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="要求完成时间" prop="requiredFinishTime" required>
            <el-date-picker
              v-model="formData.requiredFinishTime"
              type="datetime"
              placeholder="请选择要求完成时间"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              :disabled-date="disabledDate"
              :disabled="readonly"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="判定人">
            <div class="readonly-field">
              <span>{{ formData.judger || '系统自动获取' }}</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="判定时间">
            <div class="readonly-field">
              <span>{{ formData.judgeTime || '系统自动获取' }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DiseaseJudgeForm',
  props: {
    diseaseDetail: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 表单数据
      formData: {
        judgeType: '',
        judgeComment: '',
        requiredFinishTime: '',
        judger: '',
        judgeTime: '',
        specialProject: ''
      },
      
      // 表单校验规则
      formRules: {
        judgeType: [
          { required: true, message: '请选择判定类型', trigger: 'change' }
        ],
        judgeComment: [
          { required: true, message: '请输入判定说明', trigger: 'blur' },
          { min: 5, max: 500, message: '判定说明长度应在5-500个字符之间', trigger: 'blur' }
        ],
        requiredFinishTime: [
          { required: true, message: '请选择要求完成时间', trigger: 'change' }
        ]
      },
      
      // 专项大中修项目选项
      specialProjects: [
        { value: 'project1', label: '橘子洲大桥维修项目' },
        { value: 'project2', label: '银盆岭大桥改造项目' },
        { value: 'project3', label: '三汊矶大桥加固项目' }
      ]
    }
  },
  watch: {
    diseaseDetail: {
      handler(newDetail) {
        this.initFormData(newDetail)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData(diseaseDetail) {
      // 获取当前用户信息（实际应该从用户状态获取）
      const currentUser = this.$store.state.user?.name || '系统管理员'
      const currentTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
      
      this.formData = {
        judgeType: diseaseDetail.judgeType || '',
        judgeComment: diseaseDetail.judgeComment || '',
        requiredFinishTime: diseaseDetail.requiredFinishTime || '',
        judger: diseaseDetail.judger || currentUser,
        judgeTime: diseaseDetail.judgeTime || currentTime,
        specialProject: diseaseDetail.specialProject || ''
      }
    },
    
    // 禁用过去的日期
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7 // 禁用今天之前的日期
    },
    
    
    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.judgeForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善必填信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.disease-judge-form {
  .el-form {
    // 统一标签样式，与病害基本信息保持一致
    :deep(.el-form-item__label) {
      width: 120px;
      font-weight: 600;
      color: #ffffff !important;
      flex-shrink: 0;
      font-size: 14px;
      line-height: 1.5;
    }
    
    // 单选框字体颜色优化
    :deep(.el-radio__label) {
      color: #f0f8ff !important;
      font-weight: 500;
    }
    
    .el-radio-group {
      .el-radio {
        margin-right: 24px;
        margin-bottom: 12px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
    
    
    // 只读字段样式
    .readonly-field {
      min-height: 32px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      display: flex;
      align-items: center;
      
      span {
        color: #e2e8f0;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

// 优化只读模式样式，使其更明显地表示不可编辑状态
.disease-judge-form :deep(.el-form.is-disabled) {
  // 输入框只读样式
  .el-input__inner,
  .el-textarea__inner {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #e2e8f0 !important;
    cursor: not-allowed;
    
    &::placeholder {
      color: rgba(226, 232, 240, 0.5) !important;
    }
  }
  
  // 日期选择器只读样式
  .el-date-editor.el-input {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    cursor: not-allowed;
    
    .el-input__inner {
      color: #e2e8f0 !important;
      cursor: not-allowed;
    }
    
    .el-input__prefix,
    .el-input__suffix {
      color: rgba(226, 232, 240, 0.5) !important;
    }
  }
  
  // 单选框只读样式
  .el-radio {
    cursor: not-allowed;
    
    .el-radio__input {
      cursor: not-allowed;
      
      .el-radio__inner {
        background-color: rgba(255, 255, 255, 0.05) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        cursor: not-allowed;
      }
      
      &.is-checked .el-radio__inner {
        background-color: rgba(92, 157, 255, 0.3) !important;
        border-color: rgba(92, 157, 255, 0.5) !important;
        
        &::after {
          background-color: rgba(255, 255, 255, 0.8) !important;
        }
      }
    }
    
    .el-radio__label {
      color: rgba(226, 232, 240, 0.7) !important;
      cursor: not-allowed;
    }
    
    &.is-checked .el-radio__label {
      color: #e2e8f0 !important;
      font-weight: 500;
    }
  }
  
  // 文本域只读样式
  .el-textarea {
    .el-textarea__inner {
      background-color: rgba(255, 255, 255, 0.05) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      color: #e2e8f0 !important;
      cursor: not-allowed;
      resize: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .disease-judge-form {
    .form-actions {
      .el-button {
        width: 80px;
        margin: 4px;
      }
    }
  }
}</style>
