<template>
  <div class="disease-review-form">
    <!-- 显示处置信息（只读） -->
    <div v-if="showDisposeInfo" class="dispose-summary">
      <h5>处置信息</h5>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-item">
            <label>处置方式</label>
            <span>{{ disposeTypeText }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <label>处置人</label>
            <span>{{ formData.processor || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <label>联系方式</label>
            <span>{{ formData.processorContact || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="summary-item">
            <label>提交时间</label>
            <span>{{ formData.submitTime || '-' }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="summary-item">
            <label>处置说明</label>
            <span>{{ formData.disposeComment || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <!-- 处置照片展示 -->
      <div v-if="hasDisposeImages" class="dispose-images-display">
        <el-row :gutter="12">
          <el-col v-if="formData.sceneImages && formData.sceneImages.length > 0" :span="4">
            <div class="image-category">
              <h6>现场照片</h6>
              <ImageViewer
                :images="formData.sceneImages"
                :grid-type="1"
                :show-upload="false"
                :show-delete="false"
              />
            </div>
          </el-col>
          <el-col v-if="formData.personnelImages && formData.personnelImages.length > 0" :span="4">
            <div class="image-category">
              <h6>人车照片</h6>
              <ImageViewer
                :images="formData.personnelImages"
                :grid-type="1"
                :show-upload="false"
                :show-delete="false"
              />
            </div>
          </el-col>
          <el-col v-if="formData.beforeImages && formData.beforeImages.length > 0" :span="4">
            <div class="image-category">
              <h6>{{ disposeBeforeLabel }}</h6>
              <ImageViewer
                :images="formData.beforeImages"
                :grid-type="1"
                :show-upload="false"
                :show-delete="false"
              />
            </div>
          </el-col>
          <el-col v-if="formData.processImages && formData.processImages.length > 0" :span="4">
            <div class="image-category">
              <h6>{{ disposeProcessLabel }}</h6>
              <ImageViewer
                :images="formData.processImages"
                :grid-type="1"
                :show-upload="false"
                :show-delete="false"
              />
            </div>
          </el-col>
          <el-col v-if="formData.afterImages && formData.afterImages.length > 0" :span="4">
            <div class="image-category">
              <h6>{{ disposeAfterLabel }}</h6>
              <ImageViewer
                :images="formData.afterImages"
                :grid-type="1"
                :show-upload="false"
                :show-delete="false"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 复核信息表单 -->
    <div class="review-section">
      <h4><i class="el-icon-check"></i> 复核信息</h4>
      
      <el-form 
        ref="reviewForm"
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
        :disabled="readonly"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="复核结果" prop="reviewResult" required>
              <el-select 
                v-model="formData.reviewResult" 
                placeholder="请选择复核结果"
                style="width: 100%"
              >
                <el-option
                  v-for="option in reviewResultOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复核人">
              <el-input 
                v-model="formData.reviewer" 
                placeholder="系统自动获取"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="复核时间">
          <el-input 
            v-model="formData.reviewTime" 
            placeholder="系统自动获取"
            :disabled="true"
          />
        </el-form-item>

        <el-form-item label="复核意见" prop="reviewComment" required>
          <el-input
            v-model="formData.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入复核意见"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <!-- 不通过时显示整改要求 -->
        <el-form-item 
          v-if="formData.reviewResult === 'reject'" 
          label="整改要求" 
          prop="rectificationRequirement"
          required
        >
          <el-input
            v-model="formData.rectificationRequirement"
            type="textarea"
            :rows="3"
            placeholder="请详细说明整改要求"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

      </el-form>
    </div>

    <!-- 归档信息（复核通过后显示） -->
    <div v-if="showArchiveInfo" class="archive-section">
      <h4><i class="el-icon-folder"></i> 归档信息</h4>
      <div class="archive-info">
        <div class="summary-item">
          <label>归档时间</label>
          <span>{{ formData.archiveTime || '-' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ImageViewer } from '@/components/Inspection'
import { getDefaultData } from '@/utils/inspection/defaultData'

export default {
  name: 'DiseaseReviewForm',
  components: {
    ImageViewer
  },
  props: {
    diseaseDetail: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      
      // 表单数据
      formData: {
        // 处置信息（只读显示）
        judgeType: '',
        processor: '',
        processorContact: '',
        submitTime: '',
        disposeComment: '',
        sceneImages: [],
        personnelImages: [],
        beforeImages: [],
        processImages: [],
        afterImages: [],
        
        // 复核信息
        reviewResult: '',
        reviewer: '',
        reviewTime: '',
        reviewComment: '',
        rectificationRequirement: '',
        
        // 归档信息
        archiveTime: ''
      },
      
      // 表单校验规则
      formRules: {
        reviewResult: [
          { required: true, message: '请选择复核结果', trigger: 'change' }
        ],
        reviewComment: [
          { required: true, message: '请输入复核意见', trigger: 'blur' },
          { min: 5, max: 500, message: '复核意见长度应在5-500个字符之间', trigger: 'blur' }
        ],
        rectificationRequirement: [
          { required: true, message: '请输入整改要求', trigger: 'blur' },
          { min: 10, max: 500, message: '整改要求长度应在10-500个字符之间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    showDisposeInfo() {
      return this.formData.processor || this.formData.disposeComment
    },
    
    showArchiveInfo() {
      return this.formData.reviewResult === 'pass' && this.formData.archiveTime
    },
    
    disposeTypeText() {
      const typeMap = {
        'daily_maintenance': '日常养护',
        'emergency_repair': '应急维修',
        'out_of_scope': '养护范围外'
      }
      return typeMap[this.formData.judgeType] || this.formData.judgeType
    },
    
    disposeBeforeLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修前' : '处置前'
    },
    
    disposeProcessLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修中' : '处置中'
    },
    
    disposeAfterLabel() {
      return this.formData.judgeType === 'emergency_repair' ? '维修后' : '处置后'
    },
    
    hasDisposeImages() {
      const imageCategories = ['sceneImages', 'personnelImages', 'beforeImages', 'processImages', 'afterImages']
      return imageCategories.some(category => 
        this.formData[category] && this.formData[category].length > 0
      )
    },
    
    reviewResultOptions() {
      const selectOptions = getDefaultData('selectOptions')
      return selectOptions.reviewResultOptions || [
        { value: 'pass', label: '通过' },
        { value: 'reject', label: '不通过' }
      ]
    }
  },
  watch: {
    diseaseDetail: {
      handler(newDetail) {
        this.initFormData(newDetail)
      },
      immediate: true,
      deep: true
    },
    
    // 监听复核结果变化，更新校验规则
    'formData.reviewResult'(newResult) {
      this.updateFormRules(newResult)
    }
  },
  methods: {
    // 初始化表单数据
    initFormData(diseaseDetail) {
      const currentUser = this.$store.state.user?.name || '系统管理员'
      const currentTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
      
      this.formData = {
        // 处置信息
        judgeType: diseaseDetail.judgeType || '',
        processor: diseaseDetail.processor || '',
        processorContact: diseaseDetail.processorContact || '',
        submitTime: diseaseDetail.submitTime || '',
        disposeComment: diseaseDetail.disposeComment || '',
        sceneImages: diseaseDetail.sceneImages || [],
        personnelImages: diseaseDetail.personnelImages || [],
        beforeImages: diseaseDetail.beforeImages || [],
        processImages: diseaseDetail.processImages || [],
        afterImages: diseaseDetail.afterImages || [],
        
        // 复核信息
        reviewResult: diseaseDetail.reviewResult || '',
        reviewer: diseaseDetail.reviewer || currentUser,
        reviewTime: diseaseDetail.reviewTime || currentTime,
        reviewComment: diseaseDetail.reviewComment || '',
        rectificationRequirement: diseaseDetail.rectificationRequirement || '',
        
        // 归档信息
        archiveTime: diseaseDetail.archiveTime || ''
      }
    },
    
    // 更新表单校验规则
    updateFormRules(reviewResult) {
      if (reviewResult === 'reject') {
        // 不通过时，整改要求为必填
        this.formRules.rectificationRequirement = [
          { required: true, message: '请输入整改要求', trigger: 'blur' },
          { min: 10, max: 500, message: '整改要求长度应在10-500个字符之间', trigger: 'blur' }
        ]
      } else {
        // 通过时，整改要求不是必填
        this.formRules.rectificationRequirement = []
      }
      
      // 重新验证表单
      this.$nextTick(() => {
        if (this.$refs.reviewForm) {
          this.$refs.reviewForm.clearValidate('rectificationRequirement')
        }
      })
    },
    
    
    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.reviewForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善必填信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.disease-review-form {
  .dispose-summary {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--inspection-card-bg);
    border-radius: 6px;
    border: 1px solid #e9ecef;
    
    h5 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
    
    .summary-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      label {
        width: 100px;
        font-weight: 500;
        color: #606266;
        flex-shrink: 0;
      }
      
      span {
        color: #303133;
        word-break: break-all;
      }
    }
    
    .dispose-images-display {
      margin-top: 16px;
      
      .image-category {
        text-align: center;
        
        h6 {
          margin: 0 0 8px 0;
          font-size: 12px;
          color: #606266;
          font-weight: 500;
        }
      }
    }
  }
  
  .review-section {
    h4 {
      display: flex;
      align-items: center;
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #67c23a;
      }
    }
  }
  
  .archive-section {
    margin-top: 24px;
    padding: 16px;
    background: var(--inspection-card-bg);
    border-radius: 6px;
    border: 1px solid #b3e19d;
    
    h4 {
      display: flex;
      align-items: center;
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #67c23a;
      
      i {
        margin-right: 8px;
      }
    }
    
    .archive-info {
      .summary-item {
        display: flex;
        align-items: center;
        
        label {
          width: 100px;
          font-weight: 500;
          color: #606266;
        }
        
        span {
          color: #303133;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .disease-review-form {
    .dispose-images-display {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .disease-review-form {
    .dispose-summary,
    .archive-section {
      .summary-item {
        flex-direction: column;
        align-items: flex-start;
        
        label {
          width: auto;
          margin-bottom: 4px;
        }
      }
    }
    
    .form-actions {
      .el-button {
        width: 80px;
        margin: 4px !important;
      }
    }
  }
}</style>
