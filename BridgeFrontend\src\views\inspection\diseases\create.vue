<template>
  <div class="disease-create inspection-container">
    <div class="page-container"
      <!-- 病害处置流程步骤 -->
      <el-card class="workflow-card" shadow="never">
        <WorkflowSteps
          :current-step="0"
          :current-handler="currentUser"
          :current-time="currentTime"
          :steps="workflowSteps"
        />
      </el-card>

      <!-- 新增病害信息表单 -->
      <el-card class="form-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-info"></i> 新增病害信息</h3>
        </div>
        
        <el-form 
          ref="diseaseForm"
          :model="formData" 
          :rules="formRules" 
          label-width="140px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="桥梁/隧道名称" prop="bridgeId" required>
                <el-select
                  v-model="formData.bridgeId"
                  placeholder="请选择桥梁/隧道"
                  style="width: 100%"
                  filterable
                  clearable
                  @change="handleBridgeChange"
                >
                  <el-option
                    v-for="option in bridgeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报属性" prop="reportAttribute" required>
                <el-select
                  v-model="formData.reportAttribute"
                  placeholder="请选择上报属性"
                  style="width: 100%"
                  @change="handleReportAttributeChange"
                >
                  <el-option
                    v-for="option in reportAttributeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="病害部位" prop="diseasePart" required>
                <el-select
                  v-model="formData.diseasePart"
                  placeholder="请选择病害部位"
                  style="width: 100%"
                  @change="handleDiseasePartChange"
                >
                  <el-option
                    v-for="option in diseasePartOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病害类型" prop="diseaseType" required>
                <el-select
                  v-model="formData.diseaseType"
                  placeholder="请选择病害类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in filteredDiseaseTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="病害数量" prop="diseaseCount" required>
                <el-input-number
                  v-model="formData.diseaseCount"
                  :min="1"
                  :max="999"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病害位置" prop="diseaseLocation" required>
                <el-input
                  v-model="formData.diseaseLocation"
                  placeholder="请输入病害位置"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="病害描述" prop="diseaseDescription" required>
            <el-input
              v-model="formData.diseaseDescription"
              type="textarea"
              :rows="4"
              placeholder="请详细描述病害情况"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <!-- 上级交办特有字段 -->
          <div v-if="formData.reportAttribute === 'superior'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="整改期限" prop="rectificationDeadline">
                  <el-select
                    v-model="formData.rectificationDeadline"
                    placeholder="请选择整改期限"
                    style="width: 100%"
                  >
                    <el-option label="7天内" value="7days" />
                    <el-option label="15天内" value="15days" />
                    <el-option label="30天内" value="30days" />
                    <el-option label="3个月内" value="3months" />
                    <el-option label="6个月内" value="6months" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="交办领导" prop="assignedLeader">
                  <el-input
                    v-model="formData.assignedLeader"
                    placeholder="请输入交办领导姓名"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 病害图片上传 -->
          <el-form-item label="病害图片" prop="diseaseImages">
            <div class="image-upload-section">
              <ImageViewer
                :images="formData.diseaseImages"
                :grid-type="4"
                :show-upload="true"
                :show-delete="true"
                @upload="handleImageUpload"
                @delete="handleImageDelete"
              />
              <div class="upload-tip">
                <p>建议上传病害现场照片，支持 jpg、png 格式，单张图片不超过 2MB</p>
              </div>
            </div>
          </el-form-item>

          <!-- 附件上传 -->
          <el-form-item label="附件">
            <el-upload
              ref="fileUpload"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="uploadData"
              :file-list="formData.attachments"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :before-upload="beforeFileUpload"
              multiple
            >
              <el-button slot="trigger" icon="el-icon-folder-add">选择文件</el-button>
              <div class="el-upload__tip" slot="tip">
                支持常见文档格式，单个文件不超过10MB
              </div>
            </el-upload>
          </el-form-item>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button 
              :loading="saveLoading"
              @click="handleSave"
            >
              保存
            </el-button>
            <el-button 
              type="primary" 
              icon="el-icon-check"
              :loading="submitLoading"
              @click="handleSubmit"
            >
              提交
            </el-button>
          </div>
        </el-form>
    </el-card>

    <!-- 图片上传弹窗 -->
    <el-dialog
      title="上传病害图片"
      :visible.sync="imageUploadVisible"
      width="600px"
    >
      <el-upload
        ref="imageUploader"
        :action="uploadAction"
        :headers="uploadHeaders"
        :data="uploadData"
        :accept="'.jpg,.jpeg,.png,.gif'"
        :limit="10"
        :multiple="true"
        :auto-upload="false"
        :on-change="handleImageFileChange"
        :before-upload="beforeImageUpload"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          最多可上传10张图片，每张不超过2MB，支持jpg、png格式
        </div>
      </el-upload>

      <div slot="footer">
        <el-button @click="imageUploadVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmImageUpload">确定上传</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { WorkflowSteps, ImageViewer } from '@/components/Inspection'
import { addDisease } from '@/api/inspection/diseases'

export default {
  name: 'DiseaseCreate',
  components: {
    WorkflowSteps,
    ImageViewer
  },
  data() {
    return {
      saveLoading: false,
      submitLoading: false,
      imageUploadVisible: false,
      
      // 工作流步骤配置
      workflowSteps: [
        { key: 'report', title: '上报' },
        { key: 'judge', title: '判定' },
        { key: 'dispose', title: '处置' },
        { key: 'review', title: '复核' },
        { key: 'archive', title: '归档' }
      ],
      
      // 表单数据
      formData: {
        bridgeId: '',
        reportAttribute: '',
        diseasePart: '',
        diseaseType: '',
        diseaseCount: 1,
        diseaseLocation: '',
        diseaseDescription: '',
        rectificationDeadline: '',
        assignedLeader: '',
        diseaseImages: [],
        attachments: []
      },
      
      // 表单校验规则
      formRules: {
        bridgeId: [
          { required: true, message: '请选择桥梁/隧道', trigger: 'change' }
        ],
        reportAttribute: [
          { required: true, message: '请选择上报属性', trigger: 'change' }
        ],
        diseasePart: [
          { required: true, message: '请选择病害部位', trigger: 'change' }
        ],
        diseaseType: [
          { required: true, message: '请选择病害类型', trigger: 'change' }
        ],
        diseaseCount: [
          { required: true, message: '请输入病害数量', trigger: 'blur' },
          { type: 'number', min: 1, message: '病害数量至少为1', trigger: 'blur' }
        ],
        diseaseLocation: [
          { required: true, message: '请输入病害位置', trigger: 'blur' },
          { min: 5, max: 200, message: '病害位置长度在5-200个字符之间', trigger: 'blur' }
        ],
        diseaseDescription: [
          { required: true, message: '请输入病害描述', trigger: 'blur' },
          { min: 10, max: 1000, message: '病害描述长度在10-1000个字符之间', trigger: 'blur' }
        ]
      },
      
      // 上传配置
      uploadAction: '/api/upload/files',
      uploadHeaders: {},
      uploadData: {}
    }
  },
  computed: {
    ...mapGetters('inspection', ['selectOptions']),
    
    currentUser() {
      return this.$store.state.user?.name || '系统管理员'
    },
    
    currentTime() {
      return new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/\//g, '-')
    },
    
    bridgeOptions() {
      return this.selectOptions.bridgeOptions || []
    },
    
    reportAttributeOptions() {
      return this.selectOptions.reportAttributeOptions || []
    },
    
    diseasePartOptions() {
      return this.selectOptions.diseasePartOptions || []
    },
    
    // 根据病害部位筛选病害类型
    filteredDiseaseTypeOptions() {
      const allTypes = this.selectOptions.diseaseTypeOptions || []
      
      // 这里可以根据病害部位筛选相关的病害类型
      // 暂时返回所有类型，实际可以根据业务需求进行筛选
      return allTypes
    }
  },
  async created() {
    await this.initPageData()
  },
  methods: {
    ...mapActions('inspection', ['initSelectOptions']),
    
    // 初始化页面数据
    async initPageData() {
      await this.initSelectOptions()
    },
    
    // 桥梁选择变化
    handleBridgeChange(bridgeId) {
      // 可以根据桥梁ID联动加载相关信息
      console.log('选择桥梁:', bridgeId)
    },
    
    // 上报属性变化
    handleReportAttributeChange(attribute) {
      // 根据上报属性调整表单校验规则
      if (attribute === 'superior') {
        // 上级交办需要额外字段
        this.formRules.rectificationDeadline = [
          { required: true, message: '请选择整改期限', trigger: 'change' }
        ]
        this.formRules.assignedLeader = [
          { required: true, message: '请输入交办领导', trigger: 'blur' }
        ]
      } else {
        // 移除上级交办的校验规则
        delete this.formRules.rectificationDeadline
        delete this.formRules.assignedLeader
        
        // 清空相关字段
        this.formData.rectificationDeadline = ''
        this.formData.assignedLeader = ''
      }
    },
    
    // 病害部位变化
    handleDiseasePartChange(part) {
      // 重置病害类型选择
      this.formData.diseaseType = ''
      console.log('选择病害部位:', part)
    },
    
    // 图片上传
    handleImageUpload() {
      this.imageUploadVisible = true
    },
    
    // 图片删除
    handleImageDelete(index) {
      this.formData.diseaseImages.splice(index, 1)
    },
    
    // 图片文件选择变化
    handleImageFileChange(file, fileList) {
      console.log('图片文件选择:', file, fileList)
    },
    
    // 图片上传前检查
    beforeImageUpload(file) {
      const isImage = /\.(jpg|jpeg|png|gif)$/i.test(file.name)
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    // 确认图片上传
    confirmImageUpload() {
      // 模拟上传成功，添加图片
      const mockImages = [
        { url: '/static/images/disease1.jpg', alt: '病害图片1' },
        { url: '/static/images/disease2.jpg', alt: '病害图片2' }
      ]
      
      this.formData.diseaseImages = [
        ...this.formData.diseaseImages,
        ...mockImages
      ]
      
      this.imageUploadVisible = false
      this.$message.success('图片上传成功')
    },
    
    // 附件选择变化
    handleFileChange(file, fileList) {
      this.formData.attachments = fileList
    },
    
    // 附件移除
    handleFileRemove(file, fileList) {
      this.formData.attachments = fileList
    },
    
    // 附件上传前检查
    beforeFileUpload(file) {
      const isValidSize = file.size / 1024 / 1024 < 10
      
      if (!isValidSize) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },
    
    // 保存
    async handleSave() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }
        
        this.saveLoading = true
        
        const saveData = this.formatSubmitData()
        // 调用保存API
        // await saveDiseaseData(saveData)
        
        this.$message.success('保存成功')
        
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败')
      } finally {
        this.saveLoading = false
      }
    },
    
    // 提交
    async handleSubmit() {
      try {
        const isValid = await this.validateForm()
        if (!isValid) {
          return
        }
        
        // 检查必要的附件
        if (!this.checkRequiredData()) {
          return
        }
        
        const result = await this.$confirm(
          '病害信息提交后将进入判定流程，确定要提交吗？',
          '提交确认',
          {
            confirmButtonText: '确定提交',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => false)
        
        if (!result) {
          return
        }
        
        this.submitLoading = true
        
        const submitData = this.formatSubmitData()
        
        // 调用提交API
        await addDisease(submitData)
        
        this.$message.success('提交成功')
        
        // 跳转到病害列表页面
        this.$router.push({ name: 'DiseasesManagement' })
        
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      } finally {
        this.submitLoading = false
      }
    },
    
    // 检查必要数据
    checkRequiredData() {
      if (this.formData.diseaseImages.length === 0) {
        this.$message.warning('建议至少上传一张病害现场图片')
      }
      return true
    },
    
    // 格式化提交数据
    formatSubmitData() {
      const currentTime = new Date().toISOString()
      
      return {
        ...this.formData,
        reporter: this.currentUser,
        reportTime: currentTime,
        diseaseStatus: 'judging',
        diseaseCode: this.generateDiseaseCode()
      }
    },
    
    // 生成病害编号
    generateDiseaseCode() {
      const timestamp = Date.now().toString().slice(-6)
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `D${timestamp}${random}`
    },
    
    // 取消
    handleCancel() {
      this.$router.go(-1)
    },
    
    // 表单验证
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.diseaseForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善必填信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式
@import '@/styles/inspection-theme.scss';

.disease-create {
  
  .page-container {
    .workflow-card,
    .form-card {
      margin-bottom: 20px;
    }
    
    .card-header {
      margin-bottom: 20px;
      
      h3 {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        
        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }
    
    .image-upload-section {
      .upload-tip {
        margin-top: 12px;
        
        p {
          margin: 0;
          font-size: 12px;
          color: #909399;
          line-height: 1.5;
        }
      }
    }
    
    .form-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #e4e7ed;
      margin-top: 20px;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .disease-create {
    .form-actions {
      .el-button {
        width: 80px;
        margin: 4px !important;
      }
    }
  }
}</style>
