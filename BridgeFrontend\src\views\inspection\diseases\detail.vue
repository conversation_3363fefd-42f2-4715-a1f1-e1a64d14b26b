<template>
  <div class="disease-detail">
    <!-- 使用传统的卡片布局结构，保持现有功能 -->
    <div class="page-container">
      <!-- 状态流程卡片 -->
      <el-card class="workflow-card" shadow="never">
        <div class="workflow-header">
          <div class="header-left">
            <i class="el-icon-s-grid workflow-icon"></i>
            <h3>病害处理流程</h3>
          </div>
          <div class="current-handler" v-if="currentHandler">
            <i class="el-icon-user"></i>
            <span>当前处理人：{{ currentHandler }}</span>
            <span v-if="currentHandleTime" class="handle-time">{{ formatTime(currentHandleTime) }}</span>
          </div>
        </div>
        
        <div class="workflow-container">
          <div class="workflow-steps">
            <div 
              v-for="(step, index) in workflowSteps" 
              :key="step.key"
              class="workflow-step"
              :class="{ 
                'active': index === currentWorkflowStep,
                'completed': index < currentWorkflowStep,
                'pending': index > currentWorkflowStep
              }"
            >
              <div class="step-circle">
                <div class="step-icon">
                  <i v-if="index < currentWorkflowStep" class="el-icon-check"></i>
                  <i v-else-if="index === currentWorkflowStep" :class="getStepIcon(step.key)"></i>
                  <span v-else>{{ index + 1 }}</span>
                </div>
              </div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-desc">{{ getStepDescription(step.key) }}</div>
                <div class="step-time" v-if="getStepTime(step.key)">{{ formatTime(getStepTime(step.key)) }}</div>
              </div>
              <div 
                v-if="index < workflowSteps.length - 1" 
                class="step-connector"
                :class="{ 'active': index < currentWorkflowStep }"
              ></div>
            </div>
          </div>
          
          <!-- 状态说明 -->
          <div class="status-legend">
            <div class="legend-item">
              <div class="legend-dot completed"></div>
              <span>已完成</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot active"></div>
              <span>当前阶段</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot pending"></div>
              <span>未开始</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 病害基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-info"></i> 病害基本信息</h3>
        </div>
        
        <div class="info-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">巡检人员</div>
                <div class="info-value">{{ diseaseDetail.inspector || '-' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">巡检单位</div>
                <div class="info-value">{{ diseaseDetail.inspectionUnit || '-' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">上报属性</div>
                <div class="info-value">
                  <StatusTag 
                    v-if="diseaseDetail.reportAttribute"
                    :status="diseaseDetail.reportAttribute"
                    type="inspection"
                  />
                  <span v-else>-</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">所属桥梁/隧道</div>
                <div class="info-value">{{ diseaseDetail.bridgeName || '-' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">病害类型</div>
                <div class="info-value">{{ diseaseDetail.diseaseType || '-' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <div class="info-label">病害数量</div>
                <div class="info-value">{{ diseaseDetail.diseaseCount || 0 }}处</div>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <div class="info-label">病害位置</div>
                <div class="info-value">{{ diseaseDetail.diseaseLocation || '-' }}</div>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <div class="info-label">病害描述</div>
                <div class="info-value description">{{ diseaseDetail.diseaseDescription || '-' }}</div>
              </div>
            </el-col>
          </el-row>
          
          <!-- 病害照片 -->
          <div v-if="diseaseDetail.diseaseImages && diseaseDetail.diseaseImages.length > 0" class="disease-images">
            <h4>病害照片</h4>
            <ImageViewer
              :images="diseaseDetail.diseaseImages"
              :grid-type="4"
              :show-upload="false"
              :show-delete="false"
            />
          </div>
        </div>
      </el-card>

      <!-- 判定信息卡片 -->
      <el-card v-if="showJudgeInfo" class="judge-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-edit"></i> 判定信息</h3>
        </div>
        
        <DiseaseJudgeForm
          :disease-detail="diseaseDetail"
          :readonly="!isJudgeEditable"
          @save="handleSaveJudge"
          @submit="handleSubmitJudge"
          @cancel="handleCancelEdit"
        />
      </el-card>

      <!-- 处置信息卡片 -->
      <el-card v-if="showDisposeInfo" class="dispose-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-setting"></i> 处置信息</h3>
        </div>
        
        <DiseaseDisposeForm
          :disease-detail="diseaseDetail"
          :readonly="!isDisposeEditable"
          @save="handleSaveDispose"
          @submit="handleSubmitDispose"
          @cancel="handleCancelEdit"
        />
      </el-card>

      <!-- 复核信息卡片 -->
      <el-card v-if="showReviewInfo" class="review-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-check"></i> 复核信息</h3>
        </div>
        
        <DiseaseReviewForm
          :disease-detail="diseaseDetail"
          :readonly="!isReviewEditable"
          @save="handleSaveReview"
          @submit="handleSubmitReview"
          @cancel="handleCancelEdit"
        />
      </el-card>

      <!-- 归档信息卡片 -->
      <el-card v-if="showArchiveInfo" class="archive-card" shadow="never">
        <div class="card-header">
          <h3><i class="el-icon-folder"></i> 归档信息</h3>
        </div>
        
        <div class="archive-info">
          <div class="info-item">
            <label>归档时间</label>
            <span>{{ diseaseDetail.archiveTime || '-' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮区 -->
      <div class="action-buttons">
        <el-button @click="handleGoBack">返回</el-button>
        
        <!-- 判定提交按钮（判定中状态显示） -->
        <el-button 
          v-if="showJudgeSubmitButton"
          type="primary" 
          @click="triggerJudgeSubmit"
        >
          判定提交
        </el-button>
        
        <!-- 复核提交按钮（复核中状态显示） -->
        <el-button 
          v-if="showReviewSubmitButton"
          type="primary" 
          @click="triggerReviewSubmit"
        >
          复核提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { StatusTag, ImageViewer } from '@/components/Inspection'
import DiseaseJudgeForm from './components/DiseaseJudgeForm'
import DiseaseDisposeForm from './components/DiseaseDisposeForm'
import DiseaseReviewForm from './components/DiseaseReviewForm'

export default {
  name: 'DiseaseDetail',
  components: {
    StatusTag,
    ImageViewer,
    DiseaseJudgeForm,
    DiseaseDisposeForm,
    DiseaseReviewForm
  },
  data() {
    return {
      loading: false,
      diseaseDetail: {},
      
      // 工作流步骤配置
      workflowSteps: [
        { key: 'report', title: '上报' },
        { key: 'judge', title: '判定' },
        { key: 'dispose', title: '处置' },
        { key: 'review', title: '复核' },
        { key: 'archive', title: '归档' }
      ],
      
      // 编辑状态
      isEditingJudge: false,
      isEditingDispose: false,
      isEditingReview: false
    }
  },
  computed: {
    ...mapGetters('inspection', ['currentDiseaseDetail']),
    
    diseaseId() {
      return this.$route.params.id
    },
    
    currentAction() {
      return this.$route.query.action || 'view'
    },
    
    // 从路由参数获取病害数据
    routeDiseaseData() {
      return this.$route.params.diseaseData || null
    },
    
    // 当前工作流步骤
    currentWorkflowStep() {
      const statusMap = {
        'judging': 1,
        'planning': 2,
        'disposing': 2,
        'reviewing': 3,
        'archived': 4
      }
      return statusMap[this.diseaseDetail.diseaseStatus] || 0
    },
    
    // 当前处理人信息
    currentHandler() {
      const status = this.diseaseDetail.diseaseStatus
      if (status === 'judging') {
        return this.diseaseDetail.currentJudger || ''
      } else if (status === 'disposing') {
        return this.diseaseDetail.currentProcessor || ''
      } else if (status === 'reviewing') {
        return this.diseaseDetail.currentReviewer || ''
      }
      return ''
    },
    
    // 当前处理时间
    currentHandleTime() {
      const status = this.diseaseDetail.diseaseStatus
      if (status === 'judging') {
        return this.diseaseDetail.judgeStartTime || ''
      } else if (status === 'disposing') {
        return this.diseaseDetail.disposeStartTime || ''
      } else if (status === 'reviewing') {
        return this.diseaseDetail.reviewStartTime || ''
      }
      return ''
    },
    
    // 是否显示判定信息
    showJudgeInfo() {
      return ['judging', 'planning', 'disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)
    },
    
    // 是否显示处置信息
    showDisposeInfo() {
      return ['disposing', 'reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)
    },
    
    // 是否显示复核信息
    showReviewInfo() {
      return ['reviewing', 'archived'].includes(this.diseaseDetail.diseaseStatus)
    },
    
    // 是否显示归档信息
    showArchiveInfo() {
      return this.diseaseDetail.diseaseStatus === 'archived'
    },
    
    // 是否显示提交按钮
    showSubmitButton() {
      return this.diseaseDetail.diseaseStatus === 'judging'
    },
    
    // 判定区域是否可编辑（只有判定中状态可编辑）
    isJudgeEditable() {
      return this.diseaseDetail.diseaseStatus === 'judging'
    },
    
    // 处置区域是否可编辑（只有处置中状态可编辑）
    isDisposeEditable() {
      return ['planning', 'disposing'].includes(this.diseaseDetail.diseaseStatus)
    },
    
    // 复核区域是否可编辑（只有复核中状态可编辑）
    isReviewEditable() {
      return this.diseaseDetail.diseaseStatus === 'reviewing'
    },
    
    // 是否显示判定提交按钮
    showJudgeSubmitButton() {
      return this.diseaseDetail.diseaseStatus === 'judging'
    },
    
    // 是否显示复核提交按钮
    showReviewSubmitButton() {
      return this.diseaseDetail.diseaseStatus === 'reviewing'
    },
    
  },
  async created() {
    await this.initPageData()
  },
  methods: {
    ...mapActions('inspection', ['fetchDiseaseDetail']),
    
    // 初始化页面数据
    async initPageData() {
      this.loading = true
      
      try {
        // 优先使用路由传递的数据，如果没有则从API获取
        if (this.routeDiseaseData) {
          console.log('使用路由传递的病害数据:', this.routeDiseaseData)
          this.diseaseDetail = { ...this.routeDiseaseData }
        } else {
          await this.fetchDiseaseDetail(this.diseaseId)
          this.diseaseDetail = this.currentDiseaseDetail || this.getDefaultDiseaseDetail()
        }
        
        // 根据action设置编辑状态
        this.setEditingState()
        
        // 初始化编辑状态 - 确保在对应阶段默认可编辑
        this.initEditingState()
        
      } catch (error) {
        console.error('加载病害详情失败:', error)
        this.$message.error('加载病害详情失败')
        
        // 使用默认数据
        this.diseaseDetail = this.getDefaultDiseaseDetail()
      } finally {
        this.loading = false
      }
    },
    
    // 设置编辑状态
    setEditingState() {
      this.isEditingJudge = this.currentAction === 'judge'
      this.isEditingDispose = this.currentAction === 'dispose'
      this.isEditingReview = this.currentAction === 'review'
    },
    
    // 初始化编辑状态
    initEditingState() {
      const status = this.diseaseDetail.diseaseStatus
      
      // 如果没有指定action，则根据状态默认设置编辑状态
      if (!this.currentAction || this.currentAction === 'view') {
        if (status === 'judging') {
          this.isEditingJudge = true
        } else if (status === 'planning' || status === 'disposing') {
          this.isEditingDispose = true
        } else if (status === 'reviewing') {
          this.isEditingReview = true
        }
      }
    },
    
    // 获取默认病害详情数据
    getDefaultDiseaseDetail() {
      return {
        id: this.diseaseId,
        bridgeName: '橘子洲大桥',
        inspector: '张三',
        inspectionUnit: '长沙市政养护单位',
        reportAttribute: 'daily',
        diseaseType: '裂缝',
        diseaseCount: 3,
        diseaseLocation: '承载桁+200m，距止测所约53米，共3处桥面的裂缝',
        diseaseDescription: '桥面板出现3处的裂缝，最大宽度30.3mm，长度1.2m，可能影响结构耐久性，建议及时处理。',
        diseaseStatus: 'judging', // 默认为判定中状态，这样可以测试编辑功能
        reportTime: '2024-01-08 09:30:00',
        // 添加判定信息示例数据
        judgeType: 'daily_maintenance',
        judgeComment: '经判定，该病害属于日常养护范围，建议在一周内完成修复工作。裂缝位置不影响结构安全，但需及时处理防止进一步扩大。',
        requiredFinishTime: '2024-01-15 18:00:00',
        judger: '李工程师',
        judgeTime: '2024-01-08 14:30:00',
        diseaseImages: [
          { url: require('@/assets/images/test/disease1.png'), alt: '病害图片1' },
          { url: require('@/assets/images/test/disease2.png'), alt: '病害图片2' },
          { url: require('@/assets/images/test/disease3.png'), alt: '病害图片3' },
          { url: require('@/assets/images/test/disease4.png'), alt: '病害图片4' }
        ]
      }
    },
    
    // 保存判定信息
    async handleSaveJudge(judgeData) {
      try {
        // 这里调用保存判定API
        // await saveDiseaseJudge(this.diseaseId, judgeData)
        
        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存判定信息失败:', error)
        this.$message.error('保存失败')
      }
    },
    
    // 提交判定信息
    async handleSubmitJudge(judgeData) {
      try {
        // 这里调用提交判定API
        // await submitDiseaseJudge(this.diseaseId, judgeData)
        
        this.$message.success('判定信息提交成功')
        // 提交成功后刷新页面数据或跳转
        await this.initPageData()
      } catch (error) {
        console.error('提交判定信息失败:', error)
        this.$message.error('提交失败')
      }
    },
    
    
    // 保存处置信息
    async handleSaveDispose(disposeData) {
      try {
        // 这里调用保存处置API
        // await saveDiseaseDispose(this.diseaseId, disposeData)
        
        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存处置信息失败:', error)
        this.$message.error('保存失败')
      }
    },
    
    
    // 保存复核信息
    async handleSaveReview(reviewData) {
      try {
        // 这里调用保存复核API
        // await saveDiseaseReview(this.diseaseId, reviewData)
        
        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存复核信息失败:', error)
        this.$message.error('保存失败')
      }
    },
    
    // 提交复核信息
    async handleSubmitReview(reviewData) {
      try {
        // 这里调用提交复核API
        // await submitDiseaseReview(this.diseaseId, reviewData)
        
        this.$message.success('复核信息提交成功')
        // 提交成功后刷新页面数据或跳转
        await this.initPageData()
      } catch (error) {
        console.error('提交复核信息失败:', error)
        this.$message.error('提交失败')
      }
    },
    
    
    // 取消编辑
    handleCancelEdit() {
      this.isEditingJudge = false
      this.isEditingDispose = false
      this.isEditingReview = false
      
      // 移除action参数
      this.$router.replace({
        name: 'DiseaseDetail',
        params: { id: this.diseaseId }
      })
    },
    
    
    // 返回列表
    handleGoBack() {
      this.$router.go(-1)
    },
    
    // 触发判定提交（通过$refs调用子组件方法）
    triggerJudgeSubmit() {
      // 这里可以调用判定表单组件的提交方法，或者触发提交事件
      // 如果判定表单组件有ref，可以直接调用: this.$refs.judgeForm.submit()
      // 目前模拟表单数据并调用提交方法
      const mockJudgeData = {
        judgeType: 'daily_maintenance',
        judgeComment: '测试判定信息',
        requiredFinishTime: '2024-01-15 18:00:00'
      }
      this.handleSubmitJudge(mockJudgeData)
    },
    
    // 触发复核提交（通过$refs调用子组件方法）
    triggerReviewSubmit() {
      // 这里可以调用复核表单组件的提交方法，或者触发提交事件
      // 如果复核表单组件有ref，可以直接调用: this.$refs.reviewForm.submit()
      // 目前模拟表单数据并调用提交方法
      const mockReviewData = {
        reviewResult: 'approved',
        reviewComment: '测试复核信息'
      }
      this.handleSubmitReview(mockReviewData)
    },
    
    // 获取步骤图标
    getStepIcon(stepKey) {
      const iconMap = {
        'report': 'el-icon-edit',
        'judge': 'el-icon-s-claim',
        'dispose': 'el-icon-s-tools',
        'review': 'el-icon-view',
        'archive': 'el-icon-folder'
      }
      return iconMap[stepKey] || 'el-icon-circle-check'
    },
    
    // 获取步骤描述
    getStepDescription(stepKey) {
      const descMap = {
        'report': '病害信息已上报',
        'judge': '专家进行病害判定',
        'dispose': '制定处置方案并执行',
        'review': '验收处置效果',
        'archive': '病害处理完成归档'
      }
      return descMap[stepKey] || ''
    },
    
    // 获取步骤完成时间
    getStepTime(stepKey) {
      const timeMap = {
        'report': this.diseaseDetail.reportTime,
        'judge': this.diseaseDetail.judgeTime,
        'dispose': this.diseaseDetail.disposeTime,
        'review': this.diseaseDetail.reviewTime,
        'archive': this.diseaseDetail.archiveTime
      }
      return timeMap[stepKey] || ''
    },
    
    // 格式化时间显示
    formatTime(time) {
      if (!time) return ''
      
      // 如果是日期字符串，转换为更友好的格式
      const date = new Date(time)
      if (isNaN(date.getTime())) return time
      
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (days === 0) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (days === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式和通用混入
@import '@/styles/inspection-theme.scss';
@import '@/styles/mixins/inspection-common.scss';

.disease-detail {
  @include inspection-page-container;
  background: var(--inspection-bg-primary);
  min-height: 100%; // 适应父容器高度
  overflow-y: visible;
  
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    padding-bottom: 50px; // 确保底部内容不被遮挡
    
    .workflow-card,
    .info-card,
    .judge-card,
    .dispose-card,
    .review-card,
    .archive-card {
      margin-bottom: 20px;
      background: linear-gradient(135deg, rgba(9, 26, 75, 0.95) 0%, rgba(30, 58, 138, 0.9) 100%);
      border: 1px solid rgba(92, 157, 255, 0.3);
      border-radius: 16px;
      color: #f1f5f9;
      box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3);
      
      &:hover {
        box-shadow: 0 0 0 1px rgba(92, 157, 255, 0.3), 0 8px 25px rgba(92, 157, 255, 0.2);
        transform: translateY(-2px);
      }
    }
    
    // 状态流程卡片样式 - 使用深色主题
    .workflow-card {
      background: linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important;
      border: 1px solid rgba(79, 70, 229, 0.3) !important;
      border-radius: 16px !important;
      box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
      backdrop-filter: blur(10px) !important;
      overflow: hidden;
      color: #f1f5f9 !important;
      
      :deep(.el-card__body) {
        padding: 24px !important;
      }
      
        .workflow-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 32px;
          padding-bottom: 16px;
          border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        
        .header-left {
          display: flex;
          align-items: center;
          
          .workflow-icon {
            font-size: 20px;
            color: #5C9DFF;
            margin-right: 12px;
            padding: 8px;
            background: rgba(92, 157, 255, 0.2);
            border-radius: 8px;
          }
          
          h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #f1f5f9;
            letter-spacing: -0.025em;
          }
        }
        
        .current-handler {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          background: linear-gradient(135deg, rgba(92, 157, 255, 0.15), rgba(116, 167, 245, 0.1));
          border-radius: 12px;
          font-size: 14px;
          color: #e2e8f0;
          border: 1px solid rgba(92, 157, 255, 0.3);
          
          i {
            margin-right: 6px;
            color: #5C9DFF;
          }
          
          .handle-time {
            margin-left: 8px;
            padding: 2px 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 12px;
            color: #cbd5e1;
          }
        }
      }
      
      .workflow-container {
        .workflow-steps {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: flex-start;
          gap: 20px;
          position: relative;
          
          // 横向连接线
          &::before {
            content: '';
            position: absolute;
            top: 24px;
            left: 60px;
            right: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            z-index: 1;
          }
          
          .workflow-step {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 2;
            
            .step-circle {
              margin-bottom: 12px;
              
              .step-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                background: white;
                
                i {
                  font-size: 18px;
                }
                
                span {
                  font-size: 16px;
                  font-weight: 700;
                }
              }
            }
            
            .step-content {
              text-align: center;
              
              .step-title {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 4px;
                letter-spacing: -0.025em;
              }
              
              .step-desc {
                font-size: 12px;
                color: #64748B;
                line-height: 1.4;
                margin-bottom: 6px;
              }
              
              .step-time {
                font-size: 11px;
                color: #94A3B8;
                font-weight: 500;
                padding: 2px 6px;
                background: rgba(148, 163, 184, 0.1);
                border-radius: 4px;
                display: inline-block;
              }
            }
            
            // 未开始状态 - 灰色
            &.pending {
              .step-circle .step-icon {
                background: rgba(148, 163, 184, 0.2);
                color: #94A3B8;
                border: 2px solid rgba(148, 163, 184, 0.3);
              }
              
              .step-content {
                .step-title {
                  color: #94A3B8;
                }
                
                .step-desc {
                  color: #64748B;
                }
              }
            }
            
            // 当前阶段 - 蓝色（项目主题色）
            &.active {
              .step-circle .step-icon {
                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);
                color: white;
                border: 3px solid rgba(92, 157, 255, 0.3);
                box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);
                transform: scale(1.05);
                animation: pulse 2s infinite;
              }
              
              .step-content {
                .step-title {
                  color: #f1f5f9;
                  font-weight: 700;
                }
                
                .step-desc {
                  color: #e2e8f0;
                }
                
                .step-time {
                  background: rgba(92, 157, 255, 0.2);
                  color: #5C9DFF;
                }
              }
            }
            
            // 已完成状态 - 绿色
            &.completed {
              .step-circle .step-icon {
                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);
                color: white;
                border: 2px solid rgba(48, 176, 143, 0.3);
                box-shadow: 0 4px 12px rgba(48, 176, 143, 0.2);
                
                i {
                  font-size: 20px;
                  font-weight: bold;
                }
              }
              
              .step-content {
                .step-title {
                  color: #f1f5f9;
                  font-weight: 600;
                }
                
                .step-desc {
                  color: #e2e8f0;
                }
                
                .step-time {
                  background: rgba(48, 176, 143, 0.2);
                  color: #30B08F;
                }
              }
            }
          }
        }
        
        .status-legend {
          margin-top: 32px;
          padding-top: 20px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          justify-content: center;
          gap: 32px;
          
          .legend-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #cbd5e1;
            font-weight: 500;
            
            .legend-dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              margin-right: 8px;
              
              &.completed {
                background: linear-gradient(135deg, #30B08F 0%, #10b981 100%);
              }
              
              &.active {
                background: linear-gradient(135deg, #5C9DFF 0%, #74a7f5 100%);
                animation: pulse-dot 2s infinite;
              }
              
              &.pending {
                background: rgba(148, 163, 184, 0.3);
                border: 2px solid rgba(148, 163, 184, 0.2);
              }
            }
          }
        }
      }
    }
    
    .card-header {
      margin-bottom: 20px;
      
      h3 {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #f1f5f9;
        
        i {
          margin-right: 8px;
          color: #5C9DFF;
          padding: 6px;
          background: rgba(92, 157, 255, 0.2);
          border-radius: 6px;
        }
      }
    }
    
    .info-section {
      .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        border-left: 3px solid #5C9DFF;
        
        .info-label {
          width: 120px;
          font-weight: 600;
          color: #ffffff;
          flex-shrink: 0;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .info-value {
          color: #e2e8f0;
          word-break: break-all;
          font-size: 14px;
          line-height: 1.5;
          
          &.description {
            line-height: 1.6;
            color: #e2e8f0;
          }
        }
        
        // 兼容旧的label和span结构
        label {
          width: 120px;
          font-weight: 600;
          color: #ffffff;
          flex-shrink: 0;
          font-size: 14px;
          line-height: 1.5;
        }
        
        span {
          color: #e2e8f0;
          word-break: break-all;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .description {
          margin: 0;
          line-height: 1.6;
          color: #e2e8f0;
        }
      }
      
      .disease-images {
        margin-top: 24px;
        
        h4 {
          margin: 0 0 16px 0;
          font-size: 14px;
          font-weight: 500;
          color: #f1f5f9;
        }
      }
    }
    
    .archive-info {
      .info-item {
        display: flex;
        align-items: center;
        
        label {
          width: 120px;
          font-weight: 500;
          color: #cbd5e1;
        }
        
        span {
          color: #f1f5f9;
        }
      }
    }
    
    .action-buttons {
      text-align: center;
      padding: 20px 0;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }
}

// 动画定义
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(92, 157, 255, 0.2), 0 8px 20px rgba(92, 157, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(92, 157, 255, 0.15), 0 8px 20px rgba(92, 157, 255, 0.25);
  }
}

@keyframes pulse-dot {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 深色主题统一样式 - 与检查模块整体风格保持一致

// 响应式设计
@media (max-width: 768px) {
  .disease-detail {
    .page-container {
      padding: 16px;
      
      .workflow-card {
        .workflow-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          
          .header-left h3 {
            font-size: 18px;
          }
          
          .current-handler {
            align-self: stretch;
          }
        }
        
        .workflow-container {
          .workflow-steps {
            .workflow-step {
              .step-circle .step-icon {
                width: 40px;
                height: 40px;
                
                i {
                  font-size: 16px;
                }
                
                span {
                  font-size: 14px;
                }
              }
              
              .step-content {
                .step-title {
                  font-size: 14px;
                }
                
                .step-desc {
                  font-size: 13px;
                }
              }
              
              .step-connector {
                left: 19px;
              }
            }
          }
          
          .status-legend {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
            
            .legend-item {
              font-size: 12px;
            }
          }
        }
      }
      
      .card-header h3 {
        font-size: 16px;
        
        i {
          font-size: 18px;
          padding: 6px;
        }
      }
      
      .info-section {
        .info-item {
          flex-direction: column;
          align-items: flex-start;
          padding: 12px;
          
          .info-label,
          label {
            width: auto;
            margin-bottom: 8px;
            
            &::after {
              display: none;
            }
          }
        }
      }
      
      .action-buttons {
        padding: 24px 0;
        
        .el-button {
          width: 120px;
          margin: 6px 4px;
          padding: 10px 16px;
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .disease-detail {
    .page-container {
      padding: 12px;
      
      .workflow-container {
        .status-legend {
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          gap: 20px;
        }
      }
      
      .action-buttons {
        .el-button {
          width: 100%;
          margin: 6px 0;
        }
      }
    }
  }
}</style>
