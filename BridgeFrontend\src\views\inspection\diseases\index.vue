<template>
  <div class="disease-management inspection-container">
    <div class="page-container">
      <!-- TAB切换 -->
      <TabSwitch
        v-model="activeTab" 
        :tabs="tabOptions"
        @tab-click="handleTabClick"
      />

      <!-- 筛选条件 -->
      <FilterSection
        v-model="searchForm"
        :configs="filterConfigs"
        :options="selectOptions"
        :has-batch-selection="selectedRows.length > 0"
        @search="handleSearch"
        @reset="handleReset"
        @add="handleAddDisease"
        @batch="handleBatchReview"
      >
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.bridgeName"
:placeholder="bridgeNameText"
                    clearable
            filterable
            class="filter-select"
          >
            <el-option
              v-for="option in options.bridgeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

                  <el-select
            v-model="formData.reportAttribute"
            placeholder="上报属性"
                    clearable
            class="filter-select"
                  >
                    <el-option
              v-for="option in options.reportAttributeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>

                  <el-select
            v-model="formData.diseaseType"
            placeholder="病害类型"
                    clearable
            class="filter-select"
                  >
                    <el-option
              v-for="option in options.diseaseTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>

                  <!-- 自定义日期范围选择器 -->
                  <div class="custom-date-range-selector filter-select">
                    <div 
                      class="date-range-display"
                      :class="{ 'is-focused': isDatePickerFocused }"
                      @click="toggleDatePicker"
                      @blur="handleDatePickerBlur"
                      @keydown.enter="toggleDatePicker"
                      @keydown.space.prevent="toggleDatePicker"
                      tabindex="0"
                    >
                      <span class="date-range-text">
                        {{ dateRangeDisplayText }}
                      </span>
                      <span class="el-input__suffix">
                        <!-- 清空按钮 - 当有值时显示，替换下拉箭头 -->
                        <i 
                          v-if="searchForm.reportTimeRange && searchForm.reportTimeRange.length === 2"
                          class="el-icon-circle-close el-input__icon clear-icon"
                          @click.stop="clearDateRange"
                        ></i>
                        <!-- 下拉箭头 - 当没有值时显示 -->
                        <i 
                          v-else
                          class="el-icon-arrow-down el-input__icon dropdown-icon"
                        ></i>
                      </span>
                    </div>
                    
                    <!-- 隐藏的日期选择器 -->
                    <el-date-picker
                      ref="hiddenDatePicker"
                      v-model="searchForm.reportTimeRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="position: absolute; opacity: 0; pointer-events: none; z-index: -1;"
                      @change="handleDateRangeChange"
                    />
                  </div>
        </template>
            
      </FilterSection>

      <!-- 操作按钮区域 -->
      <div class="operation-buttons">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAddDisease"
        >
          新增
        </el-button>
        <el-button 
          type="success" 
          icon="el-icon-check" 
          :disabled="selectedRows.length === 0"
          @click="handleBatchReview"
        >
          批量验收
        </el-button>
      </div>

      <!-- 数据表格 -->
      <div class="common-table">
        <el-table
          ref="diseaseTable"
          v-loading="loading"
          :data="tableData"
          :key="`${activeTab}-${tableData.length}`"
          style="width: 100%"
          @sort-change="handleSortChange"
          @selection-change="handleSelectionChange"
          row-key="id"
          :row-style="{ height: '32px' }"
          size="small"
        >
          <!-- 多选列 -->
          <el-table-column
            type="selection"
            width="55"
            align="center"
            :selectable="isRowSelectable"
          />

          <!-- 序号列 -->
          <el-table-column width="80" align="center">
            <template slot="header">
              <span class="table-header-wrap">序号</span>
            </template>
            <template slot-scope="scope">
              <span>{{ String(scope.$index + 1).padStart(3, '0') }}</span>
            </template>
          </el-table-column>

          <!-- 桥梁名称 -->
          <el-table-column
            prop="bridgeName"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot="header">
              <span class="table-header-wrap">{{ bridgeNameText }}</span>
            </template>
          </el-table-column>

          <!-- 上报属性 -->
          <el-table-column
            prop="reportAttribute"
            width="100"
            align="center"
          >
            <template slot="header">
              <span class="table-header-wrap">上报属性</span>
            </template>
            <template slot-scope="scope">
              {{ getReportAttributeText(scope.row.reportAttribute) }}
            </template>
          </el-table-column>

          <!-- 病害编号 -->
          <el-table-column
            prop="diseaseCode"
            width="150"
            align="center"
            show-overflow-tooltip
          >
            <template slot="header">
              <span class="table-header-wrap">病害编号</span>
            </template>
          </el-table-column>

          <!-- 病害部位 -->
          <el-table-column
            prop="diseasePart"
            width="110"
            align="center"
          >
            <template slot="header">
              <span class="table-header-wrap">病害部位</span>
            </template>
          </el-table-column>

          <!-- 病害类型 -->
          <el-table-column
            prop="diseaseType"
            width="130"
            align="center"
          >
            <template slot="header">
              <span class="table-header-wrap">病害类型</span>
            </template>
          </el-table-column>

          <!-- 病害状态 -->
          <el-table-column
            prop="diseaseStatus"
            width="100"
            align="center"
            sortable="custom"
          >
            <template slot="header">
              <span class="table-header-wrap">病害状态</span>
            </template>
            <template slot-scope="scope">
              {{ getDiseaseStatusText(scope.row.diseaseStatus) }}
            </template>
          </el-table-column>

          <!-- 病害位置 -->
          <el-table-column
            prop="diseaseLocation"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot="header">
              <span class="table-header-wrap">病害位置</span>
            </template>
          </el-table-column>

          <!-- 病害数量 -->
          <el-table-column
            prop="diseaseCount"
            width="80"
            align="center"
            sortable="custom"
          >
            <template slot="header">
              <span class="table-header-wrap">病害数量</span>
            </template>
            <template slot-scope="scope">
              {{ scope.row.diseaseCount }}
            </template>
          </el-table-column>

          <!-- 病害描述 -->
          <el-table-column
            prop="diseaseDescription"
            min-width="180"
            show-overflow-tooltip
          >
            <template slot="header">
              <span class="table-header-wrap">病害描述</span>
            </template>
          </el-table-column>

          <!-- 上报人 -->
          <el-table-column
            prop="reporter"
            width="90"
            align="center"
          >
            <template slot="header">
              <span class="table-header-wrap">上报人</span>
            </template>
          </el-table-column>

          <!-- 上报时间 -->
          <el-table-column
            prop="reportTime"
            width="180"
            align="center"
            sortable="custom"
          >
            <template slot="header">
              <span class="table-header-wrap">上报时间</span>
            </template>
          </el-table-column>

          <!-- 所属单位 -->
          <el-table-column
            prop="unit"
            min-width="140"
            show-overflow-tooltip
          >
            <template slot="header">
              <span class="table-header-wrap">所属单位</span>
            </template>
          </el-table-column>

          <!-- 联系方式 -->
          <el-table-column
            prop="contactNumber"
            width="130"
            align="center"
          >
            <template slot="header">
              <span class="table-header-wrap">联系方式</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="small" 
                :loading="isButtonLoading('view', scope.row.id)"
                @click="handleActionClick({ action: 'view', record: scope.row })"
              >
                查看
              </el-button>
              <el-button 
                v-if="canShowAction('judge', scope.row)" 
                type="text" 
                size="small" 
                :loading="isButtonLoading('judge', scope.row.id)"
                @click="handleActionClick({ action: 'judge', record: scope.row })"
              >
                判定
              </el-button>
              <el-button 
                v-if="canShowAction('review', scope.row)" 
                type="text" 
                size="small" 
                :loading="isButtonLoading('review', scope.row.id)"
                @click="handleActionClick({ action: 'review', record: scope.row })"
              >
                复核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper inspection-pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>

    <!-- 批量复核弹窗 -->
    <BatchReviewDialog
      :visible.sync="batchReviewVisible"
      :selected-rows="selectedRows"
      @submit="handleBatchReviewSubmit"
    />
    
    <!-- 新增病害弹窗 -->
    <DiseaseCreateDialog
      :visible.sync="createDialogVisible"
      @success="handleCreateSuccess"
      @close="handleCreateClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { TabSwitch, FilterSection, StatusTag } from '@/components/Inspection'
import BatchReviewDialog from './components/BatchReviewDialog'
import DiseaseCreateDialog from './components/DiseaseCreateDialog'

export default {
  name: 'DiseaseManagement',
  components: {
    TabSwitch,
    FilterSection,
    StatusTag,
    BatchReviewDialog,
    DiseaseCreateDialog
  },
  data() {
    return {
      // 当前激活的tab
      activeTab: 'bridge',
      
      // 搜索表单
      searchForm: {
        bridgeName: '',
        reportAttribute: '',
        diseaseType: '',
        reportTimeRange: []
      },
      
      // 选中的行数据
      selectedRows: [],
      
      // 批量复核弹窗状态
      batchReviewVisible: false,
      
      // 新增病害弹窗状态
      createDialogVisible: false,
      
      // 操作按钮加载状态
      loadingButtons: [],
      
      // 用户权限（实际应该从用户状态获取）
      userPermissions: [
        'inspection:disease:view',
        'inspection:disease:judge',
        'inspection:disease:review',
        'inspection:disease:edit',
        'inspection:disease:delete'
      ],
      
      // 日期选择器焦点状态
      isDatePickerFocused: false,
      
      // 排序参数
      sortParams: {
        prop: '',
        order: ''
      },

      // TAB配置
      tabOptions: [
        {
          name: 'bridge',
          label: '桥梁巡检',
          icon: 'bridge'
        },
        {
          name: 'tunnel',
          label: '隧道巡检',
          icon: 'tunnel'
        }
      ],

      // 筛选配置
      filterConfigs: {
      },

    }
  },
  computed: {
    ...mapGetters('inspection', [
      'diseaseList',
      'diseasePagination',
      'selectOptions',
      'loadingStates'
    ]),
    
    tableData() {
      return this.diseaseList || []
    },
    
    pagination() {
      return this.diseasePagination || {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    },
    
    loading() {
      return this.loadingStates.diseaseList
    },

    // 日期范围显示文本
    dateRangeDisplayText() {
      if (this.searchForm.reportTimeRange && this.searchForm.reportTimeRange.length === 2) {
        return `${this.searchForm.reportTimeRange[0]} 至 ${this.searchForm.reportTimeRange[1]}`
      }
      return '上报日期'
    },

    // 动态的桥梁/隧道名称文本
    bridgeNameText() {
      return this.activeTab === 'tunnel' ? '隧道名称' : '桥梁名称'
    }
  },
  
  watch: {
    // 监听表格数据变化
    'diseaseList'() {
      // 数据变化后的处理逻辑
    }
  },
  
  async created() {
    await this.initPageData()
  },
  
  mounted() {
    // 组件挂载完成
  },
  
  beforeDestroy() {
    // 清理相关资源
  },
  
  methods: {
    ...mapActions('inspection', [
      'fetchDiseaseList',
      'updateFilters',
      'updatePagination',
      'initSelectOptions'
    ]),
    
    
    
    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func.apply(this, args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },
    




    // 初始化页面数据
    async initPageData() {
      // 初始化下拉选项
      await this.initSelectOptions()
      
      // 更新筛选条件
      this.updateFilters({
        inspectionType: this.activeTab
      })
      
      // 获取病害列表
      await this.fetchDiseaseList()
    },
    
    // TAB切换
    async handleTabClick(tab) {
      this.activeTab = tab.name
      
      this.updateFilters({ inspectionType: this.activeTab })
      
      // 清空选中状态
      this.selectedRows = []
      
      await this.fetchDiseaseList()
    },
    
    // 搜索
    async handleSearch(formData) {
      // 搜索前验证
      if (!this.validateSearchForm(formData)) {
        return
      }
      
      const filters = {
        bridgeName: formData.bridgeName,
        reportAttribute: formData.reportAttribute,
        diseaseType: formData.diseaseType
      }
      
      // 处理时间范围
      if (formData.reportTimeRange && formData.reportTimeRange.length === 2) {
        // 验证日期范围的合理性
        const startDate = new Date(formData.reportTimeRange[0])
        const endDate = new Date(formData.reportTimeRange[1])
        
        if (startDate > endDate) {
          this.$message.warning('开始日期不能晚于结束日期')
          return
        }
        
        // 检查日期范围是否过大（比如超过1年）
        const daysDiff = (endDate - startDate) / (1000 * 60 * 60 * 24)
        if (daysDiff > 365) {
          this.$message.warning('日期范围不能超过一年')
          return
        }
        
        filters.reportStartTime = formData.reportTimeRange[0]
        filters.reportEndTime = formData.reportTimeRange[1]
      }
      
      this.updateFilters(filters)
      
      // 重置分页
      this.updatePagination({
        type: 'disease',
        pageNum: 1,
        pageSize: this.pagination.pageSize
      })
      
      await this.fetchDiseaseList()
    },

    // 验证搜索表单
    validateSearchForm(formData) {
      // 检查是否至少有一个搜索条件
      const hasCondition = formData.bridgeName || 
                          formData.reportAttribute || 
                          formData.diseaseType || 
                          (formData.reportTimeRange && formData.reportTimeRange.length === 2)
      
      if (!hasCondition) {
        this.$message.warning('请至少选择一个搜索条件')
        return false
      }
      
      return true
    },
    
    // 重置搜索
    async handleReset(resetData) {
      // 重置搜索表单，确保所有字段都被清空
      this.searchForm = {
        bridgeName: '',
        reportAttribute: '',
        diseaseType: '',
        reportTimeRange: []
      }
      
      this.updateFilters({
        bridgeName: '',
        reportAttribute: '',
        diseaseType: '',
        reportStartTime: '',
        reportEndTime: ''
      })
      
      // 重置分页
      this.updatePagination({
        type: 'disease',
        pageNum: 1,
        pageSize: 10
      })
      
      await this.fetchDiseaseList()
    },
    
    // 新增病害
    handleAddDisease() {
      this.createDialogVisible = true
    },
    
    // 批量复核
    handleBatchReview() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要复核的病害记录')
        return
      }
      
      // 检查选中记录是否都是复核中状态
      const reviewableRows = this.selectedRows.filter(row => row.diseaseStatus === 'reviewing')
      if (reviewableRows.length === 0) {
        this.$message.warning('选中的记录中没有可复核的病害（状态为复核中）')
        return
      }
      
      if (reviewableRows.length < this.selectedRows.length) {
        this.$message.warning(`共选中${this.selectedRows.length}条记录，其中${reviewableRows.length}条可复核`)
        this.selectedRows = reviewableRows
      }
      
      this.batchReviewVisible = true
    },
    
    // 批量复核提交
    async handleBatchReviewSubmit(reviewData) {
      try {
        const diseaseIds = this.selectedRows.map(row => row.id)
        
        // 这里调用批量复核API
        // await batchReviewDiseases({ diseaseIds, ...reviewData })
        
        this.$message.success('批量复核成功')
        this.batchReviewVisible = false
        this.selectedRows = []
        
        // 刷新列表
        await this.fetchDiseaseList()
      } catch (error) {
        console.error('批量复核失败:', error)
        this.$message.error('批量复核失败')
      }
    },
    
    // 新增病害成功
    async handleCreateSuccess(data) {
      this.$message.success('新增病害成功')
      this.createDialogVisible = false
      
      // 刷新列表
      await this.fetchDiseaseList()
    },
    
    // 新增病害弹框关闭
    handleCreateClose() {
      this.createDialogVisible = false
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 检查行是否可选择
    isRowSelectable(row) {
      // 只有复核中的记录可以被选择用于批量复核
      return row.diseaseStatus === 'reviewing'
    },

    // 获取上报属性文字
    getReportAttributeText(value) {
      const textMap = {
        'daily': '日常巡检',
        'center': '中心巡检',
        'regular': '定检',
        'superior': '上级交办'
      }
      return textMap[value] || value || '-'
    },

    // 获取病害状态文字
    getDiseaseStatusText(value) {
      const textMap = {
        'judging': '判定中',
        'planning': '计划中', 
        'disposing': '处置中',
        'reviewing': '复核中',
        'archived': '已归档'
      }
      return textMap[value] || value || '-'
    },

    // 判断是否显示操作按钮
    canShowAction(action, row) {
      const status = row.diseaseStatus
      
      switch (action) {
        case 'view':
          return true // 所有状态都可以查看
        case 'judge':
          return status === 'judging' && this.hasPermission('inspection:disease:judge')
        case 'review':
          return status === 'reviewing' && this.hasPermission('inspection:disease:review')
        default:
          return false
      }
    },

    // 检查用户权限
    hasPermission(permission) {
      return this.userPermissions.includes(permission)
    },

    // 切换日期选择器显示
    toggleDatePicker() {
      this.isDatePickerFocused = true
      this.$nextTick(() => {
        this.$refs.hiddenDatePicker.focus()
      })
    },

    // 处理日期选择器失焦
    handleDatePickerBlur() {
      // 延迟执行，确保点击操作能正常完成
      setTimeout(() => {
        this.isDatePickerFocused = false
      }, 200)
    },

    // 处理日期范围变化
    handleDateRangeChange(value) {
      this.searchForm.reportTimeRange = value
      // 日期选择完成后移除焦点状态
      this.isDatePickerFocused = false
    },

    // 清空日期范围
    clearDateRange() {
      this.searchForm.reportTimeRange = []
    },
    
    // 排序变化
    async handleSortChange({ prop, order }) {
      this.sortParams = { prop, order }
      await this.fetchDiseaseList({ sortBy: prop, sortOrder: order })
    },
    
    // 分页大小变化
    async handleSizeChange(size) {
      this.updatePagination({
        type: 'disease',
        pageNum: 1,
        pageSize: size
      })
      await this.fetchDiseaseList()
    },
    
    // 页码变化
    async handleCurrentChange(page) {
      this.updatePagination({
        type: 'disease',
        pageNum: page,
        pageSize: this.pagination.pageSize
      })
      await this.fetchDiseaseList()
    },
    
    // 检查按钮是否在加载状态
    isButtonLoading(action, recordId) {
      return this.loadingButtons.includes(`${action}-${recordId}`)
    },

    // 设置按钮加载状态
    setButtonLoading(action, recordId, loading) {
      const key = `${action}-${recordId}`
      if (loading) {
        if (!this.loadingButtons.includes(key)) {
          this.loadingButtons.push(key)
        }
      } else {
        const index = this.loadingButtons.indexOf(key)
        if (index > -1) {
          this.loadingButtons.splice(index, 1)
        }
      }
    },

    // 操作按钮点击
    async handleActionClick({ action, record }) {
      const buttonKey = `${action}-${record.id}`
      
      try {
        this.setButtonLoading(action, record.id, true)
        
        // 添加操作反馈
        let message = ''
        switch (action) {
          case 'view':
            message = '正在跳转到详情页面...'
            break
          case 'judge':
            message = '正在跳转到判定页面...'
            break
          case 'review':
            message = '正在跳转到复核页面...'
            break
        }
        
        if (message) {
          this.$message.info(message)
        }
        
        // 稍微延迟以显示加载状态
        await new Promise(resolve => setTimeout(resolve, 300))
        
        switch (action) {
          case 'view':
            this.$router.push({
              name: 'DiseaseDetail',
              params: { 
                id: record.id,
                diseaseData: record // 传递完整的病害数据
              }
            })
            break
          case 'judge':
            this.$router.push({
              name: 'DiseaseDetail',
              params: { 
                id: record.id,
                diseaseData: record // 传递完整的病害数据
              },
              query: { action: 'judge' }
            })
            break
          case 'dispose':
            this.$router.push({
              name: 'DiseaseDetail',
              params: { 
                id: record.id,
                diseaseData: record // 传递完整的病害数据
              },
              query: { action: 'dispose' }
            })
            break
          case 'review':
            this.$router.push({
              name: 'DiseaseDetail',
              params: { 
                id: record.id,
                diseaseData: record // 传递完整的病害数据
              },
              query: { action: 'review' }
            })
            break
          case 'edit':
            this.$router.push({
              name: 'DiseaseDetail',
              params: { 
                id: record.id,
                diseaseData: record // 传递完整的病害数据
              },
              query: { action: 'edit' }
            })
            break
          case 'delete':
            await this.handleDeleteDisease(record)
            break
          default:
            console.log('未知操作:', action)
        }
      } catch (error) {
        console.error('操作执行失败:', error)
        this.$message.error('操作执行失败')
      } finally {
        this.setButtonLoading(action, record.id, false)
      }
    },
    
    // 删除病害
    async handleDeleteDisease(record) {
      try {
        // 这里调用删除API
        // await deleteDisease(record.id)
        
        this.$message.success('删除成功')
        await this.fetchDiseaseList()
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式
@import '@/styles/inspection-theme.scss';

.disease-management {
  .page-container {
    .pagination-wrapper {
      margin-top: auto; // 自动推到底部
      padding: 16px 0;
      display: flex;
      justify-content: center;
      flex-shrink: 0; // 防止被压缩
      background: var(--inspection-bg-primary);
      border-radius: 4px;
      margin-bottom: 8px;
    }
  }

  // 表头换行样式
  .table-header-wrap {
    line-height: 1.4;
    word-break: keep-all;
    white-space: pre-line;
  }

  // 表格操作按钮样式 - 移除自定义样式，让主题文件处理
  .common-table {
    :deep(.el-table) {
      // 危险按钮样式
      .el-button--text.danger-button {
        color: #f56c6c;
        
        &:hover {
          color: #f78989;
        }
      }

      .el-tag {
        border: none;
        
        &.el-tag--small {
          height: 20px;
          line-height: 18px;
          padding: 0 6px;
        }
      }
    }
  }



  // 自定义日期范围选择器样式，完全匹配filter-select样式
  .custom-date-range-selector {
    position: relative;
    width: 100%;
    min-width: 180px;
    flex: 1;
    display: flex; // 确保与其他filter-select对齐
    align-items: center; // 垂直居中对齐

    .date-range-display {
      position: relative;
      box-sizing: border-box;
      display: inline-flex; // 改为inline-flex确保更好的对齐
      align-items: center; // 垂直居中对齐
      width: 100%;
      height: 40px !important;
      padding: 0 30px 0 15px;
      // 使用与filter-select完全相同的样式
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      border-radius: 8px !important;
      color: #f8fafc !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
      line-height: 140% !important;
      cursor: pointer;
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &:focus,
      &.is-focused {
        outline: none;
        border-color: #409EFF !important;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
      }

      .date-range-text {
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // 移除line-height，使用父容器的flex对齐
        color: #f8fafc !important; // 直接使用与病害类型相同的颜色值
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        font-size: var(--sds-typography-body-size-medium, 16px) !important;
        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
        
        &:empty::before {
          content: '上报日期';
          color: rgba(255, 255, 255, 0.5) !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
        }
      }

      .el-input__suffix {
        position: absolute;
        top: 0;
        right: 15px;
        height: 100%;
        display: flex;
        align-items: center;
        pointer-events: none;

        .el-input__icon {
          color: rgba(255, 255, 255, 0.7) !important;
          font-size: 14px !important;
          transition: color 0.3s ease !important;

          &:hover {
            color: rgba(255, 255, 255, 0.9) !important;
          }

          &.clear-icon {
            pointer-events: auto;
            cursor: pointer;
            
            &:hover {
              color: #f56c6c !important;
            }
          }
          
          &.dropdown-icon {
            pointer-events: none;
          }
        }
      }
    }

    // 确保隐藏的日期选择器完全不可见
    .el-date-editor {
      position: absolute !important;
      opacity: 0 !important;
      pointer-events: none !important;
      z-index: -1 !important;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  // 操作按钮区域样式
  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .el-button {
      &.el-button--primary {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
      }
      
      &.el-button--success {
        background: linear-gradient(135deg, #10B981 0%, #047857 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        
        &:disabled {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.4);
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  // 基础样式已在主题文件中定义
}

// 全局日期选择器样式优化 - 解决遮挡问题
:deep(.el-picker-panel) {
  background: rgba(30, 41, 59, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  z-index: 3000 !important;

  // 日期选择器头部
  .el-picker-panel__header {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 12px 16px !important;

    .el-picker-panel__icon-btn {
      color: rgba(255, 255, 255, 0.8) !important;
      background: transparent !important;
      border: none !important;
      width: 32px !important;
      height: 32px !important;
      border-radius: 6px !important;
      transition: all 0.2s ease !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }
    }

    .el-date-picker__header-label {
      color: #ffffff !important;
      font-weight: 500 !important;
      font-size: 14px !important;
    }
  }

  // 日期选择器主体内容
  .el-picker-panel__content {
    background: transparent !important;
    padding: 8px 16px 16px !important;
  }

  // 日期表格
  .el-date-table {
    background: transparent !important;

    // 表头（星期）
    th {
      color: rgba(255, 255, 255, 0.6) !important;
      font-weight: 500 !important;
      font-size: 12px !important;
      padding: 8px 0 !important;
      border: none !important;
      background: transparent !important;
    }

    // 日期单元格
    td {
      background: transparent !important;
      border: none !important;
      padding: 2px !important;

      .cell {
        width: 32px !important;
        height: 32px !important;
        line-height: 32px !important;
        border-radius: 6px !important;
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 13px !important;
        font-weight: 400 !important;
        transition: all 0.2s ease !important;
        border: 1px solid transparent !important;
        background: transparent !important;
        margin: 1px !important;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        &:hover {
          background: rgba(59, 130, 246, 0.2) !important;
          color: #ffffff !important;
          border-color: rgba(59, 130, 246, 0.3) !important;
        }
      }

      // 当前日期
      &.today .cell {
        background: rgba(59, 130, 246, 0.15) !important;
        color: #3B82F6 !important;
        border-color: rgba(59, 130, 246, 0.4) !important;
        font-weight: 500 !important;
      }

      // 选中的日期
      &.selected .cell {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%) !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        border: none !important;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
      }

      // 范围内的日期 - 关键修复：确保文字可见
      &.in-range .cell {
        background: rgba(59, 130, 246, 0.15) !important;
        color: #ffffff !important;
        border-radius: 0 !important;
        border: none !important;
        font-weight: 400 !important;
      }

      // 范围开始日期
      &.start-date .cell {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%) !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        border-radius: 6px 0 0 6px !important;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
      }

      // 范围结束日期
      &.end-date .cell {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%) !important;
        color: #ffffff !important;
        font-weight: 500 !important;
        border-radius: 0 6px 6px 0 !important;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
      }

      // 如果开始和结束是同一天
      &.start-date.end-date .cell {
        border-radius: 6px !important;
      }

      // 不可用日期
      &.disabled .cell {
        background: transparent !important;
        color: rgba(255, 255, 255, 0.3) !important;
        cursor: not-allowed !important;

        &:hover {
          background: transparent !important;
          color: rgba(255, 255, 255, 0.3) !important;
          border-color: transparent !important;
        }
      }

      // 其他月份的日期
      &.prev-month .cell,
      &.next-month .cell {
        color: rgba(255, 255, 255, 0.4) !important;
      }
    }
  }

  // 底部按钮区域
  .el-picker-panel__footer {
    background: transparent !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 12px 16px !important;

    .el-button {
      &.el-button--text {
        color: rgba(255, 255, 255, 0.7) !important;
        
        &:hover {
          color: #3B82F6 !important;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%) !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 6px 12px !important;
        font-size: 12px !important;
      }
    }
  }

  // 快捷选项
  .el-picker-panel__sidebar {
    background: rgba(30, 41, 59, 0.5) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;

    .el-picker-panel__shortcut {
      color: rgba(255, 255, 255, 0.8) !important;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }
    }
  }
}
</style>
