<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="50%"
    top="5vh"
    custom-class="inspection-detail-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    @close="handleClose"
  >
    <!-- 自定义关闭按钮 -->
    <div class="custom-close-btn" @click="handleClose">
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z" fill="white"/>
      </svg>
    </div>
    
    <div class="dialog-content">
      <!-- 巡检基本信息 -->
      <div class="basic-info-section">
        <div class="section-header">
          <div class="header-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="section-title">基本信息</h3>
        </div>
        
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">巡检对象</label>
            <div class="info-value">{{ inspectionData.bridgeName || '未知' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">巡检类型</label>
            <div class="info-value">
              <span :class="['type-badge', `type-${inspectionData.type}`]">
                {{ getInspectionTypeText(inspectionData.type) }}
              </span>
            </div>
          </div>
          
          <div class="info-item">
            <label class="info-label">巡检人员</label>
            <div class="info-value">{{ inspectionData.inspector || '未知' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">巡检单位</label>
            <div class="info-value">{{ inspectionData.inspectionUnit || '桥梁巡检养护单位' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">巡检上报时间</label>
            <div class="info-value">{{ formatDateTime(inspectionData.date, inspectionData.time) }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">巡检方式</label>
            <div class="info-value">{{ inspectionData.method || '步行' }}</div>
          </div>
          
          <div class="info-item full-width">
            <label class="info-label">巡检项目</label>
            <div class="info-value">
              <el-button 
                type="text" 
                class="template-link"
                @click="handleTemplateClick"
              >
                {{ inspectionData.templateName || '日常巡检模板' }}
                <i class="el-icon-link template-icon"></i>
              </el-button>
            </div>
          </div>
          
          <div class="info-item full-width">
            <label class="info-label">巡检说明</label>
            <div class="info-value description">{{ inspectionData.description || 'XXXXXXXXX' }}</div>
          </div>
        </div>
        
        <!-- 巡检上报位置 -->
        <div class="location-section">
          <label class="info-label">巡检上报位置</label>
          <div class="location-map">
            <img :src="inspectionData.locationImage || defaultLocationImage" alt="巡检位置" class="map-image" />
          </div>
        </div>
        
        <!-- 巡检上报图片 -->
        <div class="images-section">
          <label class="info-label">巡检上报图片</label>
          <div class="images-grid">
            <div 
              v-for="(image, index) in inspectionImages" 
              :key="index"
              class="image-item"
              @click="handleImagePreview(image, index)"
            >
              <img :src="image.url" :alt="`巡检图片${index + 1}`" class="inspection-image" />
              <div class="image-overlay">
                <i class="el-icon-zoom-in"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 病害信息 -->
      <div v-if="hasDisease" class="disease-info-section">
        <div class="section-header">
          <div class="header-icon disease-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3 class="section-title">病害信息</h3>
        </div>
        
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">病害部位</label>
            <div class="info-value">{{ diseaseData.location || '桥路连接位置' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">病害类型</label>
            <div class="info-value">{{ diseaseData.type || '桥头沉降跳车' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">病害数量</label>
            <div class="info-value">{{ diseaseData.count || '1处' }}</div>
          </div>
          
          <div class="info-item">
            <label class="info-label">病害描述</label>
            <div class="info-value">{{ diseaseData.description || '桥头沉降跳车XXXX' }}</div>
          </div>
        </div>
        
        <!-- 病害位置 -->
        <div class="location-section">
          <label class="info-label">病害位置</label>
          <div class="location-map">
            <img :src="diseaseData.locationImage || defaultLocationImage" alt="病害位置" class="map-image" />
          </div>
        </div>
        
        <!-- 病害图片 -->
        <div class="images-section">
          <label class="info-label">病害图片</label>
          <div class="images-grid">
            <div 
              v-for="(image, index) in diseaseImages" 
              :key="index"
              class="image-item"
              @click="handleImagePreview(image, index)"
            >
              <img :src="image.url" :alt="`病害图片${index + 1}`" class="inspection-image" />
              <div class="image-overlay">
                <i class="el-icon-zoom-in"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 病害状态 -->
        <div class="disease-status-section">
          <label class="info-label">病害状态</label>
          <div class="status-container">
            <el-button 
              :type="getStatusButtonType(diseaseData.status)"
              :class="['status-button', `status-${diseaseData.status}`]"
              @click="handleStatusClick"
            >
              {{ getStatusText(diseaseData.status) }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <div class="footer-close-btn" @click="handleClose">
        关闭
      </div>
    </div>
    
    <!-- 图片预览弹框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="imagePreviewVisible"
      width="70%"
      custom-class="image-preview-dialog"
      :modal="true"
      :append-to-body="true"
    >
      <div class="image-preview-container">
        <img :src="currentPreviewImage" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
    
    <!-- 模板内容弹框 -->
    <el-dialog
      title="巡检项目模板"
      :visible.sync="templateDialogVisible"
      width="50%"
      custom-class="template-dialog"
      :modal="true"
      :append-to-body="true"
    >
      <div class="template-content">
        <div class="template-item" v-for="(item, index) in templateItems" :key="index">
          <div class="template-item-title">{{ item.title }}</div>
          <div class="template-item-content">{{ item.content }}</div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: 'InspectionDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    inspectionData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewImage: '',
      
      // 模板弹框相关
      templateDialogVisible: false,
      
        // 默认位置图片 - 使用test文件夹中的地图图片
        defaultLocationImage: require('@/assets/images/test/mapdemo.png'),
      
      // 模板项目数据
      templateItems: [
        { title: '桥梁结构检查', content: '检查桥梁主体结构是否有裂缝、变形等问题' },
        { title: '桥面状况检查', content: '检查桥面铺装、伸缩缝、排水设施等状况' },
        { title: '桥梁支座检查', content: '检查桥梁支座是否正常，有无异常位移' },
        { title: '桥梁附属设施检查', content: '检查护栏、标志标线、照明设施等' },
        { title: '安全隐患排查', content: '全面排查可能存在的安全隐患' }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    dialogTitle() {
      if (!this.inspectionData) return '巡检详情'
      
      // 安全获取桥梁名称
      let bridgeName = '未知桥梁'
      if (this.inspectionData.bridgeData && this.inspectionData.bridgeData.bridgeName) {
        bridgeName = this.inspectionData.bridgeData.bridgeName
      } else if (this.inspectionData.bridgeName) {
        bridgeName = this.inspectionData.bridgeName
      }
      
      const inspectionType = this.getInspectionTypeText(this.inspectionData.type || 'unknown')
      
      return `${bridgeName} - ${inspectionType}详情`
    },
    
    // 是否有病害信息
    hasDisease() {
      return this.diseaseData && Object.keys(this.diseaseData).length > 0
    },
    
    // 病害数据
    diseaseData() {
      return this.inspectionData.disease || {}
    },
    
    // 巡检图片
    inspectionImages() {
      // 使用test文件夹中的图片
      return [
        { url: require('@/assets/images/test/inspection_image1.png') },
        { url: require('@/assets/images/test/inspection_image2.png') }
      ]
    },
    
    // 病害图片
    diseaseImages() {
      // 使用test文件夹中的图片
      return [
        { url: require('@/assets/images/test/inspection_image1.png') },
        { url: require('@/assets/images/test/inspection_image2.png') }
      ]
    }
  },
    watch: {
      visible(newVal) {
        if (newVal) {
          this.initDialog()
          this.$nextTick(() => {
            this.forceApplyDialogStyles()
          })
        }
      }
    },
  methods: {
    // 初始化弹框
    initDialog() {
      // 初始化默认病害数据（如果没有提供）
      if (!this.inspectionData.disease) {
        this.$set(this.inspectionData, 'disease', {
          location: '桥路连接位置',
          type: '桥头沉降跳车',
          count: '1处',
          description: '桥头沉降跳车XXXX',
          status: 'unprocessed', // unprocessed, processing, processed
          locationImage: this.defaultLocationImage
        })
      }
    },
    
    // 强制应用弹框样式
    forceApplyDialogStyles() {
      const dialog = document.querySelector('.inspection-detail-dialog')
      if (dialog) {
        // 应用深色主题样式
        const forceStyles = {
          border: '1px solid rgba(79, 70, 229, 0.3) !important',
          borderColor: 'rgba(79, 70, 229, 0.3) !important',
          borderWidth: '1px !important',
          borderStyle: 'solid !important',
          boxShadow: '0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important',
          background: 'linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important',
          backgroundColor: '#091A4B !important',
          borderRadius: '8px !important',
          color: '#f1f5f9 !important'
        }
        
        Object.keys(forceStyles).forEach(property => {
          dialog.style.setProperty(property, forceStyles[property], 'important')
        })
        
        // 应用深色主题到各个区域
        this.applyDarkThemeToAllAreas()
        
        // 不需要特殊的关闭按钮定位
      }
    },
    
    // 应用深色主题到弹框的所有区域
    applyDarkThemeToAllAreas() {
      // 应用到header区域
      const header = document.querySelector('.inspection-detail-dialog .el-dialog__header')
      if (header) {
        const headerStyles = {
          'background': '#091A4B',
          'color': '#f1f5f9',
          'border-bottom': '1px solid #ffffff',
          'padding': '20px 24px'
        }
        Object.keys(headerStyles).forEach(property => {
          header.style.setProperty(property, headerStyles[property], 'important')
        })
        
        const title = header.querySelector('.el-dialog__title')
        if (title) {
          title.style.setProperty('color', '#f1f5f9', 'important')
          title.style.setProperty('font-weight', '600', 'important')
        }
      }
      
      // 应用到body区域
      const body = document.querySelector('.inspection-detail-dialog .el-dialog__body')
      if (body) {
        const bodyStyles = {
          'background': '#091A4B',
          'color': '#e2e8f0',
          'padding': '24px'
        }
        Object.keys(bodyStyles).forEach(property => {
          body.style.setProperty(property, bodyStyles[property], 'important')
        })
      }
      
      // 应用到footer区域
      const footer = document.querySelector('.inspection-detail-dialog .el-dialog__footer')
      if (footer) {
        const footerStyles = {
          'background': '#091A4B',
          'border-top': 'none',
          'padding': '12px 24px'
        }
        Object.keys(footerStyles).forEach(property => {
          footer.style.setProperty(property, footerStyles[property], 'important')
        })
      }
    },
    
    // 确保关闭按钮正确定位
    positionCloseButton() {
      // 底部关闭按钮不需要特殊定位
    },
    
    // 获取巡检类型文本
    getInspectionTypeText(type) {
      const typeMap = {
        'daily': '日常巡检',
        'regular': '经常巡检',
        'center': '中心巡检',
        'uninspected': '未巡检'
      }
      return typeMap[type] || '日常巡检'
    },
    
    // 格式化日期时间
    formatDateTime(date, time) {
      if (!date) return '未知时间'
      if (time) {
        return `${date} ${time}`
      }
      return date
    },
    
    // 处理模板点击
    handleTemplateClick() {
      this.templateDialogVisible = true
    },
    
    // 处理图片预览
    handleImagePreview(image, index) {
      this.currentPreviewImage = image.url
      this.imagePreviewVisible = true
    },
    
    // 获取状态按钮类型
    getStatusButtonType(status) {
      const typeMap = {
        'unprocessed': 'danger',
        'processing': 'warning', 
        'processed': 'success'
      }
      return typeMap[status] || 'danger'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'unprocessed': '未处理',
        'processing': '处理中',
        'processed': '已处理'
      }
      return textMap[status] || '未处理'
    },
    
    // 处理状态点击
    handleStatusClick() {
      const status = this.diseaseData.status
      if (status === 'processed') {
        // 跳转到病害及处置详情页面
        this.$emit('view-disease-detail', {
          diseaseId: this.diseaseData.id || 'default',
          diseaseData: this.diseaseData
        })
      } else {
        this.$message.info(`当前病害状态：${this.getStatusText(status)}`)
      }
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.imagePreviewVisible = false
      this.templateDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入公共弹框样式
@import '@/styles/components/dialog.scss';
// 导入主题样式
@import '@/styles/inspection-theme.scss';

// 巡检详情弹框样式 - 基础样式通过公共样式类应用
// 仅保留特定的定制样式

:deep(.inspection-detail-dialog) {
  // 所有基础样式已通过公共样式类应用，无需重复定义
  
  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

// 弹框内容样式
.dialog-content {
  padding: 24px;
}

// 区域样式
.basic-info-section,
.disease-info-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 区域头部样式
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(79, 70, 229, 0.3);
  
  .header-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: #6366f1;
    
    &.disease-icon {
      background: rgba(220, 38, 38, 0.1);
      border-color: rgba(220, 38, 38, 0.3);
      color: #ef4444;
    }
  }
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #f1f5f9;
    margin: 0;
  }
}

// 信息网格样式
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  
  .info-item {
    &.full-width {
      grid-column: span 2;
    }
  }
}

// 信息项样式
.info-item {
  .info-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 6px;
  }
  
  .info-value {
    font-size: 14px;
    color: #e2e8f0;
    line-height: 1.5;
    
    &.description {
      padding: 12px;
      background: rgba(30, 58, 138, 0.2);
      border-radius: 6px;
      border: 1px solid rgba(30, 58, 138, 0.3);
    }
  }
}

// 类型标签样式
.type-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  
  &.type-daily {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }
  
  &.type-regular {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
  }
  
  &.type-center {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
    border: 1px solid rgba(168, 85, 247, 0.3);
  }
  
  &.type-uninspected {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
    border: 1px solid rgba(156, 163, 175, 0.3);
  }
}

// 模板链接样式
.template-link {
  color: #6366f1 !important;
  font-weight: 500;
  padding: 0;
  
  &:hover {
    color: #8b5cf6 !important;
  }
  
  .template-icon {
    margin-left: 4px;
    font-size: 12px;
  }
}

// 位置区域样式
.location-section {
  margin-bottom: 24px;
  
  .info-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 8px;
  }
  
  .location-map {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(30, 58, 138, 0.3);
    
    .map-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      display: block;
    }
  }
}

// 图片区域样式
.images-section {
  margin-bottom: 24px;
  
  .info-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 8px;
  }
  
  .images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(30, 58, 138, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      
      .image-overlay {
        opacity: 1;
      }
    }
    
    .inspection-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      display: block;
    }
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      i {
        font-size: 24px;
        color: #ffffff;
      }
    }
  }
}

// 病害状态区域样式
.disease-status-section {
  .info-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 8px;
  }
  
  .status-container {
    .status-button {
      cursor: pointer;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      
      &.status-processed {
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
        }
      }
    }
  }
}


// 图片预览弹框样式
:deep(.image-preview-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}

.image-preview-container {
  text-align: center;
  
  .preview-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
  }
}

// 模板弹框样式
.template-content {
  .template-item {
    margin-bottom: 16px;
    padding: 16px;
    background: rgba(30, 58, 138, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(30, 58, 138, 0.3);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .template-item-title {
      font-weight: 600;
      color: #f1f5f9;
      margin-bottom: 8px;
    }
    
    .template-item-content {
      color: #cbd5e1;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    
    .info-item.full-width {
      grid-column: span 1;
    }
  }
  
  .images-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  :deep(.inspection-detail-dialog.modern-dialog) {
    width: 95% !important;
    top: 2vh !important;
  }
}

// 所有z-index、关闭按钮、footer样式已通过公共样式类应用，无需重复定义
</style>
