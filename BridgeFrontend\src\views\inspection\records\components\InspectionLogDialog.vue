<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="50%"
    top="5vh"
    custom-class="inspection-log-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    @close="handleClose"
  >
    <!-- 自定义关闭按钮 -->
    <div class="custom-close-btn" @click="handleClose">
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z" fill="white"/>
      </svg>
    </div>
    
    <div class="dialog-content">
      <!-- 日历组件 -->
      <CalendarView
        :current-month="currentMonth"
        :inspection-logs="inspectionLogs"
        :selected-date="selectedDate"
        @month-change="handleMonthChange"
        @date-click="handleDateClick"
        @inspection-click="handleInspectionClick"
      />
    </div>
    
    <div slot="footer" class="dialog-footer">
      <div class="footer-close-btn" @click="handleClose">
        关闭
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { CalendarView } from '@/components/Inspection'

export default {
  name: 'InspectionLogDialog',
  components: {
    CalendarView
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bridgeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 当前月份
      currentMonth: '',
      // 选中的日期
      selectedDate: '',
      // 巡检日志数据
      inspectionLogs: []
    }
  },
  computed: {
    ...mapGetters('inspection', ['loadingStates']),
    
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    dialogTitle() {
      if (this.bridgeData && this.bridgeData.bridgeName) {
        return `${this.bridgeData.bridgeName} - ${this.currentMonthText}巡检日志`
      }
      return '巡检日志'
    },
    
    currentMonthText() {
      if (!this.currentMonth) return ''
      const [year, month] = this.currentMonth.split('-')
      return `${year}年${month}月`
    },
    
    loading() {
      const isLoading = this.loadingStates.inspectionLog
      return isLoading
    }
  },
  watch: {
    visible(newVal, oldVal) {

      if (newVal) {

        // 确保所有loading状态被重置，避免遮盖弹框
        this.$store.commit('inspection/SET_INSPECTION_RECORDS_LOADING', false)
        this.$store.commit('inspection/SET_INSPECTION_LOG_LOADING', false)
        

        this.initDialog()

        // 立即应用深色主题样式，避免白色闪烁
        this.$nextTick(() => {
          this.forceApplyDialogStyles()
        })
      } else {
        
        // 🧹 清理样式保护资源
        if (this.styleObserver) {
          this.styleObserver.disconnect()
          this.styleObserver = null
        }
        
        if (this.styleProtectionInterval) {
          clearInterval(this.styleProtectionInterval)
          this.styleProtectionInterval = null
        }
      }
    },
    
    bridgeData: {
      handler(newData) {
        // 防止重复调用：initDialog已经会调用loadInspectionLogs
        // 只在弹框已经显示且不在初始化过程中时才重新加载
        if (newData && this.visible && this.inspectionLogs.length > 0) {
          this.loadInspectionLogs()
        }
      },
      deep: true
    }
  },
  // 🧹 生命周期：清理资源
  beforeDestroy() {
    // 清理样式观察器
    if (this.styleObserver) {
      this.styleObserver.disconnect()
      this.styleObserver = null
    }
    
    // 清理定时器
    if (this.styleProtectionInterval) {
      clearInterval(this.styleProtectionInterval)
      this.styleProtectionInterval = null
    }
    
  },
  methods: {
    // 🔍 调试方法：深入分析弹框样式问题
    debugDialogStyles() {
      
      // 1. 查找弹框元素
      const dialogElements = {
        wrapper: document.querySelector('.el-dialog__wrapper'),
        dialog: document.querySelector('.inspection-log-dialog'),
        modernDialog: document.querySelector('.modern-dialog'),
        customClass: document.querySelector('.inspection-log-dialog.modern-dialog')
      }
      
      
      // 2. 检查每个元素的计算样式
      Object.keys(dialogElements).forEach(key => {
        const element = dialogElements[key]
        if (element) {
          const computedStyle = window.getComputedStyle(element)
        }
      })
      
      // 3. 检查CSS选择器匹配情况
      const testSelectors = [
        '.inspection-log-dialog',
        '.modern-dialog', 
        '.inspection-log-dialog.modern-dialog',
        '.inspection-log-dialog.modern-dialog.force-high-zindex',
        '.el-dialog',
        ':deep(.inspection-log-dialog.modern-dialog)'
      ]
      
      testSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector)
        } catch (e) {
          console.warn(`❌ [选择器] "${selector}" 无效:`, e.message)
        }
      })
      
      // 4. 检查样式表中的规则
      this.checkStyleSheetRules()
      
      // 5. 动态应用样式测试
      this.testDynamicStyles()
    },
    
    // 检查样式表规则
    checkStyleSheetRules() {
      
      Array.from(document.styleSheets).forEach((sheet, index) => {
        try {
          Array.from(sheet.cssRules || []).forEach(rule => {
            if (rule.selectorText && rule.selectorText.includes('inspection-log-dialog')) {
            }
          })
        } catch (e) {
          console.warn(`❌ [样式表 ${index}] 无法访问:`, e.message)
        }
      })
    },
    
    // 计算CSS选择器优先级
    calculateSpecificity(selector) {
      const ids = (selector.match(/#/g) || []).length
      const classes = (selector.match(/\./g) || []).length
      const elements = (selector.match(/[a-zA-Z]/g) || []).length - classes
      return { ids, classes, elements, total: ids * 100 + classes * 10 + elements }
    },
    
    // 🚀 强制应用弹框样式的最终解决方案
    forceApplyDialogStyles() {
      
      const dialog = document.querySelector('.inspection-log-dialog')
      if (dialog) {
          // 🌙 设置深色主题样式 - 与整体框架风格协调
          const forceStyles = {
            border: '1px solid rgba(79, 70, 229, 0.3) !important',
            borderColor: 'rgba(79, 70, 229, 0.3) !important',
            borderWidth: '1px !important',
            borderStyle: 'solid !important',
            boxShadow: '0 0 0 1px rgba(79, 70, 229, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important',
            background: 'linear-gradient(135deg, #091A4B 0%, #1e3a8a 100%) !important',
            backgroundColor: '#091A4B !important',
            borderRadius: '8px !important',
            color: '#f1f5f9 !important'
          }
        
        Object.keys(forceStyles).forEach(property => {
          dialog.style.setProperty(property, forceStyles[property], 'important')
        })
        
        
        // 🔄 设置观察器，防止Element UI重新覆盖样式
        this.setupStyleProtection(dialog, forceStyles)
        
        // 🌙 应用深色主题到各个区域
        this.applyDarkThemeToAllAreas()
        
        // 不需要特殊的关闭按钮定位
        
        // 🚀 额外的样式强制措施，立即再次应用一遍
        setTimeout(() => {
          Object.keys(forceStyles).forEach(property => {
            dialog.style.setProperty(property, forceStyles[property], 'important')
          })
        }, 100)
      } else {
        console.warn('❌ [强制样式] 未找到弹框元素')
      }
    },
    
    // 🛡️ 设置样式保护，防止被覆盖
    setupStyleProtection(dialog, targetStyles) {
      
      // 使用MutationObserver监听样式变化
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            
            // 检查是否需要重新应用样式 - 增强检测条件
            const computedStyles = window.getComputedStyle(dialog)
            const currentBorder = computedStyles.border
            const currentBorderColor = computedStyles.borderColor
            const currentBorderWidth = computedStyles.borderWidth
            
            // 多重检测条件，确保边框颜色正确
            const hasCorrectBorder = (
              currentBorder.includes('2px solid rgb(79, 70, 229)') ||
              currentBorder.includes('0.02rem solid rgb(79, 70, 229)') ||
              (currentBorderColor.includes('79, 70, 229') && currentBorderWidth === '2px')
            )
            
            if (!hasCorrectBorder) {
              Object.keys(targetStyles).forEach(property => {
                dialog.style.setProperty(property, targetStyles[property], 'important')
              })
            }
          }
        })
      })
      
      observer.observe(dialog, {
        attributes: true,
        attributeFilter: ['style']
      })
      
      // 保存observer引用，以便后续清理
      this.styleObserver = observer
      
      // 也设置定时检查 - 增强检测条件
      this.styleProtectionInterval = setInterval(() => {
        const computedStyles = window.getComputedStyle(dialog)
        const currentBorder = computedStyles.border
        const currentBorderColor = computedStyles.borderColor
        const currentBorderWidth = computedStyles.borderWidth
        
        // 多重检测条件，确保边框颜色正确
        const hasCorrectBorder = (
          currentBorder.includes('2px solid rgb(79, 70, 229)') ||
          currentBorder.includes('0.02rem solid rgb(79, 70, 229)') ||
          (currentBorderColor.includes('79, 70, 229') && currentBorderWidth === '2px')
        )
        
        if (!hasCorrectBorder) {
          Object.keys(targetStyles).forEach(property => {
            dialog.style.setProperty(property, targetStyles[property], 'important')
          })
        }
      }, 1000)
    },
    
    // 🎨 确保关闭按钮正确定位
    positionCloseButton() {
      // 底部关闭按钮不需要特殊定位
    },
    
    // 🌙 应用深色主题到弹框的所有区域
    applyDarkThemeToAllAreas() {
      
      // 应用到header区域
      const header = document.querySelector('.inspection-log-dialog .el-dialog__header')
      if (header) {
        const headerStyles = {
          'background': '#091A4B',
          'color': '#f1f5f9',
          'border-bottom': '1px solid #ffffff',
          'padding': '20px 24px'
        }
        Object.keys(headerStyles).forEach(property => {
          header.style.setProperty(property, headerStyles[property], 'important')
        })
        
        // 标题文字颜色
        const title = header.querySelector('.el-dialog__title')
        if (title) {
          title.style.setProperty('color', '#f1f5f9', 'important')
          title.style.setProperty('font-weight', '600', 'important')
        }
      }
      
      // 应用到body区域
      const body = document.querySelector('.inspection-log-dialog .el-dialog__body')
      if (body) {
        const bodyStyles = {
          'background': '#091A4B',
          'color': '#e2e8f0',
          'padding': '24px'
        }
        Object.keys(bodyStyles).forEach(property => {
          body.style.setProperty(property, bodyStyles[property], 'important')
        })
        
        // 表格样式
        const tables = body.querySelectorAll('.el-table')
        tables.forEach(table => {
          table.style.setProperty('background-color', 'transparent', 'important')
          table.style.setProperty('color', '#e2e8f0', 'important')
          
          const cells = table.querySelectorAll('th, td')
          cells.forEach(cell => {
            cell.style.setProperty('background-color', 'rgba(30, 58, 138, 0.2)', 'important')
            cell.style.setProperty('color', '#e2e8f0', 'important')
            cell.style.setProperty('border-color', 'rgba(30, 58, 138, 0.3)', 'important')
          })
          
          const headers = table.querySelectorAll('th')
          headers.forEach(header => {
            header.style.setProperty('background-color', 'rgba(30, 58, 138, 0.4)', 'important')
            header.style.setProperty('color', '#f1f5f9', 'important')
            header.style.setProperty('font-weight', '600', 'important')
          })
        })
        
        // 输入框样式
        const inputs = body.querySelectorAll('.el-input__inner, .el-select .el-input__inner, .el-date-editor .el-input__inner')
        inputs.forEach(input => {
          input.style.setProperty('background-color', 'rgba(30, 58, 138, 0.3)', 'important')
          input.style.setProperty('border-color', 'rgba(30, 58, 138, 0.4)', 'important')
          input.style.setProperty('color', '#e2e8f0', 'important')
        })
        
        // 标签文字
        const labels = body.querySelectorAll('.el-form-item__label')
        labels.forEach(label => {
          label.style.setProperty('color', '#cbd5e1', 'important')
        })
        
      }
      
      // 应用到footer区域
      const footer = document.querySelector('.inspection-log-dialog .el-dialog__footer')
      if (footer) {
        const footerStyles = {
          'background': '#091A4B',
          'border-top': 'none',
          'padding': '12px 24px'
        }
        Object.keys(footerStyles).forEach(property => {
          footer.style.setProperty(property, footerStyles[property], 'important')
        })
        
        // 按钮样式
        const primaryBtns = footer.querySelectorAll('.el-button--primary')
        primaryBtns.forEach(btn => {
          btn.style.setProperty('background-color', '#1e3a8a', 'important')
          btn.style.setProperty('border-color', '#1e3a8a', 'important')
          btn.style.setProperty('color', '#ffffff', 'important')
        })
        
        const defaultBtns = footer.querySelectorAll('.el-button--default')
        defaultBtns.forEach(btn => {
          btn.style.setProperty('background-color', 'rgba(30, 58, 138, 0.6)', 'important')
          btn.style.setProperty('border-color', 'rgba(30, 58, 138, 0.4)', 'important')
          btn.style.setProperty('color', '#e2e8f0', 'important')
        })
        
      }
      
    },
    
    // 动态测试样式应用
    testDynamicStyles() {
      
      const dialog = document.querySelector('.inspection-log-dialog')
      if (dialog) {
        // 保存原始样式
        const originalStyle = dialog.style.cssText
        
        // 测试不同的样式应用方法
        const testStyles = [
          { name: '内联样式', style: 'border: 5px solid red !important; box-shadow: 0 0 20px yellow !important;' },
          { name: '类名强制', className: 'debug-border-test' }
        ]
        
        testStyles.forEach(test => {
          
          if (test.style) {
            dialog.style.cssText = originalStyle + test.style
          }
          
          if (test.className) {
            dialog.classList.add(test.className)
          }
          
          // 检查效果
          setTimeout(() => {
            const computedStyle = window.getComputedStyle(dialog)
            
            // 恢复样式
            dialog.style.cssText = originalStyle
            if (test.className) {
              dialog.classList.remove(test.className)
            }
          }, 100)
        })
        
        // 🚀 最后应用我们的强制样式
        setTimeout(() => {
          this.forceApplyDialogStyles()
        }, 500)
      }
    },
    
    // 初始化弹窗
    initDialog() {
      
      // 确保所有loading状态被重置，避免遮盖弹框
      this.$store.commit('inspection/SET_INSPECTION_RECORDS_LOADING', false)
      this.$store.commit('inspection/SET_INSPECTION_LOG_LOADING', false)
      

        // 检查全局loading状态
        this.checkGlobalLoadingState()
      
      // 设置默认月份为当前月份
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      this.currentMonth = `${year}-${month}`
      
      // 清空选中日期
      this.selectedDate = ''
      
      // 确保加载状态被重置
      this.$store.commit('inspection/SET_INSPECTION_LOG_LOADING', false)
      
      // 加载巡检日志数据
      this.loadInspectionLogs()
    },
    
    // 加载巡检日志数据（使用测试数据，避免API调用导致loading遮盖）
    loadInspectionLogs() {
      
      // 确保loading状态始终为false，避免遮盖弹框
      this.$store.commit('inspection/SET_INSPECTION_LOG_LOADING', false)
      this.$store.commit('inspection/SET_INSPECTION_RECORDS_LOADING', false)
      
      // 直接使用测试数据，避免API调用
        this.inspectionLogs = this.getDefaultLogs()
      
    },
    
    // 格式化巡检日志数据
    formatInspectionLogs(logs) {
      return logs.map(log => ({
        id: log.logId || log.id,
        date: log.date,
        type: this.mapInspectionType(log.inspectionType),
        time: log.time,
        inspector: log.inspector,
        description: log.description || ''
      }))
    },
    
    // 映射巡检类型
    mapInspectionType(type) {
      const typeMap = {
        '日常巡检': 'daily',
        '经常巡检': 'regular',
        '中心巡检': 'center',
        '未巡检': 'uninspected'
      }
      return typeMap[type] || 'daily'
    },
    
    // 获取默认日志数据
    getDefaultLogs() {
      const currentDate = new Date()
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0')
      
      return [
        // 单次巡检日期
        {
          id: 'default_1',
          date: `${year}-${month}-05`,
          type: 'daily',
          time: '09:20',
          inspector: '李华',
          description: '日常桥体结构巡检，发现轻微裂缝'
        },
        {
          id: 'default_2',
          date: `${year}-${month}-08`,
          type: 'regular',
          time: '14:30',
          inspector: '张明',
          description: '经常性桥梁支座检查，状态良好'
        },
        {
          id: 'default_3',
          date: `${year}-${month}-15`,
          type: 'center',
          time: '10:15',
          inspector: '王强',
          description: '中心组织专项巡检，重点关注承重结构'
        },
        // 同一天多次巡检
        {
          id: 'default_4',
          date: `${year}-${month}-12`,
          type: 'daily',
          time: '08:30',
          inspector: '赵敏',
          description: '晨检-日常结构巡检'
        },
        {
          id: 'default_5',
          date: `${year}-${month}-12`,
          type: 'regular',
          time: '16:45',
          inspector: '刘军',
          description: '下午补充巡检-排水系统检查'
        },
        {
          id: 'default_6',
          date: `${year}-${month}-22`,
          type: 'daily',
          time: '09:15',
          inspector: '陈涛',
          description: '日常维护巡检'
        },
        {
          id: 'default_7',
          date: `${year}-${month}-22`,
          type: 'center',
          time: '14:20',
          inspector: '李娜',
          description: '中心巡检-安全隐患排查'
        },
        {
          id: 'default_8',
          date: `${year}-${month}-22`,
          type: 'regular',
          time: '17:30',
          inspector: '孙凯',
          description: '晚间补充巡检'
        },
        // 其他单次巡检
        {
          id: 'default_9',
          date: `${year}-${month}-25`,
          type: 'uninspected',
          time: '',
          inspector: '',
          description: '计划巡检未执行'
        },
        {
          id: 'default_10',
          date: `${year}-${month}-28`,
          type: 'center',
          time: '11:00',
          inspector: '马超',
          description: '中心专项检测，使用专业设备'
        }
      ]
    },
    
    // 月份切换
    async handleMonthChange(month) {
      this.currentMonth = month
      await this.loadInspectionLogs()
    },
    
    // 日期点击
    handleDateClick(dateData) {
      this.selectedDate = dateData.dateStr
    },
    
    // 巡检记录点击
    handleInspectionClick(inspectionData) {
      
      this.$emit('inspection-detail', {
        ...inspectionData,
        bridgeData: this.bridgeData
      })
      
    },
    
    // 深度DOM检查方法
    performDeepDOMInspection() {
    },

    // 获取最高z-index的元素
    getHighestZIndexElements() {
      const allElements = document.querySelectorAll('*')
      const elementsWithZIndex = Array.from(allElements)
        .map(el => ({
          element: el.tagName + (el.className && typeof el.className === 'string' ? '.' + el.className.split(' ').join('.') : ''),
          zIndex: getComputedStyle(el).zIndex,
          actualZIndex: parseInt(getComputedStyle(el).zIndex) || 0
        }))
        .filter(item => item.actualZIndex > 0)
        .sort((a, b) => b.actualZIndex - a.actualZIndex)
        .slice(0, 10)

      return elementsWithZIndex
    },

    // 检查全局loading状态
    checkGlobalLoadingState() {
    },

    // 递归查找所有loading状态
    findAllLoadingStates(obj, path = '') {
      const loadingStates = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const currentPath = path ? `${path}.${key}` : key
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            if (key.toLowerCase().includes('loading') || 
                (typeof obj[key] === 'object' && 
                 Object.keys(obj[key]).some(k => k.toLowerCase().includes('loading')))) {
              loadingStates[currentPath] = obj[key]
            }
            // 递归检查嵌套对象
            const nestedStates = this.findAllLoadingStates(obj[key], currentPath)
            Object.assign(loadingStates, nestedStates)
          } else if (key.toLowerCase().includes('loading')) {
            loadingStates[currentPath] = obj[key]
          }
        }
      }
      return loadingStates
    },
    
    // 关闭弹窗
    handleClose() {
      // 确保加载状态被重置
      this.$store.commit('inspection/SET_INSPECTION_LOG_LOADING', false)
      
      this.dialogVisible = false
      this.selectedDate = ''
      this.inspectionLogs = []
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入公共弹框样式
@import '@/styles/components/dialog.scss';

// 巡检日志弹出框样式 - 使用公共样式，仅保留特定的定制内容
// 主要样式通过CSS类应用：inspection-dialog-base modern-dialog dark-theme force-high-zindex

// 仅保留特定于巡检日志的样式
:deep(.inspection-log-dialog) {
  // 所有基础样式已通过公共样式类应用，无需重复定义
}

// 仅保留巡检日志弹框特有的样式，其他已通过公共样式类应用

// 内容区域最小高度
.dialog-content {
  min-height: 400px;
}
</style>
