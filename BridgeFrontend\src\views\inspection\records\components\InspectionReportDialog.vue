<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="55%"
    top="10vh"
    custom-class="inspection-report-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    @close="handleClose"
  >
    <!-- 自定义关闭按钮 -->
    <div class="custom-close-btn" @click="handleClose">
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z" fill="white"/>
      </svg>
    </div>
    <div v-loading="loading" class="dialog-content">

      <!-- 报告列表表格 -->
        <div class="common-table">
          <el-table
            :data="reportList"
            style="width: 100%"
          >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column 
          prop="bridgeName" 
          label="桥梁名称" 
          min-width="120"
          show-overflow-tooltip
        />
        
        <el-table-column 
          prop="inspectionDate" 
          label="巡检日期" 
          width="120"
          align="center"
        />
        
        <el-table-column 
          prop="inspector" 
          label="巡检人员" 
          width="100"
          align="center"
        />
        
        <el-table-column 
          prop="contactNumber" 
          label="联系方式" 
          width="120"
          align="center"
        />
        
        <el-table-column 
          prop="reportGenerateTime" 
          label="报告生成时间" 
          width="160"
          align="center"
        />
        
        <el-table-column 
          label="操作" 
          width="200" 
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewReport(scope.row)"
            >
              查看报告
            </el-button>
          </template>
        </el-table-column>
        </el-table>
      </div>
      
      <!-- 空数据提示 -->
      <el-empty 
        v-if="!loading && reportList.length === 0"
        description="暂无报告数据"
        :image-size="100"
      />
      
      
    </div>
    
    <!-- 添加弹框底部 -->
    <div slot="footer" class="dialog-footer">
      <div class="footer-close-btn" @click="handleClose">
        关闭
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getInspectionReports } from '@/api/inspection/records'
import { getDefaultData } from '@/utils/inspection/defaultData'

export default {
  name: 'InspectionReportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bridgeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      reportList: [],
      
      // DOM观察器
      mutationObserver: null,
      
      // 防抖计时器
      reapplyTimer: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    
    dialogTitle() {
      if (this.bridgeData && this.bridgeData.bridgeName) {
        return `${this.bridgeData.bridgeName} - 巡检报告列表`
      }
      return '巡检报告列表'
    }
  },
    watch: {
    visible(newVal) {
      if (newVal) {
        this.initDialog()
        // 强制应用深色主题样式 - 多次尝试确保成功
        this.$nextTick(() => {
          this.forceApplyDialogStyles()
          // 设置观察器监听DOM变化
          this.setupMutationObserver()
          
        })
      } else {
        // 清理观察器
        if (this.mutationObserver) {
          this.mutationObserver.disconnect()
          this.mutationObserver = null
        }
      }
    },
    
    bridgeData: {
      handler(newData) {
        if (newData && this.visible) {
          this.loadReportList()
        }
      },
      deep: true
    }
  },
  beforeDestroy() {
    // 清理观察器
    if (this.mutationObserver) {
      this.mutationObserver.disconnect()
      this.mutationObserver = null
    }
    
    // 清理计时器
    if (this.reapplyTimer) {
      clearTimeout(this.reapplyTimer)
      this.reapplyTimer = null
    }
  },
  methods: {
    // 🚀 强制应用深色主题样式 - 解决Element UI样式覆盖问题
    forceApplyDialogStyles() {
      // 使用公共样式，只需要轻量级的样式强化
      setTimeout(() => {
        const dialog = document.querySelector('.el-dialog.inspection-report-dialog') ||
                       document.querySelector('.el-dialog.inspection-dialog-base') ||
                       document.querySelector('.el-dialog')
        
        if (dialog) {
          // 应用深色主题到各个区域
          this.applyDarkThemeToAllAreas(dialog)
        } else {
          // 如果还没找到，再次尝试
          setTimeout(() => this.forceApplyDialogStyles(), 200)
        }
      }, 100)
    },
    
    // 🎨 应用深色主题到弹框的所有区域
    applyDarkThemeToAllAreas(dialogElement = null) {
      // 如果没有传入dialog元素，尝试查找
      const dialog = dialogElement || document.querySelector('.el-dialog')
      
      if (!dialog) {
        return
      }
      
      // Header区域 - 使用dialog元素作为基础查找
      const header = dialog.querySelector('.el-dialog__header')
      
      if (header) {
        const headerStyles = {
          'background': '#091A4B',
          'color': '#f1f5f9',
          'border-bottom': '1px solid #ffffff', // 改为白色分割线，与巡检日志一致
          'padding': '20px 24px'
        }
        Object.keys(headerStyles).forEach(property => {
          header.style.setProperty(property, headerStyles[property], 'important')
        })
        
        const title = header.querySelector('.el-dialog__title')
        if (title) {
          title.style.setProperty('color', '#f1f5f9', 'important')
          title.style.setProperty('font-weight', '600', 'important')
        }

      }
      
      // Body区域
      const body = dialog.querySelector('.el-dialog__body')
      
      if (body) {
        const bodyStyles = {
          'background': '#091A4B',
          'color': '#e2e8f0',
          'padding': '24px'
        }
        Object.keys(bodyStyles).forEach(property => {
          body.style.setProperty(property, bodyStyles[property], 'important')
        })
        
        // 表格深色主题
        const tables = body.querySelectorAll('.el-table')
        tables.forEach(table => {
          table.style.setProperty('background-color', 'transparent', 'important')
          table.style.setProperty('color', '#e2e8f0', 'important')
          
          const cells = table.querySelectorAll('th, td')
          cells.forEach(cell => {
            cell.style.setProperty('background-color', 'rgba(30, 58, 138, 0.2)', 'important')
            cell.style.setProperty('color', '#e2e8f0', 'important')
            cell.style.setProperty('border-color', 'rgba(30, 58, 138, 0.3)', 'important')
          })
          
          const headers = table.querySelectorAll('th')
          headers.forEach(header => {
            header.style.setProperty('background-color', 'rgba(30, 58, 138, 0.4)', 'important')
            header.style.setProperty('color', '#f1f5f9', 'important')
            header.style.setProperty('font-weight', '600', 'important')
          })
        })

      }
      
      // Footer区域  
      const footer = dialog.querySelector('.el-dialog__footer')
      
      if (footer) {
        const footerStyles = {
          'background': '#091A4B',
          'border-top': '1px solid rgba(79, 70, 229, 0.3)',
          'padding': '16px 24px'
        }
        Object.keys(footerStyles).forEach(property => {
          footer.style.setProperty(property, footerStyles[property], 'important')
        })

      }
    },
    
    // 🔧 设置DOM变化观察器，确保样式始终正确应用
    setupMutationObserver() {
      // 如果已存在观察器，先断开
      if (this.mutationObserver) {
        this.mutationObserver.disconnect()
      }
      
      // 创建新的观察器
      this.mutationObserver = new MutationObserver((mutations) => {
        let shouldReapplyStyles = false
        
        mutations.forEach((mutation) => {
          // 检查是否有新的DOM节点添加
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach((node) => {
              // 如果添加的是Element UI的dialog相关元素，重新应用样式
              if (node.nodeType === 1 && 
                  (node.classList?.contains('el-dialog__header') ||
                   node.classList?.contains('el-dialog__footer') ||
                   node.classList?.contains('el-dialog__body'))) {
                shouldReapplyStyles = true
              }
            })
          }
          
          // 检查class变化
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            shouldReapplyStyles = true
          }
        })
        
        if (shouldReapplyStyles) {
          // 防抖处理，避免频繁触发
          clearTimeout(this.reapplyTimer)
          this.reapplyTimer = setTimeout(() => this.forceApplyDialogStyles(), 200)
        }
      })
      
      // 开始观察document的变化
      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      })
    },
    
    // 初始化弹窗
    initDialog() {
      // 清空数据
      this.reportList = []
      
      // 加载报告列表
      this.loadReportList()
    },
    
    // 加载报告列表
    async loadReportList() {
      if (!this.bridgeData || !this.bridgeData.id) {
        return
      }
      
      this.loading = true
      
      try {
        const response = await getInspectionReports(this.bridgeData.id)
        
        if (response.data) {
          this.reportList = this.formatReportList(response.data.list || [])
        }
      } catch (error) {
        console.error('加载报告列表失败:', error)
        this.$message.error('加载报告列表失败')
        
        // 使用默认数据
        const defaultData = this.getDefaultReportList()
        this.reportList = defaultData.list
      } finally {
        this.loading = false
        
      }
    },
    
    // 格式化报告列表数据
    formatReportList(list) {
      return list.map(report => ({
        id: report.id,
        bridgeName: report.bridgeName || this.bridgeData.bridgeName,
        inspectionDate: report.inspectionDate,
        inspector: report.inspector,
        contactNumber: report.contactNumber,
        reportGenerateTime: report.reportGenerateTime,
        reportUrl: report.reportUrl
      }))
    },
    
    // 获取默认报告数据
    getDefaultReportList() {
      const currentDate = new Date()
      const dateStr = currentDate.toISOString().split('T')[0]
      
      return {
        list: [
          {
            id: 'report_1',
            bridgeName: this.bridgeData.bridgeName || '橘子洲大桥',
            inspectionDate: dateStr,
            inspector: '罗展',
            contactNumber: '18273455512',
            reportGenerateTime: dateStr,
            reportUrl: ''
          },
          {
            id: 'report_2',
            bridgeName: this.bridgeData.bridgeName || '橘子洲大桥',
            inspectionDate: dateStr,
            inspector: '张明',
            contactNumber: '13800138000',
            reportGenerateTime: dateStr,
            reportUrl: ''
          }
        ],
        total: 2
      }
    },
    

    // 查看报告
    handleViewReport(report) {
      console.log('点击查看报告:', report)
      this.$emit('view-report', report)
      this.handleClose()
    },
    
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.reportList = []
    }
  }
}
</script>

<style lang="scss" scoped>
// 引入公共弹框样式
@import '@/styles/components/dialog.scss';
// 引入公共表格样式
@import '@/styles/components/table.scss';

// 针对报告弹框的特定定制
:deep(.inspection-report-dialog) {
  // 继承公共样式，这里可以添加报告特有的样式定制
  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}

// 弹框内容区域样式定制
.dialog-content {
  padding: 0 !important; // 移除内边距，让表格占满整个区域
  background: #091A4B !important; // 确保背景色与弹框一致
  
  // 确保表格容器正确应用样式
  .common-table {
    width: 100%;
    height: 100%;
    
    // 确保表格样式正确应用，重写可能冲突的样式
    :deep(.el-table) {
      background: transparent !important;
      border: none !important;
      margin: 0;
      
      // 表头样式强化
      .el-table__header-wrapper {
        .el-table__header {
          background: transparent !important;
          
          th {
            background: linear-gradient(180deg, #67718F 0%, #7B85A3 100%) !important;
            color: #FFF !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
            border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
            
            .cell {
              color: #FFF !important;
            }
          }
        }
      }
      
      // 表格体样式强化
      .el-table__body-wrapper {
        .el-table__body {
          tr {
            background: transparent !important;
            
            &:hover {
              background: rgba(255, 255, 255, 0.05) !important;
            }
            
            td {
              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
              border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
              border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
              
              .cell {
                color: #FFF !important;
              }
            }
          }
        }
      }
      
      // 移除可能导致重复显示的样式
      &::before,
      &::after {
        display: none !important;
      }
      
      // 移除条纹背景，避免与自定义背景冲突
      .el-table__row--striped {
        background: transparent !important;
        
        td {
          background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
        }
      }
      
      // 操作列按钮样式 - 使用公共样式
      .el-button--text,
      .el-button[type="text"],
      td .el-button {
        color: #409EFF !important;
        background: transparent !important;
        border: none !important;
        padding: 6px 8px !important;
        margin: 0 2px !important;
        height: auto !important;
        min-height: 28px !important;
        font-size: 14px !important;
        line-height: 1.2 !important;
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 999 !important;
        cursor: pointer !important;
        flex-shrink: 0 !important;
        white-space: nowrap !important;
        
        &:hover {
          color: #66b1ff !important;
          background: rgba(64, 158, 255, 0.1) !important;
        }
        
        &:focus {
          color: #409EFF !important;
          background: transparent !important;
        }
      }
    }
    
    // 🚨 关键修复：固定右侧表格的样式
    .el-table__fixed-right {
      background: transparent !important;
      
      // 固定表格的表头
      .el-table__fixed-header-wrapper {
        background: transparent !important;
        
        .el-table__header {
          background: transparent !important;
          
          th {
            background: linear-gradient(180deg, #1E3A8A 0%, #1E40AF 100%) !important;
            color: #FFF !important;
            border: none !important;
          }
        }
      }
      
      // 固定表格的表体 - 这是关键！
      .el-table__fixed-body-wrapper {
        background: transparent !important;
        
        .el-table__body {
          background: transparent !important;
          
          tr {
            background: transparent !important;
            
            td {
              background: linear-gradient(180deg, #243066 0%, #1C2A4E 100%) !important;
              color: #FFF !important;
              border: none !important;
              text-align: center !important;
              vertical-align: middle !important;
              
              // 固定列中的按钮样式 - 统一公共样式
              .el-button--text,
              .el-button[type="text"],
              .el-button {
                color: #409EFF !important;
                background: transparent !important;
                border: none !important;
                padding: 6px 8px !important;
                margin: 0 2px !important;
                height: auto !important;
                min-height: 28px !important;
                font-size: 14px !important;
                line-height: 1.2 !important;
                display: inline-block !important;
                text-align: center !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 999 !important;
                cursor: pointer !important;
                flex-shrink: 0 !important;
                white-space: nowrap !important;
                
                &:hover {
                  color: #66b1ff !important;
                  background: rgba(64, 158, 255, 0.1) !important;
                }
                
                &:focus {
                  color: #409EFF !important;
                  background: transparent !important;
                }
              }
            }
          }
        }
      }
    }
  }
  
  // 🔧 主表格样式保持
  .el-table {
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          td {
            // 针对操作列单元格的特殊处理
            &:last-child {
              text-align: center !important;
              vertical-align: middle !important;
              
              * {
                visibility: visible !important;
                opacity: 1 !important;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计保持兼容
@media (max-width: 768px) {
  .dialog-content {
    .el-table {
      font-size: 12px;
    }
  }
}</style>
