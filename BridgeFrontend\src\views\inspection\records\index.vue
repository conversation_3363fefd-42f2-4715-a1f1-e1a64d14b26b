<template>
  <div class="inspection-records inspection-container">
    <div class="page-container">
      <!-- TAB切换 -->
      <TabSwitch
        v-model="activeTab"
        :tabs="tabOptions"
        @tab-click="handleTabClick"
      />

      <!-- 筛选条件 -->
      <FilterSection
        v-model="searchForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
      >
        <!-- 自定义筛选项 -->
        <template #filters="{ formData, options }">
          <el-select
            v-model="formData.bridgeName"
:placeholder="bridgeNameText"
            clearable
            filterable
            @change="handleBridgeChange"
            class="filter-select"
          >
            <el-option
              v-for="option in bridgeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-select
            v-model="formData.inspectionUnit"
            :placeholder="formData.inspectionUnit ? `巡检单位 | ${getUnitDisplayText()}` : '巡检单位 | 全部'"
            clearable
            filterable
            class="filter-select"
          >
            <el-option
              v-for="option in unitOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <el-select
            v-model="formData.inspectionMonth"
            :placeholder="formData.inspectionMonth ? `巡检月度 | ${getMonthDisplayText()}` : '巡检月度 | 全部'"
            clearable
            filterable
            class="filter-select"
          >
            <el-option label="全部" :value="null"></el-option>
            <el-option
              v-for="option in monthOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
      </FilterSection>

      <!-- 数据表格 -->
      <div class="common-table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :key="tableData.length"
          style="width: 100%"
          @sort-change="handleSortChange"
          row-key="id"
          :row-style="{ height: '32px' }"
          size="small"
        >
          <el-table-column width="80" align="center">
            <template slot="header">
              <span class="table-header-wrap">序号</span>
            </template>
            <template slot-scope="scope">
              <span>{{ String(scope.$index + 1).padStart(3, '0') }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="bridgeName"
:label="bridgeNameText"
            min-width="120"
            show-overflow-tooltip
          />

          <el-table-column
            prop="inspectors"
            label="巡检人员"
            min-width="120"
          >
            <template slot-scope="scope">
              <span>{{ formatInspectors(scope.row.inspectors) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="inspectionUnit"
            label="巡检单位"
            min-width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="contactNumbers"
            label="联系方式"
            min-width="120"
          >
            <template slot-scope="scope">
              <span>{{ formatContactNumbers(scope.row.contactNumbers) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="monthlyInspectionCount"
            width="100"
            align="center"
            sortable="custom"
          >
            <template slot="header">
              <span class="table-header-wrap">本月缺<br/>巡检次数</span>
            </template>
            <template slot-scope="scope">
              <span
                :class="[
                  'missing-patrol-count',
                  scope.row.monthlyInspectionCount === 0 ? 'count-0' :
                  scope.row.monthlyInspectionCount === 1 ? 'count-1' : 'count-2-plus'
                ]"
              >
                {{ scope.row.monthlyInspectionCount }}次
              </span>
            </template>
          </el-table-column>

          <el-table-column
            prop="lastInspectionTime"
            width="160"
            align="center"
            sortable="custom"
          >
            <template slot="header">
              <span class="table-header-wrap">上次<br/>巡检时间</span>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row.lastInspectionTime || '暂无' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="130"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="log-button"
                @click="handleActionClick({ action: 'log', record: scope.row })"
              >
                日志
              </el-button>
              <el-button
                type="text"
                size="small"
                class="report-button"
                @click="handleActionClick({ action: 'report', record: scope.row })"
              >
                报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>


      <!-- 分页 -->
      <div class="pagination-wrapper inspection-pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>

    <!-- 巡检日志日历弹窗 -->
    <InspectionLogDialog
      :visible.sync="logDialogVisible"
      :bridge-data="selectedBridge"
      @inspection-detail="handleInspectionDetail"
    />

    <!-- 巡检报告列表弹窗 -->
    <InspectionReportDialog
      :visible.sync="reportDialogVisible"
      :bridge-data="selectedBridge"
      @view-report="handleViewReport"
    />

    <!-- 巡检详情弹窗 -->
    <InspectionDetailDialog
      :visible.sync="detailDialogVisible"
      :inspection-data="selectedInspection"
      @update:visible="detailDialogVisible = $event"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { TabSwitch, FilterSection, ActionButtons } from '@/components/Inspection'
import InspectionLogDialog from './components/InspectionLogDialog'
import InspectionReportDialog from './components/InspectionReportDialog'
import InspectionDetailDialog from './components/InspectionDetailDialog'

export default {
  name: 'InspectionRecords',
  components: {
    TabSwitch,
    FilterSection,
    ActionButtons,
    InspectionLogDialog,
    InspectionReportDialog,
    InspectionDetailDialog
  },
  data() {
    return {
      // 当前激活的tab
      activeTab: 'bridge',

      // 搜索表单
      searchForm: {
        bridgeName: '',
        inspectionUnit: '',
        inspectionMonth: null
      },

      // 动态的桥梁/隧道名称文本
      bridgeNameText: '桥梁名称',

      // 弹窗状态
      logDialogVisible: false,
      reportDialogVisible: false,
      detailDialogVisible: false,

      // 当前选中的桥梁数据
      selectedBridge: null,
      
      // 当前选中的巡检数据
      selectedInspection: null,

      // 排序参数
      sortParams: {
        prop: '',
        order: ''
      },

      // TAB配置
      tabOptions: [
        {
          name: 'bridge',
          label: '桥梁巡检',
          icon: 'bridge'
        },
        {
          name: 'tunnel',
          label: '隧道巡检',
          icon: 'tunnel'
        }
      ],

      // 筛选配置
      filterConfigs: {}
    }
  },
  computed: {
    ...mapGetters('inspection', [
      'inspectionRecordsList',
      'inspectionPagination',
      'selectOptions',
      'loadingStates'
    ]),

    tableData() {
      return this.inspectionRecordsList || []
    },

    pagination() {
      return this.inspectionPagination || {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    },

    loading() {
      return this.loadingStates.inspectionRecords
    },

    bridgeOptions() {
      return this.selectOptions.bridgeOptions || []
    },

    unitOptions() {
      return this.selectOptions.inspectionUnitOptions || []
    },

    monthOptions() {
      const months = []
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()

      // 生成最近12个月的选项
      for (let i = 0; i < 12; i++) {
        const date = new Date(currentYear, currentDate.getMonth() - i, 1)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const monthStr = month.toString().padStart(2, '0')
        months.push({
          label: `${year}年${monthStr}月`,
          value: `${year}-${monthStr}`
        })
      }

      return months
    }
  },
  async created() {
    // 初始化页面数据
    await this.initPageData()
  },
  methods: {
    ...mapActions('inspection', [
      'fetchInspectionRecords',
      'updateFilters',
      'updatePagination',
      'initSelectOptions'
    ]),

    // 初始化页面数据
    async initPageData() {
      // 初始化下拉选项
      await this.initSelectOptions()

      // 更新筛选条件
      this.updateFilters({
        inspectionType: this.activeTab
      })

      // 获取巡检记录列表
      await this.fetchInspectionRecords()
    },

    // TAB切换
    async handleTabClick(tab) {
      this.activeTab = tab.name
      
      // 更新桥梁/隧道名称文本
      this.bridgeNameText = tab.name === 'tunnel' ? '隧道名称' : '桥梁名称'
      
      this.updateFilters({ inspectionType: this.activeTab })
      await this.fetchInspectionRecords()
    },

    // 桥梁选择变化，联动其他字段
    handleBridgeChange(bridgeId) {
      // 这里可以根据桥梁ID联动加载相关的巡检单位等信息
      // 暂时保持空实现，等接口对接时完善
    },

    // 搜索
    async handleSearch(formData) {
      this.updateFilters({
        bridgeName: formData.bridgeName,
        inspectionUnit: formData.inspectionUnit,
        inspectionMonth: formData.inspectionMonth
      })

      // 重置分页
      this.updatePagination({
        type: 'inspection',
        pageNum: 1,
        pageSize: this.pagination.pageSize
      })

      await this.fetchInspectionRecords()
    },

    // 重置搜索
    async handleReset(resetData) {
      this.searchForm = { ...resetData }

      this.updateFilters({
        bridgeName: '',
        inspectionUnit: '',
        inspectionMonth: null
      })

      // 重置分页
      this.updatePagination({
        type: 'inspection',
        pageNum: 1,
        pageSize: 10
      })

      await this.fetchInspectionRecords()
    },

    // 排序变化
    async handleSortChange({ prop, order }) {
      this.sortParams = { prop, order }
      await this.fetchInspectionRecords({ sortBy: prop, sortOrder: order })
    },

    // 分页大小变化
    async handleSizeChange(size) {
      this.updatePagination({
        type: 'inspection',
        pageNum: 1,
        pageSize: size
      })
      await this.fetchInspectionRecords()
    },

    // 页码变化
    async handleCurrentChange(page) {
      this.updatePagination({
        type: 'inspection',
        pageNum: page,
        pageSize: this.pagination.pageSize
      })
      await this.fetchInspectionRecords()
    },

    // 操作按钮点击
    handleActionClick({ action, record }) {
      this.selectedBridge = record

      switch (action) {
        case 'log':
          this.logDialogVisible = true
          break
        case 'report':
          this.reportDialogVisible = true
          break
        case 'detail':
          this.handleViewDetail(record)
          break
        default:
          console.log('未知操作:', action)
      }
    },

    // 查看详情
    handleViewDetail(record) {
      this.$router.push({
        name: 'InspectionDetail',
        params: { id: record.id }
      })
    },


    // 查看报告
    handleViewReport(reportData) {
      this.$router.push({
        name: 'InspectionReportDetail',
        params: { id: reportData.id }
      })
    },

    // 格式化巡检人员显示
    formatInspectors(inspectors) {
      if (Array.isArray(inspectors)) {
        return inspectors.join('，')
      }
      return inspectors || ''
    },

    // 格式化联系方式显示
    formatContactNumbers(contactNumbers) {
      if (Array.isArray(contactNumbers)) {
        return contactNumbers.join('，')
      }
      return contactNumbers || ''
    },

    // 获取巡检单位显示文本
    getUnitDisplayText() {
      if (!this.searchForm.inspectionUnit) return '全部'
      const unit = this.unitOptions.find(option => option.value === this.searchForm.inspectionUnit)
      return unit ? unit.label : '全部'
    },

    // 获取巡检月度显示文本
    getMonthDisplayText() {
      if (!this.searchForm.inspectionMonth && this.searchForm.inspectionMonth !== 0) return '全部'
      const month = this.monthOptions.find(option => option.value === this.searchForm.inspectionMonth)
      return month ? month.label : '全部'
    },

    // 处理巡检详情事件
    handleInspectionDetail(inspectionData) {
      
      // 确保有桥梁数据
      if (!this.selectedBridge) {
        console.error('❌ [InspectionRecords] 没有选中的桥梁数据')
        this.$message.warning('请先选择桥梁')
        return
      }

      // 组装巡检数据，包含桥梁信息
      this.selectedInspection = {
        ...inspectionData,
        bridgeData: this.selectedBridge
      }
      
      // 显示巡检详情弹窗
      this.detailDialogVisible = true
    },

    // 查看病害详情
    handleDiseaseDetail(diseaseData) {
      console.log('查看病害详情:', diseaseData)
      // 这里可以添加查看病害详情的逻辑
    }

  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式
@import '@/styles/inspection-theme.scss';

.inspection-records {
  .page-container {
    .pagination-wrapper {
      margin-top: auto; // 自动推到底部
      padding: 16px 0;
      display: flex;
      justify-content: center;
      flex-shrink: 0; // 防止被压缩
      background: var(--inspection-bg-primary);
      border-radius: 4px;
      margin-bottom: 8px;
    }
  }

  // 表头换行样式
  .table-header-wrap {
    line-height: 1.4;
    word-break: keep-all;
    white-space: pre-line;
  }
}</style>
