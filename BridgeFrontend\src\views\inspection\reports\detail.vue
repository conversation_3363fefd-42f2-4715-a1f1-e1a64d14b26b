<template>
  <div class="inspection-report-detail inspection-container">
    <div v-loading="loading" class="page-container">
      <!-- 报告操作栏 -->
      <div class="report-actions">
        <el-button type="primary" size="small" icon="el-icon-download" :loading="downloading" @click="handleDownload">
          下载报告
        </el-button>
      </div>

      <!-- 报告内容卡片 -->
      <el-card class="report-card" shadow="never">
        <div class="report-content" ref="reportContent">
          <!-- 报告标题 -->
          <div class="report-title">
            <h2>桥梁巡检报告</h2>
            <div class="report-meta">
              <span>报告编号：{{ reportDetail.reportCode || 'R' + Date.now() }}</span>
              <span>生成时间：{{ reportDetail.generateTime || getCurrentTime() }}</span>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section">
            <el-row :gutter="40">
              <el-col :span="12">
                <div class="info-item">
                  <label>桥梁名称</label>
                  <span>{{ reportDetail.bridgeName || '人行天桥' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>巡检日期</label>
                  <span>{{ reportDetail.inspectionDate || getCurrentDate() }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 巡检项目检查表 -->
          <div class="common-table-section">
            <h3>巡检项目检查情况</h3>
            <div class="common-table">
              <el-table
                :data="inspectionItems"
                border
                style="width: 100%"
              >
              <el-table-column prop="itemName" label="检查项" width="120" />
              <el-table-column prop="isIntact" label="是否完好" width="100" align="center">
                <template slot-scope="scope">
                  <span class="status-text" :class="{ 'status-good': scope.row.isIntact }">
                    {{ scope.row.isIntact ? '是' : '否' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="diseaseType" label="病害类型" width="120" />
              <el-table-column prop="diseaseCount" label="病害数量" width="100" align="center" />
              <el-table-column prop="diseasePart" label="病害部位" width="120" />
              <el-table-column prop="diseaseDescription" label="病害描述" min-width="200" />
              </el-table>
            </div>
          </div>

          <!-- 其他问题 -->
          <div class="other-issues-section">
            <h3>其他危及行人、行船、行车安全病害</h3>
            <div class="issue-content">
              <p>{{ reportDetail.safetyIssues || '无特殊安全隐患发现。' }}</p>
            </div>
          </div>

          <!-- 保护区域施工情况 -->
          <div class="protection-area-section">
            <h3>桥梁保护区域内施工</h3>
            <div class="issue-content">
              <p>{{ reportDetail.constructionIssues || '保护区域内无违规施工行为。' }}</p>
            </div>
          </div>

          <!-- 巡检人员信息 -->
          <div class="inspector-info-section">
            <el-row :gutter="40">
              <el-col :span="12">
                <div class="info-item">
                  <label>巡查人</label>
                  <span>{{ reportDetail.inspector || '胡艳红' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>巡检日期</label>
                  <span>{{ reportDetail.inspectionDate || getCurrentDate() }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 巡检图片（如果有） -->
          <div v-if="reportDetail.inspectionImages && reportDetail.inspectionImages.length > 0" class="images-section">
            <h3>巡检图片</h3>
            <ImageViewer
              :images="reportDetail.inspectionImages"
              :grid-type="4"
              :show-upload="false"
              :show-delete="false"
            />
          </div>

          <!-- 报告签名区域 -->
          <div class="signature-section">
            <el-row :gutter="60">
              <el-col :span="8">
                <div class="signature-item">
                  <label>巡检人员签名：</label>
                  <div class="signature-line">{{ reportDetail.inspector || '胡艳红' }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="signature-item">
                  <label>项目负责人签名：</label>
                  <div class="signature-line">{{ reportDetail.projectManager || '张明' }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="signature-item">
                  <label>日期：</label>
                  <div class="signature-line">{{ reportDetail.inspectionDate || getCurrentDate() }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" size="small" round icon="el-icon-arrow-left" @click="handleGoBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { saveAs } from 'file-saver'
import { getToken } from '@/utils/auth'
import { blobValidate } from '@/utils/ruoyi'
import { getInspectionReportDetail } from '@/api/inspection/records'
import { ImageViewer } from '@/components/Inspection'

export default {
  name: 'InspectionReportDetail',
  components: {
    ImageViewer
  },
  data() {
    return {
      loading: false,
      reportDetail: {},
      downloading: false,
      
      // 巡检项目数据
      inspectionItems: [
        {
          itemName: '抗震设施',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '附属设施',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '支座',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '人行道',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '栏杆和护栏',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '桥路连接位置',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '桥面铺装',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '排水系统',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '伸缩装置',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '下部结构',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        },
        {
          itemName: '上部结构',
          isIntact: true,
          diseaseType: '',
          diseaseCount: '',
          diseasePart: '',
          diseaseDescription: ''
        }
      ]
    }
  },
  computed: {
    reportId() {
      return this.$route.params.id
    }
  },
  async created() {
    await this.loadReportDetail()
  },
  methods: {
    // 加载报告详情
    async loadReportDetail() {
      this.loading = true
      
      try {
        const response = await getInspectionReportDetail(this.reportId)
        this.reportDetail = response.data || this.getDefaultReportDetail()
        
        // 更新巡检项目数据
        if (this.reportDetail.inspectionItems) {
          this.inspectionItems = this.reportDetail.inspectionItems
        }
        
      } catch (error) {
        console.error('加载报告详情失败:', error)
        this.$message.error('加载报告详情失败')
        
        // 使用默认数据
        this.reportDetail = this.getDefaultReportDetail()
      } finally {
        this.loading = false
      }
    },
    
    // 获取默认报告详情
    getDefaultReportDetail() {
      return {
        reportCode: `R${Date.now()}`,
        bridgeName: '人行天桥',
        inspectionDate: this.getCurrentDate(),
        inspector: '胡艳红',
        projectManager: '张明',
        generateTime: this.getCurrentTime(),
        safetyIssues: '无特殊安全隐患发现。',
        constructionIssues: '保护区域内无违规施工行为。',
        inspectionImages: []
      }
    },
    
    // 获取当前日期
    getCurrentDate() {
      return new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).replace(/\//g, '-')
    },
    
    // 获取当前时间
    getCurrentTime() {
      return new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
    },
    
    // 下载报告
    async handleDownload() {
      if (this.downloading) return
      this.downloading = true
      try {
        const { data: blob, headers } = await axios({
          method: 'get',
          url: `${process.env.VUE_APP_BASE_API}/api/inspection/reports/${this.reportId}/download`,
          responseType: 'blob',
          headers: { Authorization: 'Bearer ' + getToken() }
        })
        const isBlob = await blobValidate(blob)
        if (!isBlob) {
          const resText = await blob.text()
          const rspObj = JSON.parse(resText)
          const msg = rspObj.msg || '下载失败'
          this.$message.error(msg)
          return
        }
        const headerName = headers && (headers['download-filename'] || headers['content-disposition'])
        let filename = this.reportDetail.reportCode ? `${this.reportDetail.reportCode}.pdf` : `inspection-report-${this.reportId}.pdf`
        if (headerName) {
          const dispo = decodeURIComponent(headerName)
          const match = /filename\*=UTF-8''([^;]+)|filename="?([^";]+)"?/i.exec(dispo)
          const fromHeader = match && (match[1] || match[2])
          if (fromHeader) filename = fromHeader
        }
        saveAs(new Blob([blob]), filename)
      } catch (e) {
        console.error(e)
        this.$message.error('下载文件出现错误，请联系管理员！')
      } finally {
        this.downloading = false
      }
    },

    
    
    // 返回
    handleGoBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式
@import '@/styles/inspection-theme.scss';

.inspection-report-detail {
  
  .page-container {
    .report-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-bottom: 16px;
      margin-top: 24px; // 向下移动下载按钮
      
      .el-button {
        margin-left: 0;
      }
    }
    
    .report-card {
      margin-bottom: 20px;
      
      .report-content {
        .report-title {
          text-align: center;
          margin-bottom: 40px;
          
          h2 {
            margin: 0 0 16px 0;
            font-size: 24px;
            color: var(--inspection-text-primary);
          }
          
          .report-meta {
            font-size: 14px;
            color: var(--inspection-text-secondary);
            
            span {
              margin: 0 20px;
            }
          }
        }
        
        .basic-info-section {
          margin: 40px 0;
          
          .info-item {
            display: flex;
            align-items: center;
            margin: 16px 0;
            
            label {
              font-weight: 600;
              color: var(--inspection-text-primary);
              margin-right: 16px;
              min-width: 80px;
            }
            
            span {
              color: var(--inspection-text-secondary);
            }
          }
        }
        
          .common-table-section {
          margin: 40px 0;
          
          h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--inspection-text-primary);
          }
          
          .common-table {
            .status-text {
              font-weight: 500;
              
              &.status-good {
                color: #67c23a;
              }
            }
          }
        }
        
        .other-issues-section,
        .protection-area-section {
          margin: 40px 0;
          
          h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--inspection-text-primary);
          }
          
          .issue-content {
            padding: 16px;
            background: var(--inspection-card-bg);
            border-radius: 6px;
            border: 1px solid #e9ecef;
            
            p {
              margin: 0;
              color: var(--inspection-text-secondary);
              line-height: 1.6;
            }
          }
        }
        
        .inspector-info-section {
          margin: 40px 0;
          
          .info-item {
            display: flex;
            align-items: center;
            margin: 16px 0;
            
            label {
              font-weight: 600;
              color: var(--inspection-text-primary);
              margin-right: 16px;
              min-width: 80px;
            }
            
            span {
              color: var(--inspection-text-secondary);
            }
          }
        }
        
        .images-section {
          margin: 40px 0;
          
          h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--inspection-text-primary);
          }
        }
        
        .signature-section {
          margin-top: 60px;
          
          .signature-item {
            text-align: center;
            margin: 30px 0;
            
            label {
              display: inline-block;
              margin-bottom: 20px;
              font-weight: 500;
              color: var(--inspection-text-primary);
            }
            
            .signature-line {
              border-bottom: 1px solid var(--inspection-text-secondary);
              display: inline-block;
              min-width: 150px;
              text-align: center;
              padding: 8px 0;
              color: var(--inspection-text-secondary);
            }
          }
        }
      }
    }
    
    .action-buttons {
      text-align: center;
      padding: 16px 0 24px;
      
      .el-button {
        min-width: 88px;
      }
    }
  }
}

.preview-container {
  height: 75vh;
  .preview-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  .preview-image {
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: 0 auto;
  }
}

// 打印样式
@media print {
  .inspection-report-detail {
    .page-header,
    .report-actions,
    .action-buttons {
      display: none !important;
    }
    
    .report-card {
      box-shadow: none !important;
      border: none !important;
    }
  }
}
</style>
