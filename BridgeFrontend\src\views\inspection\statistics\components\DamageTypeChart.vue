<template>
  <div class="damage-type-chart">
    <div 
      ref="chartContainer" 
      :style="{ width: '100%', height: height }"
      v-loading="loading"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DamageType<PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 响应式处理
      window.addEventListener('resize', this.handleResize)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    updateChart() {
      if (!this.chart) return

      const data = this.formatChartData()
      const totalCount = data.reduce((sum, item) => sum + item.value, 0)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          show: true,
          orient: 'vertical',
          right: '5%',
          top: 'middle',
          itemWidth: 18,
          itemHeight: 14,
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          },
          formatter: function(name) {
            const item = data.find(d => d.name === name)
            return name + '  ' + (item ? item.value : '')
          }
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: totalCount.toString(),
            fontSize: 28,
            fontWeight: 'bold',
            fill: '#ffffff'
          }
        },
        series: [
          {
            name: '病害类型',
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['35%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold',
                color: '#ffffff'
              }
            },
            data: data
          }
        ],
        color: ['#40E0D0', '#FFD700', '#FF8C00', '#FF6B6B', '#9370DB']
      }

      this.chart.setOption(option, true)
    },
    
    formatChartData() {
      if (!this.chartData || this.chartData.length === 0) {
        return [
          { value: 581, name: '抗浮' },
          { value: 140, name: '下沉' },
          { value: 95, name: 'TOP' },
          { value: 80, name: '排包' },
          { value: 65, name: 'a581' }
        ]
      }
      
      return this.chartData.map(item => ({
        value: item.count || item.value,
        name: item.type || item.name
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
// 图表外框容器样式 - 与筛选区域样式一致
.damage-type-chart {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理
  min-height: 320px !important; // 🔧 与右侧容器设置保持一致
  height: 100% !important; // 🔧 使用100%高度适应父容器
  width: 100% !important;
  position: relative;
  display: flex;
  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局
  overflow: hidden; // 🔧 确保内容不会溢出边框
  
  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #2A3B6B;
    border-top-right-radius: 10px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    pointer-events: none;
    z-index: 2;
    // 只在左上角和右下角添加亮边框，与筛选区域保持一致
    background:
      // 左上角亮边框
      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      // 右下角亮边框
      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;
    background-position: top left, top left, bottom right, bottom right;
    background-repeat: no-repeat;
  }

  // 图表内容容器样式
  div[ref="chartContainer"] {
    position: relative;
    z-index: 3; // 确保图表在伪元素之上
    width: 100% !important;
    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%
    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间
  }
}
</style>
