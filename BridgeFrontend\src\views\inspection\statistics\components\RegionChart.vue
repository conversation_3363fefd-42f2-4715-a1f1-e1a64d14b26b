<template>
  <div class="chart-wrapper">
    <div 
      ref="regionChart"
      :style="{ width: '100%', height: height }"
      v-loading="loading"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RegionChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chartInstance: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      const element = this.$refs.regionChart
      if (element) {
        this.chartInstance = echarts.init(element)
        this.updateChart()
      }
    },
    
    // 更新图表
    updateChart() {
      if (!this.chartInstance) return
      
      const data = this.getChartData()
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = `${params[0].name}<br/>`
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['计划巡检', '实际巡检'],
          right: '10%',
          top: '2%',
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          }
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '15%', // 🔧 减少底部边距，给图表更多空间
          top: '15%', // 🔧 适度增加顶部边距，为图例留出空间
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.regions,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            interval: 0,
            rotate: 0, // 🔧 不旋转标签，保持水平显示
            color: '#ffffff',
            fontSize: 11,
            margin: 8, // 🔧 减少标签与轴的距离
            textStyle: {
              baseline: 'top'
            }
          },
          nameTextStyle: {
            color: '#ffffff'
          }
        },
        yAxis: {
          type: 'value',
          name: '数量',
          min: 0,
          max: 200,
          interval: 20,
          nameTextStyle: {
            color: '#ffffff'
          },
          axisLabel: {
            formatter: '{value}',
            color: '#ffffff'
          }
        },
        series: [
          {
            name: '计划巡检',
            type: 'bar',
            data: data.plannedData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#87CEEB'
                }, {
                  offset: 1, color: '#B0E0E6'
                }]
              }
            },
            barWidth: '30%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontSize: 12,
              color: '#ffffff'
            }
          },
          {
            name: '实际巡检',
            type: 'bar',
            data: data.actualData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#4682B4'
                }, {
                  offset: 1, color: '#5F9EA0'
                }]
              }
            },
            barWidth: '30%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontSize: 12,
              color: '#ffffff'
            }
          }
        ]
      }
      
      this.chartInstance.setOption(option, true)
      
      // 强制重新渲染图表
      setTimeout(() => {
        if (this.chartInstance) {
          this.chartInstance.resize()
        }
      }, 100)
    },
    
    // 获取图表数据
    getChartData() {
      if (!this.chartData || this.chartData.length === 0) {
        return this.getDefaultData()
      }
      
      const regions = this.chartData.map(item => item.region)
      const plannedData = this.chartData.map(item => item.planned || 0)
      const actualData = this.chartData.map(item => item.actual || 0)
      
      return { regions, plannedData, actualData }
    },
    
    // 获取默认数据
    getDefaultData() {
      return {
        regions: ['岳麓区', '开福区', '天心区', '芙蓉区', '雨花区'],
        plannedData: [140, 180, 120, 120, 180],
        actualData: [37, 128, 94, 60, 18]
      }
    },
    
    // 窗口大小变化处理
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 图表外框容器样式 - 与筛选区域样式一致
.chart-wrapper {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理
  min-height: 200px !important; // 🔧 减少最小高度，适应flex布局
  height: 100% !important; // 🔧 使用100%高度适应父容器
  width: 100% !important;
  position: relative;
  display: flex;
  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局
  overflow: hidden; // 🔧 确保内容不会溢出边框
  
  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #2A3B6B;
    border-top-right-radius: 10px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    pointer-events: none;
    z-index: 2;
    // 只在左上角和右下角添加亮边框，与筛选区域保持一致
    background:
      // 左上角亮边框
      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      // 右下角亮边框
      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;
    background-position: top left, top left, bottom right, bottom right;
    background-repeat: no-repeat;
  }
}

// 图表内容容器样式
div[ref="regionChart"] {
  position: relative;
  z-index: 3; // 确保图表在伪元素之上
  width: 100% !important;
  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%
  min-height: 200px; // 🔧 减少最小高度，适应更紧凑的布局
}
</style>
