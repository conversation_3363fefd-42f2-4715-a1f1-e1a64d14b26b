<template>
  <div class="statistics-cards">
    <el-row :gutter="20">
      <!-- 应巡次数 -->
      <el-col :span="6">
        <el-card class="statistic-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon should-inspect">
                <svg-icon icon-class="statistics1" />
              </div>
              <div class="card-info">
                <h4>应巡次数（养护公司）</h4>
                <p class="card-value">{{ statisticsData.shouldInspectCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 实巡次数 -->
      <el-col :span="6">
        <el-card class="statistic-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon actual-inspect">
                <svg-icon icon-class="statistics2" />
              </div>
              <div class="card-info">
                <h4>实巡次数（养护公司）</h4>
                <p class="card-value">{{ statisticsData.actualInspectCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 巡检完成率 -->
      <el-col :span="6">
        <el-card class="statistic-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon completion-rate">
                <svg-icon icon-class="statistics3" />
              </div>
              <div class="card-info">
                <h4>巡检完成率（养护公司）</h4>
                <p class="card-value">{{ statisticsData.completionRate || 0 }}%</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 桥梁中心巡检次数 -->
      <el-col :span="6">
        <el-card class="statistic-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon center-inspect">
                <svg-icon icon-class="statistics1" />
              </div>
              <div class="card-info">
                <h4>桥梁中心巡检次</h4>
                <p class="card-value">{{ statisticsData.centerInspectCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 病害统计行 -->
    <el-row :gutter="20" class="disease-stats-row">
      <!-- 病害总量 -->
      <el-col :span="6">
        <el-card class="statistic-card disease-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon total-damage">
                <svg-icon icon-class="statistics2" />
              </div>
              <div class="card-info">
                <h4>病害总量</h4>
                <p class="card-value">{{ statisticsData.totalDamageCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 未处理病害 -->
      <el-col :span="6">
        <el-card class="statistic-card disease-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon unprocessed-damage">
                <svg-icon icon-class="statistics3" />
              </div>
              <div class="card-info">
                <h4>未处理病害</h4>
                <p class="card-value">{{ statisticsData.unprocessedCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 处理中病害 -->
      <el-col :span="6">
        <el-card class="statistic-card disease-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon disposing-damage">
                <svg-icon icon-class="statistics1" />
              </div>
              <div class="card-info">
                <h4>处理中病害</h4>
                <p class="card-value">{{ statisticsData.disposingCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 已处理病害 -->
      <el-col :span="6">
        <el-card class="statistic-card disease-card" shadow="hover">
          <div v-loading="loading" class="card-content">
            <div class="card-header">
              <div class="card-icon processed-damage">
                <svg-icon icon-class="statistics2" />
              </div>
              <div class="card-info">
                <h4>已处理病害</h4>
                <p class="card-value">{{ statisticsData.processedCount || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'StatisticsCards',
  props: {
    statisticsData: {
      type: Object,
      default: () => ({
        shouldInspectCount: 0,
        actualInspectCount: 0,
        completionRate: 0,
        centerInspectCount: 0,
        totalDamageCount: 0,
        unprocessedCount: 0,
        disposingCount: 0,
        processedCount: 0
      })
    },
    loading: {
      type: Boolean,
      default: false
    },
  }
}
</script>

<style lang="scss" scoped>
.statistics-cards {
  // 统一统计卡片区域的内边距，与TAB和筛选区域保持一致
  padding: 14px 0;
  
  .disease-stats-row {
    margin-top: 20px;
  }
  
  .statistic-card {
    transition: all 0.3s ease;
    
    // 使用桥梁巡检按钮样式的背景和边框
    background: linear-gradient(135deg, #334067 0%, #2a3558 100%) !important;
    border: none !important;
    border-radius: 10px !important;
    
    // 模拟桥梁巡检按钮的特殊边框效果
    box-shadow:
      // 左边框
      inset 1px 0 0 0 rgba(255, 255, 255, 0.25),
      // 上边框  
      inset 0 1px 0 0 rgba(255, 255, 255, 0.25),
      // 右边框
      inset -1px 0 0 0 rgba(255, 255, 255, 0.25),
      // 下边框
      inset 0 -1px 0 0 rgba(255, 255, 255, 0.25),
      // 外部阴影效果
      0 2px 8px rgba(0, 123, 255, 0.2) !important;
    
    // 相对定位以支持伪元素
    position: relative;
    
    // 使用伪元素遮挡右上角和左下角的边框，与TAB按钮效果一致
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      right: -1px;
      width: 12px;
      height: 12px;
      background: #2a3558; // 渐变的结束色
      border-top-right-radius: 10px;
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: -1px;
      width: 12px;
      height: 12px;
      background: #2a3558; // 渐变的结束色
      border-bottom-left-radius: 10px;
      z-index: 1;
    }
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .card-content {
      .card-header {
        display: flex;
        align-items: flex-start;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%; // 改为圆形
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            font-size: 30px;
            color: #fff;
          }

          .svg-icon {
            width: 30px;
            height: 30px;
            color: #fff;
          }
          
          // 不同类型的图标颜色
          &.should-inspect {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.actual-inspect {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.completion-rate {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.center-inspect {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
          
          &.total-damage {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
          }
          
          &.unprocessed-damage {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
          
          &.disposing-damage {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          }
          
          &.processed-damage {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
          }
        }
        
        .card-info {
          flex: 1;
          
          h4 {
            margin: 0 0 8px 0;
            color: #FFF !important;
            font-family: "PingFang SC" !important;
            font-size: 16px !important;
            font-style: normal !important;
            font-weight: 500 !important;
            line-height: 140% !important; /* 22.4px */
          }
          
          .card-value {
            margin: 0;
            color: #FFF !important;
            font-family: YouSheBiaoTiHei !important;
            font-size: 40px !important;
            font-style: normal !important;
            font-weight: 400 !important;
            line-height: 140% !important; /* 56px */
          }
        }
      }
    }
    
    // 病害卡片特殊样式
    &.disease-card {
      .card-content .card-header .card-info .card-value {
        color: #FFF !important;
        font-family: YouSheBiaoTiHei !important;
        font-size: 40px !important;
        font-style: normal !important;
        font-weight: 400 !important;
        line-height: 140% !important; /* 56px */
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .statistics-cards {
    .el-col {
      margin-bottom: 16px;
    }
    
    .statistic-card {
      .card-content .card-header {
        .card-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%; // 确保在小屏幕上也是圆形
          margin-right: 12px;
          
          i {
            font-size: 25px;
          }

          .svg-icon {
            width: 25px;
            height: 25px;
            color: #fff;
          }
        }
        
        .card-info {
          h4 {
            font-size: 13px;
          }
          
          .card-value {
            font-size: 24px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .statistics-cards {
    .statistic-card {
      .card-content .card-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        
        .card-icon {
          margin-right: 0;
          margin-bottom: 12px;
        }
        
        .card-info {
          .card-value {
            font-size: 32px;
          }
        }
      }
    }
  }
}</style>
