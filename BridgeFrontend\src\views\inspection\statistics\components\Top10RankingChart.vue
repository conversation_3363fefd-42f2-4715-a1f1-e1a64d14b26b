<template>
  <div class="top10-ranking-chart" v-loading="loading">
    <div class="ranking-list">
      <div
        v-for="(item, index) in displayData"
        :key="index"
        class="ranking-item"
        :class="{ 'top-three': index < 3 }"
      >
        <div class="rank-badge" :class="`rank-${index + 1}`">
          TOP{{ index + 1 }}
        </div>
        <div class="bridge-info">
          <span class="bridge-name">{{ item.bridgeName || item.name }}</span>
          <div class="progress-container">
            <div 
              class="progress-bar"
              :style="{ width: `${getProgressWidth(item.count || item.value)}%` }"
            ></div>
          </div>
        </div>
        <span class="count">{{ item.count || item.value }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Top10RankingChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: <PERSON>olean,
      default: false
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  computed: {
    displayData() {
      if (!this.chartData || this.chartData.length === 0) {
        return [
          { bridgeName: 'XXXX病害', count: 932 },
          { bridgeName: 'XXXX病害', count: 899 },
          { bridgeName: 'XXXX病害', count: 702 },
          { bridgeName: 'XXXX病害', count: 543 },
          { bridgeName: 'XXXX病害', count: 208 }
        ]
      }
      return this.chartData.slice(0, 5)
    },
    maxCount() {
      if (this.displayData.length === 0) return 1
      return Math.max(...this.displayData.map(item => item.count || item.value || 0))
    }
  },
  methods: {
    getProgressWidth(count) {
      if (this.maxCount === 0) return 0
      return Math.max((count / this.maxCount) * 100, 5) // 最小宽度5%
    }
  }
}
</script>

<style lang="scss" scoped>
// 图表外框容器样式 - 与筛选区域样式一致
.top10-ranking-chart {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理
  min-height: 320px !important; // 🔧 与右侧容器设置保持一致
  height: 100% !important; // 🔧 使用100%高度适应父容器
  width: 100% !important;
  position: relative;
  display: flex;
  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局
  overflow: hidden; // 🔧 确保内容不会溢出边框
  
  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #2A3B6B;
    border-top-right-radius: 10px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    pointer-events: none;
    z-index: 2;
    // 只在左上角和右下角添加亮边框，与筛选区域保持一致
    background:
      // 左上角亮边框
      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      // 右下角亮边框
      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;
    background-position: top left, top left, bottom right, bottom right;
    background-repeat: no-repeat;
  }
  
  .ranking-list {
    position: relative;
    z-index: 3; // 确保内容在伪元素之上
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%
    min-height: 280px; // 🔧 与容器设置协调，减去header和padding空间
    justify-content: center;
    
    .ranking-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 0;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: rgba(64, 158, 255, 0.05);
        border-radius: 4px;
        transform: translateX(2px);
      }
      
      &.top-three {
        .rank-badge {
          background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
          color: #fff;
          font-weight: bold;
        }
      }
      
      .rank-badge {
        width: 50px;
        height: 20px;
        border-radius: 10px;
        background: #40E0D0;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: 600;
        flex-shrink: 0;
        
        &.rank-1 {
          background: #40E0D0;
          color: #fff;
        }
        
        &.rank-2 {
          background: #40E0D0;
          color: #fff;
        }
        
        &.rank-3 {
          background: #40E0D0;
          color: #fff;
        }
        
        &.rank-4 {
          background: #40E0D0;
          color: #fff;
        }
        
        &.rank-5 {
          background: #40E0D0;
          color: #fff;
        }
      }
      
      .bridge-info {
        flex: 1;
        min-width: 0;
        
        .bridge-name {
          display: block;
          font-size: 12px;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 2px;
        }
        
        .progress-container {
          width: 100%;
          height: 6px;
          background-color: #f5f7fa;
          border-radius: 3px;
          overflow: hidden;
          
          .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);
            border-radius: 3px;
            transition: width 0.6s ease;
            animation: progressAnimation 1s ease-out;
          }
        }
      }
      
      .count {
        font-size: 11px;
        color: #ffffff;
        font-weight: 600;
        flex-shrink: 0;
        min-width: 20px;
        text-align: right;
      }
    }
  }
}

@keyframes progressAnimation {
  from {
    width: 0%;
  }
  to {
    width: var(--target-width);
  }
}

// 响应式优化
@media (max-width: 768px) {
  .top10-ranking-chart {
    .ranking-list {
      .ranking-item {
        .bridge-info {
          .bridge-name {
            font-size: 11px;
          }
        }
        
        .count {
          font-size: 10px;
        }
      }
    }
  }
}
</style>
