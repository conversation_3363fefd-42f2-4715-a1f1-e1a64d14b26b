<template>
  <div class="chart-wrapper">
    <div 
      ref="trendChart"
      :style="{ width: '100%', height: height }"
      v-loading="loading"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Trend<PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    chartType: {
      type: String,
      default: 'line' // line | bar
    },
    height: {
      type: String,
      default: '400px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chartInstance: null
    }
  },
  mounted() {
    this.initChart()
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart()
      },
      deep: true
    },
    chartType() {
      this.updateChart()
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      if (this.$refs.trendChart) {
        this.chartInstance = echarts.init(this.$refs.trendChart)
        this.updateChart()
      }
    },
    
    // 更新图表
    updateChart() {
      if (!this.chartInstance) return
      
      const data = this.getChartData()
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['市府区', '美湖区', '龙泉区', '开福区', '芙蓉区'],
          top: '2%',
          textStyle: {
            color: '#ffffff',
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          top: '12%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: this.chartType === 'bar',
            data: data.categories,
            axisLabel: {
              color: '#ffffff'
            },
            nameTextStyle: {
              color: '#ffffff'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '次数',
            max: 900,
            interval: 100,
            nameTextStyle: {
              color: '#ffffff'
            },
            axisLabel: {
              formatter: '{value}',
              color: '#ffffff'
            }
          }
        ],
        series: [
          {
            name: '市府区',
            type: 'line',
            smooth: true,
            data: data.districtData.shifuqu,
            itemStyle: {
              color: '#40E0D0'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: '美湖区',
            type: 'line',
            smooth: true,
            data: data.districtData.meihuqu,
            itemStyle: {
              color: '#FFD700'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: '龙泉区',
            type: 'line',
            smooth: true,
            data: data.districtData.longquanqu,
            itemStyle: {
              color: '#FFFFFF'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: '开福区',
            type: 'line',
            smooth: true,
            data: data.districtData.kaifuqu,
            itemStyle: {
              color: '#FF6B6B'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: '芙蓉区',
            type: 'line',
            smooth: true,
            data: data.districtData.furongqu,
            itemStyle: {
              color: '#FF8C00'
            },
            lineStyle: {
              width: 2
            }
          }
        ]
      }
      
      this.chartInstance.setOption(option, true)
    },
    
    // 获取图表数据
    getChartData() {
      if (!this.chartData || this.chartData.length === 0) {
        return this.getDefaultData()
      }
      
      // 如果有传入数据，处理为区域数据格式
      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]
      
      // 如果chartData有区域数据，使用传入的数据
      const districtData = {
        shifuqu: this.chartData.shifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],
        meihuqu: this.chartData.meihuqu || [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],
        longquanqu: this.chartData.longquanqu || [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],
        kaifuqu: this.chartData.kaifuqu || [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],
        furongqu: this.chartData.furongqu || [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]
      }
      
      return {
        categories,
        districtData
      }
    },
    
    // 获取默认数据
    getDefaultData() {
      const categories = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6]
      
      const districtData = {
        shifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],
        meihuqu: [100, 120, 180, 210, 250, 280, 320, 410, 500, 600, 780, 830, 900],
        longquanqu: [150, 200, 320, 350, 420, 450, 500, 520, 550, 580, 620, 650, 680],
        kaifuqu: [240, 310, 580, 630, 660, 750, 770, 760, 650, 850, 900, 920, 940],
        furongqu: [200, 250, 580, 600, 650, 680, 720, 750, 780, 820, 860, 890, 920]
      }
      
      return {
        categories,
        districtData
      }
    },
    
    // 窗口大小变化处理
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 图表外框容器样式 - 与筛选区域样式一致
.chart-wrapper {
  background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 10px !important;
  padding: 0 20px 14px 20px !important; // 🔧 移除顶部padding，由外部chart-header处理
  min-height: 320px !important; // 🔧 与DamageTypeChart保持一致的最小高度
  height: 100% !important; // 🔧 使用100%高度适应父容器
  width: 100% !important;
  position: relative;
  display: flex;
  flex-direction: column; // 🔧 改为列方向，为图表提供更好的布局
  overflow: hidden; // 🔧 确保内容不会溢出边框
  
  // 使用伪元素实现左上角和右下角的亮边框效果，与筛选区域风格一致
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #2A3B6B;
    border-top-right-radius: 10px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    pointer-events: none;
    z-index: 2;
    // 只在左上角和右下角添加亮边框，与筛选区域保持一致
    background:
      // 左上角亮边框
      linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      // 右下角亮边框
      linear-gradient(270deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
    background-size: 30px 1px, 1px 30px, 30px 1px, 1px 30px;
    background-position: top left, top left, bottom right, bottom right;
    background-repeat: no-repeat;
  }
}

// 图表内容容器样式
div[ref="trendChart"] {
  position: relative;
  z-index: 3; // 确保图表在伪元素之上
  width: 100% !important;
  flex: 1; // 🔧 使用flex占满剩余空间，替代height: 100%
  min-height: 280px; // 🔧 与DamageTypeChart内部图表保持一致的最小高度
}
</style>
