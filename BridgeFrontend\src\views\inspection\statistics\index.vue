<template>
  <div class="inspection-statistics inspection-container">
    <div class="page-container">
      <!-- TAB切换 -->
      <el-tabs
        v-model="activeTab"
        @tab-click="handleTabClick"
        class="statistics-tabs inspection-tabs"
      >
        <el-tab-pane name="bridge">
          <span slot="label">
            <svg-icon icon-class="bridge" />
            桥梁统计
          </span>
        </el-tab-pane>
        <el-tab-pane name="tunnel">
          <span slot="label">
            <svg-icon icon-class="tunnel" />
            隧道统计
          </span>
        </el-tab-pane>
      </el-tabs>

      <!-- 筛选条件（采用通用筛选组件） -->
      <FilterSection
        v-model="filterForm"
        :configs="filterConfigs"
        :options="selectOptions"
        @search="handleSearch"
        @reset="handleReset"
        class="filter-area"
      >
        <template #filters="{ formData, options }">
          <!-- 桥梁/隧道名称 -->
          <el-select
            v-model="formData.bridgeName"
            :placeholder="bridgeNameText"
            clearable
            filterable
            class="filter-select"
          >
            <el-option
              v-for="option in options.bridgeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>


          <!-- 日期范围（与病害列表页一致的自定义样式） -->
          <div class="custom-date-range-selector filter-select">
            <div
              class="date-range-display"
              :class="{ 'is-focused': isDatePickerFocused }"
              @click="toggleDatePicker"
              @blur="handleDatePickerBlur"
              @keydown.enter="toggleDatePicker"
              @keydown.space.prevent="toggleDatePicker"
              tabindex="0"
            >
              <span class="date-range-text">
                {{ dateRangeDisplayText }}
              </span>
              <span class="el-input__suffix">
                <i
                  v-if="filterForm.reportTimeRange && filterForm.reportTimeRange.length === 2"
                  class="el-icon-circle-close el-input__icon clear-icon"
                  @click.stop="clearDateRange"
                ></i>
                <i
                  v-else
                  class="el-icon-arrow-down el-input__icon dropdown-icon"
                ></i>
              </span>
            </div>

            <!-- 隐藏的日期选择器 -->
            <el-date-picker
              ref="hiddenDatePicker"
              v-model="filterForm.reportTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="position: absolute; opacity: 0; pointer-events: none; z-index: -1;"
              @change="handleDateRangeChange"
            />
          </div>

          <!-- 区域 -->
          <el-select
            v-model="formData.region"
            placeholder="区域"
            clearable
            class="filter-select"
          >
            <el-option label="开福区" value="kaifu" />
            <el-option label="雨花区" value="yuhua" />
            <el-option label="芙蓉区" value="furong" />
            <el-option label="天心区" value="tianxin" />
            <el-option label="岳麓区" value="yuelu" />
          </el-select>

          <!-- 项目部 -->
          <el-select
            v-model="formData.projectDept"
            placeholder="项目部"
            clearable
            class="filter-select"
          >
            <el-option label="第一项目部" value="dept1" />
            <el-option label="第二项目部" value="dept2" />
            <el-option label="第三项目部" value="dept3" />
          </el-select>
        </template>
      </FilterSection>

      <!-- 统计卡片 -->
      <StatisticsCards
        :statistics-data="statisticsData"
        :loading="loading"
        class="cards-area"
      />

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-row">
        <!-- 巡检趋势分析 -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <div class="chart-header">
              <h4>巡检趋势分析</h4>
            </div>
            <TrendChart
              :chart-data="statisticsData.trendData"
              :chart-type="'line'"
              :loading="loading"
              height="300px"
            />
          </el-card>
        </el-col>

        <!-- 各区巡检完成情况 -->
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <div class="chart-header">
              <h4>各区巡检完成情况</h4>
            </div>
            <RegionChart
              :chart-data="statisticsData.regionData"
              :loading="loading"
              height="300px"
            />
          </el-card>
        </el-col>
      </el-row>

      <!-- 第二行：左侧最近巡检记录 + 右侧病害分析 -->
      <el-row :gutter="20" class="data-row">
        <!-- 左侧：最近巡检记录 -->
        <el-col :span="12">
          <!-- 🔧 标题移到卡片外部 -->
          <div class="chart-title">
            <h4>最近巡检记录</h4>
          </div>

          <!-- 🔧 外框只包围表格内容 -->
          <div class="table-container">
            <div class="common-table">
              <el-table
                v-loading="loading"
                :data="recentRecords"
                size="small"
                style="width: 100%;"
              >
              <el-table-column type="index" label="序号" width="50" align="center" />
              <el-table-column prop="bridgeName" label="桥梁名称" min-width="100" show-overflow-tooltip />
              <el-table-column prop="inspectionDate" label="检测日期" width="100" align="center" />
              <el-table-column prop="inspector" label="巡检人员" width="80" align="center" />
              <el-table-column prop="contactNumber" label="联系方式" width="120" align="center" />
              <el-table-column prop="monthlyFine" label="发现问题" width="80" align="center" />
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="mini"
                    @click="viewRecordDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
              </el-table>
            </div>
          </div>
        </el-col>

        <!-- 右侧：病害分析区域 -->
        <el-col :span="12">

              <div class="chart-title">
                <h4>病害类型分布</h4>
              </div>
              <div class="table-container-right">
                  <DamageTypeChart
                    :chart-data="statisticsData.damageTypeData"
                    :loading="loading"
                    height="100%"
                  />
              </div>

            <!-- 下半部分：桥梁病害数量TOP10 -->

              <div class="chart-title">
                <h4>病害数量top10</h4>
              </div>
            <div class="table-container-right">
                <Top10RankingChart
                  :chart-data="statisticsData.bridgeRanking"
                  :loading="loading"
                  height="100%"
                />
            </div>

        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import * as echarts from 'echarts'
import StatisticsCards from './components/StatisticsCards'
import TrendChart from './components/TrendChart'
import RegionChart from './components/RegionChart'
import DamageTypeChart from './components/DamageTypeChart'
import Top10RankingChart from './components/Top10RankingChart'
import { FilterSection } from '@/components/Inspection'

export default {
  name: 'InspectionStatistics',
  components: {
    StatisticsCards,
    TrendChart,
    RegionChart,
    DamageTypeChart,
    Top10RankingChart,
    FilterSection
  },
  data() {
    return {
      // 当前激活的tab
      activeTab: 'bridge',


      // 筛选表单
      filterForm: {
        bridgeName: '',
        reportTimeRange: [],
        timeRange: 'month',
        region: '',
        projectDept: ''
      },


      // 最近巡检记录
      recentRecords: [],


      // 日期选择器焦点状态（与病害列表页一致）
      isDatePickerFocused: false,

      // 筛选配置
      filterConfigs: {}
    }
  },
  computed: {
    ...mapGetters('inspection', [
      'statisticsData',
      'selectOptions',
      'loadingStates'
    ]),

    loading() {
      return this.loadingStates.inspectionRecords
    },

    bridgeOptions() {
      return this.selectOptions.bridgeOptions || []
    },
    // 动态的桥梁/隧道名称文本
    bridgeNameText() {
      return this.activeTab === 'tunnel' ? '隧道名称' : '桥梁名称'
    },


    // 日期范围显示文本（与病害列表页一致）
    dateRangeDisplayText() {
      if (this.filterForm.reportTimeRange && this.filterForm.reportTimeRange.length === 2) {
        return `${this.filterForm.reportTimeRange[0]} 至 ${this.filterForm.reportTimeRange[1]}`
      }
      return '日期范围'
    }
  },
  async created() {
    await this.initPageData()
  },
  mounted() {
    // 页面挂载后的初始化操作
  },
  methods: {
    ...mapActions('inspection', [
      'fetchStatisticsData',
      'fetchRecentInspectionRecords',
      'initSelectOptions',
      'updateFilters'
    ]),

    // 搜索
    async handleSearch(formData) {
      this.updateFilters({
        inspectionType: this.activeTab,
        bridgeName: formData.bridgeName,
        reportTimeRange: formData.reportTimeRange,
        timeRange: formData.timeRange,
        region: formData.region,
        projectDept: formData.projectDept
      })
      await this.fetchStatisticsData()
      await this.loadRecentRecords()
    },

    // 重置
    async handleReset() {
      this.filterForm = {
        bridgeName: '',
        reportTimeRange: [],
        timeRange: 'month',
        region: '',
        projectDept: ''
      }
      this.updateFilters({
        inspectionType: this.activeTab,
        bridgeName: '',
        reportTimeRange: [],
        timeRange: this.filterForm.timeRange,
        region: '',
        projectDept: ''
      })
      await this.fetchStatisticsData()
      await this.loadRecentRecords()
    },

    // 初始化页面数据
    async initPageData() {
      try {
        // 初始化下拉选项
        await this.initSelectOptions()

        // 更新筛选条件
        this.updateFilters({
          inspectionType: this.activeTab,
          timeRange: this.filterForm.timeRange
        })

        // 获取统计数据
        await this.fetchStatisticsData()

        // 获取最近巡检记录
        await this.loadRecentRecords()

      } catch (error) {
        console.error('初始化页面数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },

    // 加载最近巡检记录
    async loadRecentRecords() {
      try {
        await this.fetchRecentInspectionRecords({ limit: 8 })

        // 从store获取数据或使用默认数据
        this.recentRecords = this.getDefaultRecentRecords()

      } catch (error) {
        console.error('加载最近巡检记录失败:', error)
        this.recentRecords = this.getDefaultRecentRecords()
      }
    },

    // 获取默认最近巡检记录
    getDefaultRecentRecords() {
      const today = new Date()
      const dates = []

      // 生成最近8天的日期
      for (let i = 0; i < 8; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)
        dates.push(date.toISOString().split('T')[0])
      }

      return [
        {
          id: 1,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[0],
          inspector: '吴亮吉',
          contactNumber: '13580037492',
          monthlyFine: 22
        },
        {
          id: 2,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[1],
          inspector: '陈秀英',
          contactNumber: '15210087395',
          monthlyFine: 26
        },
        {
          id: 3,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[1],
          inspector: '陈昭吉',
          contactNumber: '15910018495',
          monthlyFine: 6
        },
        {
          id: 4,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[2],
          inspector: '王建军',
          contactNumber: '13122238579',
          monthlyFine: 9
        },
        {
          id: 5,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[3],
          inspector: '吴超栋',
          contactNumber: '13720089685',
          monthlyFine: 33
        },
        {
          id: 6,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[4],
          inspector: '江融行',
          contactNumber: '18202038579',
          monthlyFine: 11
        },
        {
          id: 7,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[5],
          inspector: '刘君君',
          contactNumber: '18310049683',
          monthlyFine: 62
        },
        {
          id: 8,
          bridgeName: 'XXXX大桥',
          inspectionDate: dates[6],
          inspector: '袁如谦',
          contactNumber: '17821229583',
          monthlyFine: 18
        }
      ]
    },

    // TAB切换
    async handleTabClick(tab) {
      this.activeTab = tab.name
      this.updateFilters({ inspectionType: this.activeTab })
      await this.fetchStatisticsData()
      await this.loadRecentRecords()
    },


    // 查看所有记录
    viewAllRecords() {
      this.$router.push({ name: 'InspectionRecords' })
    },

    // 查看记录详情
    viewRecordDetail(record) {
      console.log('查看记录详情:', record)
      // 这里可以打开详情弹窗或跳转到详情页面
    },


    // 日期范围选择器相关方法（与病害列表页一致）
    // 切换日期选择器显示
    toggleDatePicker() {
      this.isDatePickerFocused = true
      this.$nextTick(() => {
        this.$refs.hiddenDatePicker.focus()
      })
    },

    // 处理日期选择器失焦
    handleDatePickerBlur() {
      // 延迟执行，确保点击操作能正常完成
      setTimeout(() => {
        this.isDatePickerFocused = false
      }, 200)
    },

    // 处理日期范围变化
    handleDateRangeChange(value) {
      this.filterForm.reportTimeRange = value
      // 日期选择完成后移除焦点状态
      this.isDatePickerFocused = false
    },

    // 清空日期范围
    clearDateRange() {
      this.filterForm.reportTimeRange = []
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入主题样式
@import '@/styles/inspection-theme.scss';
@import '@/styles/components/table.scss';

.inspection-statistics {

  // 自定义日期范围选择器样式，匹配病害列表页
  .custom-date-range-selector {
    position: relative;
    width: 100%;
    min-width: 180px;
    flex: 1;
    display: flex; // 确保与其他filter-select对齐
    align-items: center; // 垂直居中对齐

    .date-range-display {
      position: relative;
      box-sizing: border-box;
      display: inline-flex; // 改为inline-flex确保更好的对齐
      align-items: center; // 垂直居中对齐
      width: 100%;
      height: 40px !important;
      padding: 0 30px 0 15px;
      // 使用与filter-select完全相同的样式
      background: rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
      border-radius: 8px !important;
      color: #f8fafc !important;
      font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      font-size: var(--sds-typography-body-size-medium, 16px) !important;
      font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
      line-height: 140% !important;
      cursor: pointer;
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }

      &:hover {
        border-color: rgba(255, 255, 255, 0.4) !important;
      }

      &:focus,
      &.is-focused {
        outline: none;
        border-color: #409EFF !important;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
      }

      .date-range-text {
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // 移除line-height，使用父容器的flex对齐
        color: #f8fafc !important; // 直接使用与病害类型相同的颜色值
        font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        font-size: var(--sds-typography-body-size-medium, 16px) !important;
        font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;

        &:empty::before {
          content: '日期范围';
          color: rgba(255, 255, 255, 0.5) !important;
          font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
          font-size: var(--sds-typography-body-size-medium, 16px) !important;
          font-weight: var(--sds-typography-body-font-weight-regular, 400) !important;
        }
      }

      .el-input__suffix {
        position: absolute;
        top: 0;
        right: 15px;
        height: 100%;
        display: flex;
        align-items: center;
        pointer-events: none;

        .el-input__icon {
          color: rgba(255, 255, 255, 0.7) !important;
          font-size: 14px !important;
          transition: color 0.3s ease !important;

          &:hover {
            color: rgba(255, 255, 255, 0.9) !important;
          }

          &.clear-icon {
            pointer-events: auto;
            cursor: pointer;

            &:hover {
              color: #f56c6c !important;
            }
          }

          &.dropdown-icon {
            pointer-events: none;
          }
        }
      }
    }

    // 确保隐藏的日期选择器完全不可见
    .el-date-editor {
      position: absolute !important;
      opacity: 0 !important;
      pointer-events: none !important;
      z-index: -1 !important;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }

  .page-container {
    .statistics-tabs {
      margin-bottom: 20px;
    }

    .filter-area {
      margin-bottom: 20px;
    }

    .cards-area {
      margin-bottom: 20px;
    }


    .charts-row {
      margin-bottom: 20px;

      .chart-card {
        height: 420px;
        overflow: hidden;
        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding: 14px 20px 0 20px;
          flex-shrink: 0;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
          }
        }
      }
    }

    .data-row {
      .chart-title {
        margin-bottom: 20px;
        margin-left: 40px;

        h4 {
          margin: 0;
          font-size: 16px;
          color: #fff;
          font-weight: 600;
        }
      }

      .table-container-right {
        height: 340px;
        margin: 20px;
      }

      .table-container {
        height: 735px;
        margin: 20px;
        background: linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 10px !important;
        position: relative;
        overflow: hidden;

        .common-table {
          height: 100%;
          display: flex;
          flex-direction: column;
          padding: 14px 20px;
          position: relative;
          z-index: 3;
        }
      }

    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .inspection-statistics {
    .charts-row,
    .data-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .inspection-statistics {
    .data-row {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
