<template>
  <div class="login">
    <div class="login-left">
      <div class="left-title">长沙市智慧桥隧综合管理平台</div>
      <div class="left-title-en">CHANGSHA SMART BRIDGE AND TUNNEL COMPREHENSIVE MANAGEMENT PLATFORM</div>
    </div>

    <div class="login-right">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
        <h3 class="form-title">欢迎登录</h3>

        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码" @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>

        <el-form-item prop="code" v-if="captchaEnabled">
          <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" class="code-input" @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
          </el-input>

          <div class="login-code">
            <img :src="codeUrl" @click="getCode" class="login-code-img" />
          </div>
        </el-form-item>

        <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>

        <el-form-item style="width: 100%">
          <el-button :loading="loading" type="primary" style="width: 100%" @click.native.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>

          <div class="register" v-if="register">
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- <div class="background-image">
      <svg-icon icon-class="background" preserveAspectRatio="none" />
    </div> -->

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright ©{{ currentYear }} 长沙市 “智慧桥隧”综合管理平台</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login'
import Cookies from 'js-cookie'
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: 'Login',
  data() {
    return {
      codeUrl: '',
      loginForm: {
        username: 'screen',
        password: '123456',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
        password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
        code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: false,
      // 注册开关
      register: false,
      redirect: undefined,
      currentYear: new Date().getFullYear()
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    getCookie() {
      const username = Cookies.get('username')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.loginForm.rememberMe) {
            Cookies.set('username', this.loginForm.username, { expires: 30 })
            Cookies.set('password', encrypt(this.loginForm.password), { expires: 30 })
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('rememberMe')
          }
          this.$store
            .dispatch('Login', this.loginForm)
            .then(() => {
              this.$router.push({ path: this.redirect || '/' }).catch(() => {})
            })
            .catch(() => {
              this.loading = false
              if (this.captchaEnabled) {
                this.getCode()
              }
            })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
$height: 30px;
.login {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: url('~@/assets/images/public/background.jpg') no-repeat center;
  background-size: cover;

  .login-left {
    position: absolute;
    top: 10%;
    left: 10%;
    .left-title {
      font-weight: bold;
      color: white;
      font-size: px(28px);
      padding: px(11px) 0;
      line-height: 1;
      border-bottom: px(1px) solid white;
      letter-spacing: px(5px);
    }
    .left-title-en {
      margin-top: px(11px);
      line-height: 1;
      color: white;
      font-size: px(12px);
      letter-spacing: px(1px);
    }
  }
  .login-right {
    position: absolute;
    right: 0;
    top: 0;
    width: 30%;
    height: 100%;
    // background-color: rgba(65, 102, 112, 0.8);
    @include flex(center, center);
    backdrop-filter: blur(1px);
    // background: hsla(0, 0%, 100%, 0.3);
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.3) 80%, rgba(255, 255, 255, 0) 100%);
    .login-form {
      width: auto;
      // padding: px(5px);
      .form-title {
        color: white;
        font-size: px(18px);
        margin: 0px auto px(20px) auto;
        text-align: center;
      }
      .el-form-item {
        width: px(250px);
        margin-bottom: px(20px);
      }
      .el-input {
        font-size: px(16px);
        width: px(250px);
        height: px($height);
        line-height: px($height);
        .el-input__inner {
          width: px(250px);
          height: px($height);
          line-height: px($height);
          padding: 0 px(35px);
          border-radius: 60px;
        }
        .el-input__prefix {
          width: px(30px);
          @include flex(center, center);
          .input-icon {
            height: px(12px);
            width: px(12px);
            margin-left: 2px;
          }
        }
      }
      .code-input {
        width: 55%;
        input {
          width: 100% !important;
        }
      }
      .login-code {
        width: 45%;
        height: px($height);
        line-height: px($height);
        float: right;

        .login-code-img {
          height: px($height);
          line-height: 1;
          cursor: pointer;
          vertical-align: middle;
        }
      }
      .el-checkbox {
        @include flex(flex-start, center);
        margin-bottom: px(20px);
        .el-checkbox__input {
          width: px(15px);
          height: px(15px);
          margin-right: px(5px);
          .el-checkbox__inner {
            width: px(15px);
            height: px(15px);
            border: none;
            &::after {
              border: px(2px) solid white;
              border-left: 0;
              border-top: 0;
              width: px(3px);
              height: px(7px);
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%) rotate(45deg) scaleY(1);
            }
          }
        }

        .el-checkbox__label {
          color: white;
          line-height: 1;
          font-size: px(14px);
          padding: 0;
        }
      }

      .el-button {
        font-size: px(16px);
        border-radius: px(60px);
        height: px($height);
        line-height: px($height);
        padding: 0;
        // border: none;
      }
      .register {
        float: right;
        margin-top: px(20px);
        .link-type {
          color: white;
          font-size: px(14px);
        }
      }
    }
  }

  .el-login-footer {
    height: px(30px);
    line-height: px(30px);
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: px(12px);
    letter-spacing: 1px;
  }
}

@media (min-width: 5120px) {
  .login {
    background: url('~@/assets/images/public/background5k.jpg') no-repeat center;
    background-size: cover;
  }
}
// @import "~@/assets/styles/8k/login8k.scss";
</style>
