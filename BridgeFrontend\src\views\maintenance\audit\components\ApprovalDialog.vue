<template>
  <el-dialog
    :title="readonly ? '查看' : '审批'"
    :visible.sync="visible"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :show-close="false"
    custom-class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    top="5vh"
  >
    <!-- 自定义关闭按钮 -->
    <div class="custom-close-btn" @click="handleClose">
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15.9 18L12.75 14.85L14.85 12.75L18 15.9L21.15 12.75L23.25 14.85L20.1 18L23.25 21.15L21.15 23.25L18 20.1L14.85 23.25L12.75 21.15L15.9 18ZM18 30C11.4 30 6 24.6 6 18C6 11.4 11.4 6 18 6C24.6 6 30 11.4 30 18C30 24.6 24.6 30 18 30ZM18 27C22.95 27 27 22.95 27 18C27 13.05 22.95 9 18 9C13.05 9 9 13.05 9 18C9 22.95 13.05 27 18 27Z" fill="white"/>
      </svg>
    </div>

    <!-- Tab页签 - 使用公共样式风格 -->
    <el-tabs v-model="activeTab" class="approval-tabs inspection-tabs">
      <!-- 病害信息Tab -->
      <el-tab-pane name="disease">
        <span slot="label">
          <i class="el-icon-document"></i>
          病害信息
        </span>
        <div class="disease-content">
          <!-- 病害信息 -->
          <div class="section">
            <h3 class="section-title">病害信息</h3>
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>桥梁/隧道名称:</label>
                  <el-input v-model="diseaseInfo.bridgeName" readonly />
                </div>
                <div class="info-item">
                  <label>病害部位:</label>
                  <el-input v-model="diseaseInfo.diseasePart" readonly />
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>病害类型:</label>
                  <el-input v-model="diseaseInfo.diseaseType" readonly />
                </div>
                <div class="info-item">
                  <label>病害位置:</label>
                  <el-input v-model="diseaseInfo.diseaseLocation" readonly />
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>病害数量:</label>
                  <el-input v-model="diseaseInfo.diseaseCount" readonly />
                </div>
                <div class="info-item">
                  <!-- 空位，保持布局对称 -->
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item full-width">
                <label>病害描述:</label>
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="diseaseInfo.diseaseDescription"
                  readonly
                />
              </div>
            </div>
          </div>

          <!-- 上报信息 -->
          <div class="section">
            <h3 class="section-title">上报信息</h3>
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>上报人:</label>
                  <el-input v-model="diseaseInfo.reporter" readonly />
                </div>
                <div class="info-item">
                  <label>联系方式:</label>
                  <el-input v-model="diseaseInfo.contactPhone" readonly />
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item full-width">
                <label>病害照片:</label>
                <div class="image-gallery">
                  <div
                    v-for="(images, type) in groupedDiseaseImages"
                    :key="type"
                    class="image-category"
                  >
                    <div class="category-title">{{ getCategoryLabel(type) }}</div>
                    <div class="image-list">
                      <div
                        v-for="(image, index) in images"
                        :key="index"
                        class="image-item"
                        @click="previewImage(image)"
                      >
                        <img :src="image.url" :alt="image.name" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 处置信息 -->
          <div class="section">
            <h3 class="section-title">处置信息</h3>
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>养护单位:</label>
                  <el-input v-model="disposalInfo.maintenanceUnit" readonly />
                </div>
                <div class="info-item">
                  <label>管理单位:</label>
                  <el-input v-model="disposalInfo.managementUnit" readonly />
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-group">
                <div class="info-item">
                  <label>处置人员:</label>
                  <el-input v-model="disposalInfo.disposalPersonnel" readonly />
                </div>
                <div class="info-item">
                  <label>联系方式:</label>
                  <el-input v-model="disposalInfo.contactPhone" readonly />
                </div>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item full-width">
                <label>处置时间:</label>
                <el-input v-model="disposalInfo.disposalTime" readonly />
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item full-width">
                <label>处置说明:</label>
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="disposalInfo.disposalDescription"
                  readonly
                />
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item full-width">
                <label>处置照片:</label>
                <div class="image-gallery">
                  <div
                    v-for="(images, type) in groupedDisposalImages"
                    :key="type"
                    class="image-category"
                  >
                    <div class="category-title">{{ getCategoryLabel(type) }}</div>
                    <div class="image-list">
                      <div
                        v-for="(image, index) in images"
                        :key="index"
                        class="image-item"
                        @click="previewImage(image)"
                      >
                        <img :src="image.url" :alt="image.name" />
                        <div class="image-overlay">
                          <i class="el-icon-zoom-in"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 审批信息Tab -->
      <el-tab-pane name="approval">
        <span slot="label">
          <i class="el-icon-check"></i>
          审批信息
        </span>
        <div class="approval-content">
          <!-- 审批记录 -->
          <div class="section">
            <h3 class="section-title">
              <i class="el-icon-info"></i>
              审批记录
            </h3>
            <div class="approval-table-container inspection-table">
              <el-table
                :data="approvalRecords"
                class="approval-records-table"
                border
              >
                <el-table-column prop="serialNumber" label="序号" width="80" align="center" />
                <el-table-column prop="approvalStep" label="审批环节" min-width="150" />
                <el-table-column prop="handler" label="处理人" width="120" align="center" />
                <el-table-column prop="approvalStatus" label="审批状态" width="120" align="center">
                  <template slot-scope="scope">
                    <el-tag
                      :type="getStatusTagType(scope.row.approvalStatus)"
                      size="small"
                    >
                      {{ scope.row.approvalStatus }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="approvalOpinion" label="审批意见" min-width="150" />
                <el-table-column prop="handlerDepartment" label="处理人部门" min-width="120" />
                <el-table-column prop="receiveTime" label="接收时间" width="150" align="center" />
                <el-table-column prop="handleTime" label="办理时间" width="150" align="center" />
              </el-table>
            </div>
          </div>

          <!-- 审批处理 -->
          <div v-if="!readonly" class="section">
            <h3 class="section-title">
              <i class="el-icon-info"></i>
              审批处理
            </h3>
            <div class="approval-form">
              <div class="form-item">
                <label class="required">处理意见:</label>
                <el-input
                  v-model="approvalForm.opinion"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入"
                  class="opinion-input"
                />
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹框底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <template v-if="readonly">
        <!-- 只读模式只显示关闭按钮 -->
        <el-button @click="handleClose">关闭</el-button>
      </template>
      <template v-else-if="activeTab === 'approval'">
        <!-- 审核模式显示审核按钮 -->
        <el-button @click="handleReject" class="reject-btn">退回</el-button>
        <el-button type="primary" @click="handleApprove" class="approve-btn">通过</el-button>
      </template>
      <template v-else>
        <!-- 其他tab页显示关闭按钮 -->
        <el-button @click="handleClose">关闭</el-button>
      </template>
    </div>

    <!-- 图片预览 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      class="photo-preview-dialog common-dialog"
      top="10vh"
    >
      <div class="preview-container">
        <img :src="previewImageUrl" class="preview-image" />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: 'ApprovalDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    approvalData: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  
  watch: {
    visible(newVal) {
      // 弹框显示时初始化数据，样式由公共样式类处理
      if (newVal) {
        // 初始化相关数据
      }
    }
  },
  
  data() {
    return {
      activeTab: 'approval', // 默认显示审批信息tab
      previewVisible: false,
      previewImageUrl: '',
      
      // 审批表单
      approvalForm: {
        opinion: ''
      },
      
      // 病害信息
      diseaseInfo: {
        bridgeName: '长沙湘江大桥',
        diseasePart: '桥面',
        diseaseType: '裂缝',
        diseaseLocation: '主桥中跨',
        diseaseCount: '3处',
        diseaseDescription: '桥面出现多条纵向裂缝，长度约2-5米，宽度1-3mm，位于行车道中央位置，可能影响行车安全。',
        reporter: '张三',
        contactPhone: '13800138000'
      },
      
      // 处置信息
      disposalInfo: {
        maintenanceUnit: '养护公司',
        managementUnit: 'XXX部门',
        disposalPersonnel: '李四',
        contactPhone: '13900139000',
        disposalTime: '2025-08-12 16:30',
        disposalDescription: '已对裂缝进行清理和修补，使用专用修补材料填充，确保结构安全。'
      },
      
      // 审批记录
      approvalRecords: [
        {
          serialNumber: 1,
          approvalStep: '提交',
          handler: '张三',
          approvalStatus: '通过',
          approvalOpinion: '无异议',
          handlerDepartment: '养护公司',
          receiveTime: '2025-08-12 16:30',
          handleTime: '2025-08-12 16:30'
        },
        {
          serialNumber: 2,
          approvalStep: '公司审批',
          handler: '李四',
          approvalStatus: '通过',
          approvalOpinion: '无问题',
          handlerDepartment: '养护公司',
          receiveTime: '2025-08-12 16:30',
          handleTime: '2025-08-12 16:30'
        },
        {
          serialNumber: 3,
          approvalStep: '中心复核',
          handler: '王五',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDepartment: 'XXX部门',
          receiveTime: '2025-08-12 16:30',
          handleTime: '2025-08-12 16:30'
        }
      ],
      
      // 照片类型选项
      photoTypes: [
        { key: 'scene', label: '现场照片' },
        { key: 'personnel', label: '人车照片' },
        { key: 'before', label: '维修前' },
        { key: 'during', label: '维修中' },
        { key: 'after', label: '维修后' }
      ],
      
      // 模拟病害照片数据
      diseaseImages: [
        { type: 'scene', url: 'https://picsum.photos/200/150?random=1', name: '现场照片1' },
        { type: 'scene', url: 'https://picsum.photos/200/150?random=2', name: '现场照片2' },
        { type: 'personnel', url: 'https://picsum.photos/200/150?random=3', name: '人车照片1' }
      ],
      
      // 模拟处置照片数据
      disposalImages: [
        { type: 'before', url: 'https://picsum.photos/200/150?random=4', name: '维修前1' },
        { type: 'during', url: 'https://picsum.photos/200/150?random=5', name: '维修中1' },
        { type: 'after', url: 'https://picsum.photos/200/150?random=6', name: '维修后1' },
        { type: 'after', url: 'https://picsum.photos/200/150?random=7', name: '维修后2' }
      ]
    }
  },
  computed: {
    // 按类型分组的病害照片
    groupedDiseaseImages() {
      const grouped = {}
      this.diseaseImages.forEach(image => {
        if (!grouped[image.type]) {
          grouped[image.type] = []
        }
        grouped[image.type].push(image)
      })
      return grouped
    },
    
    // 按类型分组的处置照片
    groupedDisposalImages() {
      const grouped = {}
      this.disposalImages.forEach(image => {
        if (!grouped[image.type]) {
          grouped[image.type] = []
        }
        grouped[image.type].push(image)
      })
      return grouped
    }
  },
  methods: {
    // 获取分类标签
    getCategoryLabel(type) {
      const category = this.photoTypes.find(item => item.key === type)
      return category ? category.label : type
    },
    
    // 预览图片
    previewImage(image) {
      this.previewImageUrl = image.url
      this.previewVisible = true
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '通过': 'success',
        '退回': 'danger',
        '待审核': 'warning',
        '': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    // 处理审批通过
    handleApprove() {
      if (!this.approvalForm.opinion.trim()) {
        this.$message.warning('请输入处理意见')
        return
      }
      
      this.$confirm('确认通过该审批？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }).then(() => {
        // 模拟审批操作
        this.$message.success('审批通过成功')
        this.$emit('approve', {
          opinion: this.approvalForm.opinion,
          data: this.approvalData
        })
        this.handleClose()
      })
    },
    
    // 处理审批退回
    handleReject() {
      if (!this.approvalForm.opinion.trim()) {
        this.$message.warning('请输入处理意见')
        return
      }
      
      this.$confirm('确认退回该审批？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟退回操作
        this.$message.success('审批退回成功')
        this.$emit('reject', {
          opinion: this.approvalForm.opinion,
          data: this.approvalData
        })
        this.handleClose()
      })
    },
    
    // 样式完全依赖公共样式类，无需手动应用
    
    // 关闭弹框
    handleClose() {
      this.activeTab = 'approval'
      this.approvalForm.opinion = ''
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss">
// 导入maintenance主题样式，使用统一的深色主题
@import '@/assets/styles/maintenance-theme.scss';

// 审批弹框使用公共样式，无需自定义样式
// 通过 common-dialog、inspection-tabs、inspection-table 等类继承公共样式
</style>

