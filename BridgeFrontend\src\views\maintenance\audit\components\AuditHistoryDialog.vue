<template>
  <el-dialog
    title="审核历史"
    :visible.sync="dialogVisible"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    top="5vh"
  >
    <div class="dialog-content">
      <!-- 项目基本信息 -->
      <div class="project-info" v-if="projectInfo.projectName">
        <h4>项目信息</h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>项目名称:</label>
              <span>{{ projectInfo.projectName }}</span>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="info-item">
              <label>项目类型:</label>
              <span>{{ getProjectTypeText(projectInfo.projectType) }}</span>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="info-item">
              <label>提交人:</label>
              <span>{{ projectInfo.submitter }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label>提交时间:</label>
              <span>{{ projectInfo.submitTime }}</span>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="info-item">
              <label>当前状态:</label>
              <status-tag :status="projectInfo.auditStatus" type="audit" />
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="info-item">
              <label>养护单位:</label>
              <span>{{ projectInfo.maintenanceUnit }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 审核流程时间线 -->
      <div class="audit-timeline">
        <h4>审核流程</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in auditHistory"
            :key="index"
            :timestamp="record.auditTime"
            :type="getTimelineType(record.auditResult)"
            :icon="getTimelineIcon(record.auditResult)"
            placement="top"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="audit-stage">{{ record.auditStage }}</span>
                <span class="audit-result">
                  <status-tag :status="record.auditResult" type="audit" />
                </span>
              </div>
              
              <div class="timeline-body">
                <div class="auditor-info">
                  <span class="auditor">审核人: {{ record.auditor }}</span>
                  <span class="department">{{ record.auditorDepartment }}</span>
                </div>
                
                <div class="audit-comment" v-if="record.auditComment">
                  <label>审核意见:</label>
                  <p>{{ record.auditComment }}</p>
                </div>
                
                <!-- 审核附件 -->
                <div class="audit-attachments" v-if="record.attachments && record.attachments.length > 0">
                  <label>审核附件:</label>
                  <div class="attachments-list">
                    <div 
                      v-for="(file, fileIndex) in record.attachments" 
                      :key="fileIndex"
                      class="attachment-item"
                    >
                      <i :class="getFileIcon(file.name)"></i>
                      <span class="file-name">{{ file.name }}</span>
                      <el-button
                        type="text"
                        size="mini"
                        @click="downloadFile(file)"
                      >
                        下载
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      
      <!-- 暂无记录 -->
      <div v-if="auditHistory.length === 0" class="no-history">
        <el-empty description="暂无审核记录" />
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAuditHistory } from '@/api/maintenance/audit'
import StatusTag from '@/components/Maintenance/StatusTag'

export default {
  name: 'AuditHistoryDialog',
  components: {
    StatusTag
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      projectInfo: {},
      auditHistory: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.projectId) {
        this.loadAuditHistory()
      }
    }
  },
  methods: {
    // 加载审核历史
    async loadAuditHistory() {
      this.loading = true
      try {
        const response = await getAuditHistory(this.projectId)
        this.projectInfo = response.data.projectInfo || {}
        this.auditHistory = response.data.auditHistory || []
      } catch (error) {
        this.$message.error('加载审核历史失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取时间线类型
    getTimelineType(auditResult) {
      const typeMap = {
        'audit_passed': 'success',
        'audit_rejected': 'danger',
        'pending': 'info',
        'in_progress': 'warning'
      }
      return typeMap[auditResult] || 'info'
    },
    
    // 获取时间线图标
    getTimelineIcon(auditResult) {
      const iconMap = {
        'audit_passed': 'el-icon-check',
        'audit_rejected': 'el-icon-close',
        'pending': 'el-icon-time',
        'in_progress': 'el-icon-loading'
      }
      return iconMap[auditResult] || 'el-icon-time'
    },
    
    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || type
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture'
      }
      
      return iconMap[extension] || 'el-icon-document'
    },
    
    // 下载文件
    downloadFile(file) {
      window.open(file.url, '_blank')
    },
    
    // 样式完全依赖公共样式类，无需手动应用
  }
}
</script>

<style lang="scss">
@import '@/assets/styles/maintenance-theme.scss';

// 审核历史弹框使用统一的深色主题样式

// 审核历史弹框使用公共样式
</style>
