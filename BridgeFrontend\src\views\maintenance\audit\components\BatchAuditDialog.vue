<template>
  <el-dialog
    title="批量审核"
    :visible.sync="dialogVisible"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    @close="handleClose"
    top="5vh"
  >
    <div class="dialog-content">
      <!-- 选中项目列表 -->
      <div class="selected-items">
        <h4>选中项目 ({{ selectedItems.length }}个)</h4>
        <div class="items-list">
          <div 
            v-for="item in selectedItems" 
            :key="item.id"
            class="item-card"
          >
            <div class="item-info">
              <div class="item-name">{{ item.projectName }}</div>
              <div class="item-meta">
                <span>{{ getProjectTypeText(item.projectType) }}</span>
                <span>{{ item.submitter }}</span>
                <span>{{ item.submitTime }}</span>
              </div>
            </div>
            <div class="item-status">
              <status-tag :status="item.auditStatus" type="audit" />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 审核表单 -->
      <div class="audit-form">
        <el-form
          ref="auditForm"
          :model="auditData"
          :rules="auditRules"
          label-width="100px"
        >
          <el-form-item label="审核结果" prop="auditResult" required>
            <el-radio-group v-model="auditData.auditResult">
              <el-radio label="pass">通过</el-radio>
              <el-radio label="reject">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审核意见" prop="auditComment" required>
            <el-input
              v-model="auditData.auditComment"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item label="审核人">
            <el-input
              v-model="auditData.auditor"
              placeholder="审核人姓名"
              readonly
            />
          </el-form-item>
          
          <el-form-item label="审核时间">
            <el-input
              v-model="auditData.auditTime"
              placeholder="审核时间"
              readonly
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        :loading="submitting"
        @click="handleConfirm"
      >
        确定审核
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import StatusTag from '@/components/Maintenance/StatusTag'

export default {
  name: 'BatchAuditDialog',
  components: {
    StatusTag
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      submitting: false,
      
      // 审核数据
      auditData: {
        auditResult: 'pass',
        auditComment: '',
        auditor: '',
        auditTime: ''
      },
      
      // 表单验证规则
      auditRules: {
        auditResult: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ],
        auditComment: [
          { required: true, message: '请输入审核意见', trigger: 'blur' },
          { min: 5, max: 500, message: '审核意见长度在 5 到 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initAuditData()
      }
    }
  },
  methods: {
    // 初始化审核数据
    initAuditData() {
      this.auditData = {
        auditResult: 'pass',
        auditComment: '',
        auditor: this.$store.getters.userName || '当前用户',
        auditTime: this.formatDateTime(new Date())
      }
      
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.auditForm) {
          this.$refs.auditForm.clearValidate()
        }
      })
    },
    
    // 确认审核
    async handleConfirm() {
      try {
        // 表单验证
        await this.$refs.auditForm.validate()
        
        this.submitting = true
        
        // 构造审核数据
        const auditPayload = {
          projectIds: this.selectedItems.map(item => item.id),
          auditResult: this.auditData.auditResult,
          auditComment: this.auditData.auditComment,
          auditor: this.auditData.auditor,
          auditTime: this.auditData.auditTime
        }
        
        // 触发确认事件
        this.$emit('confirm', auditPayload)
        
        // 关闭弹窗
        this.handleClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.initAuditData()
    },
    
    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || type
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    
    // 样式完全依赖公共样式类，无需手动应用
  }
}
</script>

<style lang="scss">
@import '@/assets/styles/maintenance-theme.scss';

// 批量审核弹框使用统一的深色主题样式

// 批量审核弹框使用公共样式
</style>
