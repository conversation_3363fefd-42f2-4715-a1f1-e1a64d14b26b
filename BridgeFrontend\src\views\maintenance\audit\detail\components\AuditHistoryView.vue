<template>
  <div class="audit-history-view">
    <!-- 审核流程时间线 -->
    <div class="audit-timeline">
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in auditHistory"
          :key="index"
          :timestamp="record.auditTime"
          :type="getTimelineType(record.auditResult)"
          :icon="getTimelineIcon(record.auditResult)"
          placement="top"
        >
          <div class="timeline-content">
            <div class="timeline-header">
              <span class="audit-stage">{{ record.auditStage }}</span>
              <span class="audit-result">
                <status-tag :status="record.auditResult" type="audit" />
              </span>
            </div>
            
            <div class="timeline-body">
              <div class="auditor-info">
                <span class="auditor">审核人: {{ record.auditor }}</span>
                <span class="department">{{ record.auditorDepartment }}</span>
                <span class="receive-time" v-if="record.receiveTime">
                  接收时间: {{ record.receiveTime }}
                </span>
              </div>
              
              <div class="audit-comment" v-if="record.auditComment">
                <label>审核意见:</label>
                <p>{{ record.auditComment }}</p>
              </div>
              
              <!-- 审核附件 -->
              <div class="audit-attachments" v-if="record.attachments && record.attachments.length > 0">
                <label>审核附件:</label>
                <div class="attachments-list">
                  <div 
                    v-for="(file, fileIndex) in record.attachments" 
                    :key="fileIndex"
                    class="attachment-item"
                  >
                    <i :class="getFileIcon(file.name)"></i>
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    <el-button
                      type="text"
                      size="mini"
                      @click="downloadFile(file)"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 处理时长 -->
              <div class="process-duration" v-if="record.processDuration">
                <span class="duration-label">处理时长:</span>
                <span class="duration-value">{{ record.processDuration }}</span>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    
    <!-- 暂无记录 -->
    <div v-if="auditHistory.length === 0 && !loading" class="no-history">
      <el-empty description="暂无审核记录" />
    </div>
    
    <!-- 加载中 -->
    <div v-if="loading" class="loading-container">
      <el-loading text="加载中..." />
    </div>
  </div>
</template>

<script>
import { getAuditHistory } from '@/api/maintenance/audit'
import StatusTag from '@/components/Maintenance/StatusTag'

export default {
  name: 'AuditHistoryView',
  components: {
    StatusTag
  },
  props: {
    projectId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      auditHistory: []
    }
  },
  created() {
    this.loadAuditHistory()
  },
  methods: {
    // 加载审核历史
    async loadAuditHistory() {
      this.loading = true
      try {
        const response = await getAuditHistory(this.projectId)
        this.auditHistory = response.data.auditHistory || []
      } catch (error) {
        this.$message.error('加载审核历史失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取时间线类型
    getTimelineType(auditResult) {
      const typeMap = {
        'audit_passed': 'success',
        'audit_rejected': 'danger',
        'pending': 'info',
        'in_progress': 'warning'
      }
      return typeMap[auditResult] || 'info'
    },
    
    // 获取时间线图标
    getTimelineIcon(auditResult) {
      const iconMap = {
        'audit_passed': 'el-icon-check',
        'audit_rejected': 'el-icon-close',
        'pending': 'el-icon-time',
        'in_progress': 'el-icon-loading'
      }
      return iconMap[auditResult] || 'el-icon-time'
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture'
      }
      
      return iconMap[extension] || 'el-icon-document'
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 下载文件
    downloadFile(file) {
      window.open(file.url, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.audit-history-view {
  .audit-timeline {
    .el-timeline {
      .timeline-content {
        .timeline-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .audit-stage {
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
          }
        }
        
        .timeline-body {
          .auditor-info {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 12px;
            
            .auditor {
              color: #3b82f6;
              font-weight: bold;
            }
            
            .department,
            .receive-time {
              color: #9ca3af;
              font-size: 14px;
            }
          }
          
          .audit-comment {
            margin-bottom: 12px;
            
            label {
              color: #9ca3af;
              display: block;
              margin-bottom: 8px;
            }
            
            p {
              color: #ffffff;
              background: #374151;
              border-radius: 4px;
              padding: 12px;
              margin: 0;
              white-space: pre-wrap;
              word-break: break-word;
            }
          }
          
          .audit-attachments {
            margin-bottom: 12px;
            
            label {
              color: #9ca3af;
              display: block;
              margin-bottom: 8px;
            }
            
            .attachments-list {
              .attachment-item {
                display: flex;
                align-items: center;
                background: #4b5563;
                border-radius: 4px;
                padding: 8px 12px;
                margin-bottom: 8px;
                
                i {
                  color: #9ca3af;
                  margin-right: 8px;
                }
                
                .file-name {
                  color: #ffffff;
                  flex: 1;
                  margin-right: 12px;
                }
                
                .file-size {
                  color: #9ca3af;
                  font-size: 12px;
                  margin-right: 12px;
                }
                
                .el-button {
                  color: #3b82f6;
                  
                  &:hover {
                    color: #2563eb;
                  }
                }
              }
            }
          }
          
          .process-duration {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .duration-label {
              color: #9ca3af;
              font-size: 14px;
            }
            
            .duration-value {
              color: #22c55e;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
  
  .no-history {
    text-align: center;
    padding: 40px;
    
    .el-empty {
      .el-empty__description {
        color: #9ca3af;
      }
    }
  }
  
  .loading-container {
    text-align: center;
    padding: 40px;
  }
}
</style>
