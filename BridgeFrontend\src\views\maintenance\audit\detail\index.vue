<template>
  <div class="maintenance-theme">
    <div class="page-container">
      <div class="card-container">
        <!-- 页面标题栏 -->
        <div class="page-header">
          <div class="header-left">
            <el-button
              type="text"
              icon="el-icon-arrow-left"
              class="back-btn"
              @click="handleBack"
            >
              返回
            </el-button>
            <h2>审核详情</h2>
          </div>
          
          <div class="header-right">
            <el-button
              v-if="canAudit"
              type="primary"
              @click="handleAudit"
            >
              审核
            </el-button>
          </div>
        </div>
        
        <!-- 项目基本信息 -->
        <div class="project-summary">
          <el-row :gutter="24">
            <el-col :span="18">
              <div class="summary-info">
                <h3>{{ projectData.projectName }}</h3>
                <div class="meta-info">
                  <span class="meta-item">
                    <i class="el-icon-folder"></i>
                    {{ getProjectTypeText(projectData.projectType) }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-user"></i>
                    {{ projectData.submitter }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-time"></i>
                    {{ projectData.submitTime }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-office-building"></i>
                    {{ projectData.maintenanceUnit }}
                  </span>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="summary-status">
                <div class="status-item">
                  <label>当前状态:</label>
                  <status-tag :status="projectData.auditStatus" type="audit" />
                </div>
                <div class="status-item">
                  <label>当前审核人:</label>
                  <span>{{ projectData.currentAuditor || '-' }}</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
          <div class="tab-container">
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'basic' }"
              @click="switchTab('basic')"
            >
              基本信息
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'tasks' }"
              @click="switchTab('tasks')"
            >
              养护项目
            </div>
            <div 
              v-if="showDiseaseTab"
              class="tab-item"
              :class="{ 'is-active': activeTab === 'diseases' }"
              @click="switchTab('diseases')"
            >
              病害养护
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'history' }"
              @click="switchTab('history')"
            >
              审核历史
            </div>
          </div>
        </div>
        
        <!-- 标签内容 -->
        <div class="tab-content">
          <!-- 基本信息 -->
          <div v-if="activeTab === 'basic'" class="basic-info">
            <basic-info-view :project-data="projectData" />
          </div>
          
          <!-- 养护项目 -->
          <div v-if="activeTab === 'tasks'" class="tasks-info">
            <tasks-info-view 
              :project-id="projectId"
              :project-data="projectData"
              :readonly="true"
            />
          </div>
          
          <!-- 病害养护 -->
          <div v-if="activeTab === 'diseases' && showDiseaseTab" class="diseases-info">
            <diseases-info-view 
              :project-id="projectId"
              :project-data="projectData"
              :readonly="true"
            />
          </div>
          
          <!-- 审核历史 -->
          <div v-if="activeTab === 'history'" class="history-info">
            <audit-history-view :project-id="projectId" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAuditDetail } from '@/api/maintenance/audit'
import StatusTag from '@/components/Maintenance/StatusTag'
import BasicInfoView from '../../repairs/detail/components/BasicInfoView'
import TasksInfoView from '../../repairs/detail/components/TasksInfoView'
import DiseasesInfoView from '../../repairs/detail/components/DiseasesInfoView'
import AuditHistoryView from './components/AuditHistoryView'

export default {
  name: 'MaintenanceAuditDetail',
  components: {
    StatusTag,
    BasicInfoView,
    TasksInfoView,
    DiseasesInfoView,
    AuditHistoryView
  },
  data() {
    return {
      loading: false,
      activeTab: 'basic',
      projectData: {},
      projectId: ''
    }
  },
  computed: {
    // 是否显示病害养护标签
    showDiseaseTab() {
      return this.projectData.projectType === 'monthly'
    },
    
    // 是否可以审核
    canAudit() {
      const userRole = this.$store.getters.userRole || 'company_admin'
      
      if (userRole === 'company_admin') {
        return this.projectData.auditStatus === 'primary_audit'
      } else if (userRole === 'bridge_center') {
        return this.projectData.auditStatus === 'secondary_audit'
      }
      
      return false
    }
  },
  async created() {
    this.projectId = this.$route.params.id
    await this.loadProjectDetail()
  },
  methods: {
    // 加载项目详情
    async loadProjectDetail() {
      try {
        this.loading = true
        const response = await getAuditDetail(this.projectId)
        this.projectData = response.data || {}
      } catch (error) {
        this.$message.error('加载项目详情失败')
        this.handleBack()
      } finally {
        this.loading = false
      }
    },
    
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab
    },
    
    // 返回列表
    handleBack() {
      this.$router.push('/maintenance/audit')
    },
    
    // 审核项目
    handleAudit() {
      this.$router.push(`/maintenance/projects/approve/${this.projectId}`)
    },
    
    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.page-header {
  @extend .common-page-header;
}

.project-summary {
  padding: 24px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  margin-bottom: 24px;
  
  .summary-info {
    h3 {
      color: #ffffff;
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 12px;
    }
    
    .meta-info {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #9ca3af;
        font-size: 14px;
        
        i {
          color: #3b82f6;
        }
      }
    }
  }
  
  .summary-status {
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      label {
        color: #9ca3af;
        min-width: 80px;
        margin-right: 12px;
      }
      
      span {
        color: #ffffff;
      }
    }
  }
}

.tab-navigation {
  @extend .common-tab-navigation;
}

.tab-content {
  padding: 24px;
  min-height: 400px;
}
</style>
