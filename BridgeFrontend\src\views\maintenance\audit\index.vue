<template>
  <div class="maintenance-audit-page maintenance-theme inspection-container">
    <div class="page-container">
      <!-- TAB切换 -->
      <div class="inspection-tabs">
        <TabSwitch
          v-model="infrastructureType"
          :tabs="tabOptions"
          @tab-click="handleTabClick"
        />
      </div>

      <!-- 审核状态标签 -->
      <div class="secondary-tab-navigation">
        <div class="secondary-tabs">
          <div 
            class="tab-item"
            :class="{ 'is-active': auditStatus === 'pending' }"
            @click="switchAuditStatus('pending')"
          >
            <i class="el-icon-clock"></i>
            待审核
          </div>
          <div 
            class="tab-item"
            :class="{ 'is-active': auditStatus === 'completed' }"
            @click="switchAuditStatus('completed')"
          >
            <i class="el-icon-check"></i>
            已审核
          </div>
        </div>
      </div>

      <!-- 筛选表单 -->
      <div class="filter-section">
        <div class="filter-content">
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索审核项目、发起人"
            clearable
            class="filter-select"
          />

          <el-select
            v-model="queryParams.auditItem"
            placeholder="审核项"
            clearable
            class="filter-select"
          >
            <el-option label="全部审核项" value="" />
            <el-option 
              v-for="item in auditItems" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
            />
          </el-select>
          
          <el-select
            v-model="queryParams.initiator"
            placeholder="发起人"
            clearable
            class="filter-select"
          >
            <el-option label="全部发起人" value="" />
            <el-option 
              v-for="user in initiators" 
              :key="user.value" 
              :label="user.label" 
              :value="user.value" 
            />
          </el-select>
          
          <el-select
            v-model="queryParams.initiateTime"
            placeholder="发起时间"
            clearable
            class="filter-select"
          >
            <el-option label="全部时间" value="" />
            <el-option label="今天" value="today" />
            <el-option label="本周" value="week" />
            <el-option label="本月" value="month" />
          </el-select>
          
          <el-select
            v-model="queryParams.unit"
            placeholder="单位"
            clearable
            class="filter-select"
          >
            <el-option label="全部单位" value="" />
            <el-option 
              v-for="unit in units" 
              :key="unit.value" 
              :label="unit.label" 
              :value="unit.value" 
            />
          </el-select>
          
          <div class="filter-actions">
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </div>
        </div>
      </div>
        
      <!-- 数据表格 -->
      <div class="inspection-table common-table">
        <el-table
          v-loading="loading"
          :data="auditList"
          class="audit-table"
          stripe
        >
          <el-table-column 
            prop="serialNumber" 
            label="序号" 
            width="80" 
            align="center" 
          />
          
          <el-table-column 
            prop="auditItem" 
            label="审核项" 
            min-width="200"
            show-overflow-tooltip 
          />
          
          <el-table-column 
            prop="initiator" 
            label="发起人" 
            width="120" 
            align="center" 
          />
          
          <el-table-column 
            prop="initiateTime" 
            label="发起时间" 
            width="180" 
            align="center" 
          />
          
          <el-table-column 
            prop="unit" 
            label="单位" 
            min-width="180"
            show-overflow-tooltip 
          />
          
          <el-table-column 
            label="操作" 
            width="100" 
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="auditStatus === 'pending' ? handleAudit(scope.row) : handleView(scope.row)"
              >
                {{ auditStatus === 'pending' ? '审核' : '查看' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
        
      <!-- 分页 -->
      <div class="inspection-pagination">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-sizes="[2, 5, 10, 20]"
          :page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          small
        />
      </div>
    </div>

    <!-- 审批弹框 -->
    <ApprovalDialog
      :visible.sync="approvalDialogVisible"
      :approval-data="currentApprovalData"
      :readonly="currentApprovalData.readonly || false"
      @approve="handleApprovalSuccess"
      @reject="handleApprovalSuccess"
    />
  </div>
</template>

<script>
import ApprovalDialog from './components/ApprovalDialog.vue'
import TabSwitch from '@/components/Inspection/TabSwitch'

export default {
  name: 'MaintenanceAudit',
  components: {
    ApprovalDialog,
    TabSwitch
  },
  data() {
    return {
      loading: false,
      infrastructureType: 'bridge', // bridge, tunnel
      auditStatus: 'pending', // pending, completed
      auditList: [],
      total: 0,
      
      // TAB选项
      tabOptions: [
        {
          name: 'bridge',
          label: '桥梁养护维修审核',
          icon: 'bridge-icon'
        },
        {
          name: 'tunnel',
          label: '隧道养护维修审核',
          icon: 'tunnel-icon'
        }
      ],
      
      // 审批弹框相关
      approvalDialogVisible: false,
      currentApprovalData: {},
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 2, // 设置小的分页大小以便看到分页组件
        keyword: '',
        auditItem: '',
        initiator: '',
        initiateTime: '',
        unit: ''
      },
      
      // 审核项选项
      auditItems: [
        { label: 'XXXXXX项目', value: 'project_1' },
        { label: 'YYYYYY项目', value: 'project_2' },
        { label: 'ZZZZZZ项目', value: 'project_3' }
      ],
      
      // 发起人选项
      initiators: [
        { label: '周文革', value: 'zhouwenge' },
        { label: '罗子安', value: 'luozian' },
        { label: '江辞舟', value: 'jiangcizhou' },
        { label: '朱建军', value: 'zhujianjun' },
        { label: '王若峰', value: 'wangruofeng' },
        { label: '林雨欣', value: 'linyuxin' },
        { label: '郑辰逸', value: 'zhengchenyi' },
        { label: '张格然', value: 'zhanggeran' },
        { label: '李建军', value: 'lijianjun' },
        { label: '赵晓红', value: 'zhaoxiaohong' },
        { label: '黄淑芬', value: 'huangshufen' },
        { label: '陈丽华', value: 'chenlihua' }
      ],
      
      // 单位选项
      units: [
        { label: '长沙市桥梁管理处', value: 'changsha_bridge_mgmt' },
        { label: '长沙市隧道管理处', value: 'changsha_tunnel_mgmt' },
        { label: '湖南省交通厅', value: 'hunan_transport_dept' }
      ],

      // 静态测试数据 - 减少数据量以便看到分页组件
      mockAuditData: {
        bridge: {
          pending: [
            {
              id: '001',
              serialNumber: '001',
              auditItem: 'XXXXXX项目',
              initiator: '周文革',
              initiateTime: '2025/09/01 16:16',
              unit: '长沙市桥梁管理处'
            },
            {
              id: '002',
              serialNumber: '002',
              auditItem: 'YYYYYY项目',
              initiator: '罗子安',
              initiateTime: '2025/09/01 15:30',
              unit: '长沙市桥梁管理处'
            },
            {
              id: '003',
              serialNumber: '003',
              auditItem: 'ZZZZZZ项目',
              initiator: '江辞舟',
              initiateTime: '2025/09/01 14:45',
              unit: '长沙市桥梁管理处'
            }
          ],
          completed: [
            {
              id: '004',
              serialNumber: '004',
              auditItem: 'AAAAAA项目',
              initiator: '朱建军',
              initiateTime: '2025/08/28 14:30',
              unit: '长沙市桥梁管理处'
            },
            {
              id: '005',
              serialNumber: '005',
              auditItem: 'BBBBBB项目',
              initiator: '王若峰',
              initiateTime: '2025/08/25 10:15',
              unit: '长沙市桥梁管理处'
            }
          ]
        },
        tunnel: {
          pending: [
            {
              id: '101',
              serialNumber: '001',
              auditItem: 'CCCCCC隧道项目',
              initiator: '林雨欣',
              initiateTime: '2025/09/01 15:30',
              unit: '长沙市隧道管理处'
            },
            {
              id: '102',
              serialNumber: '002',
              auditItem: 'DDDDDD隧道项目',
              initiator: '郑辰逸',
              initiateTime: '2025/09/01 14:20',
              unit: '长沙市隧道管理处'
            }
          ],
          completed: [
            {
              id: '103',
              serialNumber: '003',
              auditItem: 'EEEEEE隧道项目',
              initiator: '张格然',
              initiateTime: '2025/08/30 16:45',
              unit: '长沙市隧道管理处'
            }
          ]
        }
      }
    }
  },
  created() {
    this.loadAuditList()
  },
  methods: {
    // 获取审核列表
    loadAuditList() {
      this.loading = true
      
      // 获取当前数据源
      const currentData = this.mockAuditData[this.infrastructureType][this.auditStatus]
      
      // 应用筛选条件
      let filteredData = [...currentData]
      
      // 关键词搜索
      if (this.queryParams.keyword) {
        const keyword = this.queryParams.keyword.toLowerCase()
        filteredData = filteredData.filter(item => 
          item.auditItem.toLowerCase().includes(keyword) ||
          item.initiator.toLowerCase().includes(keyword) ||
          item.unit.toLowerCase().includes(keyword)
        )
      }
      
      if (this.queryParams.auditItem) {
        filteredData = filteredData.filter(item => 
          item.auditItem.includes(this.queryParams.auditItem) ||
          this.auditItems.find(opt => opt.value === this.queryParams.auditItem)?.label === item.auditItem
        )
      }
      
      if (this.queryParams.initiator) {
        filteredData = filteredData.filter(item => 
          item.initiator === this.queryParams.initiator ||
          this.initiators.find(opt => opt.value === this.queryParams.initiator)?.label === item.initiator
        )
      }
      
      if (this.queryParams.unit) {
        filteredData = filteredData.filter(item => 
          item.unit.includes(this.queryParams.unit) ||
          this.units.find(opt => opt.value === this.queryParams.unit)?.label === item.unit
        )
      }
      
      // 分页处理
      this.total = filteredData.length
      const startIndex = (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      const endIndex = startIndex + this.queryParams.pageSize
      
      // 模拟异步加载
      setTimeout(() => {
        this.auditList = filteredData.slice(startIndex, endIndex)
        this.loading = false
      }, 300)
    },
    
    // TAB切换处理
    handleTabClick(tab) {
      this.infrastructureType = tab.name
      this.queryParams.pageNum = 1
      this.loadAuditList()
    },
    
    // 切换基础设施类型
    switchInfrastructureType(type) {
      this.infrastructureType = type
      this.queryParams.pageNum = 1
      this.loadAuditList()
    },
    
    // 切换审核状态
    switchAuditStatus(status) {
      this.auditStatus = status
      this.queryParams.pageNum = 1
      this.loadAuditList()
    },
    
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.loadAuditList()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 2, // 保持与初始设置一致
        keyword: '',
        auditItem: '',
        initiator: '',
        initiateTime: '',
        unit: ''
      }
      this.loadAuditList()
    },
    
    // 审核项目
    handleAudit(row) {
      this.currentApprovalData = row
      this.approvalDialogVisible = true
    },
    
    // 查看项目（已审核状态）
    handleView(row) {
      // 对于已审核的项目，以只读模式打开审批弹框
      this.currentApprovalData = { ...row, readonly: true }
      this.approvalDialogVisible = true
    },
    
    // 审批操作成功
    handleApprovalSuccess(data) {
      this.$message.success(`审批操作成功`)
      // 刷新列表
      this.loadAuditList()
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.queryParams.pageNum = 1
      this.loadAuditList()
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.loadAuditList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.maintenance-audit-page {
  // 样式已通过主题文件提供
  
  // 使用公共状态Tab导航样式
  .secondary-tab-navigation {
    @extend .common-status-tab-navigation;
    
    .secondary-tabs {
      @extend .status-tabs;
    }
  }
}

// Element UI下拉选项样式覆盖
:deep(.el-select-dropdown) {
  background: #374151;
  border: 1px solid #4b5563;
  
  .el-select-dropdown__item {
    color: #ffffff;
    
    &:hover {
      background: #4b5563;
    }
    
    &.selected {
      background: #3b82f6;
      color: #ffffff;
    }
  }
}
</style>
