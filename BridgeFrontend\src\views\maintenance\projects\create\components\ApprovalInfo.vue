<template>
  <div class="approval-info">
    <!-- 审批记录 -->
    <div class="approval-records">
      <div class="section-title">
        <i class="el-icon-document"></i>
        审批记录
      </div>
      
      <el-table
        :data="approvalHistory"
        class="approval-table"
        border
      >
        <el-table-column
          prop="id"
          label="序号"
          min-width="60"
          align="center"
        />
        
        <el-table-column
          prop="stepName"
          label="审批环节"
          min-width="140"
          align="center"
        />
        
        <el-table-column
          prop="approver"
          label="处理人"
          min-width="80"
          align="center"
        />
        
        <el-table-column
          prop="status"
          label="审批状态"
          min-width="80"
          align="center"
        />
        
        <el-table-column
          prop="comment"
          label="审批意见"
          min-width="80"
          align="center"
        />
        
        <el-table-column
          prop="department"
          label="处理人部门"
          min-width="100"
          align="center"
        />
        
        <el-table-column
          prop="receiveTime"
          label="接收时间"
          min-width="100"
          align="center"
        />
        
        <el-table-column
          prop="processTime"
          label="办理时间"
          min-width="100"
          align="center"
        />
      </el-table>
    </div>
    
    <!-- 无审批记录时显示提示 -->
    <div v-if="!hasApprovalHistory" class="no-data">
      <p>暂无审批记录</p>
    </div>
    
    <!-- 审批处理（仅在审批模式下显示） -->
    <div v-if="showApprovalForm" class="approval-process">
      <div class="section-title">
        <i class="el-icon-edit"></i>
        审批处理
      </div>
      
      <div class="process-form">
        <div class="comment-label">*处理意见:</div>
        <el-input
          v-model="approvalFormData.comment"
          type="textarea"
          :rows="6"
          placeholder="请输入"
          class="comment-input"
        />
      </div>
      
    </div>
    
    <!-- 注意：审批信息组件内不显示任何操作按钮，所有按钮都统一在父组件中控制 -->
  </div>
</template>

<script>
import { approveProject, getProjectApprovalHistory } from '@/api/maintenance/projects'

export default {
  name: 'ApprovalInfo',
  components: {
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    // 是否显示审批表单（审批模式 vs 查看模式）
    showApprovalForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      
      // 审批历史记录
      approvalHistory: [],
      
      // 审批表单数据
      approvalFormData: {
        comment: ''
      }
    }
  },
  computed: {
    // 是否有审批历史记录
    hasApprovalHistory() {
      return this.approvalHistory && this.approvalHistory.length > 0
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.initializeData(newVal)
        }
      },
      immediate: true,
      deep: true
    },
    
    projectId: {
      handler(newVal) {
        if (newVal) {
          this.loadApprovalHistory()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    initializeData(data) {
      if (data && data.approvalHistory) {
        this.approvalHistory = data.approvalHistory
      }
    },
    
    // 加载审批历史
    async loadApprovalHistory() {
      if (!this.projectId) return
      
      try {
        this.loading = true
        const response = await getProjectApprovalHistory(this.projectId)
        if (response.code === 200) {
          this.approvalHistory = response.data || []
        }
      } catch (error) {
        console.error('加载审批历史失败:', error)
        this.$message.error('加载审批历史失败')
      } finally {
        this.loading = false
      }
    },
    
    // 公共方法：审批通过（供父组件调用）
    async approveProject() {
      if (!this.approvalFormData.comment.trim()) {
        this.$message.error('请输入处理意见')
        return false
      }
      
      try {
        await this.$confirm('确认通过该申请？', '确认操作', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        this.loading = true
        
        const response = await approveProject(this.projectId, {
          result: 'approved',
          comment: this.approvalFormData.comment
        })
        
        if (response.code === 200) {
          this.$message.success('审批通过成功')
          this.$emit('approval-submitted', {
            result: 'approved',
            data: response.data
          })
          this.approvalFormData.comment = ''
          await this.loadApprovalHistory()
          return true
        } else {
          throw new Error(response.message || '操作失败')
        }
      } catch (error) {
        if (error.message !== 'cancel') {
          console.error('审批失败:', error)
          this.$message.error(error.message || '审批失败')
        }
        return false
      } finally {
        this.loading = false
      }
    },
    
    // 公共方法：审批退回（供父组件调用）
    async rejectProject() {
      if (!this.approvalFormData.comment.trim()) {
        this.$message.error('请输入处理意见')
        return false
      }
      
      try {
        await this.$confirm('确认退回该申请？', '确认操作', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        this.loading = true
        
        const response = await approveProject(this.projectId, {
          result: 'returned',
          comment: this.approvalFormData.comment
        })
        
        if (response.code === 200) {
          this.$message.success('退回成功')
          this.$emit('approval-submitted', {
            result: 'returned',
            data: response.data
          })
          this.approvalFormData.comment = ''
          await this.loadApprovalHistory()
          return true
        } else {
          throw new Error(response.message || '操作失败')
        }
      } catch (error) {
        if (error.message !== 'cancel') {
          console.error('退回失败:', error)
          this.$message.error(error.message || '退回失败')
        }
        return false
      } finally {
        this.loading = false
      }
    },
    
    
    // 验证方法（为了保持接口一致性）
    validate() {
      return Promise.resolve(true)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.approval-info {
  color: #ffffff;
  
  
  // 无数据提示
  .no-data {
    text-align: center;
    padding: 40px 0;
    color: #9ca3af;
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
  
  .section-title {
    color: #ffffff;
    font-size: 16px;
    font-weight: normal;
    margin-bottom: 20px;
    text-align: left;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409eff;
      font-size: 18px;
    }
  }
  
  // 审批记录表格
  .approval-records {
    margin-bottom: 32px;
    
    .approval-table {
      width: 100%;
      
      :deep(.el-table) {
        background: transparent;
        color: #ffffff;
        width: 100%;
        
        th {
          background: rgba(255, 255, 255, 0.05);
          color: #ffffff;
          border: 1px solid rgba(255, 255, 255, 0.1);
          text-align: center;
          font-weight: normal;
          padding: 12px 8px;
        }
        
        td {
          background: transparent;
          color: #ffffff;
          border: 1px solid rgba(255, 255, 255, 0.1);
          text-align: center;
          white-space: pre-line; // 支持换行显示
          padding: 12px 8px;
        }
        
        &::before {
          background: rgba(255, 255, 255, 0.1);
        }
        
        .el-table__empty-block {
          background: transparent;
          color: #9ca3af;
        }
        
        // 确保表格占满容器宽度
        .el-table__header-wrapper,
        .el-table__body-wrapper {
          width: 100% !important;
        }
      }
    }
  }
  
  // 审批处理区域
  .approval-process {
    margin-bottom: 32px;
    
    .process-form {
      margin-bottom: 24px;
      
      .comment-label {
        color: #ffffff;
        margin-bottom: 8px;
        font-size: 14px;
      }
      
      .comment-input {
        :deep(.el-textarea__inner) {
          background: #374151;
          border: 1px solid #9ca3af;
          color: #ffffff;
          
          &::placeholder {
            color: #9ca3af;
          }
          
          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
    
  }
}
</style>