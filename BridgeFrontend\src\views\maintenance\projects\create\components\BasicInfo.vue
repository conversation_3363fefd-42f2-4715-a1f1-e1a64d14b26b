<template>
  <div class="basic-info-form">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="maintenance-form"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName" required>
            <el-input
              v-model="formData.projectName"
              placeholder="请输入项目名称"
              maxlength="50"
              show-word-limit
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目类型" prop="projectType" required>
            <el-select
              v-model="formData.projectType"
              placeholder="请选择项目类型"
              style="width: 100%"
              :disabled="readonly"
              @change="handleProjectTypeChange"
            >
              <el-option
                v-for="type in projectTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="项目开始时间" prop="startDate" required>
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="选择开始时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="readonly"
              @change="handleInput"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目结束时间" prop="endDate" required>
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              placeholder="选择结束时间"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="readonly"
              @change="handleInput"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="管理单位" prop="managementUnit" required>
            <el-select
              v-model="formData.managementUnit"
              placeholder="请选择管理单位"
              style="width: 100%"
              filterable
              :disabled="readonly"
              @change="handleInput"
            >
              <el-option
                v-for="unit in managementUnits"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="监理单位">
            <el-input
              v-model="formData.supervisionUnit"
              placeholder="请输入监理单位"
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="养护单位" prop="maintenanceUnit" required>
            <el-select
              v-model="formData.maintenanceUnit"
              placeholder="请选择养护单位"
              style="width: 100%"
              filterable
              :disabled="readonly"
              @change="handleMaintenanceUnitChange"
            >
              <el-option
                v-for="unit in maintenanceUnits"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目负责人" prop="manager" required>
            <el-select
              v-model="formData.manager"
              placeholder="请选择项目负责人"
              style="width: 100%"
              filterable
              :disabled="readonly"
              @change="handleManagerChange"
            >
              <el-option
                v-for="manager in projectManagers"
                :key="manager.id"
                :label="manager.name"
                :value="manager.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactPhone" required>
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系方式"
              maxlength="11"
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12" v-if="showWorkload">
          <el-form-item label="工作量">
            <el-input
              v-model="formData.workload"
              placeholder="请输入工作量"
              type="number"
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="项目内容">
        <el-input
          v-model="formData.projectContent"
          type="textarea"
          :rows="4"
          placeholder="请输入项目内容"
          maxlength="500"
          show-word-limit
          :disabled="readonly"
          @input="handleInput"
        />
      </el-form-item>
      
      <el-form-item label="附件">
        <file-upload
          v-model="formData.attachments"
          :multiple="true"
          accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
          :max-size="10 * 1024 * 1024"
          :disabled="readonly"
          @input="handleInput"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getManagementUnits, getMaintenanceUnits, getProjectManagers } from '@/api/maintenance/projects'
import FileUpload from '@/components/Maintenance/FileUpload'

export default {
  name: 'BasicInfo',
  components: {
    FileUpload
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    projectType: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        projectName: '',
        projectType: '',
        startDate: '',
        endDate: '',
        managementUnit: '',
        supervisionUnit: '',
        maintenanceUnit: '',
        manager: '',
        contactPhone: '',
        workload: '',
        projectContent: '',
        attachments: []
      },
      
      // 选项数据
      managementUnits: [],
      maintenanceUnits: [],
      projectManagers: [],
      
      // 项目类型选项
      projectTypes: [
        { label: '月度养护', value: 'monthly' },
        { label: '保洁项目', value: 'cleaning' },
        { label: '应急养护', value: 'emergency' },
        { label: '预防养护', value: 'preventive' }
      ],
      
      // 表单验证规则
      rules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        projectType: [
          { required: true, message: '请选择项目类型', trigger: 'change' }
        ],
        startDate: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择结束时间', trigger: 'change' },
          { validator: this.validateEndDate, trigger: 'change' }
        ],
        managementUnit: [
          { required: true, message: '请选择管理单位', trigger: 'change' }
        ],
        maintenanceUnit: [
          { required: true, message: '请选择养护单位', trigger: 'change' }
        ],
        manager: [
          { required: true, message: '请选择项目负责人', trigger: 'change' }
        ],
        contactPhone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 是否显示工作量字段
    showWorkload() {
      return ['cleaning', 'emergency', 'preventive'].includes(this.formData.projectType)
    }
  },
  watch: {
    // 只监听外部传入的value，单向数据流
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 只在数据真正不同时才更新，避免循环
          const hasChanges = Object.keys(newVal).some(key => 
            JSON.stringify(newVal[key]) !== JSON.stringify(this.formData[key])
          )
          if (hasChanges) {
            this.formData = { ...this.formData, ...newVal }
          }
        }
      },
      immediate: true,
      deep: true
    },
    
    // 监听外部项目类型变化
    projectType: {
      handler(newVal) {
        if (newVal && newVal !== this.formData.projectType) {
          this.formData.projectType = newVal
        }
      },
      immediate: true
    }
  },
  async created() {
    await this.loadOptions()
  },
  methods: {
    // 加载选项数据
    async loadOptions() {
      try {
        const [managementRes, maintenanceRes] = await Promise.all([
          getManagementUnits(),
          getMaintenanceUnits()
        ])
        
        this.managementUnits = managementRes.data || []
        this.maintenanceUnits = maintenanceRes.data || []
      } catch (error) {
        this.$message.error('加载选项数据失败')
      }
    },
    
    // 统一的输入处理方法
    handleInput() {
      this.$nextTick(() => {
        this.$emit('input', { ...this.formData })
      })
    },
    
    // 项目类型变化
    handleProjectTypeChange(type) {
      // 只有当值真正变化时才触发事件
      if (type !== this.projectType) {
        this.$emit('project-type-change', type)
      }
      this.handleInput()
    },
    
    // 养护单位变化
    async handleMaintenanceUnitChange(unitId) {
      this.formData.manager = ''
      this.formData.contactPhone = ''
      this.projectManagers = []
      
      if (unitId) {
        try {
          const response = await getProjectManagers(unitId)
          this.projectManagers = response.data || []
        } catch (error) {
          this.$message.error('加载项目负责人失败')
        }
      }
      this.handleInput()
    },
    
    // 项目负责人变化 - 自动填充联系方式
    handleManagerChange(managerId) {
      if (managerId) {
        const selectedManager = this.projectManagers.find(manager => manager.id === managerId)
        if (selectedManager) {
          this.formData.contactPhone = selectedManager.phone
        }
      } else {
        this.formData.contactPhone = ''
      }
      this.handleInput()
    },
    
    // 验证结束时间
    validateEndDate(rule, value, callback) {
      if (value && this.formData.startDate) {
        if (new Date(value) <= new Date(this.formData.startDate)) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    
    // 表单验证
    validate() {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善基本信息')
          }
          resolve(valid)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.basic-info-form {
  @extend .common-form;
  
  // 覆盖特定样式以适应创建表单
  .maintenance-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }
}
</style>
