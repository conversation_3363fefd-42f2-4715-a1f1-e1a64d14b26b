<template>
  <div class="bridge-config">
    <div v-if="!readonly" class="config-header">
      <el-button
        type="primary"
        icon="el-icon-connection"
        @click="showBridgeSelector = true"
      >
        <i class="el-icon-s-home" v-if="infrastructureType === 'bridge'"></i>
        <i class="el-icon-place" v-else></i>
        关联{{ infrastructureType === 'bridge' ? '桥梁' : '隧道' }}
      </el-button>
    </div>
    
    <!-- 已关联桥梁/隧道列表 -->
    <div class="common-table">
      <el-table
        :data="bridgeList"
        class="maintenance-table"
        :empty-text="`暂无关联${infrastructureType === 'bridge' ? '桥梁' : '隧道'}`"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column 
          prop="name" 
          :label="`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}名称`" 
          min-width="120" 
          show-overflow-tooltip 
        />
        
        <el-table-column 
          prop="code" 
          :label="`${infrastructureType === 'bridge' ? '桥梁' : '隧道'}编号`" 
          width="120" 
          align="center" 
        />
        
        <el-table-column prop="road" label="所在道路" width="120" align="center" />
        
        <el-table-column prop="managementUnit" label="管理单位" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="maintenanceContent" label="养护内容" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="maintenanceStaff" label="养护人员" width="100" align="center" />
        
        <el-table-column v-if="!readonly" label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              class="danger-text"
              @click="removeBridgeAssociation(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 桥梁/隧道选择器 -->
    <bridge-selector
      :visible.sync="showBridgeSelector"
      :multiple="true"
      :selected-data="bridgeList"
      :infrastructure-type="infrastructureType"
      @confirm="handleBridgeSelection"
    />
  </div>
</template>

<script>
import BridgeSelector from '@/components/Maintenance/BridgeSelector'

export default {
  name: 'BridgeConfig',
  components: {
    BridgeSelector
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    infrastructureType: {
      type: String,
      default: 'bridge', // bridge, tunnel
      validator: value => ['bridge', 'tunnel'].includes(value)
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      bridgeList: [],
      showBridgeSelector: false
    }
  },
  watch: {
    // 只监听外部传入的value，单向数据流
    value: {
      handler(newVal) {
        if (newVal && newVal.bridges && Array.isArray(newVal.bridges)) {
          // 只在数据真正不同时才更新，避免循环
          if (JSON.stringify(newVal.bridges) !== JSON.stringify(this.bridgeList)) {
            this.bridgeList = [...newVal.bridges]
          }
        }
      },
      immediate: true,
      deep: true
    },
    
    showBridgeSelector(visible) {
      // 桥梁选择器显示状态变化
    }
  },
  methods: {
    // 统一的数据更新方法
    emitChange() {
      this.$nextTick(() => {
        this.$emit('input', {
          bridges: this.bridgeList
        })
      })
    },
    
    // 处理桥梁选择
    handleBridgeSelection(selectedBridges) {
      // 为新选择的桥梁/隧道设置默认的养护内容和人员
      const processedBridges = selectedBridges.map(bridge => ({
        ...bridge,
        maintenanceContent: bridge.maintenanceProject || '排水系统养护',
        maintenanceStaff: bridge.maintenanceStaff || '待分配'
      }))

      this.bridgeList = processedBridges

      if (processedBridges.length > 0) {
        this.$message.success(`成功关联 ${processedBridges.length} 个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)
        this.emitChange()
      }
    },

    // 移除桥梁/隧道关联
    removeBridgeAssociation(index) {
      this.bridgeList.splice(index, 1)
      this.$message.success(`已移除${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}关联`)
      this.emitChange()
    },
    
    // 表单验证
    validate() {
      if (this.bridgeList.length === 0) {
        this.$message.error(`请至少关联一个${this.infrastructureType === 'bridge' ? '桥梁' : '隧道'}`)
        return false
      }
      
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

.bridge-config {
  .config-header {
    margin-bottom: 24px;
    
    .el-button {
      i {
        margin-right: 4px;
      }
    }
  }
  
  .common-table {
    .maintenance-table {
      min-height: 200px;
    }
  }
  
  .danger-text {
    color: #ef4444 !important;
    
    &:hover {
      color: #dc2626 !important;
    }
  }
}
</style>
