<template>
  <div class="completion-info">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="160px"
      label-position="left"
      class="maintenance-form"
    >
      <!-- 双列布局：验收结论 & 竣工单位自检情况 -->
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="验收结论" prop="acceptanceResult" required>
            <el-input
              v-model="formData.acceptanceResult"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="竣工单位自检情况" prop="selfInspection" required>
            <el-input
              v-model="formData.selfInspection"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 双列布局：现场验收情况 & 管理方代表意见 -->
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="现场验收情况" prop="onSiteInspection" required>
            <el-input
              v-model="formData.onSiteInspection"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="管理方代表意见" prop="managerOpinion" required>
            <el-input
              v-model="formData.managerOpinion"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              :disabled="readonly"
              @input="handleInput"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 处置照片上传 -->
      <el-form-item label="处置照片">
        <div class="photo-upload-section">
          <!-- 照片分类Tab -->
          <el-tabs v-model="activePhotoTab" class="photo-tabs">
            <el-tab-pane
              v-for="category in photoCategories" 
              :key="category.key"
              :label="category.label"
              :name="category.key"
            >
              <div class="photo-upload-content">
                <!-- 上传按钮 -->
                <div v-if="!readonly" class="photo-upload-btn" @click="triggerPhotoUpload(category.key)">
                  <i class="el-icon-plus upload-icon"></i>
                  <input
                    :ref="`photoInput_${category.key}`"
                    type="file"
                    accept="image/*"
                    multiple
                    style="display: none"
                    @change="handlePhotoUpload"
                  />
                </div>
                
                <!-- 已上传的照片网格 -->
                <div v-if="getCurrentCategoryPhotos().length > 0" class="photo-grid">
                  <div 
                    v-for="(photo, index) in getCurrentCategoryPhotos()" 
                    :key="index"
                    class="photo-item"
                  >
                    <img :src="photo.url" :alt="photo.name" class="photo-image" />
                    <div class="photo-overlay">
                      <el-button
                        type="text"
                        icon="el-icon-delete"
                        class="delete-photo-btn"
                        @click="removePhoto(activePhotoTab, index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form-item>

      <!-- 附件上传 -->
      <el-form-item label="附件">
        <file-upload
          v-model="formData.attachments"
          :multiple="true"
          accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
          :max-size="10 * 1024 * 1024"
          :disabled="readonly"
          @input="handleInput"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import FileUpload from '@/components/Maintenance/FileUpload'

export default {
  name: 'CompletionInfo',
  components: {
    FileUpload
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activePhotoTab: 'before',
      photoCategories: [
        { key: 'before', label: '处置前' },
        { key: 'during', label: '处置中' },
        { key: 'after', label: '处置后' }
      ],
      formData: {
        acceptanceResult: '',       // 验收结论
        selfInspection: '',         // 竣工单位自检情况
        onSiteInspection: '',       // 现场验收情况
        managerOpinion: '',         // 管理方代表意见
        photos: {                   // 处置照片
          before: [],               // 处置前
          during: [],               // 处置中
          after: []                 // 处置后
        },
        attachments: []             // 附件列表
      },
      rules: {
        acceptanceResult: [
          { required: true, message: '请输入验收结论', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        selfInspection: [
          { required: true, message: '请输入竣工单位自检情况', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        onSiteInspection: [
          { required: true, message: '请输入现场验收情况', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        managerOpinion: [
          { required: true, message: '请输入管理方代表意见', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.formData = { ...this.formData, ...newVal }
        }
      },
      immediate: true,
      deep: true
    },
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    }
  },
  methods: {
    // 输入处理
    handleInput() {
      this.$emit('input', this.formData)
    },
    
    // 触发照片上传
    triggerPhotoUpload(categoryKey) {
      const inputRef = this.$refs[`photoInput_${categoryKey}`]
      if (inputRef && inputRef[0]) {
        inputRef[0].click()
      } else if (inputRef) {
        inputRef.click()
      }
    },
    
    // 照片上传处理
    handlePhotoUpload(event) {
      const files = Array.from(event.target.files)
      const validFiles = []
      
      for (const file of files) {
        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          this.$message.error(`文件 ${file.name} 不是图片格式`)
          continue
        }
        
        // 检查文件大小
        if (file.size > 5 * 1024 * 1024) {
          this.$message.error(`图片 ${file.name} 大小超过5MB限制`)
          continue
        }
        
        validFiles.push(file)
      }
      
      // 处理有效文件
      validFiles.forEach(file => {
        const reader = new FileReader()
        reader.onload = (e) => {
          const photoObj = {
            name: file.name,
            url: e.target.result,
            file: file
          }
          this.formData.photos[this.activePhotoTab].push(photoObj)
          this.handleInput()
        }
        reader.readAsDataURL(file)
      })
      
      // 清空input值，允许重复选择
      event.target.value = ''
    },
    
    // 获取当前分类的照片
    getCurrentCategoryPhotos() {
      return this.formData.photos[this.activePhotoTab] || []
    },
    
    // 删除照片
    removePhoto(category, index) {
      this.formData.photos[category].splice(index, 1)
      this.handleInput()
    },
    
    // 表单验证
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            resolve(this.formData)
          } else {
            reject(new Error('表单验证失败'))
          }
        })
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
      this.formData = {
        acceptanceResult: '',
        selfInspection: '',
        onSiteInspection: '',
        managerOpinion: '',
        photos: {
          before: [],
          during: [],
          after: []
        },
        attachments: []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.completion-info {
  .maintenance-form {
    max-width: 1200px;
    margin: 0 auto;
    
    // 调整表单项间距，使布局更均匀
    ::v-deep .el-form-item {
      margin-bottom: 24px;
      
      .el-form-item__label {
        text-align: left;
        justify-content: flex-start;
        font-weight: 500;
      }
      
      .el-form-item__content {
        text-align: left;
      }
    }
    
    // 确保行间距合适
    .el-row {
      margin-bottom: 8px;
    }
  }
}

// 处置照片上传区域
.photo-upload-section {
  width: 100%;
  
  .photo-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 20px;
      border-bottom: none; // 移除下边框
    }
    
    ::v-deep .el-tabs__nav-wrap::after {
      display: none; // 隐藏导航下方的线条
    }
    
    ::v-deep .el-tabs__item {
      color: #9ca3af;
      
      &.is-active {
        color: #3b82f6;
      }
    }
    
    ::v-deep .el-tabs__active-bar {
      background-color: #3b82f6;
    }
  }
  
  .photo-upload-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .photo-upload-btn {
    width: 120px;
    height: 120px;
    border: 2px dashed #4b5563;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: #374151;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #3b82f6;
      background: rgba(59, 130, 246, 0.05);
    }
    
    .upload-icon {
      font-size: 32px;
      color: #ffffff;
    }
  }
  
  .photo-grid {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .photo-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #4b5563;
    
    .photo-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .photo-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      .delete-photo-btn {
        color: #ef4444;
        font-size: 20px;
        
        &:hover {
          color: #dc2626;
        }
      }
    }
    
    &:hover .photo-overlay {
      opacity: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .completion-info {
    .maintenance-form {
      max-width: 100%;
    }
  }
  
  .photo-upload-section {
    .photo-upload-content {
      justify-content: center;
    }
  }
}
</style>