<template>
  <div class="disease-config">
    <!-- 添加病害按钮 - 复用ProjectConfig的样式 -->
    <div v-if="!readonly" class="add-project-row" style="margin-bottom: 24px;">
      <el-button 
        type="primary" 
        icon="el-icon-link"
        class="add-project-btn"
        @click="showDiseaseDialog = true"
      >
        关联病害
      </el-button>
    </div>
    
    <!-- 已关联病害列表 - 复用通用表格样式 -->
    <div class="common-table">
      <el-table
        :data="diseaseList"
        class="maintenance-table"
        empty-text="暂无关联病害"
        style="width: 100%"
        :row-style="{ height: '32px' }"
        size="small"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
        
        <el-table-column prop="diseasePart" label="病害部位" width="100" align="center" />
        
        <el-table-column prop="diseaseType" label="病害类型" width="120" align="center" />
        
        <el-table-column prop="diseaseCount" label="病害数量" width="100" align="center" />
        
        <el-table-column prop="diseaseDescription" label="病害描述" min-width="150" show-overflow-tooltip />
        
        <el-table-column v-if="!readonly" label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              class="maintenance-danger-text"
              @click="removeDiseaseAssociation(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 病害选择弹窗 -->
    <el-dialog
      title="关联病害"
      :visible.sync="showDiseaseDialog"
      custom-class="disease-dialog inspection-dialog-base modern-dialog dark-theme force-high-zindex common-dialog-wide project-dialog-fixed-size"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
      :modal-append-to-body="true"
      :append-to-body="true"
      top="5vh"
      destroy-on-close
    >
      <div class="dialog-content">
        <!-- 搜索表单 - 复用通用搜索表单样式 -->
        <div class="search-form">
          <el-form :model="searchParams" inline>
            <el-form-item label="桥梁名称">
              <el-input
                v-model="searchParams.bridgeName"
                placeholder="请输入桥梁名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            
            <el-form-item label="病害类型">
              <el-select
                v-model="searchParams.type"
                placeholder="请选择病害类型"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="type in diseaseTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.label"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="searchDiseases">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 病害列表 - 复用通用表格样式 -->
        <div class="common-table">
          <el-table
            ref="diseaseSelectionTable"
            v-loading="diseaseLoading"
            :data="availableDiseases"
            class="maintenance-table"
            style="width: 100%"
            :row-style="{ height: '32px' }"
            size="small"
            @selection-change="handleDiseaseSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            
            <el-table-column type="index" label="序号" width="60" align="center" />
            
            <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
            
            <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
            
            <el-table-column prop="diseasePart" label="病害部位" width="100" align="center" />
            
            <el-table-column prop="diseaseType" label="病害类型" width="120" align="center" />
            
            <el-table-column prop="diseaseCount" label="病害数量" width="100" align="center" />
            
            <el-table-column prop="diseaseDescription" label="病害描述" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="searchParams.pageNum"
            :page-sizes="[10, 20, 50]"
            :page-size="searchParams.pageSize"
            :total="diseaseTotal"
            layout="total, sizes, prev, pager, next"
            @size-change="handleDiseaseSizeChange"
            @current-change="handleDiseaseCurrentChange"
          />
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmDiseaseSelection"
          :disabled="selectedDiseases.length === 0"
        >
          确定 {{ selectedDiseases.length > 0 ? `(已选择 ${selectedDiseases.length} 项)` : '' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDiseaseList } from '@/api/maintenance/projects'

export default {
  name: 'DiseaseConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      diseaseList: [],
      showDiseaseDialog: false,
      diseaseLoading: false,
      availableDiseases: [],
      selectedDiseases: [],
      diseaseTotal: 0,
      
      // 搜索参数
      searchParams: {
        pageNum: 1,
        pageSize: 20,
        bridgeName: '',
        type: ''
      },
      
      // 病害类型选项
      diseaseTypes: [
        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },
        { label: '照明设施缺失', value: 'lighting_missing' },
        { label: '护栏损坏', value: 'guardrail_damage' },
        { label: '桥面破损', value: 'deck_damage' },
        { label: '排水不畅', value: 'drainage_poor' }
      ]
    }
  },
  watch: {
    // 只监听外部传入的value，单向数据流
    value: {
      handler(newVal) {
        if (newVal && newVal.diseases && Array.isArray(newVal.diseases)) {
          // 只在数据真正不同时才更新，避免循环
          if (JSON.stringify(newVal.diseases) !== JSON.stringify(this.diseaseList)) {
            this.diseaseList = [...newVal.diseases]
          }
        }
      },
      immediate: true,
      deep: true
    },
    
    showDiseaseDialog(visible) {
      if (visible) {
        this.loadAvailableDiseases()
      }
    }
  },
  methods: {
    // 统一的数据更新方法
    emitChange() {
      this.$nextTick(() => {
        this.$emit('input', {
          diseases: this.diseaseList
        })
      })
    },
    
    // 加载可用病害列表
    async loadAvailableDiseases() {
      this.diseaseLoading = true
      try {
        const response = await getDiseaseList(this.searchParams)
        console.log('病害列表API响应:', response)
        
        // 适配不同的API响应格式
        let diseases = []
        let total = 0
        
        console.log('API响应结构:', response)
        
        if (response.data) {
          // 优先使用 data.rows (模拟API格式)
          if (response.data.rows) {
            diseases = response.data.rows
            total = response.data.total || 0
          }
          // 其次使用 data.list (标准格式)
          else if (response.data.list) {
            diseases = response.data.list
            total = response.data.total || 0
          }
          // 最后直接使用 data (简单格式)
          else if (Array.isArray(response.data)) {
            diseases = response.data
            total = response.data.length
          }
        }
        
        console.log('解析后的病害数据:', diseases, '总数:', total)
        
        // 数据字段映射和标准化
        this.availableDiseases = diseases.map(disease => ({
          id: disease.id,
          bridgeName: disease.bridgeName || '未知桥梁',
          diseaseCode: disease.diseaseCode || disease.code || '-',
          diseasePart: disease.location || disease.diseasePart || '-', // 修正：使用 location 字段
          diseaseType: disease.type || disease.diseaseType || '-', // 修正：使用 type 字段
          diseaseCount: disease.quantity || disease.diseaseCount || 0, // 修正：使用 quantity 字段
          diseaseDescription: disease.description || disease.diseaseDescription || '-', // 修正：使用 description 字段
          diseaseLevel: disease.level || disease.diseaseLevel || 1, // 修正：使用 level 字段
          reporter: disease.reportPerson || disease.reporter || '-', // 修正：使用 reportPerson 字段
          reportTime: disease.reportTime || new Date().toISOString()
        }))
        
        this.diseaseTotal = total
        
        // 设置已选中的病害
        this.$nextTick(() => {
          if (this.$refs.diseaseSelectionTable) {
            this.availableDiseases.forEach(disease => {
              const isSelected = this.diseaseList.some(selected => selected.id === disease.id)
              if (isSelected) {
                this.$refs.diseaseSelectionTable.toggleRowSelection(disease, true)
              }
            })
          }
        })
        
        // 如果没有数据，显示提示信息
        if (this.availableDiseases.length === 0) {
          this.$message.info('暂无可关联的病害数据')
        }
        
      } catch (error) {
        console.error('加载病害列表失败:', error)
        this.$message.error('加载病害列表失败，请稍后重试')
        
        // 提供默认的示例数据
        this.availableDiseases = this.getDefaultDiseaseData()
        this.diseaseTotal = this.availableDiseases.length
      } finally {
        this.diseaseLoading = false
      }
    },
    
    // 搜索病害
    searchDiseases() {
      this.searchParams.pageNum = 1
      this.loadAvailableDiseases()
    },
    
    // 重置搜索
    resetSearch() {
      this.searchParams = {
        pageNum: 1,
        pageSize: 20,
        bridgeName: '',
        type: ''
      }
      this.loadAvailableDiseases()
    },
    
    // 病害选择变化
    handleDiseaseSelectionChange(selection) {
      this.selectedDiseases = selection
    },
    
    // 处理弹窗关闭
    handleDialogClose(done) {
      // 重置选择状态
      this.selectedDiseases = []
      // 重置搜索条件
      this.searchParams = {
        pageNum: 1,
        pageSize: 20,
        bridgeName: '',
        type: ''
      }
      
      // 如果有done回调，调用它；否则直接关闭
      if (typeof done === 'function') {
        done()
      } else {
        this.showDiseaseDialog = false
      }
    },

    // 确认病害选择
    confirmDiseaseSelection() {
      if (this.selectedDiseases.length === 0) {
        this.$message.warning('请先选择要关联的病害')
        return
      }

      // 合并已有病害和新选择的病害
      const existingIds = this.diseaseList.map(disease => disease.id)
      const newDiseases = this.selectedDiseases.filter(disease => 
        !existingIds.includes(disease.id)
      )
      
      // 检查重复关联
      const duplicateCount = this.selectedDiseases.length - newDiseases.length
      if (duplicateCount > 0) {
        this.$message.warning(`已过滤 ${duplicateCount} 个重复的病害`)
      }
      
      if (newDiseases.length > 0) {
        this.diseaseList = [...this.diseaseList, ...newDiseases]
        this.$message.success(`成功关联 ${newDiseases.length} 个病害`)
        this.emitChange()
      } else if (duplicateCount === 0) {
        this.$message.info('未选择新的病害')
      }
      
      // 关闭弹窗
      this.handleDialogClose()
    },
    
    // 移除病害关联
    removeDiseaseAssociation(index) {
      this.diseaseList.splice(index, 1)
      this.$message.success('已移除病害关联')
      this.emitChange()
    },
    
    // 病害分页大小变化
    handleDiseaseSizeChange(val) {
      this.searchParams.pageSize = val
      this.loadAvailableDiseases()
    },
    
    // 病害当前页变化
    handleDiseaseCurrentChange(val) {
      this.searchParams.pageNum = val
      this.loadAvailableDiseases()
    },
    
    // 获取默认病害数据（用于API失败时的降级处理）
    getDefaultDiseaseData() {
      return [
        {
          id: 989,
          bridgeName: '湘江大桥',
          diseaseCode: '989',
          diseasePart: '伸缩缝',
          diseaseType: '伸缩缝缺失',
          diseaseCount: 7,
          diseaseDescription: '桥梁东侧伸缩缝存在缺失，影响行车安全',
          diseaseLevel: 3,
          reporter: '张三',
          reportTime: '2025-09-01 09:30:00'
        },
        {
          id: 988,
          bridgeName: '湘江大桥',
          diseaseCode: '988',
          diseasePart: '伸缩缝',
          diseaseType: '伸缩缝缺失',
          diseaseCount: 47,
          diseaseDescription: '桥梁西侧多处伸缩缝存在缺失现象',
          diseaseLevel: 2,
          reporter: '李四',
          reportTime: '2025-09-02 14:20:00'
        },
        {
          id: 987,
          bridgeName: '浏阳河大桥',
          diseaseCode: '987',
          diseasePart: '照明设施',
          diseaseType: '照明设施缺失',
          diseaseCount: 42,
          diseaseDescription: '桥梁照明设施老化，部分路段照明不足',
          diseaseLevel: 1,
          reporter: '王五',
          reportTime: '2025-09-03 16:45:00'
        },
        {
          id: 986,
          bridgeName: '橘子洲大桥',
          diseaseCode: '986',
          diseasePart: '护栏',
          diseaseType: '护栏损坏',
          diseaseCount: 15,
          diseaseDescription: '桥梁护栏部分段落存在损坏，需要及时修复',
          diseaseLevel: 2,
          reporter: '赵六',
          reportTime: '2025-09-04 10:15:00'
        },
        {
          id: 985,
          bridgeName: '银盆岭大桥',
          diseaseCode: '985',
          diseasePart: '桥面',
          diseaseType: '桥面破损',
          diseaseCount: 23,
          diseaseDescription: '桥面沥青出现裂缝和坑洞，影响行车舒适性',
          diseaseLevel: 3,
          reporter: '孙七',
          reportTime: '2025-09-05 15:30:00'
        },
        {
          id: 984,
          bridgeName: '猴子石大桥',
          diseaseCode: '984',
          diseasePart: '排水系统',
          diseaseType: '排水不畅',
          diseaseCount: 8,
          diseaseDescription: '桥梁排水系统堵塞，雨季积水严重',
          diseaseLevel: 2,
          reporter: '周八',
          reportTime: '2025-09-06 08:45:00'
        }
      ]
    },

    // 表单验证
    validate() {
      // 病害关联是可选的，所以总是返回true
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.disease-config {
  // 复用通用样式，无需自定义样式
}
</style>
