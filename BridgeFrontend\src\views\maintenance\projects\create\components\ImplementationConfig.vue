<template>
  <div class="implementation-config">
    <!-- 添加工作内容按钮 - 参考应急项目结构 -->
    <div v-if="!readonly" class="add-work-row">
      <el-button 
        type="primary" 
        icon="el-icon-plus"
        class="add-work-btn"
        @click="addWork"
      >
        添加工作内容
      </el-button>
    </div>
    
    <!-- 工作内容列表 - 按原型图每行一个工作内容的结构 -->
    <div class="work-list">
      <div 
        v-for="(work, index) in workList" 
        :key="index"
        class="work-row"
      >
        <div class="work-input-container">
          <el-input
            v-model="work.name"
            placeholder="请输入工作内容名称"
            class="work-name-input"
            :disabled="readonly"
            @blur="validateWork(index)"
            @input="emitChange"
          />
        </div>
        
        <el-button
          v-if="!readonly"
          type="text"
          class="cancel-btn"
          @click="removeWork(index)"
        >
          取消
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImplementationConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      workList: []
    }
  },
  watch: {
    // 监听外部传入的value，单向数据流
    value: {
      handler(newVal) {
        if (newVal && newVal.workItems && Array.isArray(newVal.workItems) && newVal.workItems.length > 0) {
          // 只在数据真正不同时才更新，避免循环
          if (JSON.stringify(newVal.workItems) !== JSON.stringify(this.workList)) {
            this.workList = [...newVal.workItems]
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 初始化时确保至少有一个空工作内容
    if (this.workList.length === 0) {
      this.initializeWork()
      this.emitChange()
    }
  },
  methods: {
    // 统一的数据更新方法
    emitChange() {
      this.$nextTick(() => {
        this.$emit('input', {
          workItems: this.workList
        })
      })
    },
    
    // 初始化工作内容（仅在组件初始化时使用）
    initializeWork() {
      if (this.workList.length === 0) {
        const newWork = {
          name: ''
        }
        this.workList = [newWork]
      }
    },
    
    // 添加工作内容（用户点击按钮时使用）
    addWork() {
      const newWork = {
        name: ''
      }
      this.workList.push(newWork)
      this.emitChange()
    },
    
    // 移除工作内容
    removeWork(index) {
      if (this.workList.length > 1) {
        this.workList.splice(index, 1)
        this.emitChange()
      } else {
        this.$message.warning('至少需要保留一个工作内容')
      }
    },
    
    // 验证工作内容
    validateWork(index) {
      const work = this.workList[index]
      if (!work.name) {
        return
      }
      
      // 检查重复
      const duplicateIndex = this.workList.findIndex((w, i) => 
        i !== index && w.name === work.name
      )
      
      if (duplicateIndex !== -1) {
        this.$message.warning('工作内容名称不能重复')
        work.name = ''
      }
    },
    
    // 表单验证
    validate() {
      // 检查是否有有效工作内容
      const validWorks = this.workList.filter(work => work.name.trim())
      
      if (validWorks.length === 0) {
        this.$message.error('请至少添加一个工作内容')
        return false
      }
      
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

// 实施配置组件使用公共样式
</style>
