<template>
  <div class="project-config">
    <!-- 添加项目按钮 - 按原型图结构 -->
    <div v-if="!readonly" class="add-project-row">
      <el-button 
        type="primary" 
        icon="el-icon-plus"
        class="add-project-btn"
        @click="addProject"
      >
        添加项目
      </el-button>
    </div>
    
    <!-- 项目列表 - 按原型图每行一个项目的结构 -->
    <div class="project-list">
      <div 
        v-for="(project, index) in projectList" 
        :key="index"
        class="project-row"
      >
        <div class="project-input-container">
          <el-input
            v-model="project.name"
            :placeholder="getPlaceholder()"
            class="project-name-input"
            :disabled="readonly"
            @blur="validateProject(index)"
            @input="emitChange"
          />
          
          <!-- 保洁项目的频次配置 - 按原型图布局 -->
          <template v-if="projectType === 'cleaning'">
            <el-input
              v-model="project.frequency"
              type="number"
              min="1"
              max="365"
              class="frequency-input"
              :disabled="readonly"
              @input="emitChange"
            />
            <span class="frequency-unit">天/1次</span>
          </template>
        </div>
        
        <el-button
          v-if="!readonly"
          type="text"
          class="cancel-btn"
          @click="removeProject(index)"
        >
          取消
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProjectConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    projectType: {
      type: String,
      required: true
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      projectList: []
    }
  },
  watch: {
    // 只监听外部传入的value，单向数据流
    value: {
      handler(newVal) {
        if (newVal && newVal.projects && Array.isArray(newVal.projects) && newVal.projects.length > 0) {
          // 只在数据真正不同时才更新，避免循环
          if (JSON.stringify(newVal.projects) !== JSON.stringify(this.projectList)) {
            this.projectList = [...newVal.projects]
          }
        }
      },
      immediate: true,
      deep: true
    },
    
    // 监听项目类型变化，重新初始化
    projectType: {
      handler(newType, oldType) {
        if (newType !== oldType && this.projectList.length > 0) {
          this.projectList = []
          this.$nextTick(() => {
            this.initializeProject()
            this.emitChange()
          })
        }
      }
    }
  },
  mounted() {
    // 初始化时确保至少有一个空项目
    if (this.projectList.length === 0) {
      this.initializeProject()
      this.emitChange()
    }
  },
  methods: {
    // 统一的数据更新方法
    emitChange() {
      this.$nextTick(() => {
        this.$emit('input', {
          projects: this.projectList
        })
      })
    },
    
    // 初始化项目（仅在组件初始化时使用）
    initializeProject() {
      if (this.projectList.length === 0) {
        const newProject = {
          name: '',
          frequency: this.projectType === 'cleaning' ? 7 : null
        }
        this.projectList = [newProject]
      }
    },
    
    // 添加项目（用户点击按钮时使用）
    addProject() {
      const newProject = {
        name: '',
        frequency: this.projectType === 'cleaning' ? 7 : null
      }
      this.projectList.push(newProject)
      this.emitChange()
    },
    
    // 移除项目
    removeProject(index) {
      if (this.projectList.length > 1) {
        this.projectList.splice(index, 1)
        this.emitChange()
      } else {
        this.$message.warning('至少需要保留一个项目')
      }
    },
    
    
    // 验证项目
    validateProject(index) {
      const project = this.projectList[index]
      if (!project.name) {
        return
      }
      
      // 检查重复
      const duplicateIndex = this.projectList.findIndex((p, i) => 
        i !== index && p.name === project.name
      )
      
      if (duplicateIndex !== -1) {
        this.$message.warning('项目名称不能重复')
        project.name = ''
      }
    },
    
    // 获取输入框占位符
    getPlaceholder() {
      const placeholders = {
        monthly: '请输入养护项目名称',
        cleaning: '请输入保洁项目名称',
        emergency: '请输入应急项目名称',
        preventive: '请输入预防养护项目名称'
      }
      return placeholders[this.projectType] || '请输入项目名称'
    },
    
    
    // 表单验证
    validate() {
      // 检查是否有有效项目
      const validProjects = this.projectList.filter(project => project.name.trim())
      
      if (validProjects.length === 0) {
        this.$message.error('请至少添加一个项目')
        return false
      }
      
      // 检查保洁项目的频次
      if (this.projectType === 'cleaning') {
        const invalidFrequency = validProjects.some(project => 
          !project.frequency || project.frequency < 1 || project.frequency > 365
        )
        
        if (invalidFrequency) {
          this.$message.error('请设置正确的清洁频次（1-365天）')
          return false
        }
      }
      
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.project-config {
  @extend .common-project-config;
  padding: 20px;
  .add-project-row {
    margin-bottom: 24px; // 增加与下方内容的间距
    display: flex;
    justify-content: flex-start;
    
    // 按钮样式已通过公共样式提供
  }
  
  // 项目列表区域
  .project-list {
    .project-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      gap: 12px; // 设置各元素之间的间距
      
      .project-input-container {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
        
        .project-name-input {
          flex: 1;
          min-width: 200px;
          
          :deep(.el-input__inner) {
            background: #374151;
            border-color: #6b7280;
            color: #ffffff;
            height: 40px;
            border-radius: 6px;
            
            &::placeholder {
              color: #9ca3af;
            }
            
            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }
        
        // 保洁项目的频次输入框
        .frequency-input {
          width: 80px;
          
          :deep(.el-input__inner) {
            background: #374151;
            border-color: #6b7280;
            color: #ffffff;
            height: 40px;
            border-radius: 6px;
            text-align: center;
            
            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }
        
        .frequency-unit {
          color: #e5e7eb;
          font-size: 14px;
          white-space: nowrap;
          margin-left: 4px;
        }
      }
      
      .cancel-btn {
        color: #ef4444;
        font-size: 14px;
        min-width: 60px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
          color: #dc2626;
          background-color: rgba(239, 68, 68, 0.1);
        }
        
        &:focus {
          color: #dc2626;
          background-color: rgba(239, 68, 68, 0.1);
        }
      }
    }
    
    // 确保最后一行不会有多余的margin
    .project-row:last-child {
      margin-bottom: 0;
    }
  }
  
  // 只读模式下的样式调整
  &.readonly {
    .project-input-container {
      .project-name-input :deep(.el-input__inner) {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
      
      .frequency-input :deep(.el-input__inner) {
        background: #1f2937;
        border-color: #374151;
        color: #d1d5db;
      }
    }
  }
}

// 确保在深色主题下有良好的视觉效果
.maintenance-theme .project-config,
.inspection-theme .project-config {
  .add-project-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .project-row {
    .project-input-container {
      .project-name-input :deep(.el-input__inner),
      .frequency-input :deep(.el-input__inner) {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
