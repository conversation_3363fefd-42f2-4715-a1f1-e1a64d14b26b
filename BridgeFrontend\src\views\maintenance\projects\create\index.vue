<template>
  <div class="maintenance-theme inspection-container">
    <div class="page-container">
      <div class="card-container">
        <!-- 弹窗标题栏 -->
        <div v-if="!isModal" class="page-header">
          <h2>{{ getPageTitle() }}</h2>
          <el-button
            type="text"
            icon="el-icon-close"
            class="close-btn"
            @click="handleClose"
          />
        </div>
        
        <!-- 步骤导航 -->
        <step-navigation
          :steps="steps"
          :current-step="currentStep"
          :allow-click="allowStepClick"
          :clickable-steps="clickableSteps"
          @step-click="handleStepClick"
        />
        
        <!-- 步骤内容 -->
        <div class="step-content">
          <!-- 基本信息 -->
          <basic-info
            v-if="currentStep === 0"
            ref="basicInfo"
            v-model="formData.basicInfo"
            :project-type="projectType"
            :readonly="isReadonlyMode"
            @project-type-change="handleProjectTypeChange"
          />
          
          <!-- 养护项目配置 -->
          <project-config
            v-if="currentStep === 1 && showProjectConfig"
            ref="projectConfig"
            v-model="formData.projectConfig"
            :project-type="projectType"
            :readonly="isReadonlyMode"
          />
          
          <!-- 病害养护配置 -->
          <disease-config
            v-if="(currentStep === 2 && projectType === 'monthly') || (currentStep === 1 && projectType === 'preventive')"
            ref="diseaseConfig"
            v-model="formData.diseaseConfig"
            :readonly="isReadonlyMode"
          />
          
          <!-- 实施信息配置 - 预防养护第三步 -->
          <implementation-config
            v-if="currentStep === 2 && projectType === 'preventive'"
            ref="implementationConfig"
            v-model="formData.implementationConfig"
            :readonly="isReadonlyMode"
          />
          
          <!-- 竣工信息配置 - 预防养护第四步 -->
          <completion-info
            v-if="currentStep === 3 && projectType === 'preventive'"
            ref="completionInfo"
            v-model="formData.completionInfo"
            :readonly="isReadonlyMode"
          />
          
          <!-- 养护桥梁配置 - 只在非审批/查看/修改模式下显示 -->
          <bridge-config
            v-if="currentStep === getLastStepIndex() && projectType !== 'preventive' && !isApprovalMode && !isViewMode && !isEditMode"
            ref="bridgeConfig"
            v-model="formData.bridgeConfig"
            :infrastructure-type="infrastructureType"
            :readonly="isReadonlyMode"
          />
          
          <!-- 审批信息 - 审批模式、查看模式、修改模式的最后一步显示 -->
          <approval-info
            v-if="currentStep === getLastStepIndex() && (isApprovalMode || isViewMode || isEditMode)"
            ref="approvalInfo"
            v-model="formData.approvalInfo"
            :project-id="currentProjectId"
            :show-approval-form="canApprove"
            @approval-submitted="handleApprovalSubmitted"
            @cancel="handleApprovalCancel"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="!isView" class="action-buttons">
          <!-- 审批模式：只显示退回/通过按钮 -->
          <template v-if="isApprovalMode">
            <el-button @click="handleReject">退回</el-button>
            <el-button type="primary" @click="handleApprove">通过</el-button>
          </template>
          
          <!-- 修改模式：在审批信息页只显示保存/提交，不显示上一步和更新 -->
          <template v-else-if="isEditMode && currentStep === getLastStepIndex()">
            <el-button @click="handleSave">保存</el-button>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </template>
          
          <!-- 非修改模式或非最后一步：显示正常的步骤按钮 -->
          <template v-else>
            <el-button @click="handleSave">保存</el-button>
            
            <el-button
              v-if="currentStep > 0"
              @click="handlePrevStep"
            >
              上一步
            </el-button>
            
            <el-button
              v-if="currentStep < getLastStepIndex()"
              type="primary"
              @click="handleNextStep"
            >
              下一步
            </el-button>
            
            <el-button
              v-if="currentStep === getLastStepIndex()"
              type="primary"
              @click="handleSubmit"
            >
              {{ isEditMode ? '更新' : '提交' }}
            </el-button>
          </template>
        </div>
        
        <!-- 查看模式的关闭按钮 -->
        <div v-if="isView" class="action-buttons">
          <el-button @click="handleClose">关闭</el-button>
          <el-button v-if="isModal" type="primary" @click="handleEdit">编辑</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StepNavigation from '@/components/Maintenance/StepNavigation'
import BasicInfo from './components/BasicInfo'
import ProjectConfig from './components/ProjectConfig'
import DiseaseConfig from './components/DiseaseConfig'
import BridgeConfig from './components/BridgeConfig'
import ImplementationConfig from './components/ImplementationConfig'
import CompletionInfo from './components/CompletionInfo'
import ApprovalInfo from './components/ApprovalInfo'
import { getProjectDetail, createProject, updateProject } from '@/api/maintenance/projects'

export default {
  name: 'MaintenanceProjectCreate',
  components: {
    StepNavigation,
    BasicInfo,
    ProjectConfig,
    DiseaseConfig,
    BridgeConfig,
    ImplementationConfig,
    CompletionInfo,
    ApprovalInfo
  },
  data() {
    return {
      loading: false,
      currentStep: 0,
      projectType: 'monthly', // 默认为月度养护
      infrastructureType: 'bridge', // bridge, tunnel
      
      // 表单数据
      formData: {
        basicInfo: {},
        projectConfig: {},
        diseaseConfig: {},
        implementationConfig: {},
        bridgeConfig: {},
        completionInfo: {},
        approvalInfo: {}
      }
    }
  },
  props: {
    // 弹框模式下传入的属性
    isModal: {
      type: Boolean,
      default: false
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    },
    isApproval: {
      type: Boolean,
      default: false
    },
    infrastructureType: {
      type: String,
      default: 'bridge'
    }
  },
  computed: {
    // 兼容路由模式和弹框模式
    currentProjectId() {
      return this.isModal ? this.projectId : this.$route.params.id
    },
    
    isEditMode() {
      return this.isModal ? this.isEdit : !!this.$route.params.id
    },
    
    isViewMode() {
      return this.isView
    },
    
    isApprovalMode() {
      return this.isApproval
    },
    
    // 是否为只读模式（查看或审批）
    isReadonlyMode() {
      return this.isView || this.isApproval
    },
    
    // 是否允许步骤点击
    allowStepClick() {
      // 查看和审批模式始终允许点击
      if (this.isViewMode || this.isApprovalMode) {
        return true
      }
      // 新增和修改模式允许有条件的点击（由clickableSteps控制具体哪些步骤可点击）
      return true
    },
    
    // 可点击的步骤列表
    clickableSteps() {
      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {
        // 查看、审批和修改模式所有步骤都可点击
        return Array.from({ length: this.steps.length }, (_, i) => i)
      }
      
      // 新增模式：当前步骤及之前的步骤可点击
      return Array.from({ length: this.currentStep + 1 }, (_, i) => i)
    },
    
    // 根据项目类型确定步骤
    steps() {
      const baseSteps = [
        { title: '基本信息' }
      ]
      
      let projectSteps = []
      
      if (this.projectType === 'monthly') {
        // 月度养护：基本信息 → 养护项目 → 病害养护 → 养护桥梁
        projectSteps = [
          ...baseSteps,
          { title: '养护项目' },
          { title: '病害养护' },
          { title: '养护桥梁' }
        ]
      } else if (this.projectType === 'cleaning') {
        // 保洁项目：基本信息 → 保洁项目 → 保洁桥梁
        projectSteps = [
          ...baseSteps,
          { title: '保洁项目' },
          { title: '保洁桥梁' }
        ]
      } else if (this.projectType === 'emergency') {
        // 应急养护：基本信息 → 应急项目 → 养护桥梁
        projectSteps = [
          ...baseSteps,
          { title: '应急项目' },
          { title: '养护桥梁' }
        ]
      } else if (this.projectType === 'preventive') {
        // 预防养护：基本信息 → 关联病害 → 实施信息 → 竣工信息
        projectSteps = [
          ...baseSteps,
          { title: '关联病害' },
          { title: '实施信息' },
          { title: '竣工信息' }
        ]
      } else {
        // 默认步骤
        projectSteps = [
          ...baseSteps,
          { title: '项目配置' },
          { title: '关联对象' }
        ]
      }
      
      // 审批模式、查看模式、修改模式下添加审批信息步骤
      if (this.isApprovalMode || this.isViewMode || this.isEditMode) {
        projectSteps.push({ title: '审批信息' })
      }
      
      return projectSteps
    },
    
    // 是否显示项目配置步骤
    showProjectConfig() {
      return ['monthly', 'cleaning', 'emergency'].includes(this.projectType)
    },
    
    // 是否显示病害配置步骤
    showDiseaseConfig() {
      return ['monthly', 'preventive'].includes(this.projectType)
    },
    
    // 是否可以进行审批操作
    canApprove() {
      // 在实际项目中，这里应该根据当前用户权限和项目状态来判断
      // 目前简化为审批模式下就可以审批
      return this.isApprovalMode
    }
  },
    async created() {
    // 兼容路由模式和弹框模式获取基础设施类型
    if (!this.isModal) {
      this.infrastructureType = this.$route.query.type || 'bridge'
    }
    
    if (this.isEditMode || this.isViewMode || this.isApprovalMode) {
      await this.loadProjectData()
    }
  },
  methods: {
    // 获取页面标题
    getPageTitle() {
      if (this.isApprovalMode) {
        return '审批项目'
      } else if (this.isViewMode) {
        return '查看项目'
      } else if (this.isEditMode) {
        return '编辑项目'
      } else {
        return '新增项目'
      }
    },
    
    // 切换到编辑模式
    handleEdit() {
      if (this.isModal) {
        this.$emit('edit', this.currentProjectId)
      } else {
        // 路由模式下的编辑跳转
        this.$router.push(`/maintenance/projects/edit/${this.currentProjectId}`)
      }
    },
    
    // 加载项目数据（编辑模式和查看模式）
    async loadProjectData() {
      try {
        this.loading = true
        
        // 临时使用静态数据，因为后端接口未公布
        // 实际项目中应该使用：const response = await getProjectDetail(this.currentProjectId)
        const mockProject = this.getMockProjectData()
        
        this.projectType = mockProject.projectType
        this.formData = {
          basicInfo: mockProject.basicInfo || {},
          projectConfig: mockProject.projectConfig || {},
          diseaseConfig: mockProject.diseaseConfig || {},
          implementationConfig: mockProject.implementationConfig || {},
          completionInfo: mockProject.completionInfo || {},
          bridgeConfig: mockProject.bridgeConfig || {},
          approvalInfo: mockProject.approvalInfo || {}
        }
        
        // 审批模式下默认跳转到审批信息页面（最后一步）
        if (this.isApprovalMode) {
          this.$nextTick(() => {
            this.currentStep = this.getLastStepIndex()
          })
        }
      } catch (error) {
        this.$message.error('加载项目数据失败')
        this.handleClose()
      } finally {
        this.loading = false
      }
    },
    
    // 获取模拟项目数据
    getMockProjectData() {
      return {
        projectType: 'monthly',
        basicInfo: {
          projectName: '2024年度桥梁月度养护项目',
          projectType: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          managementUnit: '市桥梁管理处',
          supervisionUnit: '市交通局',
          maintenanceUnit: '桥梁养护有限公司',
          manager: '张三',
          contactPhone: '13800138000',
          workload: '100万元',
          projectContent: '对辖区内桥梁进行月度常规养护，包括清洁、检查、小修等工作',
          attachments: []
        },
        projectConfig: {
          projects: [
            { name: '桥面清洁', frequency: 30 },
            { name: '栏杆维护', frequency: 60 },
            { name: '伸缩缝检查', frequency: 90 }
          ]
        },
        diseaseConfig: {
          diseases: [
            { id: 1, name: '桥面破损', level: '轻微' },
            { id: 2, name: '栏杆松动', level: '一般' }
          ]
        },
        implementationConfig: {
          works: [
            { name: '施工准备' },
            { name: '现场作业' },
            { name: '质量检查' }
          ]
        },
        completionInfo: {
          completionDate: '2024-12-31',
          acceptanceDate: '2025-01-15',
          qualityGrade: '合格',
          remark: '项目按期完成，质量达标'
        },
        bridgeConfig: {
          bridges: [
            { id: 1, name: '人民桥', location: '人民路', span: '50m' },
            { id: 2, name: '胜利桥', location: '胜利路', span: '30m' }
          ]
        },
        approvalInfo: {
          approvalHistory: [
            {
              id: 1,
              stepName: '开始申请',
              approver: '黄昭言',
              status: '通过',
              comment: '无异议',
              department: '养护公司',
              receiveTime: '2025-09-18\n10:43',
              processTime: '2025-09-18\n10:43'
            },
            {
              id: 2,
              stepName: '养护项目审批(一级)',
              approver: '刘雨桐',
              status: '通过',
              comment: '项目方案合理，同意开展',
              department: '技术部门',
              receiveTime: '2025-09-18\n11:00',
              processTime: '2025-09-18\n11:30'
            },
            {
              id: 3,
              stepName: '养护项目审批(二级)',
              approver: '罗砚秋',
              status: '通过',
              comment: '预算合理，批准执行',
              department: '财务部门',
              receiveTime: '2025-09-18\n14:00',
              processTime: '2025-09-18\n15:20'
            }
          ]
        }
      }
    },
    
    // 获取最后一步的索引
    getLastStepIndex() {
      return this.steps.length - 1
    },
    
    // 项目类型变化
    handleProjectTypeChange(type) {
      if (this.projectType === type) {
        return // 防止重复触发
      }
      
      this.projectType = type
      
      // 使用nextTick确保步骤计算完成后再检查当前步骤
      this.$nextTick(() => {
        // 重置后续步骤的数据
        this.formData.projectConfig = {}
        this.formData.diseaseConfig = {}
        this.formData.bridgeConfig = {}
        
        // 如果当前步骤超出新的步骤范围，回到第一步
        if (this.currentStep >= this.steps.length) {
          this.currentStep = 0
        }
      })
    },
    
    // 步骤点击
    async handleStepClick(stepIndex) {
      // 查看、审批和修改模式可以自由切换
      if (this.isViewMode || this.isApprovalMode || this.isEditMode) {
        this.currentStep = stepIndex
        return
      }
      
      // 新增模式的逻辑
      // 如果点击的是当前步骤或之前的步骤，允许直接切换
      if (stepIndex <= this.currentStep) {
        this.currentStep = stepIndex
        return
      }
      
      // 如果跳转到后面的步骤，需要验证从当前步骤到目标步骤的所有步骤
      for (let i = this.currentStep; i < stepIndex; i++) {
        const isValid = await this.validateStepByIndex(i)
        if (!isValid) {
          this.$message.warning(`请先完成第${i + 1}步的必填信息`)
          return
        }
      }
      
      // 所有验证通过，跳转到目标步骤
      this.currentStep = stepIndex
    },
    
    // 上一步
    handlePrevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    
    // 下一步
    async handleNextStep() {
      const isValid = await this.validateCurrentStep()
      if (!isValid) {
        return
      }
      
      if (this.currentStep < this.getLastStepIndex()) {
        this.currentStep++
      }
    },
    
    // 验证当前步骤
    async validateCurrentStep() {
      return await this.validateStepByIndex(this.currentStep)
    },
    
    // 验证指定步骤
    async validateStepByIndex(stepIndex) {
      const componentName = this.getStepComponentByIndex(stepIndex)
      
      if (componentName && this.$refs[componentName]) {
        try {
          const result = this.$refs[componentName].validate()
          // 如果返回Promise，等待结果
          if (result && typeof result.then === 'function') {
            return await result
          }
          // 如果返回boolean，直接返回
          return result
        } catch (error) {
          console.error('验证失败:', error)
          return false
        }
      }
      
      return true
    },
    
    // 获取当前步骤对应的组件名
    getCurrentStepComponent() {
      return this.getStepComponentByIndex(this.currentStep)
    },
    
    // 根据步骤索引获取组件名
    getStepComponentByIndex(stepIndex) {
      if (stepIndex === 0) {
        return 'basicInfo'
      }
      
      // 根据项目类型和步骤索引确定组件
      if (this.projectType === 'monthly') {
        // 月度养护：基本信息(0) → 养护项目(1) → 病害养护(2) → 养护桥梁(3)
        if (stepIndex === 1) return 'projectConfig'
        if (stepIndex === 2) return 'diseaseConfig'
        if (stepIndex === 3) return 'bridgeConfig'
      } else if (this.projectType === 'cleaning' || this.projectType === 'emergency') {
        // 保洁/应急：基本信息(0) → 项目配置(1) → 桥梁配置(2)
        if (stepIndex === 1) return 'projectConfig'
        if (stepIndex === 2) return 'bridgeConfig'
      } else if (this.projectType === 'preventive') {
        // 预防养护：基本信息(0) → 关联病害(1) → 实施信息(2) → 竣工信息(3)
        if (stepIndex === 1) return 'diseaseConfig'
        if (stepIndex === 2) return 'implementationConfig'
        if (stepIndex === 3) return 'completionInfo' // 竣工信息
        return null // 预防养护项目没有第4步
      }
      
      return null
    },
    
    // 保存草稿
    async handleSave() {
      try {
        this.loading = true
        const data = {
          ...this.formData,
          status: 'draft',
          infrastructureType: this.infrastructureType
        }
        
        if (this.isEditMode) {
          await updateProject(this.currentProjectId, data)
          this.$message.success('保存成功')
        } else {
          await createProject(data)
          this.$message.success('保存成功')
        }
        
        // 弹框模式下通知父组件
        if (this.isModal) {
          this.$emit('save', data)
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.loading = false
      }
    },
    
    // 提交项目
    async handleSubmit() {
      // 验证所有步骤
      if (!this.validateAllSteps()) {
        return
      }
      
      try {
        this.loading = true
        const data = {
          ...this.formData,
          status: 'pending',
          infrastructureType: this.infrastructureType
        }
        
        if (this.isEditMode) {
          await updateProject(this.currentProjectId, data)
          this.$message.success('更新成功')
        } else {
          await createProject(data)
          this.$message.success('提交成功')
        }
        
        // 弹框模式下通知父组件
        if (this.isModal) {
          this.$emit('submit', data)
        } else {
          this.handleClose()
        }
      } catch (error) {
        this.$message.error(this.isEditMode ? '更新失败' : '提交失败')
      } finally {
        this.loading = false
      }
    },
    
    // 验证所有步骤
    validateAllSteps() {
      // 这里应该验证所有步骤的数据
      return true
    },
    
    // 关闭页面
    handleClose() {
      if (this.isModal) {
        this.$emit('close')
      } else {
        this.$router.push('/maintenance/projects')
      }
    },
    
    // 处理审批提交结果
    handleApprovalSubmitted(data) {
      const { result } = data
      
      if (result === 'approved') {
        this.$message.success('审批通过成功')
      } else if (result === 'rejected') {
        this.$message.success('已驳回申请')
      } else if (result === 'returned') {
        this.$message.success('已退回修改')
      }
      
      // 弹框模式下通知父组件
      if (this.isModal) {
        this.$emit('approval-completed', data)
      } else {
        // 审批完成后关闭页面
        setTimeout(() => {
          this.handleClose()
        }, 1500)
      }
    },
    
    // 处理审批取消
    handleApprovalCancel() {
      // 可以选择关闭页面或回到上一步
      this.handleClose()
    },
    
    // 处理审批通过
    async handleApprove() {
      if (this.$refs.approvalInfo) {
        const result = await this.$refs.approvalInfo.approveProject()
        if (result) {
          // 审批成功，可以关闭弹窗或进行其他操作
          // this.handleClose()
        }
      }
    },
    
    // 处理审批退回
    async handleReject() {
      if (this.$refs.approvalInfo) {
        const result = await this.$refs.approvalInfo.rejectProject()
        if (result) {
          // 退回成功，可以关闭弹窗或进行其他操作
          // this.handleClose()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .close-btn {
    color: #9ca3af;
    font-size: 18px;
    
    &:hover {
      color: #ffffff;
      background: #ef4444;
      border-radius: 50%;
    }
  }
}

.step-content {
  padding: 24px;
  min-height: 400px;
}

.action-buttons {
  padding: 16px 24px;
  border-top: 1px solid #4b5563;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
