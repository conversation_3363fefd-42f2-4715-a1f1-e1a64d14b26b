<template>
  <div class="maintenance-theme inspection-container">
    <div class="page-container">

      <!-- TAB切换 -->
      <div class="inspection-tabs">
        <TabSwitch
          v-model="activeTab"
          :tabs="tabOptions"
          @tab-click="handleTabClick"
        />
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <div class="filter-content">
          <el-input
            v-model="searchForm.projectName"
            placeholder="项目名称"
            clearable
            class="filter-select"
          />

          <el-select
            v-model="searchForm.projectType"
            placeholder="项目类型"
            clearable
            class="filter-select"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="type in projectTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>

          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            class="filter-select"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>

          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="2025/09/01"
            end-placeholder="2025/09/01"
            format="yyyy/MM/dd"
            value-format="yyyy-MM-dd"
            class="filter-select"
          />

          <div class="filter-actions">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 新增按钮 -->
      <div class="action-bar">
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate">新增养护项目</el-button>
      </div>

      <!-- 数据表格 -->
      <div class="inspection-table common-table">
        <el-table
          v-loading="loading"
          :data="projectList"
          :key="projectList.length"
          style="width: 100%"
          @sort-change="handleSortChange"
          row-key="id"
          :row-style="{ height: '32px' }"
          size="small"
          class="compact-table"
        >
          <el-table-column width="90" align="center">
            <template slot="header">
              <span class="table-header-wrap">序号</span>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>

            <el-table-column prop="name" label="项目名称" min-width="200" show-overflow-tooltip />

            <el-table-column prop="type" label="项目类型" width="120" align="center">
              <template slot-scope="scope">
                {{ getProjectTypeText(scope.row.type) }}
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="130" align="center">
              <template slot-scope="scope">
                <StatusTag :status="scope.row.status" type="project" />
              </template>
            </el-table-column>

            <el-table-column prop="startDate" label="开始日期" width="110" align="center" />

            <el-table-column prop="endDate" label="结束日期" width="110" align="center" />

            <el-table-column prop="maintenanceUnit" label="养护单位" min-width="120" show-overflow-tooltip />

            <el-table-column prop="manager" label="负责人" width="100" align="center" />

            <el-table-column label="操作" width="200" align="center">
              <template slot-scope="scope">
                <!-- 查看按钮 - 始终显示 -->
                <el-button
                  type="text"
                  size="small"
                  @click="handleView(scope.row)"
                >
                  查看
                </el-button>

                <!-- 草稿状态：显示修改和删除 -->
                <template v-if="scope.row.status === 'draft'">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleEdit(scope.row)"
                  >
                    修改
                  </el-button>

                  <el-button
                    type="text"
                    size="small"
                    class="custom-delete-btn"
                    @click="handleDelete(scope.row)"
                  >
                    删除
                  </el-button>
                </template>

                <!-- 审批中状态：显示审批 -->
                <el-button
                  v-if="scope.row.status === 'pending'"
                  type="text"
                  size="small"
                  class="custom-approve-btn"
                  @click="handleApprove(scope.row)"
                >
                  审批
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

      <!-- 分页 -->
      <div class="inspection-pagination">
        <el-pagination
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 项目弹框 -->
    <project-dialog
      :visible.sync="modalVisible"
      :mode="modalMode"
      :project-id="currentProjectId"
      :infrastructure-type="activeTab"
      @close="handleModalClose"
      @refresh="getList"
      @edit="handleModalEdit"
      @save="handleSave"
      @submit="handleSubmit"
      @approval-completed="handleApprovalCompleted"
    />
  </div>
</template>

<script>
import { getProjectList, deleteProject } from '@/api/maintenance/projects'
import StatusTag from '@/components/Maintenance/StatusTag'
import TabSwitch from '@/components/Inspection/TabSwitch'
import ProjectDialog from '@/components/Maintenance/ProjectDialog'

export default {
  name: 'MaintenanceProjects',
  components: {
    StatusTag,
    TabSwitch,
    ProjectDialog
  },
  data() {
    return {
      loading: false,
      activeTab: 'bridge', // bridge, tunnel
      projectList: [],
      selectedRows: [],
      total: 0,

      // TAB选项
      tabOptions: [
        {
          name: 'bridge',
          label: '桥梁养护',
          icon: 'bridge-icon'
        },
        {
          name: 'tunnel',
          label: '隧道养护',
          icon: 'tunnel-icon'
        }
      ],

      // 搜索表单
      searchForm: {
        projectName: '',
        projectType: '',
        status: '',
        dateRange: null
      },

      // 筛选配置
      filterConfigs: [],

      // 选择器选项
      selectOptions: {},

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        name: '',
        type: '',
        status: '',
        startDate: '',
        endDate: '',
        infrastructureType: 'bridge' // bridge, tunnel
      },

      // 项目类型选项
      projectTypes: [
        { label: '月度养护', value: 'monthly' },
        { label: '保洁项目', value: 'cleaning' },
        { label: '应急养护', value: 'emergency' },
        { label: '预防养护', value: 'preventive' }
      ],

      // 状态选项
      statusOptions: [
        { label: '草稿', value: 'draft' },
        { label: '审批中', value: 'pending' },
        { label: '审批通过', value: 'approved' },
        { label: '审批拒绝', value: 'rejected' }
      ],
      
      // 弹框相关
      modalVisible: false,
      modalMode: 'create', // create, edit, view, approve
      currentProjectId: null
    }
  },
  computed: {
    hasAuditPermission() {
      // 这里应该根据用户权限判断
      return true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取项目列表
    async getList() {
      this.loading = true
      try {
        // 处理日期范围
        if (this.dateRange && this.dateRange.length === 2) {
          this.queryParams.startDate = this.dateRange[0]
          this.queryParams.endDate = this.dateRange[1]
        } else {
          this.queryParams.startDate = ''
          this.queryParams.endDate = ''
        }

        this.queryParams.infrastructureType = this.activeTab

        const response = await getProjectList(this.queryParams)
        this.projectList = response.data.list || []
        this.total = response.data.total || 0
      } catch (error) {
        this.$message.error('获取项目列表失败')
      } finally {
        this.loading = false
      }
    },

    // TAB切换处理
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.queryParams.infrastructureType = tab.name
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 搜索处理
    handleSearch() {
      // 将搜索表单数据同步到查询参数
      this.queryParams.name = this.searchForm.projectName || ''
      this.queryParams.type = this.searchForm.projectType || ''
      this.queryParams.status = this.searchForm.status || ''

      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        this.queryParams.startDate = this.searchForm.dateRange[0]
        this.queryParams.endDate = this.searchForm.dateRange[1]
      } else {
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }

      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置处理
    handleReset() {
      this.searchForm = {
        projectName: '',
        projectType: '',
        status: '',
        dateRange: null
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        name: '',
        type: '',
        status: '',
        startDate: '',
        endDate: '',
        infrastructureType: this.activeTab
      }
      this.getList()
    },

    // 排序变化处理
    handleSortChange({ column, prop, order }) {
      // 可以根据需要处理排序
      console.log('Sort change:', { column, prop, order })
    },

    // 切换标签（保留原有方法以兼容）
    switchTab(tab) {
      this.activeTab = tab
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 查询（保留原有方法以兼容）
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置查询（保留原有方法以兼容）
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        name: '',
        type: '',
        status: '',
        startDate: '',
        endDate: '',
        infrastructureType: this.activeTab
      }
      this.searchForm = {
        projectName: '',
        projectType: '',
        status: '',
        dateRange: null
      }
      this.getList()
    },

    // 新增项目
    handleCreate() {
      this.modalMode = 'create'
      this.currentProjectId = null
      this.modalVisible = true
    },

    // 查看项目
    handleView(row) {
      this.modalMode = 'view'
      this.currentProjectId = row.id
      this.modalVisible = true
    },

    // 编辑项目
    handleEdit(row) {
      this.modalMode = 'edit'
      this.currentProjectId = row.id
      this.modalVisible = true
    },

    // 删除项目
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该项目吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deleteProject(row.id)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 审批项目
    handleApprove(row) {
      this.modalMode = 'approve'
      this.currentProjectId = row.id
      this.modalVisible = true
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },

    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || type
    },
    
    // 弹框关闭
    handleModalClose() {
      this.modalVisible = false
      this.currentProjectId = null
    },
    
    // 从查看模式切换到编辑模式
    handleModalEdit(projectId) {
      this.modalMode = 'edit'
      this.currentProjectId = projectId
      // 保持弹框打开状态
    },

    // 处理保存事件
    handleSave(data) {
      this.$message.success('项目已保存为草稿')
      this.getList()
    },

    // 处理提交事件
    handleSubmit(data) {
      this.$message.success('项目提交成功，等待审批')
      this.modalVisible = false
      this.getList()
    },

    // 处理审批完成事件
    handleApprovalCompleted(data) {
      this.$message.success('审批操作完成')
      this.modalVisible = false
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

// 新增按钮区域样式
.action-bar {
  // 样式已通过主题文件提供
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;
  
  .el-button {
    margin-right: 12px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
