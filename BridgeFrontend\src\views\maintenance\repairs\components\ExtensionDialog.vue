<template>
  <el-dialog
    title="延期申请"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    :append-to-body="true"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    top="5vh"
  >
    <el-form
      ref="extensionForm"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目开始时间">
            <el-input
              v-model="form.startTime"
              readonly
              placeholder="请选择开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目结束时间">
            <el-input
              v-model="form.endTime"
              readonly
              placeholder="请选择结束时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="延期结束时间" prop="extensionEndTime">
        <el-date-picker
          v-model="form.extensionEndTime"
          type="datetime"
          placeholder="请选择延期结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="延期说明" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入延期说明（最少10个字符，最多500字符）"
          show-word-limit
          maxlength="500"
        />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="submitLoading"
      >
        提交申请
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExtensionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      form: {
        startTime: '',
        endTime: '',
        extensionEndTime: '',
        reason: ''
      },
      rules: {
        extensionEndTime: [
          { required: true, message: '请选择延期结束时间', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入延期说明', trigger: 'blur' },
          { min: 10, message: '延期说明至少需要10个字符', trigger: 'blur' },
          { max: 500, message: '延期说明不能超过500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate: (time) => {
          if (!this.form.endTime) return false
          // 只能选择原结束时间之后的日期
          const endTime = new Date(this.form.endTime)
          return time.getTime() <= endTime.getTime()
        }
      }
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal
        if (newVal) {
          this.initForm()
        }
      },
      immediate: true
    },
    dialogVisible(newVal) {
      if (!newVal) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    initForm() {
      if (this.projectData) {
        this.form = {
          startTime: this.projectData.startTime || this.projectData.startDate,
          endTime: this.projectData.endTime || this.projectData.endDate,
          extensionEndTime: '',
          reason: ''
        }
      }
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.extensionForm) {
          this.$refs.extensionForm.clearValidate()
        }
      })
    },
    
    handleSubmit() {
      this.$refs.extensionForm.validate((valid) => {
        if (valid) {
          this.submitLoading = true
          
          // 模拟API请求
          setTimeout(() => {
            this.submitLoading = false
            this.$message.success('延期申请提交成功')
            this.handleClose()
            this.$emit('success')
          }, 1500)
        }
      })
    },
    
    handleClose() {
      this.dialogVisible = false
      this.form = {
        startTime: '',
        endTime: '',
        extensionEndTime: '',
        reason: ''
      }
      // 清除表单验证
      if (this.$refs.extensionForm) {
        this.$refs.extensionForm.clearValidate()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

// 延期申请弹框使用统一的深色主题样式
</style>
