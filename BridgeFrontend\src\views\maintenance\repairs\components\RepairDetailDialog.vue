<template>
  <el-dialog
    title="查看详情"
    :visible.sync="dialogVisible"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :append-to-body="true"
    top="5vh"
  >
    <div class="modal-content-wrapper">
      <!-- 使用专门的维修详情查看组件 -->
      <repair-detail-view :repair-data="repairData" />
    </div>
  </el-dialog>
</template>

<script>
import RepairDetailView from './RepairDetailView.vue'

export default {
  name: 'RepairDetailDialog',
  components: {
    RepairDetailView
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    repairData: {
      type: Object,
      default: () => ({})
    },
    infrastructureType: {
      type: String,
      default: 'bridge'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

// 维修详情弹框使用统一的深色主题样式
</style>
