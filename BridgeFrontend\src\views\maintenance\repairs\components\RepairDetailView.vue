<template>
  <div class="repair-detail-view maintenance-theme">
    <div class="page-container">
      <div class="card-container">
        <!-- 导航标签页 -->
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <basic-info-view :repair-data="repairData" />
          </el-tab-pane>
          <el-tab-pane label="养护项目" name="projects">
            <projects-view :repair-data="repairData" />
          </el-tab-pane>
          <el-tab-pane label="病害养护" name="diseases">
            <diseases-view :repair-data="repairData" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import BasicInfoView from './detail/BasicInfoView.vue'
import ProjectsView from './detail/ProjectsView.vue'
import DiseasesView from './detail/DiseasesView.vue'

export default {
  name: 'RepairDetailView',
  components: {
    BasicInfoView,
    ProjectsView,
    DiseasesView
  },
  props: {
    repairData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeTab: 'projects' // 默认显示养护项目tab，便于测试
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.repair-detail-view {
  .page-container {
    padding: 0;
  }
  
  .card-container {
    background: #1a2332;
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 24px;
  }
  
  .detail-tabs {
    @extend .common-element-tabs;
  }
}
</style>
