<template>
  <div class="basic-info-view">
    <el-form
      :model="repairData"
      label-width="120px"
      class="maintenance-form"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="项目名称">
            <el-input
              :value="repairData.projectName"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目类型">
            <el-input
              :value="getProjectTypeLabel(repairData.projectType)"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="项目开始时间">
            <el-input
              :value="repairData.startDate"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目结束时间">
            <el-input
              :value="repairData.endDate"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="管理单位">
            <el-input
              :value="repairData.managementUnit"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="监理单位">
            <el-input
              :value="repairData.supervisionUnit"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="养护单位">
            <el-input
              :value="repairData.maintenanceUnit"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="项目负责人">
            <el-input
              :value="repairData.manager"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="联系方式">
            <el-input
              :value="repairData.contactPhone"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12" v-if="showWorkload">
          <el-form-item label="工作量">
            <el-input
              :value="repairData.workload"
              readonly
              class="readonly-input"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="项目内容">
            <el-input
              :value="repairData.projectContent"
              type="textarea"
              :rows="4"
              readonly
              class="readonly-textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="附件">
            <div class="attachment-list">
              <div
                v-for="(file, index) in repairData.attachments"
                :key="index"
                class="attachment-item"
              >
                <i class="el-icon-document"></i>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">({{ file.size }})</span>
              </div>
              <div v-if="!repairData.attachments || repairData.attachments.length === 0" class="no-attachments">
                暂无附件
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'BasicInfoView',
  props: {
    repairData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 是否显示工作量字段 - 与养护项目保持一致
    showWorkload() {
      return ['cleaning', 'emergency', 'preventive'].includes(this.repairData.projectType)
    }
  },
  methods: {
    getProjectTypeLabel(type) {
      const typeMap = {
        'monthly': '月度养护',
        'preventive': '预防养护',
        'emergency': '应急养护',
        'cleaning': '保洁项目'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.basic-info-view {
  @extend .readonly-form;
  
  .attachment-list {
    @extend .common-attachment-list;
  }
}
</style>
