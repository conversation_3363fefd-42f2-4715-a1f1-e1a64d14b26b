<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="病害详情"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :modal-class="'disease-detail-modal'"
    :append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="false"
    top="5vh"
  >
    <div class="modal-content-wrapper">
      <!-- 病害信息 -->
      <div class="section">
        <h3 class="section-title">病害信息</h3>
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>桥梁/隧道名称:</label>
              <el-input v-model="diseaseInfo.bridgeName" readonly />
            </div>
            <div class="info-item">
              <label>病害部位:</label>
              <el-input v-model="diseaseInfo.diseasePart" readonly />
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>病害类型:</label>
              <el-input v-model="diseaseInfo.diseaseType" readonly />
            </div>
            <div class="info-item">
              <label>病害位置:</label>
              <el-input v-model="diseaseInfo.diseaseLocation" readonly />
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>病害数量:</label>
              <el-input v-model="diseaseInfo.diseaseCount" readonly />
            </div>
            <div class="info-item">
              <!-- 空位，保持布局对称 -->
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <label>病害描述:</label>
            <el-input
              type="textarea"
              :rows="4"
              v-model="diseaseInfo.diseaseDescription"
              readonly
            />
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>上报人:</label>
              <el-input v-model="diseaseInfo.reporter" readonly />
            </div>
            <div class="info-item">
              <label>联系方式:</label>
              <el-input v-model="diseaseInfo.contactPhone" readonly />
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <label>病害照片:</label>
            <div class="image-grid">
              <div 
                v-for="(image, index) in diseaseInfo.diseaseImages" 
                :key="index"
                class="image-item"
              >
                <el-image
                  :src="image.url"
                  :preview-src-list="diseaseInfo.diseaseImages.map(img => img.url)"
                  :initial-index="index"
                  fit="cover"
                  class="disease-image"
                >
                  <template #error>
                    <div class="image-error">
                      <i class="el-icon-picture"></i>
                    </div>
                  </template>
                </el-image>
                <div class="image-caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 处置信息 -->
      <div class="section">
        <h3 class="section-title">处置信息</h3>
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>养护单位:</label>
              <el-input v-model="disposalInfo.maintenanceUnit" readonly />
            </div>
            <div class="info-item">
              <label>管理单位:</label>
              <el-input v-model="disposalInfo.managementUnit" readonly />
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-group">
            <div class="info-item">
              <label>处置人员:</label>
              <el-input v-model="disposalInfo.processor" readonly />
            </div>
            <div class="info-item">
              <label>联系方式:</label>
              <el-input v-model="disposalInfo.processorPhone" readonly />
            </div>
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <label>处置时间:</label>
            <el-input v-model="disposalInfo.processTime" readonly />
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <label>处置说明:</label>
            <el-input
              type="textarea"
              :rows="4"
              v-model="disposalInfo.processDescription"
              placeholder="请输入"
              readonly
            />
          </div>
        </div>
        
        <div class="info-row">
          <div class="info-item full-width">
            <label>处置照片:</label>
            <div class="photo-type-buttons">
              <el-button 
                v-for="type in photoTypes" 
                :key="type.key"
                size="small"
                :type="selectedPhotoType === type.key ? 'primary' : 'default'"
                @click="selectPhotoType(type.key)"
              >
                {{ type.label }}
              </el-button>
            </div>
            <div class="image-grid">
              <div 
                v-for="(image, index) in currentDisposalPhotos" 
                :key="index"
                class="image-item"
              >
                <el-image
                  :src="image.url"
                  :preview-src-list="currentDisposalPhotos.map(img => img.url)"
                  :initial-index="index"
                  fit="cover"
                  class="disposal-image"
                >
                  <template #error>
                    <div class="image-error">
                      <i class="el-icon-picture"></i>
                    </div>
                  </template>
                </el-image>
                <div class="image-caption">{{ image.caption }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="section">
        <h3 class="section-title">审核信息</h3>
        <div class="audit-table">
          <el-table
            :data="auditInfo"
            class="approval-table"
            header-row-class-name="table-header"
          >
            <el-table-column prop="serialNumber" label="序号" width="60" align="center" />
            <el-table-column prop="approvalStep" label="审批环节" min-width="120" align="center" />
            <el-table-column prop="handler" label="处理人" width="100" align="center" />
            <el-table-column prop="approvalStatus" label="审批状态" width="100" align="center" />
            <el-table-column prop="approvalOpinion" label="审批意见" width="100" align="center" />
            <el-table-column prop="handlerDept" label="处理人部门" min-width="120" align="center" />
            <el-table-column prop="receiveTime" label="接收时间" width="140" align="center" />
            <el-table-column prop="finishTime" label="办结时间" width="140" align="center" />
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DiseaseDetailDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    diseaseData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      selectedPhotoType: 'scene', // 当前选中的照片类型
      
      // 病害信息
      diseaseInfo: {
        bridgeName: 'XXXXXX大桥',
        diseasePart: 'XXXXXX部位',
        diseaseType: 'XXXXXXXXX',
        diseaseLocation: 'XXXXXXXXX',
        diseaseCount: '12',
        diseaseDescription: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
        reporter: 'XXXXXX大桥',
        contactPhone: '19321207394',
        diseaseImages: [
          {
            url: require('@/assets/images/test/inspection_image1.png'),
            caption: '照片1'
          },
          {
            url: require('@/assets/images/test/inspection_image2.png'),
            caption: '照片2'
          }
        ]
      },
      
      // 处置信息
      disposalInfo: {
        maintenanceUnit: 'XXXXXX大桥',
        managementUnit: 'XXXXXX部位',
        processor: '刘三',
        processorPhone: '19321207394',
        processTime: '2025-09-18 10:34',
        processDescription: '请输入',
        disposalPhotos: {
          scene: [
            {
              url: require('@/assets/images/test/inspection_image1.png'),
              caption: '照片3'
            },
            {
              url: require('@/assets/images/test/inspection_image2.png'),
              caption: '照片4'
            }
          ],
          personnel: [],
          before: [],
          during: [],
          after: []
        }
      },
      
      // 照片类型选项
      photoTypes: [
        { key: 'scene', label: '现场照片' },
        { key: 'personnel', label: '人车照片' },
        { key: 'before', label: '维修前' },
        { key: 'during', label: '维修中' },
        { key: 'after', label: '维修后' }
      ],
      
      // 审核信息
      auditInfo: [
        {
          serialNumber: '1',
          approvalStep: '开始申请',
          handler: '高哲',
          approvalStatus: '通过',
          approvalOpinion: '无异议',
          handlerDept: '开办公司',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '2',
          approvalStep: '养护项目审批（一级）',
          handler: '刘明明',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: 'XXX部门',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '3',
          approvalStep: '养护项目审批（二级）',
          handler: '罗政权',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: '',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    },
    
    // 当前处置照片类型的照片列表
    currentDisposalPhotos() {
      if (!this.disposalInfo.disposalPhotos) return []
      return this.disposalInfo.disposalPhotos[this.selectedPhotoType] || []
    }
  },
  watch: {
    // 监听传入的病害数据变化
    diseaseData: {
      handler(newData) {
        if (newData) {
          this.initializeData(newData)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 初始化数据
    initializeData(data) {
      if (data) {
        // 合并传入的数据和静态数据
        this.diseaseInfo = {
          ...this.diseaseInfo,
          bridgeName: data.bridgeName || this.diseaseInfo.bridgeName,
          diseasePart: data.diseasePart || this.diseaseInfo.diseasePart,
          diseaseType: data.diseaseType || this.diseaseInfo.diseaseType,
          diseaseLocation: data.diseaseLocation || this.diseaseInfo.diseaseLocation,
          diseaseCount: data.diseaseCount || this.diseaseInfo.diseaseCount
        }
      }
    },
    
    // 选择照片类型
    selectPhotoType(type) {
      this.selectedPhotoType = type
      // 这里可以根据类型过滤显示不同的照片
    },
    
    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss">
// Element UI PopupManager会自动管理z-index，无需手动设置
// 全局z-index基准值已在main.js中设置为100000，确保足够高的层级
</style>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

// 病害详情弹框使用统一的深色主题样式
</style>
