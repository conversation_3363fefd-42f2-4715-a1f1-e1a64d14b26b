<template>
  <div class="diseases-view">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <div class="filter-row">
        <el-select
          v-model="filterForm.bridgeName"
          placeholder="桥梁名称"
          class="filter-select"
          clearable
        >
          <el-option
            v-for="bridge in bridgeOptions"
            :key="bridge.value"
            :label="bridge.label"
            :value="bridge.value"
          />
        </el-select>

        <el-select
          v-model="filterForm.diseaseType"
          placeholder="病害类型"
          class="filter-select"
          clearable
        >
          <el-option
            v-for="type in diseaseTypeOptions"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>

        <el-select
          v-model="filterForm.status"
          placeholder="状态"
          class="filter-select"
          clearable
        >
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>

        <div class="filter-actions">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 完成量统计 -->
    <div class="completion-stats">
      完成量 {{ completedCount }}/{{ totalCount }}
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        class="diseases-table"
        header-row-class-name="table-header"
        row-class-name="table-row"
        v-loading="loading"
      >
        <el-table-column prop="index" label="序号" width="80" align="center" />
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" />
        <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
        <el-table-column prop="diseasePart" label="病害部位" min-width="100" />
        <el-table-column prop="diseaseType" label="病害类型" min-width="120" />
        <el-table-column prop="completionTime" label="完成时间" width="160" align="center" />
        <el-table-column prop="responsible" label="负责人" width="100" align="center" />
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 病害详情弹窗 -->
    <DiseaseDetailDialog
      v-model="showDetailDialog"
      :disease-data="selectedDisease"
      @refresh="loadTableData"
    />
  </div>
</template>

<script>
import DiseaseDetailDialog from './DiseaseDetailDialog.vue'

export default {
  name: 'DiseasesView',
  components: {
    DiseaseDetailDialog
  },
  props: {
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      showDetailDialog: false,
      selectedDisease: null,
      filterForm: {
        bridgeName: '',
        diseaseType: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          index: 1,
          bridgeName: 'XXXXXX大桥',
          diseaseCode: '989',
          diseasePart: '伸缩缝',
          diseaseType: '伸缩缝缺失',
          completionTime: '2025-09-18 10:43',
          responsible: '王景深',
          status: 'completed'
        },
        {
          id: 2,
          index: 2,
          bridgeName: 'XXXXXX大桥',
          diseaseCode: '988',
          diseasePart: '伸缩缝',
          diseaseType: '伸缩缝缺失',
          completionTime: '2025-09-18 10:43',
          responsible: '刘志强',
          status: 'completed'
        },
        {
          id: 3,
          index: 3,
          bridgeName: 'XXXXXX大桥',
          diseaseCode: '987',
          diseasePart: '照明设施',
          diseaseType: '照明设施缺失',
          completionTime: '2025-09-18 10:43',
          responsible: '赵临洲',
          status: 'completed'
        }
      ],
      bridgeOptions: [
        { label: 'XXXXXX大桥', value: 'bridge1' },
        { label: 'YYYYYY大桥', value: 'bridge2' },
        { label: 'ZZZZZZ大桥', value: 'bridge3' }
      ],
      diseaseTypeOptions: [
        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },
        { label: '照明设施缺失', value: 'lighting_missing' },
        { label: '护栏损坏', value: 'guardrail_damage' },
        { label: '路面破损', value: 'pavement_damage' }
      ],
      statusOptions: [
        { label: '已完成', value: 'completed' },
        { label: '进行中', value: 'in_progress' },
        { label: '待处理', value: 'pending' }
      ]
    }
  },
  computed: {
    totalCount() {
      return this.tableData.length
    },
    completedCount() {
      return this.tableData.filter(item => item.status === 'completed').length
    }
  },
  mounted() {
    this.loadTableData()
  },
  methods: {
    handleSearch() {
      this.loading = true
      // 模拟搜索延迟
      setTimeout(() => {
        // 这里应该调用API进行搜索
        console.log('搜索条件:', this.filterForm)
        this.loading = false
        this.$message.success('查询完成')
      }, 1000)
    },
    handleReset() {
      this.filterForm.bridgeName = ''
      this.filterForm.diseaseType = ''
      this.filterForm.status = ''
      this.loadTableData()
    },
    handleViewDetail(row) {
      this.selectedDisease = row
      this.showDetailDialog = true
    },
    loadTableData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        // 这里应该调用API获取数据
        this.loading = false
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.diseases-view {
  padding: 20px;
  color: #e5e7eb;

  .filter-section {
    margin-bottom: 20px;

    .filter-row {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;

      .filter-select {
        width: 180px;

        :deep(.el-input__wrapper) {
          background: #374151;
          border: 1px solid #4b5563;
          box-shadow: none;

          .el-input__inner {
            color: #e5e7eb;
            background: transparent;

            &::placeholder {
              color: #9ca3af;
            }
          }
        }

        :deep(.el-select__wrapper) {
          background: #374151;
          border: 1px solid #4b5563;
          box-shadow: none;

          &.is-focused {
            border-color: #3b82f6;
          }

          .el-select__placeholder {
            color: #9ca3af;
          }

          .el-select__selected-item {
            color: #e5e7eb;
          }

          .el-select__caret {
            color: #9ca3af;
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 12px;

        .el-button {
          &.el-button--primary {
            background: #3b82f6;
            border-color: #3b82f6;
            color: #ffffff;

            &:hover {
              background: #2563eb;
              border-color: #2563eb;
            }
          }

          &:not(.el-button--primary) {
            background: #374151;
            border-color: #4b5563;
            color: #e5e7eb;

            &:hover {
              background: #4b5563;
              border-color: #6b7280;
            }
          }
        }
      }
    }
  }

  .completion-stats {
    margin-bottom: 16px;
    font-size: 14px;
    color: #d1d5db;
  }

  .table-section {
    .diseases-table {
      @extend .common-table;
    }
  }
}

// 下拉选项样式
:deep(.el-select-dropdown) {
  background: #374151;
  border: 1px solid #4b5563;

  .el-select-dropdown__item {
    color: #e5e7eb;

    &:hover {
      background: #4b5563;
    }

    &.is-selected {
      background: #3b82f6;
      color: #ffffff;
    }
  }
}
</style>