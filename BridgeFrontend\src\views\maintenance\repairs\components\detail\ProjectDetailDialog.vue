<template>
  <el-dialog
    :visible.sync="visible"
    title="养护项目详情"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :modal="true"
    :lock-scroll="false"
    :append-to-body="true"
    :destroy-on-close="true"
    custom-class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    @close="handleClose"
    top="5vh"
  >
    <div class="modal-content-wrapper maintenance-theme">
      <!-- 养护项目信息 -->
      <div class="section-container">
        <div class="section-title">养护项目</div>
        <div class="form-row">
          <div class="form-item">
            <label>桥梁/隧道名称:</label>
            <div class="form-value">{{ projectInfo.bridgeName || 'XXXXXX大桥' }}</div>
          </div>
          <div class="form-item">
            <label>养护项目:</label>
            <div class="form-value">{{ projectInfo.projectType || 'XXXXXX项目' }}</div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>项目开始时间:</label>
            <div class="form-value">{{ projectInfo.startTime || '2025-09-18 10:34' }}</div>
          </div>
          <div class="form-item">
            <label>项目结束时间:</label>
            <div class="form-value">{{ projectInfo.endTime || '2025-09-18 10:34' }}</div>
          </div>
        </div>
      </div>

      <!-- 处置信息 -->
      <div class="section-container">
        <div class="section-title">处置信息</div>
        <div class="form-row">
          <div class="form-item">
            <label>养护单位:</label>
            <div class="form-value">{{ projectInfo.maintenanceUnit || 'XXXXXX大桥' }}</div>
          </div>
          <div class="form-item">
            <label>配置单位:</label>
            <div class="form-value">{{ projectInfo.configUnit || 'XXXXXX项目' }}</div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label>处置人员:</label>
            <div class="form-value">{{ projectInfo.responsible || '张三' }}</div>
          </div>
          <div class="form-item">
            <label>联系方式:</label>
            <div class="form-value">{{ projectInfo.contactPhone || '13321207394' }}</div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <label>处置时间:</label>
            <div class="form-value">{{ projectInfo.disposalTime || '2025-09-18 10:34' }}</div>
          </div>
        </div>
      </div>

      <!-- 处置备注 -->
      <div class="section-container">
        <div class="form-item full-width">
          <label>处置备注:</label>
          <div class="form-textarea">
            {{ projectInfo.remarks || '请输入' }}
          </div>
        </div>
      </div>

      <!-- 处置照片 -->
      <div class="section-container">
        <div class="form-item full-width">
          <label>处置照片:</label>
          <div class="photo-section">
            <div class="photo-tabs">
              <div 
                v-for="tab in photoTabs" 
                :key="tab.key"
                class="photo-tab"
                :class="{ 'active': activePhotoTab === tab.key }"
                @click="activePhotoTab = tab.key"
              >
                {{ tab.label }}
              </div>
            </div>
            <div class="photo-grid">
              <div 
                v-for="(photo, index) in currentPhotos" 
                :key="index"
                class="photo-item"
              >
                <img :src="photo.url" :alt="photo.name" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="section-container">
        <div class="section-title">审核信息</div>
        <div class="audit-table">
          <el-table 
            :data="auditHistory" 
            class="audit-data-table"
            header-row-class-name="audit-table-header"
            row-class-name="audit-table-row"
          >
            <el-table-column prop="serialNumber" label="序号" width="60" align="center" />
            <el-table-column prop="approvalStep" label="审批环节" width="140" />
            <el-table-column prop="handler" label="处理人" width="80" align="center" />
            <el-table-column prop="approvalStatus" label="审批状态" width="80" align="center" />
            <el-table-column prop="approvalOpinion" label="审批意见" width="80" align="center" />
            <el-table-column prop="handlerDept" label="处理人部门" width="100" />
            <el-table-column prop="receiveTime" label="接收时间" width="120" />
            <el-table-column prop="finishTime" label="办结时间" width="120" />
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProjectDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activePhotoTab: 'scene',
      photoTabs: [
        { key: 'scene', label: '现场照片' },
        { key: 'personnel', label: '人车照片' },
        { key: 'before', label: '养护前' },
        { key: 'during', label: '养护中' },
        { key: 'after', label: '养护后' }
      ],
      // 模拟照片数据
      photoData: {
        scene: [
          { url: require('@/assets/images/test/inspection_image1.png'), name: '现场照片1' },
          { url: require('@/assets/images/test/inspection_image2.png'), name: '现场照片2' }
        ],
        personnel: [],
        before: [],
        during: [],
        after: []
      },
      // 审核历史数据
      auditHistory: [
        {
          serialNumber: '1',
          approvalStep: '开始申请',
          handler: '黄职高',
          approvalStatus: '通过',
          approvalOpinion: '无异议',
          handlerDept: '养护公司',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '2',
          approvalStep: '养护项目审批 (一级)',
          handler: '刘明明',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: 'XXX部门',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '3',
          approvalStep: '养护项目审批 (二级)',
          handler: '罗敬政',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: '',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        }
      ]
    }
  },
  computed: {
    currentPhotos() {
      return this.photoData[this.activePhotoTab] || []
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
// 全局样式 - 修复弹窗遮罩层问题
.project-detail-dialog.el-dialog__wrapper {
  z-index: 3000 !important;
}

.project-detail-dialog + .v-modal {
  z-index: 2999 !important;
}
</style>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

// 项目详情弹框使用统一的深色主题样式
</style>