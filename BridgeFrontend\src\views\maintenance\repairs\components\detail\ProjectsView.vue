<template>
  <div class="projects-view maintenance-theme">
    <!-- 筛选器区域 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <el-select 
            v-model="filters.bridgeName" 
            placeholder="桥梁名称" 
            clearable
            class="filter-select"
          >
            <el-option 
              v-for="bridge in bridgeOptions" 
              :key="bridge.value" 
              :label="bridge.label" 
              :value="bridge.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <el-select 
            v-model="filters.projectType" 
            placeholder="养护项目" 
            clearable
            class="filter-select"
          >
            <el-option 
              v-for="project in projectOptions" 
              :key="project.value" 
              :label="project.label" 
              :value="project.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <el-select 
            v-model="filters.status" 
            placeholder="状态" 
            clearable
            class="filter-select"
          >
            <el-option 
              v-for="status in statusOptions" 
              :key="status.value" 
              :label="status.label" 
              :value="status.value"
            />
          </el-select>
        </div>
        
        <div class="filter-item">
          <el-select 
            v-model="filters.responsible" 
            placeholder="负责人" 
            clearable
            class="filter-select"
          >
            <el-option 
              v-for="person in responsibleOptions" 
              :key="person.value" 
              :label="person.label" 
              :value="person.value"
            />
          </el-select>
        </div>
        
        <div class="filter-actions">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 完成量统计 -->
    <div class="completion-stats">
      <span class="stats-text">完成量 {{ completedCount }}/{{ totalCount }}</span>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-section">
      <el-table 
        :data="filteredTableData" 
        class="projects-table"
        header-row-class-name="table-header"
        row-class-name="table-row"
      >
        <el-table-column prop="serialNumber" label="序号" width="80" align="center" />
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="140" />
        <el-table-column prop="projectType" label="养护项目" min-width="140" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              size="small"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="responsible" label="负责人" width="100" align="center" />
        <el-table-column prop="contactPhone" label="联系方式" width="130" />
        <el-table-column prop="completionTime" label="完成时间" width="160" />
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="handleViewDetail(scope.row)"
              class="action-btn"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 项目详情弹窗 -->
    <ProjectDetailDialog 
      :visible.sync="detailDialogVisible"
      :project-info="selectedProject"
      @close="handleDetailDialogClose"
    />
  </div>
</template>

<script>
import ProjectDetailDialog from './ProjectDetailDialog.vue'

export default {
  name: 'ProjectsView',
  components: {
    ProjectDetailDialog
  },
  props: {
    repairData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 筛选器数据
      filters: {
        bridgeName: '',
        projectType: '',
        status: '',
        responsible: ''
      },
      
      // 筛选器选项
      bridgeOptions: [
        { label: 'XXXXXX大桥', value: 'bridge1' },
        { label: 'YYYYYY大桥', value: 'bridge2' },
        { label: 'ZZZZZZ大桥', value: 'bridge3' }
      ],
      
      projectOptions: [
        { label: '排水系统养护', value: 'drainage' },
        { label: '上部结构养护', value: 'superstructure' },
        { label: '下部结构养护', value: 'substructure' },
        { label: '桥面系养护', value: 'deck' }
      ],
      
      statusOptions: [
        { label: '未完成', value: 'pending' },
        { label: '审核中', value: 'reviewing' },
        { label: '复核中', value: 'rechecking' },
        { label: '退回', value: 'rejected' },
        { label: '已完成', value: 'completed' }
      ],
      
      responsibleOptions: [
        { label: '黄昭言', value: 'huang' },
        { label: '刘雨桐', value: 'liu' },
        { label: '罗颖秋', value: 'luo' },
        { label: '林文龙', value: 'lin' },
        { label: '高枕书', value: 'gao' },
        { label: '徐桧桐', value: 'xu' },
        { label: '何叔川', value: 'he' },
        { label: '郭云舟', value: 'guo' },
        { label: '黄梓航', value: 'huang2' },
        { label: '赵景深', value: 'zhao' }
      ],
      
      // 表格数据
      tableData: [
        {
          serialNumber: '1',
          bridgeName: 'XXXXXX大桥',
          projectType: '排水系统养护',
          status: '未完成',
          responsible: '黄昭言',
          contactPhone: '15820007394',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '2',
          bridgeName: 'XXXXXX大桥',
          projectType: '排水系统养护',
          status: '审核中',
          responsible: '刘雨桐',
          contactPhone: '13122238579',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '3',
          bridgeName: 'XXXXXX大桥',
          projectType: '排水系统养护',
          status: '退回',
          responsible: '罗颖秋',
          contactPhone: '19620059483',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '4',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '复核中',
          responsible: '林文龙',
          contactPhone: '19607559483',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '5',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '高枕书',
          contactPhone: '18557189483',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '6',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '徐桧桐',
          contactPhone: '19020017495',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '007',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '何叔川',
          contactPhone: '19020017495',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '008',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '郭云舟',
          contactPhone: '19020017495',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '009',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '黄梓航',
          contactPhone: '19020017495',
          completionTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '010',
          bridgeName: 'XXXXXX大桥',
          projectType: '上部结构养护',
          status: '已完成',
          responsible: '赵景深',
          contactPhone: '19020017495',
          completionTime: '2025-09-18 10:43'
        }
      ],
      
      // 详情弹窗相关
      detailDialogVisible: false,
      selectedProject: {}
    }
  },
  
  computed: {
    // 过滤后的表格数据
    filteredTableData() {
      let data = [...this.tableData]
      
      if (this.filters.bridgeName) {
        data = data.filter(item => item.bridgeName.includes(this.filters.bridgeName))
      }
      
      if (this.filters.projectType) {
        const projectLabel = this.projectOptions.find(p => p.value === this.filters.projectType)?.label
        if (projectLabel) {
          data = data.filter(item => item.projectType === projectLabel)
        }
      }
      
      if (this.filters.status) {
        const statusLabel = this.statusOptions.find(s => s.value === this.filters.status)?.label
        if (statusLabel) {
          data = data.filter(item => item.status === statusLabel)
        }
      }
      
      if (this.filters.responsible) {
        const responsibleLabel = this.responsibleOptions.find(r => r.value === this.filters.responsible)?.label
        if (responsibleLabel) {
          data = data.filter(item => item.responsible === responsibleLabel)
        }
      }
      
      return data
    },
    
    // 总数量
    totalCount() {
      return this.tableData.length
    },
    
    // 已完成数量
    completedCount() {
      return this.tableData.filter(item => item.status === '已完成').length
    }
  },
  
  methods: {
    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        '未完成': 'info',
        '审核中': 'warning',
        '复核中': 'warning',
        '退回': 'danger',
        '已完成': 'success'
      }
      return statusMap[status] || 'info'
    },
    
    // 查询
    handleSearch() {
      // 这里可以添加搜索逻辑
      console.log('搜索条件:', this.filters)
    },
    
    // 重置
    handleReset() {
      this.filters = {
        bridgeName: '',
        projectType: '',
        status: '',
        responsible: ''
      }
    },
    
    // 查看详情
    handleViewDetail(row) {
      console.log('查看详情:', row)
      this.selectedProject = { ...row }
      this.detailDialogVisible = true
    },
    
    // 关闭详情弹窗
    handleDetailDialogClose() {
      this.detailDialogVisible = false
      this.selectedProject = {}
      // 确保移除遮罩层
      this.$nextTick(() => {
        const modal = document.querySelector('.v-modal')
        if (modal) {
          modal.remove()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.projects-view {
  // 筛选器区域
  .filter-section {
    background: #1e3a8a;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    
    .filter-row {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;
      
      .filter-item {
        flex: 1;
        min-width: 160px;
        
        .filter-select {
          width: 100%;
          
          :deep(.el-input__inner) {
            background: #1a2332;
            border: 1px solid #374151;
            color: #e5e7eb;
            
            &::placeholder {
              color: #9ca3af;
            }
            
            &:hover {
              border-color: #4f46e5;
            }
            
            &:focus {
              border-color: #4f46e5;
            }
          }
          
          :deep(.el-input__suffix) {
            .el-input__suffix-inner {
              color: #9ca3af;
            }
          }
        }
      }
      
      .filter-actions {
        display: flex;
        gap: 12px;
        
        .el-button {
          &--primary {
            background: #3b82f6;
            border-color: #3b82f6;
            
            &:hover {
              background: #2563eb;
              border-color: #2563eb;
            }
          }
          
          &:not(.el-button--primary) {
            background: #374151;
            border-color: #374151;
            color: #e5e7eb;
            
            &:hover {
              background: #4b5563;
              border-color: #4b5563;
            }
          }
        }
      }
    }
  }
  
  // 完成量统计
  .completion-stats {
    margin-bottom: 16px;
    
    .stats-text {
      color: #e5e7eb;
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  // 表格区域
  .table-section {
    .projects-table {
      @extend .common-table;
    }
  }
}

// 下拉框选项样式
:deep(.el-select-dropdown) {
  background: #1a2332 !important;
  border: 1px solid #374151 !important;
  
  .el-select-dropdown__item {
    background: #1a2332 !important;
    color: #e5e7eb !important;
    
    &:hover {
      background: #1e3a8a !important;
    }
    
    &.selected {
      background: #1e3a8a !important;
      color: #3b82f6 !important;
    }
  }
}
</style>
