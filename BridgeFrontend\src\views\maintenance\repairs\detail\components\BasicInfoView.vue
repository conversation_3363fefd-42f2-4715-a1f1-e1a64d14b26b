<template>
  <div class="basic-info-view">
    <div class="info-form">
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>项目名称:</label>
            <span>{{ projectData.projectName || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>项目类型:</label>
            <span>{{ getProjectTypeText(projectData.projectType) }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>项目开始时间:</label>
            <span>{{ projectData.startDate || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>项目结束时间:</label>
            <span>{{ projectData.endDate || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>管理单位:</label>
            <span>{{ projectData.managementUnitName || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>监理单位:</label>
            <span>{{ projectData.supervisionUnit || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>养护单位:</label>
            <span>{{ projectData.maintenanceUnitName || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>项目负责人:</label>
            <span>{{ projectData.managerName || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>联系方式:</label>
            <span>{{ projectData.contactPhone || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12" v-if="projectData.workload">
          <div class="info-item">
            <label>工作量:</label>
            <span>{{ projectData.workload }}</span>
          </div>
        </el-col>
      </el-row>
      
      <div class="info-item full-width">
        <label>项目内容:</label>
        <div class="content-text">
          {{ projectData.projectContent || '-' }}
        </div>
      </div>
      
      <div class="info-item full-width" v-if="projectData.attachments && projectData.attachments.length > 0">
        <label>附件:</label>
        <div class="attachments-list">
          <div 
            v-for="(file, index) in projectData.attachments" 
            :key="index"
            class="attachment-item"
          >
            <i :class="getFileIcon(file.name)"></i>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <el-button
              type="text"
              size="mini"
              @click="downloadFile(file)"
            >
              下载
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 项目状态信息 -->
      <div class="status-info">
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="status-card">
              <div class="status-label">项目状态</div>
              <div class="status-value">
                <status-tag :status="projectData.status" type="project" />
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="status-card">
              <div class="status-label">任务进度</div>
              <div class="status-value">
                <span :class="getProgressClass(projectData.completedTasks, projectData.totalTasks)">
                  {{ projectData.completedTasks || 0 }}/{{ projectData.totalTasks || 0 }}
                </span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="status-card">
              <div class="status-label">是否超期</div>
              <div class="status-value">
                <status-tag 
                  :status="projectData.isOverdue ? 'overdue' : 'normal'" 
                  type="task" 
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import StatusTag from '@/components/Maintenance/StatusTag'

export default {
  name: 'BasicInfoView',
  components: {
    StatusTag
  },
  props: {
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || '-'
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture'
      }
      
      return iconMap[extension] || 'el-icon-document'
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    // 下载文件
    downloadFile(file) {
      // 这里应该调用文件下载API
      window.open(file.url, '_blank')
    },
    
    // 获取进度样式类
    getProgressClass(completed, total) {
      if (!total) return 'progress-low'
      
      const progress = completed / total
      if (progress === 1) return 'progress-complete'
      if (progress >= 0.8) return 'progress-high'
      if (progress >= 0.5) return 'progress-medium'
      return 'progress-low'
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.basic-info-view {
  .info-form {
    .info-item {
      margin-bottom: 24px;
      display: flex;
      align-items: flex-start;
      
      &.full-width {
        flex-direction: column;
        align-items: stretch;
      }
      
      label {
        color: #ffffff;
        font-weight: normal;
        min-width: 120px;
        margin-right: 16px;
        flex-shrink: 0;
      }
      
      span {
        color: #ffffff;
        flex: 1;
      }
      
      .content-text {
        background: #374151;
        border: 1px solid #9ca3af;
        border-radius: 6px;
        padding: 12px 16px;
        color: #ffffff;
        min-height: 80px;
        margin-top: 8px;
        white-space: pre-wrap;
        word-break: break-word;
      }
      
      .attachments-list {
        margin-top: 8px;
        
        .attachment-item {
          display: flex;
          align-items: center;
          background: #4b5563;
          border-radius: 4px;
          padding: 8px 12px;
          margin-bottom: 8px;
          
          i {
            color: #9ca3af;
            margin-right: 8px;
          }
          
          .file-name {
            color: #ffffff;
            flex: 1;
            margin-right: 12px;
          }
          
          .file-size {
            color: #9ca3af;
            font-size: 12px;
            margin-right: 12px;
          }
          
          .el-button {
            color: #3b82f6;
            
            &:hover {
              color: #2563eb;
            }
          }
        }
      }
    }
  }
  
  .status-info {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #4b5563;
    
    .status-card {
      text-align: center;
      padding: 16px;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 8px;
      border: 1px solid rgba(59, 130, 246, 0.3);
      
      .status-label {
        color: #9ca3af;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .status-value {
        color: #ffffff;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}

// 进度样式
.progress-complete {
  color: #22c55e;
}

.progress-high {
  color: #3b82f6;
}

.progress-medium {
  color: #eab308;
}

.progress-low {
  color: #ef4444;
}
</style>
