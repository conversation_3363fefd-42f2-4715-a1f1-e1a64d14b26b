<template>
  <div class="diseases-info-view">
    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :model="queryParams" inline>
        <el-form-item label="桥梁名称">
          <el-select
            v-model="queryParams.bridgeName"
            placeholder="请选择桥梁"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="bridge in bridgeOptions"
              :key="bridge.value"
              :label="bridge.label"
              :value="bridge.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="病害类型">
          <el-select
            v-model="queryParams.diseaseType"
            placeholder="请选择类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="type in diseaseTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 完成量统计 -->
    <div class="progress-summary">
      <span class="summary-text">完成量 {{ completedCount }}/{{ totalCount }}</span>
    </div>
    
    <!-- 病害列表 -->
    <div class="diseases-table">
      <el-table
        v-loading="loading"
        :data="diseaseList"
        class="maintenance-table"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
        
        <el-table-column prop="diseasePart" label="病害部位" width="100" align="center" />
        
        <el-table-column prop="diseaseType" label="病害类型" width="120" align="center" />
        
        <el-table-column prop="completedTime" label="完成时间" width="150" align="center" />
        
        <el-table-column prop="manager" label="负责人" width="100" align="center" />
        
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleViewDisease(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 病害详情弹窗 -->
    <disease-detail-dialog
      v-model="showDiseaseDialog"
      :disease-data="selectedDisease"
    />
  </div>
</template>

<script>
import { getDiseaseDetail } from '@/api/maintenance/repairs'
import DiseaseDetailDialog from '../../components/detail/DiseaseDetailDialog'

export default {
  name: 'DiseasesInfoView',
  components: {
    'disease-detail-dialog': DiseaseDetailDialog
  },
  props: {
    projectId: {
      type: String,
      required: true
    },
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      diseaseList: [],
      selectedDisease: {},
      showDiseaseDialog: false,
      
      // 查询参数
      queryParams: {
        bridgeName: '',
        diseaseType: '',
        status: ''
      },
      
      // 选项数据
      bridgeOptions: [],
      
      // 病害类型选项
      diseaseTypes: [
        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },
        { label: '照明设施缺失', value: 'lighting_missing' },
        { label: '护栏损坏', value: 'guardrail_damage' },
        { label: '桥面破损', value: 'deck_damage' },
        { label: '排水不畅', value: 'drainage_poor' }
      ],
      
      // 状态选项
      statusOptions: [
        { label: '待处理', value: 'pending' },
        { label: '处理中', value: 'in_progress' },
        { label: '已完成', value: 'completed' }
      ]
    }
  },
  computed: {
    // 已完成病害数量
    completedCount() {
      return this.diseaseList.filter(disease => disease.status === 'completed').length
    },
    
    // 总病害数量
    totalCount() {
      return this.diseaseList.length
    }
  },
  created() {
    this.loadDiseases()
  },
  methods: {
    // 加载病害列表
    async loadDiseases() {
      this.loading = true
      try {
        // 这里应该调用获取项目病害列表的API
        // 暂时使用模拟数据
        this.diseaseList = [
          {
            id: 1,
            bridgeName: 'XXXXXX大桥',
            diseaseCode: '989',
            diseasePart: '伸缩缝',
            diseaseType: '伸缩缝缺失',
            completedTime: '2025-09-18 10:43',
            manager: '王景深',
            status: 'completed'
          },
          {
            id: 2,
            bridgeName: 'XXXXXX大桥',
            diseaseCode: '988',
            diseasePart: '伸缩缝',
            diseaseType: '伸缩缝缺失',
            completedTime: '2025-09-18 10:43',
            manager: '刘志强',
            status: 'completed'
          },
          {
            id: 3,
            bridgeName: 'XXXXXX大桥',
            diseaseCode: '987',
            diseasePart: '照明设施',
            diseaseType: '照明设施缺失',
            completedTime: '2025-09-18 10:43',
            manager: '赵临洲',
            status: 'completed'
          }
        ]
        
        // 提取桥梁选项
        this.bridgeOptions = [...new Set(this.diseaseList.map(item => item.bridgeName))]
          .map(name => ({ label: name, value: name }))
      } catch (error) {
        this.$message.error('加载病害列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 查询
    handleQuery() {
      this.loadDiseases()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        bridgeName: '',
        diseaseType: '',
        status: ''
      }
      this.loadDiseases()
    },
    
    // 查看病害详情
    async handleViewDisease(disease) {
      try {
        // 模拟API调用，实际项目中应该调用真实API
        // const response = await getDiseaseDetail(disease.id)
        // this.selectedDisease = response.data
        this.selectedDisease = disease
        this.showDiseaseDialog = true
      } catch (error) {
        this.$message.error('加载病害详情失败')
        console.error('加载病害详情失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';

.diseases-info-view {
  .filter-form {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.3);
  }
  
  .progress-summary {
    margin-bottom: 16px;
    
    .summary-text {
      color: #ffffff;
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .diseases-table {
    .maintenance-table {
      min-height: 300px;
    }
  }
}

.disease-dialog {
  .dialog-footer {
    text-align: right;
  }
}
</style>
