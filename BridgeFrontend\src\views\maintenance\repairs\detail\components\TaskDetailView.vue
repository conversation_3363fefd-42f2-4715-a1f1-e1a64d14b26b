<template>
  <div class="task-detail-view">
    <!-- 任务基本信息 -->
    <div class="task-info">
      <h3>任务信息</h3>
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>桥梁名称:</label>
            <span>{{ taskData.bridgeName || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>养护项目:</label>
            <span>{{ taskData.maintenanceProject || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>负责人:</label>
            <span>{{ taskData.manager || '-' }}</span>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>联系方式:</label>
            <span>{{ taskData.contactPhone || '-' }}</span>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <label>任务状态:</label>
            <status-tag :status="taskData.status" type="task" />
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="info-item">
            <label>完成时间:</label>
            <span>{{ taskData.completedTime || '-' }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 执行记录 -->
    <div class="execution-records" v-if="taskData.records && taskData.records.length > 0">
      <h3>执行记录</h3>
      <div class="records-list">
        <div 
          v-for="(record, index) in taskData.records" 
          :key="index"
          class="record-item"
        >
          <div class="record-header">
            <div class="record-info">
              <span class="record-time">{{ record.submitTime }}</span>
              <span class="record-submitter">提交人: {{ record.submitter }}</span>
            </div>
            <div class="record-status">
              <status-tag :status="record.status" type="task" />
            </div>
          </div>
          
          <div class="record-content">
            <div class="record-description">
              <label>执行说明:</label>
              <p>{{ record.description || '-' }}</p>
            </div>
            
            <!-- 执行照片 -->
            <div class="record-photos" v-if="record.photos && record.photos.length > 0">
              <label>执行照片:</label>
              <div class="photos-grid">
                <div 
                  v-for="(photo, photoIndex) in record.photos" 
                  :key="photoIndex"
                  class="photo-item"
                  @click="previewPhoto(photo)"
                >
                  <img :src="photo.thumbnail" :alt="photo.name" />
                  <div class="photo-overlay">
                    <i class="el-icon-zoom-in"></i>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 审核意见 -->
            <div class="audit-comments" v-if="record.auditComments && record.auditComments.length > 0">
              <label>审核意见:</label>
              <div class="comments-list">
                <div 
                  v-for="(comment, commentIndex) in record.auditComments" 
                  :key="commentIndex"
                  class="comment-item"
                >
                  <div class="comment-header">
                    <span class="comment-auditor">{{ comment.auditor }}</span>
                    <span class="comment-time">{{ comment.auditTime }}</span>
                  </div>
                  <div class="comment-content">{{ comment.comment }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 暂无记录 -->
    <div v-else class="no-records">
      <el-empty description="暂无执行记录" />
    </div>
    
    <!-- 图片预览 -->
    <el-dialog
      title="图片预览"
      :visible.sync="showPhotoPreview"
      class="photo-preview-dialog common-dialog"
      top="10vh"
    >
      <div class="photo-preview">
        <img :src="previewPhotoUrl" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import StatusTag from '@/components/Maintenance/StatusTag'

export default {
  name: 'TaskDetailView',
  components: {
    StatusTag
  },
  props: {
    taskData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showPhotoPreview: false,
      previewPhotoUrl: ''
    }
  },
  methods: {
    // 预览照片
    previewPhoto(photo) {
      this.previewPhotoUrl = photo.url
      this.showPhotoPreview = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.task-detail-view {
  .task-info {
    margin-bottom: 32px;
    
    h3 {
      color: #ffffff;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: bold;
    }
    
    .info-item {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      
      label {
        color: #9ca3af;
        min-width: 100px;
        margin-right: 12px;
      }
      
      span {
        color: #ffffff;
      }
    }
  }
  
  .execution-records {
    h3 {
      color: #ffffff;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: bold;
    }
    
    .records-list {
      .record-item {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        
        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #4b5563;
          
          .record-info {
            display: flex;
            align-items: center;
            gap: 16px;
            
            .record-time {
              color: #ffffff;
              font-weight: bold;
            }
            
            .record-submitter {
              color: #9ca3af;
              font-size: 14px;
            }
          }
        }
        
        .record-content {
          .record-description {
            margin-bottom: 16px;
            
            label {
              color: #9ca3af;
              display: block;
              margin-bottom: 8px;
            }
            
            p {
              color: #ffffff;
              background: #374151;
              border-radius: 4px;
              padding: 12px;
              margin: 0;
              white-space: pre-wrap;
              word-break: break-word;
            }
          }
          
          .record-photos {
            margin-bottom: 16px;
            
            label {
              color: #9ca3af;
              display: block;
              margin-bottom: 8px;
            }
            
            .photos-grid {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
              gap: 12px;
              
              .photo-item {
                position: relative;
                width: 120px;
                height: 120px;
                border-radius: 8px;
                overflow: hidden;
                cursor: pointer;
                transition: transform 0.2s ease;
                
                &:hover {
                  transform: scale(1.05);
                  
                  .photo-overlay {
                    opacity: 1;
                  }
                }
                
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
                
                .photo-overlay {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: rgba(0, 0, 0, 0.6);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  opacity: 0;
                  transition: opacity 0.2s ease;
                  
                  i {
                    color: #ffffff;
                    font-size: 24px;
                  }
                }
              }
            }
          }
          
          .audit-comments {
            label {
              color: #9ca3af;
              display: block;
              margin-bottom: 8px;
            }
            
            .comments-list {
              .comment-item {
                background: #374151;
                border-radius: 4px;
                padding: 12px;
                margin-bottom: 8px;
                
                .comment-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 8px;
                  
                  .comment-auditor {
                    color: #3b82f6;
                    font-weight: bold;
                  }
                  
                  .comment-time {
                    color: #9ca3af;
                    font-size: 12px;
                  }
                }
                
                .comment-content {
                  color: #ffffff;
                  white-space: pre-wrap;
                  word-break: break-word;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .no-records {
    text-align: center;
    padding: 40px;
    
    .el-empty {
      .el-empty__description {
        color: #9ca3af;
      }
    }
  }
}

.photo-preview-dialog {
  .photo-preview {
    text-align: center;
    
    img {
      max-width: 100%;
      max-height: 60vh;
      border-radius: 8px;
    }
  }
}
</style>
