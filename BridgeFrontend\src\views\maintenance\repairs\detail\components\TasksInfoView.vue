<template>
  <div class="tasks-info-view">
    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :model="queryParams" inline>
        <el-form-item label="桥梁名称">
          <el-select
            v-model="queryParams.bridgeName"
            placeholder="请选择桥梁"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="bridge in bridgeOptions"
              :key="bridge.value"
              :label="bridge.label"
              :value="bridge.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="养护项目">
          <el-select
            v-model="queryParams.maintenanceProject"
            placeholder="请选择项目"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="project in projectOptions"
              :key="project.value"
              :label="project.label"
              :value="project.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="status in statusOptions"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="负责人">
          <el-select
            v-model="queryParams.manager"
            placeholder="请选择负责人"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="manager in managerOptions"
              :key="manager.value"
              :label="manager.label"
              :value="manager.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 完成量统计 -->
    <div class="progress-summary">
      <span class="summary-text">完成量 {{ completedCount }}/{{ totalCount }}</span>
    </div>
    
    <!-- 任务列表 -->
    <div class="tasks-table">
      <el-table
        v-loading="loading"
        :data="taskList"
        class="maintenance-table"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="maintenanceProject" label="养护项目" min-width="120" show-overflow-tooltip />
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <status-tag :status="scope.row.status" type="task" />
          </template>
        </el-table-column>
        
        <el-table-column prop="manager" label="负责人" width="100" align="center" />
        
        <el-table-column prop="contactPhone" label="联系方式" width="120" align="center" />
        
        <el-table-column prop="completedTime" label="完成时间" width="150" align="center" />
        
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleViewTask(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 任务详情弹窗 -->
    <el-dialog
      title="任务详情"
      :visible.sync="showTaskDialog"
      class="task-dialog common-dialog-wide"
      top="5vh"
    >
      <task-detail-view 
        v-if="showTaskDialog"
        :task-data="selectedTask"
      />
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showTaskDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProjectTasks } from '@/api/maintenance/repairs'
import StatusTag from '@/components/Maintenance/StatusTag'
import TaskDetailView from './TaskDetailView'

export default {
  name: 'TasksInfoView',
  components: {
    StatusTag,
    TaskDetailView
  },
  props: {
    projectId: {
      type: String,
      required: true
    },
    projectData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      taskList: [],
      selectedTask: {},
      showTaskDialog: false,
      
      // 查询参数
      queryParams: {
        bridgeName: '',
        maintenanceProject: '',
        status: '',
        manager: ''
      },
      
      // 选项数据
      bridgeOptions: [],
      projectOptions: [],
      managerOptions: [],
      
      // 状态选项
      statusOptions: [
        { label: '未完成', value: 'not_started' },
        { label: '审核中', value: 'in_progress' },
        { label: '退回', value: 'rejected' },
        { label: '复核中', value: 'secondary_audit' },
        { label: '已完成', value: 'completed' }
      ]
    }
  },
  computed: {
    // 已完成任务数量
    completedCount() {
      return this.taskList.filter(task => task.status === 'completed').length
    },
    
    // 总任务数量
    totalCount() {
      return this.taskList.length
    }
  },
  created() {
    this.loadTasks()
  },
  methods: {
    // 加载任务列表
    async loadTasks() {
      this.loading = true
      try {
        const response = await getProjectTasks(this.projectId, this.queryParams)
        this.taskList = response.data.list || []
        this.bridgeOptions = response.data.bridgeOptions || []
        this.projectOptions = response.data.projectOptions || []
        this.managerOptions = response.data.managerOptions || []
      } catch (error) {
        this.$message.error('加载任务列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 查询
    handleQuery() {
      this.loadTasks()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        bridgeName: '',
        maintenanceProject: '',
        status: '',
        manager: ''
      }
      this.loadTasks()
    },
    
    // 查看任务详情
    handleViewTask(task) {
      this.selectedTask = task
      this.showTaskDialog = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.tasks-info-view {
  .filter-form {
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.3);
  }
  
  .progress-summary {
    margin-bottom: 16px;
    
    .summary-text {
      color: #ffffff;
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .tasks-table {
    .maintenance-table {
      min-height: 300px;
    }
  }
}

.task-dialog {
  .dialog-footer {
    text-align: right;
  }
}
</style>
