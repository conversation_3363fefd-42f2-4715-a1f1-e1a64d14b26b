<template>
  <div class="maintenance-theme">
    <div class="page-container">
      <div class="card-container">
        <!-- 弹窗标题栏 -->
        <div class="page-header">
          <h2>查看详情</h2>
          <el-button
            type="text"
            icon="el-icon-close"
            class="close-btn"
            @click="handleClose"
          />
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
          <div class="tab-container">
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'basic' }"
              @click="switchTab('basic')"
            >
              基本信息
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'tasks' }"
              @click="switchTab('tasks')"
            >
              养护项目
            </div>
            <div 
              v-if="showDiseaseTab"
              class="tab-item"
              :class="{ 'is-active': activeTab === 'diseases' }"
              @click="switchTab('diseases')"
            >
              病害养护
            </div>
          </div>
        </div>
        
        <!-- 标签内容 -->
        <div class="tab-content">
          <!-- 基本信息 -->
          <div v-if="activeTab === 'basic'" class="basic-info">
            <basic-info-view :project-data="projectData" />
          </div>
          
          <!-- 养护项目 -->
          <div v-if="activeTab === 'tasks'" class="tasks-info">
            <tasks-info-view 
              :project-id="projectId"
              :project-data="projectData"
            />
          </div>
          
          <!-- 病害养护 -->
          <div v-if="activeTab === 'diseases' && showDiseaseTab" class="diseases-info">
            <diseases-info-view 
              :project-id="projectId"
              :project-data="projectData"
            />
          </div>
        </div>
        
        <!-- 关闭按钮 -->
        <div class="action-buttons">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRepairDetail } from '@/api/maintenance/repairs'
import BasicInfoView from './components/BasicInfoView'
import TasksInfoView from './components/TasksInfoView'
import DiseasesInfoView from './components/DiseasesInfoView'

export default {
  name: 'MaintenanceRepairDetail',
  components: {
    BasicInfoView,
    TasksInfoView,
    DiseasesInfoView
  },
  data() {
    return {
      loading: false,
      activeTab: 'basic',
      projectData: {},
      projectId: ''
    }
  },
  computed: {
    // 是否显示病害养护标签
    showDiseaseTab() {
      return this.projectData.projectType === 'monthly'
    }
  },
  async created() {
    this.projectId = this.$route.params.id
    await this.loadProjectDetail()
  },
  methods: {
    // 加载项目详情
    async loadProjectDetail() {
      try {
        this.loading = true
        const response = await getRepairDetail(this.projectId)
        this.projectData = response.data || {}
      } catch (error) {
        this.$message.error('加载项目详情失败')
        this.handleClose()
      } finally {
        this.loading = false
      }
    },
    
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab
    },
    
    // 关闭页面
    handleClose() {
      this.$router.push('/maintenance/repairs')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .close-btn {
    color: #9ca3af;
    font-size: 18px;
    
    &:hover {
      color: #ffffff;
      background: #ef4444;
      border-radius: 50%;
    }
  }
}

.tab-navigation {
  background: #1e3a8a;
  border-bottom: 1px solid #4b5563;
  
  .tab-container {
    display: flex;
    padding: 0 24px;
    
    .tab-item {
      padding: 12px 16px;
      color: #9ca3af;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #ffffff;
      }
      
      &.is-active {
        color: #ffffff;
        border-bottom-color: #3b82f6;
        font-weight: bold;
      }
    }
  }
}

.tab-content {
  padding: 24px;
  min-height: 400px;
}

.action-buttons {
  padding: 16px 24px;
  border-top: 1px solid #4b5563;
  text-align: right;
}
</style>
