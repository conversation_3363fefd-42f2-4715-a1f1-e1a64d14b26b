<template>
  <el-dialog
    title="病害养护详情"
    :visible.sync="dialogVisible"
    class="project-detail-dialog common-dialog-wide inspection-dialog-base modern-dialog dark-theme force-high-zindex"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    append-to-body
    top="5vh"
  >
    <div class="modal-content-wrapper">
      <!-- 病害信息 -->
      <div class="section-container">
        <div class="section-title">病害信息</div>
        
        <div class="form-row">
          <div class="form-item">
            <label>桥梁/隧道名称:</label>
            <div class="form-value">{{ emergencyData.bridgeName || 'XXXXXX大桥' }}</div>
          </div>
          <div class="form-item">
            <label>病害部位:</label>
            <div class="form-value">{{ emergencyData.diseasePart || 'XXXXXX部位' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>病害类型:</label>
            <div class="form-value">{{ emergencyData.diseaseType || 'XXXXXXXXXX' }}</div>
          </div>
          <div class="form-item">
            <label>病害位置:</label>
            <div class="form-value">{{ emergencyData.diseaseLocation || 'XXXXXXXXXX' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>病害数量:</label>
            <div class="form-value">{{ emergencyData.diseaseCount || '12' }}</div>
          </div>
          <div class="form-item">
            <!-- 空位，保持布局对称 -->
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item full-width">
            <label>病害描述:</label>
            <div class="form-textarea">{{ emergencyData.diseaseDescription || 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>上报人:</label>
            <div class="form-value">{{ emergencyData.reporter || 'XXXXXX大桥' }}</div>
          </div>
          <div class="form-item">
            <label>联系方式:</label>
            <div class="form-value">{{ emergencyData.contactPhone || '19321207394' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item full-width">
            <label>病害照片:</label>
            <div class="photo-grid">
              <div 
                v-for="(photo, index) in diseasePhotos" 
                :key="index"
                class="photo-item"
                @click="previewPhoto(photo)"
              >
                <img :src="photo.url" :alt="photo.name" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 处置信息 -->
      <div class="section-container">
        <div class="section-title">处置信息</div>
        
        <div class="form-row">
          <div class="form-item">
            <label>养护单位:</label>
            <div class="form-value">{{ emergencyData.maintenanceUnit || 'XXXXXX大桥' }}</div>
          </div>
          <div class="form-item">
            <label>管理单位:</label>
            <div class="form-value">{{ emergencyData.managementUnit || 'XXXXXX部位' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>处置人员:</label>
            <div class="form-value">{{ emergencyData.processor || '张三' }}</div>
          </div>
          <div class="form-item">
            <label>联系方式:</label>
            <div class="form-value">{{ emergencyData.processorPhone || '19321207394' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <label>处置时间:</label>
            <div class="form-value">{{ emergencyData.processTime || '2025-09-18 10:34' }}</div>
          </div>
          <div class="form-item">
            <!-- 空位，保持布局对称 -->
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item full-width">
            <label>处置说明:</label>
            <div class="form-textarea">{{ emergencyData.processDescription || '请输入' }}</div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item full-width">
            <label>处置照片:</label>
            <div class="photo-section">
              <div class="photo-tabs">
                <div 
                  v-for="tab in photoTabs" 
                  :key="tab.key"
                  class="photo-tab"
                  :class="{ active: activePhotoTab === tab.key }"
                  @click="activePhotoTab = tab.key"
                >
                  {{ tab.label }}
                </div>
              </div>
              <div class="photo-grid">
                <div 
                  v-for="(photo, index) in currentPhotos" 
                  :key="index"
                  class="photo-item"
                  @click="previewPhoto(photo)"
                >
                  <img :src="photo.url" :alt="photo.name" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 审核信息 -->
      <div class="section-container">
        <div class="section-title">审核信息</div>
        
        <div class="audit-table">
          <el-table
            :data="auditHistory"
            class="audit-data-table"
            header-row-class-name="audit-table-header"
            row-class-name="audit-table-row"
          >
            <el-table-column prop="serialNumber" label="序号" width="60" align="center" />
            <el-table-column prop="approvalStep" label="审批环节" width="140" align="center" />
            <el-table-column prop="handler" label="处理人" width="100" align="center" />
            <el-table-column prop="approvalStatus" label="审批状态" width="100" align="center" />
            <el-table-column prop="approvalOpinion" label="审批意见" width="120" align="center" />
            <el-table-column prop="handlerDept" label="处理人部门" width="120" align="center" />
            <el-table-column prop="receiveTime" label="接收时间" width="140" align="center" />
            <el-table-column prop="finishTime" label="办结时间" width="140" align="center" />
          </el-table>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'EmergencyDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    emergencyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activePhotoTab: 'scene',
      photoTabs: [
        { key: 'scene', label: '现场照片' },
        { key: 'personnel', label: '人车照片' },
        { key: 'before', label: '维修前' },
        { key: 'during', label: '维修中' },
        { key: 'after', label: '维修后' }
      ],
      // 模拟病害照片数据
      diseasePhotos: [
        { url: require('@/assets/images/test/inspection_image1.png'), name: '照片1' },
        { url: require('@/assets/images/test/inspection_image2.png'), name: '照片2' }
      ],
      // 模拟处置照片数据
      photoData: {
        scene: [
          { url: require('@/assets/images/test/inspection_image1.png'), name: '照片3' },
          { url: require('@/assets/images/test/inspection_image2.png'), name: '照片4' }
        ],
        personnel: [],
        before: [],
        during: [],
        after: []
      },
      // 审核历史数据
      auditHistory: [
        {
          serialNumber: '1',
          approvalStep: '开始申请',
          handler: '黄昭言',
          approvalStatus: '通过',
          approvalOpinion: '无异议',
          handlerDept: '养护公司',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '2',
          approvalStep: '养护项目审批 (一级)',
          handler: '刘雨桐',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: 'XXX部门',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        },
        {
          serialNumber: '3',
          approvalStep: '养护项目审批 (二级)',
          handler: '罗颖秋',
          approvalStatus: '',
          approvalOpinion: '',
          handlerDept: '',
          receiveTime: '2025-09-18 10:43',
          finishTime: '2025-09-18 10:43'
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    currentPhotos() {
      return this.photoData[this.activePhotoTab] || []
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    // 预览照片
    previewPhoto(photo) {
      // 这里可以实现照片预览功能
      console.log('预览照片:', photo)
    }
  }
}
</script>

<style lang="scss">
// 全局样式 - 修复弹窗遮罩层问题
.emergency-detail-dialog.el-dialog__wrapper {
  z-index: 3000 !important;
}

.emergency-detail-dialog + .v-modal {
  z-index: 2999 !important;
}
</style>

<style lang="scss" scoped>
@import '@/assets/styles/maintenance-theme.scss';

// 应急详情弹框使用统一的深色主题样式
</style>
