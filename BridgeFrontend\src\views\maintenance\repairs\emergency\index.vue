<template>
  <div class="maintenance-theme">
    <div class="page-container">
      <div class="card-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2>应急维修管理</h2>
        </div>
        
        <!-- 标签导航 -->
        <div class="tab-navigation">
          <div class="tab-container">
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'bridge' }"
              @click="switchTab('bridge')"
            >
              <i class="el-icon-s-home"></i>
              桥梁养护维修
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeTab === 'tunnel' }"
              @click="switchTab('tunnel')"
            >
              <i class="el-icon-place"></i>
              隧道养护维修
            </div>
          </div>
        </div>
        
        <!-- 二级导航 -->
        <div class="sub-navigation">
          <div class="sub-tab-container">
            <div 
              class="sub-tab-item"
              @click="$router.push('/maintenance/repairs')"
            >
              养护/保洁项目
            </div>
            <div 
              class="sub-tab-item is-active"
            >
              应急维修
            </div>
          </div>
        </div>
        
        <!-- 筛选表单 -->
        <div class="filter-form">
          <el-form :model="queryParams" inline>
            <el-form-item label="桥梁名称">
              <el-select
                v-model="queryParams.bridgeName"
                placeholder="请选择桥梁"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="bridge in bridgeOptions"
                  :key="bridge.value"
                  :label="bridge.label"
                  :value="bridge.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="病害类型">
              <el-select
                v-model="queryParams.diseaseType"
                placeholder="请选择类型"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="type in diseaseTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="负责人">
              <el-select
                v-model="queryParams.manager"
                placeholder="请选择负责人"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="manager in managerOptions"
                  :key="manager.value"
                  :label="manager.label"
                  :value="manager.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="养护单位">
              <el-select
                v-model="queryParams.maintenanceUnit"
                placeholder="请选择单位"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="unit in unitOptions"
                  :key="unit.value"
                  :label="unit.label"
                  :value="unit.value"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="emergencyList"
            class="maintenance-table"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            
            <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
            
            <el-table-column prop="reporter" label="上报人" width="100" align="center" />
            
            <el-table-column prop="reportTime" label="上报时间" width="120" align="center" />
            
            <el-table-column prop="contactPhone" label="联系方式" width="120" align="center" />
            
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template slot-scope="scope">
                <status-tag :status="scope.row.status" type="task" />
              </template>
            </el-table-column>
            
            <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
            
            <el-table-column prop="diseasePart" label="病害部位" width="100" align="center" />
            
            <el-table-column prop="diseaseType" label="病害类型" width="120" align="center" />
            
            <el-table-column prop="diseaseCount" label="病害数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="disease-count">{{ scope.row.diseaseCount }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="manager" label="负责人" width="100" align="center" />
            
            <el-table-column prop="maintenanceUnit" label="养护单位" min-width="150" show-overflow-tooltip />
            
            <el-table-column label="操作" width="80" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="handleView(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    
    <!-- 应急维修详情弹窗 -->
    <emergency-detail-dialog
      :visible.sync="showDetailDialog"
      :emergency-data="selectedEmergency"
    />
  </div>
</template>

<script>
import { getEmergencyRepairList } from '@/api/maintenance/repairs'
import StatusTag from '@/components/Maintenance/StatusTag'
import EmergencyDetailDialog from './components/EmergencyDetailDialog'

export default {
  name: 'MaintenanceEmergencyRepairs',
  components: {
    StatusTag,
    EmergencyDetailDialog
  },
  data() {
    return {
      loading: false,
      activeTab: 'bridge', // bridge, tunnel
      emergencyList: [],
      selectedEmergency: {},
      showDetailDialog: false,
      total: 0,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        bridgeName: '',
        status: '',
        diseaseType: '',
        manager: '',
        maintenanceUnit: '',
        infrastructureType: 'bridge'
      },
      
      // 选项数据
      bridgeOptions: [],
      managerOptions: [],
      unitOptions: [],
      
      // 状态选项
      statusOptions: [
        { label: '待处理', value: 'pending' },
        { label: '处理审批中', value: 'in_review' },
        { label: '退回', value: 'rejected' },
        { label: '已处理', value: 'completed' }
      ],
      
      // 病害类型选项
      diseaseTypes: [
        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },
        { label: '照明设施缺失', value: 'lighting_missing' },
        { label: '护栏损坏', value: 'guardrail_damage' },
        { label: '桥面破损', value: 'deck_damage' },
        { label: '排水不畅', value: 'drainage_poor' }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取应急维修列表
    async getList() {
      this.loading = true
      try {
        this.queryParams.infrastructureType = this.activeTab
        
        const response = await getEmergencyRepairList(this.queryParams)
        this.emergencyList = response.data.list || []
        this.total = response.data.total || 0
        this.bridgeOptions = response.data.bridgeOptions || []
        this.managerOptions = response.data.managerOptions || []
        this.unitOptions = response.data.unitOptions || []
      } catch (error) {
        this.$message.error('获取应急维修列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        bridgeName: '',
        status: '',
        diseaseType: '',
        manager: '',
        maintenanceUnit: '',
        infrastructureType: this.activeTab
      }
      this.getList()
    },
    
    // 查看详情
    handleView(row) {
      this.selectedEmergency = row
      this.showDetailDialog = true
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

.tab-navigation {
  @extend .common-tab-navigation;
}

.sub-navigation {
  @extend .common-secondary-navigation;
  
  .sub-tab-container {
    @extend .sub-tab-container;
  }
}

.filter-form {
  padding: 24px;
  background: #1e3a8a;
  border-bottom: 1px solid #4b5563;
}

.table-container {
  padding: 0 24px;
  
  .disease-count {
    color: #3b82f6;
    font-weight: bold;
  }
}

.pagination-container {
  padding: 16px 24px;
  text-align: center;
}
</style>
