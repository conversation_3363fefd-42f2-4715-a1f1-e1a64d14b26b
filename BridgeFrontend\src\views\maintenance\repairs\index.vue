<template>
  <div class="maintenance-theme inspection-container">
    <div class="page-container">
      <div class="card-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2>养护维修</h2>
        </div>
        
        <!-- 一级Tab导航 - 基础设施类型 -->
        <div class="primary-tab-navigation">
          <div class="primary-tabs">
            <div 
              class="tab-item"
              :class="{ 'is-active': activeInfrastructure === 'bridge' }"
              @click="switchInfrastructure('bridge')"
            >
              <i class="el-icon-s-home"></i>
              桥梁养护维修
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeInfrastructure === 'tunnel' }"
              @click="switchInfrastructure('tunnel')"
            >
              <i class="el-icon-place"></i>
              隧道养护维修
            </div>
          </div>
        </div>
        
        <!-- 二级Tab导航 - 项目类型 -->
        <div class="secondary-tab-navigation">
          <div class="secondary-tabs">
            <div 
              class="tab-item"
              :class="{ 'is-active': activeProjectType === 'maintenance' }"
              @click="switchProjectType('maintenance')"
            >
              <i class="el-icon-setting"></i>
              养护/保洁项目
            </div>
            <div 
              class="tab-item"
              :class="{ 'is-active': activeProjectType === 'emergency' }"
              @click="switchProjectType('emergency')"
            >
              <i class="el-icon-warning"></i>
              应急维修
            </div>
          </div>
        </div>
        
        <!-- 筛选条件 -->
        <div class="filter-section">
          <!-- 养护/保洁项目筛选条件 -->
          <div v-if="activeProjectType === 'maintenance'" class="filter-content">
            <el-input
              v-model="queryParams.keyword"
              placeholder="项目名称、负责人、养护单位"
              clearable
              class="filter-select"
            />

            <el-select
              v-model="queryParams.projectType"
              placeholder="项目类型"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="type in projectTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
            
            <el-select
              v-model="queryParams.isOverdue"
              placeholder="是否超期"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option label="是" value="true" />
              <el-option label="否" value="false" />
            </el-select>
            
            <el-select
              v-model="queryParams.maintenanceUnit"
              placeholder="养护单位"
              clearable
              filterable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="unit in maintenanceUnits"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </el-select>
            
            <div class="filter-actions">
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </div>
          </div>
          
          <!-- 应急维修筛选条件 -->
          <div v-if="activeProjectType === 'emergency'" class="filter-content">
            <el-input
              v-model="emergencyQueryParams.keyword"
              placeholder="项目名称、负责人、养护单位"
              clearable
              class="filter-select"
            />

            <el-select
              v-model="emergencyQueryParams.bridgeName"
              placeholder="桥梁名称"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="bridge in bridgeOptions"
                :key="bridge.value"
                :label="bridge.label"
                :value="bridge.value"
              />
            </el-select>
            
            <el-select
              v-model="emergencyQueryParams.status"
              placeholder="状态"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
            
            <el-select
              v-model="emergencyQueryParams.diseaseType"
              placeholder="病害类型"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="type in diseaseTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
            
            <el-select
              v-model="emergencyQueryParams.manager"
              placeholder="负责人"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="manager in managerOptions"
                :key="manager.value"
                :label="manager.label"
                :value="manager.value"
              />
            </el-select>
            
            <el-select
              v-model="emergencyQueryParams.maintenanceUnit"
              placeholder="养护单位"
              clearable
              class="filter-select"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="unit in unitOptions"
                :key="unit.value"
                :label="unit.label"
                :value="unit.value"
              />
            </el-select>
            
            <div class="filter-actions">
              <el-button type="primary" @click="handleEmergencyQuery">查询</el-button>
              <el-button @click="resetEmergencyQuery">重置</el-button>
            </div>
          </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="inspection-table common-table">
          <!-- 养护/保洁项目表格 -->
          <el-table
            v-if="activeProjectType === 'maintenance'"
            v-loading="loading"
            :data="repairList"
            :key="repairList.length"
            style="width: 100%"
            row-key="id"
            :row-style="{ height: '32px' }"
            size="small"
            class="inspection-data-table compact-table"
          >
            <el-table-column width="90" align="center">
              <template slot="header">
                <span class="table-header-wrap">序号</span>
              </template>
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="projectName" label="项目名称" min-width="200" show-overflow-tooltip />
            
            <el-table-column prop="projectType" label="项目类型" width="100" align="center">
              <template slot-scope="scope">
                {{ getProjectTypeText(scope.row.projectType) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="projectStatus" label="项目状态" width="100" align="center">
              <template slot-scope="scope">
                {{ getProjectStatusText(scope.row.projectStatus) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="taskProgress" label="任务完成量" width="120" align="center">
              <template slot-scope="scope">
                <span :class="getProgressClass(scope.row.completedTasks, scope.row.totalTasks)">
                  {{ scope.row.completedTasks }}/{{ scope.row.totalTasks }}
                </span>
              </template>
            </el-table-column>
            
            <el-table-column prop="startDate" label="开始日期" width="110" align="center" />
            
            <el-table-column prop="endDate" label="结束日期" width="110" align="center" />
            
            <el-table-column prop="isOverdue" label="是否超期" width="100" align="center">
              <template slot-scope="scope">
                {{ scope.row.isOverdue ? '超期' : '正常' }}
              </template>
            </el-table-column>
            
            <el-table-column prop="maintenanceUnit" label="养护单位" min-width="120" show-overflow-tooltip />
            
            <el-table-column prop="manager" label="负责人" width="80" align="center" />
            
            <el-table-column label="操作" width="200" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleView(scope.row)"
                >
                  查看
                </el-button>
                
                <el-button
                  v-if="scope.row.projectStatus === 'in_progress' || scope.row.projectStatus === 'overdue'"
                  type="text"
                  size="small"
                  @click="handleExtension(scope.row)"
                >
                  延期申请
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 应急维修表格 -->
          <el-table
            v-if="activeProjectType === 'emergency'"
            v-loading="loading"
            :data="emergencyList"
            :key="emergencyList.length"
            style="width: 100%"
            row-key="id"
            :row-style="{ height: '32px' }"
            size="small"
            class="inspection-data-table compact-table"
          >
            <el-table-column width="90" align="center">
              <template slot="header">
                <span class="table-header-wrap">序号</span>
              </template>
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" show-overflow-tooltip />
            
            <el-table-column prop="reporter" label="上报人" width="100" align="center" />
            
            <el-table-column prop="reportTime" label="上报时间" width="120" align="center" />
            
            <el-table-column prop="contactPhone" label="联系方式" width="120" align="center" />
            
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template slot-scope="scope">
                {{ getEmergencyStatusText(scope.row.status) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="diseaseCode" label="病害编号" width="100" align="center" />
            
            <el-table-column prop="diseasePart" label="病害部位" width="100" align="center" />
            
            <el-table-column prop="diseaseType" label="病害类型" width="120" align="center" />
            
            <el-table-column prop="diseaseCount" label="病害数量" width="100" align="center">
              <template slot-scope="scope">
                <span class="disease-count">{{ scope.row.diseaseCount }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="manager" label="负责人" width="100" align="center" />
            
            <el-table-column prop="maintenanceUnit" label="养护单位" min-width="150" show-overflow-tooltip />
            
            <el-table-column label="操作" width="80" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleEmergencyView(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="inspection-pagination">
          <!-- 养护/保洁项目分页 -->
          <el-pagination
            v-if="activeProjectType === 'maintenance'"
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          
          <!-- 应急维修分页 -->
          <el-pagination
            v-if="activeProjectType === 'emergency'"
            :current-page="emergencyQueryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="emergencyQueryParams.pageSize"
            :total="emergencyTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleEmergencySizeChange"
            @current-change="handleEmergencyCurrentChange"
          />
        </div>
      </div>
    </div>
    
    <!-- 应急维修详情弹窗 -->
    <emergency-detail-dialog
      :visible.sync="showEmergencyDetailDialog"
      :emergency-data="selectedEmergency"
    />
    
    <!-- 维修项目详情弹窗 -->
    <repair-detail-dialog
      :visible.sync="showRepairDetailDialog"
      :repair-data="selectedRepair"
      :infrastructure-type="activeInfrastructure"
      @close="showRepairDetailDialog = false"
    />
    
    <!-- 延期申请弹窗 -->
    <extension-dialog
      :visible.sync="showExtensionDialog"
      :project-data="selectedRepair"
      @success="handleExtensionSuccess"
    />
  </div>
</template>

<script>
import { getRepairList, getEmergencyRepairList } from '@/api/maintenance/repairs'
import { getMaintenanceUnits } from '@/api/maintenance/projects'
import EmergencyDetailDialog from './emergency/components/EmergencyDetailDialog'
import RepairDetailDialog from './components/RepairDetailDialog'
import ExtensionDialog from './components/ExtensionDialog'

export default {
  name: 'MaintenanceRepairs',
  components: {
    EmergencyDetailDialog,
    RepairDetailDialog,
    ExtensionDialog
  },
  data() {
    return {
      loading: false,
      activeInfrastructure: 'bridge', // bridge, tunnel
      activeProjectType: 'maintenance', // maintenance, emergency
      repairList: [],
      total: 0,
      projectOptions: [],
      maintenanceUnits: [],
      
      // 应急维修相关数据
      emergencyList: [],
      emergencyTotal: 0,
      selectedEmergency: {},
      showEmergencyDetailDialog: false,
      bridgeOptions: [],
      managerOptions: [],
      unitOptions: [],
      
      // 维修项目详情弹框相关数据
      selectedRepair: {},
      showRepairDetailDialog: false,
      
      // 延期申请弹框相关数据
      showExtensionDialog: false,
      
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        keyword: '',
        projectName: '',
        projectType: '',
        isOverdue: '',
        maintenanceUnit: '',
        infrastructureType: 'bridge',
        repairType: 'maintenance'
      },
      
      // 应急维修查询参数
      emergencyQueryParams: {
        pageNum: 1,
        pageSize: 20,
        keyword: '',
        bridgeName: '',
        status: '',
        diseaseType: '',
        manager: '',
        maintenanceUnit: '',
        infrastructureType: 'bridge'
      },
      
      // 项目类型选项
      projectTypes: [
        { label: '月度养护', value: 'monthly' },
        { label: '保洁项目', value: 'cleaning' },
        { label: '应急养护', value: 'emergency' },
        { label: '预防养护', value: 'preventive' }
      ],
      
      // 应急维修状态选项
      statusOptions: [
        { label: '待处理', value: 'pending' },
        { label: '处理审批中', value: 'in_review' },
        { label: '退回', value: 'rejected' },
        { label: '已处理', value: 'completed' }
      ],
      
      // 病害类型选项
      diseaseTypes: [
        { label: '伸缩缝缺失', value: 'expansion_joint_missing' },
        { label: '照明设施缺失', value: 'lighting_missing' },
        { label: '护栏损坏', value: 'guardrail_damage' },
        { label: '桥面破损', value: 'deck_damage' },
        { label: '排水不畅', value: 'drainage_poor' }
      ]
    }
  },
  created() {
    this.loadOptions()
    this.getList()
  },
  methods: {
    // 加载选项数据
    async loadOptions() {
      try {
        // 使用静态数据，因为后端接口还未提供
        this.maintenanceUnits = [
          { label: '长沙市桥梁管理处', value: '长沙市桥梁管理处' },
          { label: '长沙市隧道管理处', value: '长沙市隧道管理处' },
          { label: '桥梁养护有限公司', value: '桥梁养护有限公司' }
        ]
      } catch (error) {
        this.$message.error('加载选项数据失败')
      }
    },
    
    // 获取列表数据
    getList() {
      if (this.activeProjectType === 'maintenance') {
        this.getRepairList()
      } else if (this.activeProjectType === 'emergency') {
        this.getEmergencyList()
      }
    },
    
    // 获取维修项目列表
    async getRepairList() {
      this.loading = true
      try {
        this.queryParams.infrastructureType = this.activeInfrastructure
        this.queryParams.repairType = this.activeProjectType
        
        // 使用静态数据，因为后端接口还未提供
        const mockData = this.getMockRepairList()
        this.repairList = mockData.list
        this.total = mockData.total
        this.projectOptions = mockData.projectOptions
      } catch (error) {
        this.$message.error('获取维修项目列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取应急维修列表
    async getEmergencyList() {
      this.loading = true
      try {
        this.emergencyQueryParams.infrastructureType = this.activeInfrastructure
        
        // 使用静态数据，因为后端接口还未提供
        const mockData = this.getMockEmergencyList()
        this.emergencyList = mockData.list
        this.emergencyTotal = mockData.total
        this.bridgeOptions = mockData.bridgeOptions
        this.managerOptions = mockData.managerOptions
        this.unitOptions = mockData.unitOptions
      } catch (error) {
        this.$message.error('获取应急维修列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 切换基础设施类型
    switchInfrastructure(type) {
      this.activeInfrastructure = type
      this.queryParams.pageNum = 1
      this.emergencyQueryParams.pageNum = 1
      this.getList()
    },
    
    // 切换项目类型
    switchProjectType(type) {
      this.activeProjectType = type
      this.queryParams.pageNum = 1
      this.emergencyQueryParams.pageNum = 1
      this.getList()
    },
    
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getRepairList()
    },
    
    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        keyword: '',
        projectName: '',
        projectType: '',
        isOverdue: '',
        maintenanceUnit: '',
        infrastructureType: this.activeInfrastructure,
        repairType: this.activeProjectType
      }
      this.getRepairList()
    },
    
    // 应急维修查询
    handleEmergencyQuery() {
      this.emergencyQueryParams.pageNum = 1
      this.getEmergencyList()
    },
    
    // 重置应急维修查询
    resetEmergencyQuery() {
      this.emergencyQueryParams = {
        pageNum: 1,
        pageSize: 20,
        keyword: '',
        bridgeName: '',
        status: '',
        diseaseType: '',
        manager: '',
        maintenanceUnit: '',
        infrastructureType: this.activeInfrastructure
      }
      this.getEmergencyList()
    },
    
    // 查看项目详情
    handleView(row) {
      this.selectedRepair = row
      this.showRepairDetailDialog = true
    },
    
    // 查看应急维修详情
    handleEmergencyView(row) {
      this.selectedEmergency = row
      this.showEmergencyDetailDialog = true
    },
    
    // 延期申请
    handleExtension(row) {
      this.selectedRepair = row
      this.showExtensionDialog = true
    },
    
    // 延期申请成功后处理
    handleExtensionSuccess() {
      // 刷新列表数据
      this.getList()
      // 显示成功消息已在弹框组件中处理
    },
    
    // 养护维修分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getRepairList()
    },
    
    // 养护维修当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getRepairList()
    },
    
    // 应急维修分页大小变化
    handleEmergencySizeChange(val) {
      this.emergencyQueryParams.pageSize = val
      this.getEmergencyList()
    },
    
    // 应急维修当前页变化
    handleEmergencyCurrentChange(val) {
      this.emergencyQueryParams.pageNum = val
      this.getEmergencyList()
    },
    
    // 获取项目类型文本
    getProjectTypeText(type) {
      const typeMap = {
        monthly: '月度养护',
        cleaning: '保洁项目',
        emergency: '应急养护',
        preventive: '预防养护'
      }
      return typeMap[type] || type
    },
    
    // 获取项目状态文本
    getProjectStatusText(status) {
      const statusMap = {
        draft: '草稿',
        pending: '待审批',
        waiting: '待审批',
        approved: '已通过',
        rejected: '已驳回',
        in_progress: '进行中',
        overdue: '超期',
        completed: '已完成',
        overdue_completed: '超期完成'
      }
      return statusMap[status] || status
    },
    
    // 获取应急维修状态文本
    getEmergencyStatusText(status) {
      const statusMap = {
        not_started: '未开始',
        pending: '待处理',
        in_progress: '处理中',
        completed: '已完成',
        verified: '已验收',
        archived: '已归档'
      }
      return statusMap[status] || status
    },
    
    // 获取进度样式类
    getProgressClass(completed, total) {
      const progress = completed / total
      if (progress === 1) return 'progress-complete'
      if (progress >= 0.8) return 'progress-high'
      if (progress >= 0.5) return 'progress-medium'
      return 'progress-low'
    },

    // 获取静态维修项目数据
    getMockRepairList() {
      const mockData = [
        // 1. 月度养护项目
        {
          id: 1,
          projectName: '湘江大桥2025年9月月度养护项目',
          projectType: 'monthly',
          projectStatus: 'in_progress',
          completedTasks: 0,
          totalTasks: 32,
          startDate: '2025/09/01',
          endDate: '2025/09/30',
          isOverdue: false,
          maintenanceUnit: '长沙市桥梁管理处',
          manager: '吴知非',
          // 详情数据 - 基本信息
          projectContent: '针对湘江大桥进行全面的月度养护工作，包括桥面清洁、伸缩缝检查、排水系统维护、护栏检修、照明设施检查等日常养护工作。确保桥梁结构安全，交通畅通。',
          startTime: '2025-09-01 08:00',
          endTime: '2025-09-30 18:00',
          managementUnit: '长沙市交通运输局',
          supervisionUnit: '长沙市工程监理有限公司',
          maintenanceUnit: '长沙市桥梁管理处',
          projectManager: '吴知非',
          contactPhone: '13800138001',
          workload: '32项',
          // 月度养护特有字段
          maintenanceCycle: '每月一次',
          inspectionFrequency: '每周2次',
          contractAmount: 850000,
          actualAmount: 0,
          submitter: '张三',
          submitTime: '2025-08-25 14:30:00',
          auditStatus: 'approved',
          auditTime: '2025-08-26 09:15:00',
          auditor: '李四',
          infrastructureType: 'bridge',
          bridgeIds: [1, 2, 3],
          bridgeNames: ['湘江大桥主桥', '湘江大桥引桥A', '湘江大桥引桥B'],
          attachments: [
            { name: '湘江大桥养护方案.pdf', size: '2.5MB' },
            { name: '技术规范文件.docx', size: '1.2MB' }
          ],
          projectTasks: [
            {
              id: 1,
              taskName: '桥面清洁',
              taskType: 'cleaning',
              plannedQuantity: 5000,
              unit: '平方米',
              unitPrice: 8.5,
              totalPrice: 42500,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 2,
              taskName: '伸缩缝检查维护',
              taskType: 'maintenance',
              plannedQuantity: 12,
              unit: '处',
              unitPrice: 1200,
              totalPrice: 14400,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 3,
              taskName: '排水系统清理',
              taskType: 'maintenance',
              plannedQuantity: 8,
              unit: '处',
              unitPrice: 800,
              totalPrice: 6400,
              completedQuantity: 0,
              status: 'pending'
            }
          ],
          diseaseConfig: [
            {
              id: 1,
              bridgeId: 1,
              bridgeName: '湘江大桥主桥',
              diseasePart: '桥面',
              diseaseType: '裂缝',
              diseaseLevel: 'minor',
              quantity: 3,
              unit: '处',
              treatmentMethod: '灌缝处理',
              unitPrice: 500,
              totalPrice: 1500
            },
            {
              id: 2,
              bridgeId: 1,
              bridgeName: '湘江大桥主桥',
              diseasePart: '护栏',
              diseaseType: '锈蚀',
              diseaseLevel: 'moderate',
              quantity: 50,
              unit: '米',
              treatmentMethod: '除锈刷漆',
              unitPrice: 120,
              totalPrice: 6000
            }
          ]
        },
        // 2. 预防养护项目
        {
          id: 2,
          projectName: '浏阳河大桥2025年秋季预防养护项目',
          projectType: 'preventive',
          projectStatus: 'waiting',
          completedTasks: 0,
          totalTasks: 28,
          startDate: '2025/10/01',
          endDate: '2025/11/30',
          isOverdue: false,
          maintenanceUnit: '长沙市桥梁管理处',
          manager: '郭照临',
          // 详情数据 - 基本信息
          projectContent: '浏阳河大桥预防性养护工作，包括防水涂层施工、钢结构防腐处理、桥面预防性修补等措施，延长桥梁使用寿命，减少后期维修成本。',
          startTime: '2025-10-01 08:00',
          endTime: '2025-11-30 18:00',
          managementUnit: '长沙市交通运输局',
          supervisionUnit: '湖南省工程监理咨询有限公司',
          maintenanceUnit: '长沙市桥梁管理处',
          projectManager: '郭照临',
          contactPhone: '13800138002',
          workload: '28项',
          // 预防养护特有字段
          preventiveType: '结构预防性维护',
          implementationSeason: '秋季',
          expectedEffect: '延长桥梁寿命5-8年',
          duration: '2个月',
          contractAmount: 1200000,
          actualAmount: 0,
          submitter: '王五',
          submitTime: '2025-08-25 16:20:00',
          auditStatus: 'approved',
          auditTime: '2025-08-26 10:30:00',
          auditor: '赵六',
          infrastructureType: 'bridge',
          bridgeIds: [4, 5],
          bridgeNames: ['浏阳河大桥主桥', '浏阳河大桥引桥'],
          attachments: [
            { name: '预防养护技术方案.pdf', size: '3.8MB' },
            { name: '材料技术规格书.pdf', size: '2.1MB' },
            { name: '施工组织设计.docx', size: '1.8MB' }
          ],
          projectTasks: [
            {
              id: 4,
              taskName: '防水涂层施工',
              taskType: 'preventive',
              plannedQuantity: 4200,
              unit: '平方米',
              unitPrice: 120.0,
              totalPrice: 504000,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 5,
              taskName: '钢结构防腐处理',
              taskType: 'preventive',
              plannedQuantity: 800,
              unit: '平方米',
              unitPrice: 180.0,
              totalPrice: 144000,
              completedQuantity: 0,
              status: 'pending'
            }
          ],
          diseaseConfig: []
        },
        // 3. 应急养护项目
        {
          id: 3,
          projectName: '橘子洲大桥紧急抢修项目',
          projectType: 'emergency',
          projectStatus: 'overdue',
          completedTasks: 0,
          totalTasks: 15,
          startDate: '2025/09/10',
          endDate: '2025/09/25',
          isOverdue: false,
          maintenanceUnit: '长沙市桥梁管理处',
          manager: '李慕桔',
          // 详情数据 - 基本信息
          projectContent: '橘子洲大桥因台风天气导致的护栏损坏、照明设施故障等紧急问题的抢修工作。需要立即处理以确保桥梁通行安全。',
          startTime: '2025-09-10 06:00',
          endTime: '2025-09-25 22:00',
          managementUnit: '长沙市交通运输局',
          supervisionUnit: '长沙市应急工程监理部',
          maintenanceUnit: '长沙市桥梁管理处',
          projectManager: '李慕桔',
          contactPhone: '13800138003',
          workload: '15项',
          // 应急养护特有字段
          urgencyLevel: '紧急',
          responseTime: '2小时内',
          accidentCause: '台风天气影响',
          impactScope: '影响交通通行',
          contractAmount: 580000,
          actualAmount: 0,
          submitter: '孙七',
          submitTime: '2025-09-09 20:45:00',
          auditStatus: 'approved',
          auditTime: '2025-09-10 05:15:00',
          auditor: '周八',
          infrastructureType: 'bridge',
          bridgeIds: [6],
          bridgeNames: ['橘子洲大桥'],
          attachments: [
            { name: '应急抢修方案.pdf', size: '1.5MB' },
            { name: '损坏情况照片.zip', size: '8.2MB' }
          ],
          projectTasks: [
            {
              id: 6,
              taskName: '护栏紧急修复',
              taskType: 'emergency',
              plannedQuantity: 120,
              unit: '米',
              unitPrice: 800,
              totalPrice: 96000,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 7,
              taskName: '照明设施抢修',
              taskType: 'emergency',
              plannedQuantity: 45,
              unit: '盏',
              unitPrice: 350,
              totalPrice: 15750,
              completedQuantity: 0,
              status: 'pending'
            }
          ],
          diseaseConfig: []
        },
        // 4. 保洁项目
        {
          id: 4,
          projectName: '银盆岭大桥2025年9月保洁项目',
          projectType: 'cleaning',
          projectStatus: 'completed',
          completedTasks: 0,
          totalTasks: 18,
          startDate: '2025/09/01',
          endDate: '2025/09/30',
          isOverdue: false,
          maintenanceUnit: '长沙市桥梁管理处',
          manager: '林文龙',
          // 详情数据 - 基本信息
          projectContent: '银盆岭大桥全面保洁服务，包括桥面清扫、护栏清洗、排水口疏通、垃圾清理等工作，确保桥梁外观整洁美观，为市民提供良好的通行环境。',
          startTime: '2025-09-01 06:00',
          endTime: '2025-09-30 20:00',
          managementUnit: '长沙市城市管理局',
          supervisionUnit: '长沙市环境卫生监督站',
          maintenanceUnit: '长沙市桥梁管理处',
          projectManager: '林文龙',
          contactPhone: '13800138004',
          workload: '18项',
          // 保洁项目特有字段
          cleaningScope: '桥面、护栏、排水设施',
          cleaningFrequency: '每天2次',
          cleaningStandard: '城市一级保洁标准',
          specialRequirements: '夜间照明清洁，无污染作业',
          contractAmount: 320000,
          actualAmount: 0,
          submitter: '吴九',
          submitTime: '2025-08-26 09:30:00',
          auditStatus: 'approved',
          auditTime: '2025-08-26 14:15:00',
          auditor: '郑十',
          infrastructureType: 'bridge',
          bridgeIds: [7],
          bridgeNames: ['银盆岭大桥'],
          attachments: [
            { name: '保洁作业标准.pdf', size: '0.8MB' },
            { name: '环保要求说明.docx', size: '0.5MB' }
          ],
          projectTasks: [
            {
              id: 8,
              taskName: '桥面保洁',
              taskType: 'cleaning',
              plannedQuantity: 3500,
              unit: '平方米',
              unitPrice: 6.0,
              totalPrice: 21000,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 9,
              taskName: '护栏清洗',
              taskType: 'cleaning',
              plannedQuantity: 800,
              unit: '米',
              unitPrice: 25,
              totalPrice: 20000,
              completedQuantity: 0,
              status: 'pending'
            },
            {
              id: 10,
              taskName: '排水口疏通',
              taskType: 'cleaning',
              plannedQuantity: 24,
              unit: '个',
              unitPrice: 150,
              totalPrice: 3600,
              completedQuantity: 0,
              status: 'pending'
            }
          ],
          diseaseConfig: []
        },
        // 其他简化项目数据，只保留基本信息用于列表显示
        ...Array.from({ length: 8 }, (_, i) => ({
          id: i + 5,
          projectName: `其他桥梁${i + 1}号养护项目`,
          projectType: i < 4 ? 'cleaning' : 'emergency',
          projectStatus: ['waiting', 'completed', 'overdue_completed', 'in_progress', 'waiting', 'completed', 'in_progress', 'overdue'][i],
          completedTasks: 0,
          totalTasks: 20 + Math.floor(Math.random() * 20),
          startDate: '2025/09/01',
          endDate: '2025/09/30',
          isOverdue: i >= 2,
          maintenanceUnit: '长沙市桥梁管理处',
          manager: ['何叔川', '郭云舟', '黄梓航', '赵景深', '李明', '王强', '张伟', '刘涛'][i],
          // 基本详情数据
          description: `${['何叔川', '郭云舟', '黄梓航', '赵景深', '李明', '王强', '张伟', '刘涛'][i]}负责的桥梁养护维修工作`,
          contractAmount: 300000 + Math.floor(Math.random() * 200000),
          actualAmount: 0,
          submitter: '系统管理员',
          submitTime: '2025-08-26 10:00:00',
          auditStatus: 'approved',
          auditTime: '2025-08-26 15:00:00',
          auditor: '审核员',
          infrastructureType: 'bridge',
          bridgeIds: [i + 8],
          bridgeNames: [`其他桥梁${i + 1}号`],
          projectTasks: [{
            id: i + 8,
            taskName: '常规维护',
            taskType: i < 4 ? 'cleaning' : 'emergency',
            plannedQuantity: 1000 + Math.floor(Math.random() * 2000),
            unit: '平方米',
            unitPrice: 10 + Math.floor(Math.random() * 10),
            totalPrice: 15000 + Math.floor(Math.random() * 20000),
            completedQuantity: 0,
            status: 'pending'
          }],
          diseaseConfig: []
        }))
      ]

      // 根据查询参数过滤数据
      let filteredData = [...mockData]
      
      if (this.queryParams.projectName) {
        filteredData = filteredData.filter(item => 
          item.projectName.includes(this.queryParams.projectName)
        )
      }
      
      if (this.queryParams.projectType) {
        filteredData = filteredData.filter(item => 
          item.projectType === this.queryParams.projectType
        )
      }
      
      if (this.queryParams.isOverdue !== '') {
        const isOverdue = this.queryParams.isOverdue === 'true'
        filteredData = filteredData.filter(item => 
          item.isOverdue === isOverdue
        )
      }
      
      if (this.queryParams.maintenanceUnit) {
        filteredData = filteredData.filter(item => 
          item.maintenanceUnit.includes(this.queryParams.maintenanceUnit)
        )
      }

      // 模拟分页
      const pageSize = this.queryParams.pageSize || 20
      const pageNum = this.queryParams.pageNum || 1
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedData = filteredData.slice(startIndex, endIndex)

      return {
        list: paginatedData,
        total: filteredData.length,
        projectOptions: [
          { label: 'XXXXXX项目', value: 'XXXXXX项目' }
        ]
      }
    },

    // 获取静态应急维修数据
    getMockEmergencyList() {
      const mockData = [
        {
          id: 1,
          bridgeName: '湘江大桥',
          reporter: '张三',
          reportTime: '2025-09-01 08:30',
          contactPhone: '13800138001',
          status: 'pending',
          diseaseCode: '001',
          diseasePart: '伸缩缝',
          diseaseType: '伸缩缝损坏',
          diseaseCount: 3,
          manager: '李四',
          maintenanceUnit: '长沙市桥梁管理处'
        },
        {
          id: 2,
          bridgeName: '浏阳河大桥',
          reporter: '王五',
          reportTime: '2025-09-02 10:15',
          contactPhone: '13800138002',
          status: 'in_review',
          diseaseCode: '002',
          diseasePart: '护栏',
          diseaseType: '护栏损坏',
          diseaseCount: 2,
          manager: '赵六',
          maintenanceUnit: '长沙市桥梁管理处'
        },
        {
          id: 3,
          bridgeName: '橘子洲大桥',
          reporter: '孙七',
          reportTime: '2025-09-03 14:20',
          contactPhone: '13800138003',
          status: 'rejected',
          diseaseCode: '003',
          diseasePart: '照明设施',
          diseaseType: '照明故障',
          diseaseCount: 5,
          manager: '周八',
          maintenanceUnit: '长沙市桥梁管理处'
        },
        {
          id: 4,
          bridgeName: '银盆岭大桥',
          reporter: '吴九',
          reportTime: '2025-09-04 16:45',
          contactPhone: '13800138004',
          status: 'completed',
          diseaseCode: '004',
          diseasePart: '桥面',
          diseaseType: '桥面破损',
          diseaseCount: 1,
          manager: '郑十',
          maintenanceUnit: '长沙市桥梁管理处'
        }
      ]

      // 根据查询参数过滤数据
      let filteredData = [...mockData]
      
      if (this.emergencyQueryParams.bridgeName) {
        filteredData = filteredData.filter(item => 
          item.bridgeName.includes(this.emergencyQueryParams.bridgeName)
        )
      }
      
      if (this.emergencyQueryParams.status) {
        filteredData = filteredData.filter(item => 
          item.status === this.emergencyQueryParams.status
        )
      }
      
      if (this.emergencyQueryParams.diseaseType) {
        filteredData = filteredData.filter(item => 
          item.diseaseType.includes(this.emergencyQueryParams.diseaseType)
        )
      }
      
      if (this.emergencyQueryParams.manager) {
        filteredData = filteredData.filter(item => 
          item.manager.includes(this.emergencyQueryParams.manager)
        )
      }
      
      if (this.emergencyQueryParams.maintenanceUnit) {
        filteredData = filteredData.filter(item => 
          item.maintenanceUnit.includes(this.emergencyQueryParams.maintenanceUnit)
        )
      }

      // 模拟分页
      const pageSize = this.emergencyQueryParams.pageSize || 20
      const pageNum = this.emergencyQueryParams.pageNum || 1
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedData = filteredData.slice(startIndex, endIndex)

      return {
        list: paginatedData,
        total: filteredData.length,
        bridgeOptions: [
          { label: '湘江大桥', value: '湘江大桥' },
          { label: '浏阳河大桥', value: '浏阳河大桥' },
          { label: '橘子洲大桥', value: '橘子洲大桥' },
          { label: '银盆岭大桥', value: '银盆岭大桥' }
        ],
        managerOptions: [
          { label: '李四', value: '李四' },
          { label: '赵六', value: '赵六' },
          { label: '周八', value: '周八' },
          { label: '郑十', value: '郑十' }
        ],
        unitOptions: [
          { label: '长沙市桥梁管理处', value: '长沙市桥梁管理处' }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/inspection-theme.scss';
@import '@/assets/styles/maintenance-theme.scss';

// 页面特有样式 - 参考养护项目Tab样式
// 一级Tab导航样式 - 使用inspection-theme的样式
.primary-tab-navigation {
  // 样式已通过主题文件提供
  margin-bottom: 4px;
  flex-shrink: 0;
  min-height: 40px;

  .primary-tabs {
    background: transparent;
    border: none;
    padding: 0;
    box-shadow: none;
    margin: 21px 0 0 0;
    min-height: 64px;
    display: inline-flex;
    align-items: center;
    gap: 0;
    
    .tab-item {
      display: flex;
      height: 48px;
      padding: 12px;
      justify-content: flex-start;
      align-items: center;
      border-radius: 10px;
      background: #1B2A56;
      border: none;
      box-shadow:
        inset 1px 0 0 0 rgba(255, 255, 255, 0.15),
        inset 0 1px 0 0 rgba(255, 255, 255, 0.15),
        inset -1px 0 0 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 0 rgba(255, 255, 255, 0.15);
      
      // 角落遮罩效果
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: -1px;
        right: -1px;
        width: 12px;
        height: 12px;
        background: #1B2A56;
        border-top-right-radius: 10px;
        z-index: 1;
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: -1px;
        width: 12px;
        height: 12px;
        background: #1B2A56;
        border-bottom-left-radius: 10px;
        z-index: 1;
      }
      
      margin: 0 10px 0 0;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
      min-width: 92px;
      box-sizing: border-box;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
      gap: 8px;
      
      i {
        font-size: 16px;
        flex-shrink: 0;
      }
      
      &:hover:not(.is-active) {
        color: rgba(255, 255, 255, 0.9);
        background: #1a2d5a;
        
        &::before {
          background: #1a2d5a;
        }
        &::after {
          background: #1a2d5a;
        }
      }
      
      &.is-active {
        background: linear-gradient(135deg, #334067 0%, #2a3558 100%);
        color: #fff;
        border-radius: 10px;
        border: none;
        box-shadow:
          inset 1px 0 0 0 rgba(255, 255, 255, 0.25),
          inset 0 1px 0 0 rgba(255, 255, 255, 0.25),
          inset -1px 0 0 0 rgba(255, 255, 255, 0.25),
          inset 0 -1px 0 0 rgba(255, 255, 255, 0.25),
          0 2px 8px rgba(0, 123, 255, 0.2);
        
        &::before {
          background: #2a3558;
        }
        &::after {
          background: #2a3558;
        }
      }
    }
  }
}

// 二级Tab导航样式 - 使用更轻量的样式
.secondary-tab-navigation {
  margin-bottom: 24px;
  
  .secondary-tabs {
    display: inline-flex;
    align-items: center;
    gap: 0;
    background: transparent;
    
    .tab-item {
      display: flex;
      height: 40px;
      padding: 10px 16px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      background: rgba(27, 42, 86, 0.6);
      border: 1px solid rgba(255, 255, 255, 0.1);
      margin: 0 8px 0 0;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;
      gap: 6px;
      min-width: 80px;
      
      i {
        font-size: 14px;
        flex-shrink: 0;
      }
      
      &:hover:not(.is-active) {
        background: rgba(59, 130, 246, 0.08);
        color: #94a3b8;
        border-color: rgba(255, 255, 255, 0.2);
      }
      
      &.is-active {
        background: rgba(59, 130, 246, 0.15);
        color: #60a5fa;
        border-color: #60a5fa;
        box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
      }
    }
  }
}

// 筛选区域样式优化 - 使用与养护项目一致的样式
.filter-section {
  // 样式由 inspection-theme.scss 提供
}

// 进度样式
.progress-complete {
  color: #22c55e;
  font-weight: bold;
}

.progress-high {
  color: #3b82f6;
  font-weight: bold;
}

.progress-medium {
  color: #eab308;
}

.progress-low {
  color: #ef4444;
}

// 应急维修表格样式
.disease-count {
  color: #3b82f6;
  font-weight: bold;
}
</style>
