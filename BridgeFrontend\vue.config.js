'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}
// const CopyWebpackPlugin = require('copy-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')

const name = process.env.VUE_APP_TITLE || '长沙市智慧桥隧综合管理平台' // 网页标题

const port = process.env.port || process.env.npm_config_port || 80 // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "./" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // target: `http://***************:10899`,
        target: `http://**************:10212`,
        changeOrigin: true,
        pathRewrite: {
          // ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      '/terrain': {
        target: `http://127.0.0.1:5050`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
    },
    disableHostCheck: true
  },
  css: {
    loaderOptions: {
      sass: {
        // sass-loader v8 中，这个选项名是 “prependData”
        // sass-loader v10+，这个选项名是 “additionalData”
        // prependData: `$env: ${process.env.NODE_ENV}; @import "~@/assets/styles/index.scss";`,
        // additionalData: `@import "~@/assets/styles/index.scss";`,
        additionalData: (content, loaderContext) => {
          const { resourcePath } = loaderContext;
          if (resourcePath.endsWith("variables.scss") || resourcePath.endsWith("index.scss")) return content;
          return `@import "@/assets/styles/index.scss";
          ${content}`;
        },
        sassOptions: { outputStyle: "expanded" }
      }
    }
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@c': path.resolve(__dirname, './src/components'),
        '@v': path.resolve(__dirname, './src/views')
      }
    },
    // externals: {
    //   // @cesium
    //   cesium: 'Cesium',
    //   // @xbsj-renderer
    //   'xbsj-renderer/dist-node/xr-base-utils': 'xbsj["xr-base-utils"]',
    //   'xbsj-renderer/dist-node/xr-math': 'xbsj["xr-math"]',
    //   'xbsj-renderer/dist-node/xr-utils': 'xbsj["xr-utils"]',
    //   'xbsj-renderer/dist-node/xr-cesium': 'xbsj["xr-cesium"]',
    //   // @xbsj-xe2
    //   'xbsj-xe2/dist-node/xe2': 'XE2["xe2"]',
    //   'xbsj-xe2/dist-node/xe2-base': 'XE2["xe2-base"]',
    //   'xbsj-xe2/dist-node/xe2-base-utils': 'XE2["xe2-base-utils"]',
    //   'xbsj-xe2/dist-node/xe2-utils': 'XE2["xe2-utils"]',
    //   'xbsj-xe2/dist-node/xe2-cesium': 'XE2["xe2-cesium"]',
    //   'xbsj-xe2/dist-node/xe2-mapbox': 'XE2["xe2-mapbox"]',
    //   'xbsj-xe2/dist-node/xe2-ue': 'XE2["xe2-ue"]',
    //   'xbsj-xe2/dist-node/utility-xe2-plugin': 'XE2["utility-xe2-plugin"]',
    //   'xbsj-xe2/dist-node/xe2-all-in-one': 'XE2["xe2-all-in-one"]',
    //   'xbsj-xe2/dist-node/xe2-base-objects': 'XE2["xe2-base-objects"]',
    //   'xbsj-xe2/dist-node/xe2-cesium-objects': 'XE2["xe2-cesium-objects"]',
    //   'xbsj-xe2/dist-node/xe2-ue-objects': 'XE2["xe2-ue-objects"]',
    //   'xbsj-xe2/dist-node/xe2-openlayers': 'XE2["xe2-openlayers"]',
    //   'xbsj-xe2/dist-node/xe2-openlayers-objects': 'XE2["xe2-openlayers-objects"]',
    //   // plugins
    //   'smplotting-xe2-plugin/dist-node/smplotting-xe2-plugin': 'XE2["smplotting-xe2-plugin"]',
    //   'smplotting-xe2-plugin/dist-node/smplotting-xe2-plugin-main': 'XE2["smplotting-xe2-plugin-main"]',
    //   'esobjs-xe2-plugin/dist-node/esobjs-xe2-plugin': 'XE2["esobjs-xe2-plugin"]',
    //   'esobjs-xe2-plugin/dist-node/esobjs-xe2-plugin-main': 'XE2["esobjs-xe2-plugin-main"]',
    // },
    plugins: [
      // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
      new CompressionPlugin({
        cache: false,                                  // 不启用文件缓存
        test: /\.(js|css|html|jpe?g|png|gif|svg)?$/i,  // 压缩文件格式
        filename: '[path][base].gz[query]',            // 压缩后的文件名
        algorithm: 'gzip',                             // 使用gzip压缩
        minRatio: 0.8,                                 // 压缩比例，小于 80% 的文件不会被压缩
        // threshold: 10240,                              // 对超过10k的数据压缩
        deleteOriginalAssets: false                    // 压缩后删除原文件
      }),
      // new CopyWebpackPlugin([
      //   {
      //     from: './node_modules/xbsj-xe2/dist-web',
      //     to: 'js/xbsj-xe2/dist-web',
      //     toType: 'dir'
      //   },
      //   {
      //     from: './node_modules/xbsj-xe2-assets/dist-web',
      //     to: 'js/xbsj-xe2-assets/dist-web',
      //     toType: 'dir'
      //   },
      //   {
      //     from: './node_modules/smplotting-xe2-plugin/dist-web',
      //     to: 'js/smplotting-xe2-plugin/dist-web',
      //     toType: 'dir'
      //   },
      //   {
      //     from: './node_modules/esobjs-xe2-plugin/dist-web',
      //     to: 'js/esobjs-xe2-plugin/dist-web',
      //     toType: 'dir'
      //   },
      //   {
      //     from: './node_modules/esobjs-xe2-plugin-assets/dist-web',
      //     to: 'js/esobjs-xe2-plugin-assets/dist-web',
      //     toType: 'dir'
      //   },
      //   {
      //     from: './node_modules/vue-xe2-plugin/dist-web',
      //     to: 'js/vue-xe2-plugin/dist-web',
      //     toType: 'dir'
      //   }
      // ]
      // )
    ],
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.when(process.env.NODE_ENV !== 'development', config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
            // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()

          config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial' // only package third parties that are initially dependent
              },
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                priority: 20 // the weight needs to be larger than libs and app or it will be packaged into libs or app
              },
              commons: {
                name: 'chunk-commons',
                test: resolve('src/components'), // can customize your rules
                minChunks: 3, //  minimum common number
                priority: 5,
                reuseExistingChunk: true
              }
            }
          })
          config.optimization.runtimeChunk('single')
    })
  }
}
