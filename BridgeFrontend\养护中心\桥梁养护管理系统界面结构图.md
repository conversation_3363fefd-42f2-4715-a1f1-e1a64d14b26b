# 桥梁养护管理系统界面结构图

## 概述
本文档用ASCII字符详细绘制了桥梁养护管理系统32个界面的结构布局。

---

## 图片1 (image1.jpg) - 桥梁养护项目主列表页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [🏗️ 桥梁养护] [🚇 隧道养护]                                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ [项目名称▼] [项目类型▼] [状态│全部▼] 时间 2025/09/01 至 2025/09/01▼ [查询][重置] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 新增养护项目]                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│项目名称│项目类型│状态│开始日期│结束日期│养护单位│负责人│操作              │
│001│XXXXXX项目│月度养护│[草稿]│2025/09/01│2025/09/30│长沙市桥梁管理处│吴知非│修改 删除│
│002│XXXXXX项目│月度养护│[审批中]│2025/09/01│2025/09/30│长沙市桥梁管理处│郭照临│查看 审批│
│003│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│李慕桔│查看    │
│004│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│林文龙│查看    │
│005│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│高枕书│查看    │
│006│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│徐桧桐│查看    │
│007│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│何叔川│查看    │
│008│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│郭云舟│查看    │
│009│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│黄梓航│查看    │
│010│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│赵景深│查看    │
│011│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│黄梓航│查看    │
│012│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│赵铁柱│查看    │
│013│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│吴海燕│查看    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                  < [1] 2 3 4 5 >            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体背景**：深蓝色渐变背景 (从深蓝到浅蓝的径向渐变)
- **主要色彩体系**：
  - 主色调：深蓝色系 (#1e3a8a, #3b82f6)
  - 背景色：深蓝色渐变背景
  - 表格背景：半透明深蓝色背景
  - 边框色：蓝灰色边框线
- **状态色彩规范**：
  - 草稿状态：蓝色标签背景
  - 审批中：黄色标签背景  
  - 审批通过：绿色标签背景
  - 已拒绝：红色标签背景
- **字体规范**：
  - 主标题：16px，粗体，#1f2937
  - 正文：14px，常规，#374151
  - 辅助文字：12px，#6b7280
- **圆角规范**：卡片 8px，按钮 6px，输入框 4px
- **间距系统**：基于 8px 网格系统 (8px, 16px, 24px, 32px)

### 组件清单
1. **顶部导航标签组件** 
   - 背景：白色卡片，底部边框 #e5e7eb
   - 激活标签：蓝色底线 #3b82f6，字体 #1e3a8a
   - 非激活标签：灰色字体 #6b7280
2. **筛选表单区域组件**
   - 背景：白色 (#ffffff)
   - 输入框：边框 #d1d5db，聚焦时 #3b82f6
   - 按钮：主按钮蓝色渐变 (#3b82f6 → #2563eb)
3. **数据表格组件**
   - 表头：浅灰背景 (#f9fafb)，字体 #374151
   - 表格行：交替白色 #ffffff 和 #f9fafb
   - 悬停效果：#f3f4f6 背景
4. **状态标签组件** - 圆角标签，对应状态色彩
5. **分页组件** - 蓝色主题，当前页高亮

### 交互功能
- 筛选条件实时查询，输入框聚焦蓝色边框动画
- 表格排序图标，悬停行高亮效果
- 按钮悬停渐变效果 (hover: brightness(110%))
- 响应式布局，最小宽度 1200px

---

## 图片2 (image2.jpg) - 新增项目基本信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ *项目名称:                          │ *项目类型:                            │
│ [请输入                           ] │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *项目开始时间:                      │ *项目结束时间:                        │
│ [选择时间                         ] │ [选择时间                  ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *管理单位:                          │ 监理单位:                             │
│ [请选择                    ▼]      │ [请输入                    ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *养护单位:                          │ *项目负责人:                          │
│ [请选择                    ▼]      │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *联系方式:                          │                                       │
│ [输入框                           ] │                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目内容:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 附件:                                                                       │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                            ☁️                                            │ │
│ │                    将文件拖拽或点击上传                                   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                      [保存] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **弹窗设计**：
  - 背景遮罩：半透明黑色 rgba(0,0,0,0.5)
  - 弹窗主体：深蓝色背景，圆角 12px
  - 阴影效果：深色阴影
  - 最大宽度：800px，居中定位
- **步骤导航样式**：
  - 激活步骤：蓝色圆形 #3b82f6，白色数字
  - 已完成步骤：绿色圆形 #10b981，白色对勾
  - 未激活步骤：灰色圆形 #d1d5db，灰色数字 #6b7280
  - 连接线：灰色 #d1d5db，激活时蓝色 #3b82f6
- **表单区域样式**：
  - 背景：深蓝色背景
  - 分栏布局：左右各 45%，中间 10% 间距
  - 标签样式：左对齐，白色字体，粗体
  - 必填标识：红色星号 #ef4444，位置在标签后
- **输入控件样式**：
  - 输入框：边框 #d1d5db，圆角 6px，高度 40px
  - 聚焦状态：边框 #3b82f6，外发光 0 0 0 3px rgba(59,130,246,0.1)
  - 下拉选择：带箭头图标，悬停时边框变色
  - 日期选择：日历图标，蓝色主题
  - 文本域：最小高度 80px，可拖拽调整
- **按钮样式规范**：
  - 主按钮：蓝色渐变 (#3b82f6 → #2563eb)，白色文字，圆角 6px
  - 次按钮：白色背景，灰色边框 #d1d5db，灰色文字 #6b7280
  - 悬停效果：主按钮亮度增加 110%，次按钮背景 #f9fafb

### 组件清单
1. **模态弹窗容器组件** 
   - 遮罩层 + 弹窗主体
   - 关闭按钮：右上角 X，悬停红色 #ef4444
2. **进度步骤导航组件** 
   - 圆形步骤指示器 + 连接线
   - 步骤标题，当前步骤高亮
3. **响应式表单网格组件**：
   - 自适应两列布局
   - 标签和控件垂直对齐
4. **表单控件库**：
   - 标准输入框、选择器、文本域
   - 统一的聚焦和验证样式
5. **文件上传区域组件** 
   - 虚线边框 #d1d5db，拖拽时蓝色边框
   - 上传图标和提示文字居中
6. **底部操作栏组件** - 右对齐按钮组

### 交互功能
- 表单实时验证，错误提示红色边框 #ef4444
- 文件拖拽上传，进度条显示
- 步骤导航点击跳转（已填写步骤）
- 输入框聚焦动画效果 (transition: all 0.2s ease)

---

## 图片3 (image3.jpg) - 养护项目配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题界面**：
  - 背景：深蓝色背景 #1e3a8a（或类似深蓝色）
  - 内容区域：padding 32px，最大高度限制带滚动
  - 文字颜色：白色/浅色 #ffffff
- **动态列表区域**：
  - 列表容器：深色背景，与主背景融合
  - 空状态提示：居中显示，浅色文字
- **添加按钮样式**：
  - 蓝色实心按钮：背景 #3b82f6
  - 按钮文字：白色 #ffffff
  - 加号图标：白色，字体大小 18px
  - 悬停状态：更亮的蓝色
- **列表项样式**：
  - 背景：深色透明背景
  - 输入框：深色背景，白色文字
  - 圆角：6px，margin-bottom 8px
  - 内间距：16px 20px
- **输入框样式**：
  - 项目名称输入：深色背景，白色文字，全宽度
  - placeholder：浅灰色
  - 边框：深色边框或无边框设计
- **取消按钮样式**：
  - 红色按钮：背景 #ef4444
  - 按钮文字：白色 #ffffff
  - 尺寸：适中尺寸，右对齐
- **步骤指示器**：
  - 当前步骤：蓝色高亮下划线，白色文字
  - 标签导航：白色文字，蓝色下划线激活状态

### 组件清单
1. **步骤进度组件** 
   - 进度条 + 步骤标题
   - 当前步骤数字高亮显示
2. **动态表单列表组件**
   - 可增删的表单项容器
   - 空状态和有数据状态切换
3. **添加项按钮组件** 
   - 虚线边框设计，图标 + 文字
   - 悬停动画效果
4. **可编辑列表项组件** 
   - 内联编辑输入框
   - 右侧删除按钮
5. **底部操作栏** - 三按钮布局（保存、上一步、下一步）

### 交互功能
- 动态添加项：点击添加自动聚焦新输入框
- 实时编辑：输入框失焦自动保存
- 删除确认：悬停显示删除意图，点击确认删除
- 键盘操作：Enter 添加新项，Escape 取消编辑
- 数据验证：空项目名称提示，重复名称警告

---

## 图片4 (image4.jpg) - 病害养护信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [关联病害]                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│病害编号│病害部位│病害类型│病害数量│病害描述│操作                │
│ 1 │XXXXXX大桥│989│伸缩缝│伸缩缝缺失│7│XXXXXXX│删除                        │
│ 2 │XXXXXX大桥│988│伸缩缝│伸缩缝缺失│47│XXXXXXX│删除                       │
│ 3 │XXXXXX大桥│987│照明设施│照明设施缺失│42│XXXXXXX│删除                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题界面**：
  - 背景：深蓝色背景 #1e3a8a（或类似深蓝色）
  - 文字颜色：白色 #ffffff
  - 区域划分：关联操作区 + 数据展示区
- **关联按钮区域**：
  - 按钮样式：蓝色实心按钮 #3b82f6
  - 按钮文字：白色 #ffffff
  - 图标：链接图标 + "关联病害" 文字
  - 位置：左上角，margin-bottom 16px
  - 悬停效果：更亮的蓝色
- **数据表格样式**：
  - 表格容器：深色背景，与主背景融合
  - 表头设计：
    - 背景：深色背景
    - 字体：14px，白色文字 #ffffff
    - 高度：48px，垂直居中对齐
    - 边框：深色边框或无边框
  - 表格行样式：
    - 所有行：深色背景，白色文字
    - 高度：56px，内间距 16px
    - 悬停效果：轻微高亮
  - 列宽分配：序号 | 桥梁名称 | 病害编号 | 病害部位 | 病害类型 | 病害数量 | 病害描述 | 操作
- **表格数据显示**：
  - 桥梁名称：白色文字，如"XXXXXX大桥"
  - 病害编号：白色文字，如"989"、"988"、"987"
  - 病害部位：白色文字，如"伸缩缝"、"照明设施"
  - 病害类型：白色文字，如"伸缩缝缺失"、"照明设施缺失"
  - 病害数量：白色文字，如"7"、"47"、"42"
- **删除按钮样式**：
  - 文字按钮：红色"删除"文字 #ef4444
  - 背景：透明或深色背景
  - 悬停状态：更亮的红色
- **空状态设计**：
  - 提示文字：浅色文字，"暂无关联的病害信息"
  - 居中布局，垂直间距 16px

### 组件清单
1. **关联操作按钮组件** 
   - 主要操作按钮，带图标和文字
   - 悬停动画和反馈效果
2. **响应式数据表格组件**
   - 自适应列宽，固定表头
   - 斑马纹行样式，悬停高亮
3. **状态标签组件库** 
   - 严重程度彩色标签
   - 统一的标签样式系统
4. **行内操作组件** 
   - 删除确认按钮
   - 悬停状态视觉反馈
5. **空状态占位组件** - 无数据时的友好提示

### 交互功能
- 关联按钮点击：打开病害选择弹窗
- 表格行悬停：整行高亮显示
- 删除操作：悬停显示意图，点击二次确认
- 数据加载：loading 状态和骨架屏
- 响应式适配：小屏幕时表格横向滚动

---

## 图片5 (image5.jpg) - 养护桥梁配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [关联桥梁]                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│桥梁编号│所在道路│管理单位│养护内容│养护人员│操作                │
│ 1 │XXXXXX大桥│CS-B-001│枫林一路│桥隧中心│排水系统养护│黄昭言│删除            │
│ 2 │XXXXXX大桥│CS-B-002│枫林一路│桥隧中心│上部结构养护│刘雨桐│删除            │
│ 3 │XXXXXX大桥│CS-B-003│枫林一路│桥隧中心│排水系统养护│罗砚秋│删除            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题界面**：
  - 背景：深蓝色背景 #1e3a8a（或类似深蓝色）
  - 文字颜色：白色 #ffffff
  - 布局：关联区域 + 综合信息展示区
- **关联桥梁按钮**：
  - 样式：蓝色实心按钮 #3b82f6
  - 按钮文字：白色 #ffffff
  - 图标：桥梁图标 + "关联桥梁" 文字
  - 位置：左上角，与表格间距 20px
  - 悬停效果：更亮的蓝色
- **综合信息表格**：
  - 表格设计：深色背景，与主背景融合
  - 表头样式：
    - 背景：深色背景
    - 字体：14px，白色文字 #ffffff
    - 高度：52px，垂直居中对齐
    - 列标题：序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位 | 养护内容 | 养护人员 | 操作
  - 数据行样式：
    - 所有行：深色背景，白色文字
    - 高度：60px，内间距 16px 12px
    - 悬停效果：轻微高亮
- **桥梁信息展示**：
  - 桥梁名称：白色文字，如"XXXXXX大桥"
  - 桥梁编号：白色文字，如"CS-B-001"、"CS-B-002"、"CS-B-003"
  - 所在道路：白色文字，如"枫林一路"
  - 管理单位：白色文字，如"桥隧中心"
  - 养护内容：白色文字，如"排水系统养护"、"上部结构养护"
  - 养护人员：白色文字，如"黄昭言"、"刘雨桐"、"罗砚秋"
- **操作按钮样式**：
  - 删除按钮：红色"删除"文字 #ef4444
  - 背景：透明或深色背景
  - 悬停效果：更亮的红色
- **底部按钮区域**：
  - 保存按钮：深色边框按钮，白色文字
  - 上一步按钮：蓝色实心按钮，白色文字
  - 提交按钮：蓝色实心按钮，白色文字
- **完成状态指示**：
  - 步骤 4/4：最终步骤状态
  - 养护桥梁标签：蓝色下划线激活状态

### 组件清单
1. **桥梁关联按钮组件** 
   - 主操作按钮，带桥梁图标
   - 完成状态的视觉反馈
2. **综合信息数据表格**
   - 多列复杂信息展示
   - 不同类型数据的格式化显示
3. **人员头像组件** 
   - 头像 + 姓名组合显示
   - 默认头像生成逻辑
4. **养护类型标签组件** 
   - 分类彩色标签系统
   - 语义化颜色设计
5. **最终提交操作栏** 
   - 强调提交按钮的重要性
   - 完成状态的确认流程

### 交互功能
- 关联桥梁：打开桥梁选择器，支持多选
- 信息预览：悬停显示详细信息卡片
- 完整性验证：提交前检查必填信息
- 批量操作：支持批量删除关联项
- 数据导出：支持配置信息导出预览

---

## 图片6 (image6.jpg) - 关联桥梁选择弹窗

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                       ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☐│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位                  │
│☐│XXXXXX大桥│黄昭言│1│XXX大桥│CS-B-001│枫林一路│桥隧中心                   │
│☐│XXXXXX大桥│刘雨桐│2│刘雨桐│CS-B-002│枫林一路│桥隧中心                    │
│☐│XXXXXX大桥│罗砚秋│3│罗砚秋│CS-B-003│枫林一路│桥隧中心                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题弹窗设计**：
  - 弹窗尺寸：宽度 900px，高度 600px
  - 背景遮罩：深色半透明遮罩
  - 弹窗主体：深蓝色背景 #1e3a8a（与主界面一致）
  - 动画效果：scale(0.95) → scale(1) 进入动画
- **弹窗头部区域**：
  - 标题栏：高度 64px，padding 20px 24px
  - 标题文字：18px，白色文字 #ffffff，"关联桥梁"
  - 关闭按钮：右上角白色 X 按钮，悬停效果
  - 底部分割线：深色边框线
- **搜索过滤区域**：
  - 搜索框容器：深色背景
  - 搜索输入框：
    - 宽度：100%，高度 40px
    - 深色背景，白色边框
    - placeholder：白色文字，"输入桥梁名称或编号"
    - 聚焦状态：蓝色边框高亮
- **数据表格区域**：
  - 表格容器：深色背景，与主背景融合
  - 表头固定：深色背景，白色文字
  - 复选框列：
    - 全选复选框：白色边框复选框
    - 行复选框：白色边框，未选中状态
  - 列标题：养护项目 | 养护人员 | 序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位
- **桥梁数据展示**：
  - 所有文字：白色 #ffffff
  - 第1行：XXXXXX大桥 | 黄昭言 | 1 | XXX大桥 | CS-B-001 | 枫林一路 | 桥隧中心
  - 第2行：XXXXXX大桥 | 刘雨桐 | 2 | 刘雨桐 | CS-B-002 | 枫林一路 | 桥隧中心
  - 第3行：XXXXXX大桥 | 罗砚秋 | 3 | 罗砚秋 | CS-B-003 | 枫林一路 | 桥隧中心
  - 行高：适中，便于阅读
  - 悬停效果：轻微高亮
- **选中状态样式**：
  - 复选框：白色边框，蓝色选中状态
  - 选中行：深蓝色高亮背景
  - 选中数量提示：底部白色文字显示
- **底部操作栏**：
  - 深色背景，与主背景一致
  - 按钮布局：居中排列
  - 保存按钮：深色边框按钮，白色文字
  - 上一步按钮：蓝色实心按钮，白色文字
  - 提交按钮：蓝色实心按钮，白色文字

### 组件清单
1. **模态选择器弹窗组件** 
   - 带遮罩的弹窗容器
   - 进入退出动画效果
2. **实时搜索过滤组件**
   - 搜索图标 + 输入框组合
   - 实时过滤结果更新
3. **多选数据表格组件**
   - 复选框 + 表格数据组合
   - 全选/反选功能
4. **桥梁信息卡片组件** 
   - 结构化信息展示
   - 状态和类型标识
5. **选择确认操作栏** 
   - 选择统计 + 操作按钮
   - 批量操作支持

### 交互功能
- 实时搜索：输入即时过滤，高亮匹配文字
- 多选操作：单选、全选、范围选择支持
- 选择预览：悬停显示桥梁详细信息
- 键盘导航：方向键选择，Space 切换选中
- 数据懒加载：滚动加载更多数据
- 选择记忆：关闭重开保持选择状态

---

## 图片7 (image7.jpg) - 保洁项目基本信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ *项目名称:                          │ *项目类型:                            │
│ [请输入                           ] │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *项目开始时间:                      │ *项目结束时间:                        │
│ [选择时间                         ] │ [选择时间                  ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *管理单位:                          │ 监理单位:                             │
│ [请选择                    ▼]      │ [请输入                    ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *养护单位:                          │ *项目负责人:                          │
│ [请选择                    ▼]      │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *联系方式:                          │                                       │
│ [输入框                           ] │                                       │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 工作量:                             │                                       │
│ [请输入                           ] │                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                      [保存] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题项目创建界面**：
  - 背景：深蓝色背景 #1e3a8a（与主界面一致）
  - 弹窗：深色弹窗背景
  - 布局优化：针对保洁项目简化的三步骤流程
- **深色主题步骤导航**：
  - 标签页设计：水平排列的三个标签
  - 激活状态："基本信息" - 蓝色下划线，白色文字
  - 未激活状态："保洁项目"、"保洁桥梁" - 白色文字
  - 整体背景：深色，与弹窗背景融合
- **深色主题表单设计**：
  - 表单区域：两列布局，深色背景
  - 所有标签文字：白色 #ffffff
  - 必填标识：红色星号 "*"
  - 输入框样式：
    - 默认输入框：深色背景，白色边框，白色文字
    - 下拉选择框：深色背景，白色文字，下拉箭头
    - 占位符文字：白色半透明
- **表单字段布局**：
  - 左列字段：项目名称*、项目开始时间*、管理单位*、养护单位*、联系方式*
  - 右列字段：项目类型*、项目结束时间*、监理单位、项目负责人*、工作量
  - 特殊字段：联系方式输入框显示为灰色背景
- **项目内容区域**：
  - 大文本输入框：深色背景，白色边框
  - 占位符：白色文字 "请输入"
  - 全宽布局，跨越两列
- **附件上传区域**：
  - 虚线边框：白色虚线边框
  - 上传图标：白色云朵图标
  - 提示文字：白色 "将文件拖拽或点击上传"
  - 背景：深色，与表单背景一致
- **底部操作栏**：
  - 深色背景，与弹窗背景一致
  - 保存按钮：深色边框按钮，白色文字
  - 下一步按钮：蓝色实心按钮，白色文字
  - 按钮居右对齐

### 组件清单
1. **保洁专用步骤导航** 
   - 三步骤进度指示器
   - 保洁流程专用文案
2. **工作量输入组件**
   - 数字输入框 + 单位后缀
   - 实时计算和验证
3. **保洁类型选择组件**
   - 图标化下拉选择器
   - 保洁类型专用选项
4. **时间段选择组件**
   - 多时段选择器
   - 可视化时间轴显示
5. **保洁单位选择器**
   - 带评级的单位选择
   - 联系信息展示

### 交互功能
- 工作量实时计算：输入面积自动计算预估工时
- 保洁类型联动：选择类型影响工作量计算规则
- 时间段冲突检测：避免时间段重叠
- 单位信息预览：悬停显示单位详细信息
- 成本预估：根据面积和类型预估保洁成本

---

## 图片8 (image8.jpg) - 保洁项目配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                    ] [7] 天/1次               [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                    ] [30] 天/1次              [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深色主题保洁项目配置界面**：
  - 背景：深蓝色背景 #1e3a8a（与主界面一致）
  - 弹窗：深色弹窗背景
  - 特色功能：保洁频次的精确配置
- **深色主题步骤导航**：
  - 标签页设计：水平排列的三个标签
  - 激活状态："保洁项目" - 蓝色下划线，白色文字
  - 未激活状态："基本信息"、"保洁桥梁" - 白色文字
  - 整体背景：深色，与弹窗背景融合
- **深色主题添加按钮**：
  - 蓝色实心按钮，白色"+"图标和"添加项目"文字
  - 位置：页面顶部，左对齐
  - 样式：圆角按钮，蓝色背景 #3b82f6
- **深色主题项目列表区域**：
  - 列表容器：深色背景，与弹窗背景一致
  - 项目行：深色背景，白色边框或分隔线
  - 布局：垂直排列的项目行
- **深色主题项目行设计**：
  - 项目名称：白色文字 #ffffff，"排水养护"
  - 频次设置：
    - 数字输入框：深色背景，白色文字（"7"、"30"）
    - 单位显示：白色文字"天/1次"
  - 操作按钮：红色"取消"按钮
  - 布局：项目名称 | 频次输入 | 天/1次 | 取消按钮
- **深色主题频次输入控件**：
  - 数字输入框：深色背景，白色边框，白色数字
  - 单位后缀："天/1次" 白色文字
  - 组合布局：数字框 + 单位文字水平排列
- **深色主题操作按钮**：
  - 取消按钮：红色背景或红色文字
  - 尺寸：适中，与项目行高度匹配
  - 位置：每行右侧
- **深色主题底部操作栏**：
  - 深色背景，与弹窗背景一致
  - 保存按钮：深色边框按钮，白色文字
  - 上一步按钮：蓝色实心按钮，白色文字
  - 下一步按钮：蓝色实心按钮，白色文字
  - 按钮居右对齐

### 组件清单
1. **保洁频次配置组件** 
   - 数字输入 + 单位后缀组合
   - 实时频次计算显示
2. **智能数字输入框**
   - 数字验证和格式化
   - 步进器控制（+/-按钮）
3. **频次单位指示器**
   - 固定单位显示组件
   - 视觉连接设计
4. **保洁项目行组件** 
   - 项目信息 + 频次标签 + 操作
   - 响应式布局适配
5. **频次计算器组件**
   - 自动计算月/年频次
   - 成本估算功能

### 交互功能
- 智能频次输入：支持1-365天范围，实时验证
- 频次预览：输入时显示"每月约X次"预估
- 批量频次设置：选中多项统一设置频次
- 频次模板：常用频次快速选择（每日、每周、每月）
- 成本计算：根据频次和工作量计算总成本
- 排序功能：按频次高低排序项目列表

---

## 图片9 (image9.jpg) - 保洁桥梁配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [🌉 关联桥梁]                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│桥梁编号│所在道路│管理单位│保洁内容│保洁人员│操作                │
│ 1 │XXXXXX大桥│CS-B-001│枫林一路│桥隧中心│排水系统养护│黄昭言│删除            │
│ 2 │XXXXXX大桥│CS-B-002│枫林一路│桥隧中心│上部结构养护│刘雨桐│删除            │
│ 3 │XXXXXX大桥│CS-B-003│枫林一路│桥隧中心│排水系统养护│罗砚秋│删除            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **保洁桥梁配置界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 弹窗背景：深色弹窗背景
  - 文字颜色：白色文字 #ffffff
  - 布局：关联操作区 + 桥梁信息展示区
- **步骤导航标签页**：
  - 标签设计：白色文字，激活状态显示蓝色下划线
  - "保洁桥梁"：当前激活状态，蓝色下划线标识
  - 已完成状态："基本信息"、"保洁项目"显示白色文字
- **关联桥梁按钮样式**：
  - 按钮设计：蓝色实心按钮
  - 图标：白色桥梁图标 + 白色"关联桥梁"文字
  - 尺寸：height 44px，padding 12px 24px
  - 悬停效果：按钮阴影效果
- **保洁桥梁信息表格**：
  - 表格设计：深色主题表格
  - 列配置：序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位 | 保洁内容 | 保洁人员 | 操作
  - 表头样式：
    - 背景：深色背景
    - 字体：白色文字 #ffffff
    - 高度：标准表头高度
- **数据行样式**：
  - 背景：深色行背景
  - 文字：白色数据文字 #ffffff
  - 删除操作：红色"删除"按钮
- **底部操作按钮**：
  - 保存：深色边框按钮，白色文字
  - 上一步：蓝色实心按钮，白色文字
  - 提交：蓝色实心按钮，白色文字
- **保洁内容展示**：
  - 内容文字：白色文字显示保洁内容
  - 主要内容："排水系统养护"、"上部结构养护"
  - 字体：标准14px白色文字
- **保洁人员信息**：
  - 人员姓名：白色文字显示
  - 姓名："黄昭言"、"刘雨桐"、"罗砚秋"
  - 字体：标准14px白色文字

### 数据内容详情
- **桥梁数据**：
  - 桥梁1：XXXXXX大桥 | CS-B-001 | 枫林一路 | 桥隧中心 | 排水系统养护 | 黄昭言
  - 桥梁2：XXXXXX大桥 | CS-B-002 | 枫林一路 | 桥隧中心 | 上部结构养护 | 刘雨桐  
  - 桥梁3：XXXXXX大桥 | CS-B-003 | 枫林一路 | 桥隧中心 | 排水系统养护 | 罗砚秋

### 组件清单
1. **保洁桥梁关联按钮** 
   - 蓝色实心按钮设计
   - 白色桥梁图标 + 白色文字
2. **保洁信息展示表格**
   - 深色主题表格设计
   - 白色文字表头和数据
3. **保洁数据行组件** 
   - 深色背景数据行
   - 白色文字内容显示
4. **删除操作按钮** 
   - 红色"删除"文字按钮
   - 每行操作区域
5. **底部操作按钮组** 
   - 保存：深色边框按钮
   - 上一步/提交：蓝色实心按钮

### 交互功能
- 桥梁关联：打开保洁桥梁选择器
- 保洁内容配置：内联编辑保洁项目
- 人员快速联系：点击显示联系方式
- 计划调整：时间冲突检测和建议
- 批量操作：支持批量分配人员

---

## 图片10 (image10.jpg) - 保洁桥梁关联弹窗

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                       ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位                  │
│☑│XXXXXX大桥│黄昭言│1│XXX大桥│CS-B-001│枫林一路│桥隧中心                   │
│☑│XXXXXX大桥│刘雨桐│2│刘雨桐│CS-B-002│枫林一路│桥隧中心                    │
│☑│XXXXXX大桥│罗砚秋│3│罗砚秋│CS-B-003│枫林一路│桥隧中心                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **保洁桥梁选择弹窗**：
  - 弹窗背景：深蓝色背景 #1e3a8a（深色主题）
  - 弹窗尺寸：宽度 900px，高度 600px
  - 文字颜色：白色文字 #ffffff
  - 标题栏：深蓝色背景，白色"关联桥梁"文字
- **弹窗头部深色主题**：
  - 标题文字：18px 粗体，白色 #ffffff
  - 关闭按钮：白色 ✕ 符号
  - 整体背景：深蓝色主题一致
- **搜索区域样式**：
  - 搜索框：深色背景搜索框
  - placeholder：白色提示文字"输入桥梁名称或编号"
  - 边框：深色边框样式
- **桥梁数据表格**：
  - 表头：深色背景表头
  - 表头文字：白色文字显示列标题
  - 复选框：已选中状态 ☑️，蓝色选中样式
  - 数据行：深色背景，白色数据文字
  - 列配置：复选框 | 养护项目 | 养护人员 | 序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位
- **选中状态显示**：
  - 全部复选框：已选中状态 ☑️
  - 选中行背景：深色选中背景
  - 数据内容：白色文字显示桥梁信息
- **桥梁数据内容**：
  - 第1行：XXXXXX大桥 | 黄昭言 | 1 | XXX大桥 | CS-B-001 | 枫林一路 | 桥隧中心
  - 第2行：XXXXXX大桥 | 刘雨桐 | 2 | 刘雨桐 | CS-B-002 | 枫林一路 | 桥隧中心
  - 第3行：XXXXXX大桥 | 罗砚秋 | 3 | 罗砚秋 | CS-B-003 | 枫林一路 | 桥隧中心
- **底部操作按钮**：
  - 保存：深色边框按钮，白色文字
  - 上一步：蓝色实心按钮，白色文字
  - 提交：蓝色实心按钮，白色文字

### 数据展示详情
- **已选中桥梁**：全部3座桥梁都已选中
- **桥梁信息完整性**：包含养护项目、养护人员、序号、桥梁名称、编号、道路、管理单位
- **选中状态一致**：所有复选框都显示已选中状态 ☑️

### 组件清单
1. **深色主题选择弹窗** 
   - 深蓝色背景弹窗设计
   - 白色文字和图标
2. **桥梁搜索组件**
   - 深色主题搜索框
   - 白色提示文字
3. **桥梁选择表格组件**
   - 深色背景表格
   - 蓝色选中状态复选框
4. **桥梁信息展示组件** 
   - 白色文字数据展示
   - 标准表格行布局
5. **底部操作按钮组** 
   - 深色边框保存按钮
   - 蓝色实心操作按钮

### 交互功能
- 桥梁搜索：支持按名称或编号搜索桥梁
- 批量选择：复选框支持单选和全选操作
- 数据确认：底部按钮支持保存、返回和提交操作

---

## 图片11 (image11.jpg) - 新增项目基本信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ *项目名称:                          │ *项目类型:                            │
│ [请输入                           ] │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *项目开始时间:                      │ 项目结束时间:                         │
│ [选择时间                  ▼]      │ [选择时间                  ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 管理单位:                           │ 监理单位:                             │
│ [请选择                    ▼]      │ [请输入                    ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 养护单位:                           │ *项目负责人:                          │
│ [请选择                    ▼]      │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 联系方式:                           │ 工作量:                               │
│ [输入框                           ] │ [请输入                    ]          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目内容:                                                                   │
│ [请输入                                                                   ] │
│ [                                                                         ] │
│ [                                                                         ] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 附件:                                                                       │
│ ┌─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┐ │
│ │                           ☁                                             │ │
│ │                     将文件拖拽或点击上传                                 │ │
│ └ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                      [保存] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **新增项目弹窗界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 弹窗尺寸：宽度约800px，高度约700px
  - 关闭按钮：白色 ⊗ 符号
- **标签页导航**：
  - 三个标签页：基本信息、应急项目、养护桥梁
  - 当前激活：基本信息标签页有下划线高亮
  - 导航文字：白色显示
- **表单字段布局**：
  - 双列布局：左右两列均匀分布
  - 必填标识：少数关键字段有红色 * 标记
  - 输入框样式：深色背景边框，白色文字
  - 下拉框：带有下拉箭头 ▼ 指示
- **表单字段详情**：
  - 左列：项目名称*、项目开始时间*、管理单位、养护单位、联系方式
  - 右列：项目类型*、项目结束时间、监理单位、项目负责人*、工作量
  - 全宽字段：项目内容（大型文本输入框）
- **附件上传区域**：
  - 虚线边框区域：蓝色虚线框
  - 上传图标：蓝色云朵图标 ☁
  - 提示文字：白色"将文件拖拽或点击上传"
  - 支持拖拽和点击上传功能
- **底部操作按钮**：
  - 保存：深色边框按钮，白色文字
  - 下一步：蓝色实心按钮，白色文字

### 字段配置详情
- **必填字段（红色*标记）**：
  - 项目名称：文本输入，项目标识
  - 项目类型：下拉选择，项目分类
  - 项目开始时间：时间选择器
  - 项目负责人：下拉选择，责任人
- **可选字段**：
  - 项目结束时间、管理单位、监理单位、养护单位
  - 联系方式、工作量、项目内容

### 组件清单
1. **深色主题标签页导航** 
   - 三步骤导航：基本信息、应急项目、养护桥梁
   - 白色文字，下划线激活状态
2. **双列表单布局组件**
   - 左右两列字段分布
   - 深色主题输入框和下拉框
3. **大型文本输入组件**
   - 项目内容多行文本框
   - 深色背景，白色文字
4. **文件上传组件**
   - 虚线边框拖拽区域
   - 蓝色云图标和提示文字
5. **底部操作按钮组** 
   - 保存和下一步按钮
   - 深色边框和蓝色实心样式

### 交互功能
- 标签页切换：三个步骤页面间切换
- 表单验证：必填字段实时验证
- 文件上传：支持拖拽和点击上传
- 数据保存：支持保存草稿和继续下一步

---

## 图片12 (image12.jpg) - 新增项目应急项目配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **新增项目应急项目配置界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 弹窗尺寸：与基本信息页一致
  - 关闭按钮：白色 ⊗ 符号
- **标签页导航**：
  - 三个标签页：基本信息、应急项目、养护桥梁
  - 当前激活：应急项目标签页有下划线高亮
  - 导航文字：白色显示
- **添加项目按钮**：
  - 按钮样式：蓝色实心按钮
  - 图标：白色加号 + "添加项目" 文字
  - 尺寸：圆角按钮，左对齐布局
  - 背景色：蓝色主题色
- **项目列表区域**：
  - 列表容器：深色主题背景
  - 项目间距：适中的垂直间距
  - 布局：垂直排列的项目条目
- **项目条目设计**：
  - 项目行：深色背景，圆角边框
  - 圆角：统一圆角设计
  - 内间距：适中的内边距
  - 布局：项目名称(80%) | 取消按钮(20%)
- **项目名称显示**：
  - 字体：白色文字，标准字体大小
  - 内容：显示"排水养护"等项目名称
  - 对齐：左对齐显示
- **取消按钮样式**：
  - 样式：红色"取消"按钮
  - 位置：每个项目条目右侧
  - 颜色：红色背景，白色文字
  - 尺寸：小型按钮设计
- **空白区域**：
  - 中间大片空白区域用于显示更多项目
  - 背景：深色主题一致
- **底部操作按钮**：
  - 三个按钮：保存、上一步、下一步
  - 保存：深色边框按钮，白色文字
  - 上一步、下一步：蓝色实心按钮，白色文字

### 功能特点
- **当前状态**：已添加两个"排水养护"项目
- **项目管理**：每个项目可独立取消
- **添加功能**：通过蓝色"+ 添加项目"按钮新增
- **导航功能**：支持上一步返回基本信息，下一步进入养护桥梁

### 组件清单
1. **深色主题标签页导航** 
   - 三步骤导航：基本信息、应急项目、养护桥梁
   - 白色文字，下划线激活状态
2. **蓝色添加项目按钮**
   - 蓝色实心按钮，白色加号图标
   - 左对齐布局，圆角设计
3. **项目条目列表组件**
   - 深色背景项目条目
   - 项目名称 + 红色取消按钮布局
4. **红色取消按钮组件** 
   - 小型红色按钮，白色文字
   - 每个项目条目右侧
5. **三按钮操作组** 
   - 保存、上一步、下一步按钮
   - 深色边框和蓝色实心样式混合

### 交互功能
- 项目添加：点击"+ 添加项目"按钮新增项目
- 项目删除：点击红色"取消"按钮移除项目
- 页面导航：支持保存、上一步、下一步操作
- 数据持久：项目配置可保存为草稿

---

## 图片13 (image13.jpg) - 新增项目养护桥梁配置页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [🔗 关联桥梁]                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称    │桥梁编号  │所在道路│管理单位│保洁内容    │保洁人员│操作     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 1 │XXXXXX大桥  │CS-B-001 │枫林一路│桥梁中心│排水系统养护│黄明言  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 2 │XXXXXX大桥  │CS-B-002 │枫林一路│桥梁中心│上部结构养护│刘雨桐  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 3 │XXXXXX大桥  │CS-B-003 │枫林一路│桥梁中心│排水系统养护│罗现冰  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [完成]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **新增项目养护桥梁配置界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 弹窗尺寸：与前两步一致的大尺寸弹窗
  - 关闭按钮：白色 ⊗ 符号
- **标签页导航**：
  - 三个标签页：基本信息、应急项目、养护桥梁
  - 当前激活：养护桥梁标签页有下划线高亮
  - 导航文字：白色显示
- **关联桥梁按钮**：
  - 按钮样式：蓝色实心按钮，带链接图标 🔗
  - 图标：白色链接图标 + "关联桥梁" 文字
  - 位置：表格上方左对齐
  - 背景色：蓝色主题色
- **桥梁信息表格**：
  - 表格样式：深色主题表格设计
  - 表头：深色背景，白色文字
  - 列配置：序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位 | 保洁内容 | 保洁人员 | 操作
  - 边框：深色边框线分隔各行各列
- **表格数据行**：
  - 背景：深色背景，与整体主题一致
  - 文字：白色文字显示
  - 行高：适中的行高便于阅读
  - 内容：显示具体的桥梁养护信息
- **操作列设计**：
  - 删除按钮：每行最后一列的红色"删除"按钮
  - 按钮样式：小型红色按钮
  - 位置：右对齐显示

### 当前数据状态
- **已关联桥梁**：当前显示3座桥梁
- **桥梁信息**：
  - 第1座：XXXXXX大桥（CS-B-001），排水系统养护，负责人黄明言
  - 第2座：XXXXXX大桥（CS-B-002），上部结构养护，负责人刘雨桐
  - 第3座：XXXXXX大桥（CS-B-003），排水系统养护，负责人罗现冰
- **统一管理**：所有桥梁均由桥梁中心管理，位于枫林一路

### 组件清单
1. **深色主题标签页导航** 
   - 三步骤导航：基本信息、应急项目、养护桥梁
   - 白色文字，下划线激活状态
2. **蓝色关联桥梁按钮**
   - 蓝色实心按钮，带链接图标 🔗
   - 触发桥梁选择弹窗
3. **深色主题桥梁表格**
   - 8列信息展示：序号到操作
   - 深色背景，白色文字
4. **红色删除按钮组件** 
   - 每行操作列的小型红色按钮
   - 支持单个桥梁移除
5. **三按钮完成操作组** 
   - 保存、上一步、完成按钮
   - 完成按钮表示流程结束

### 交互功能
- 桥梁关联：点击"🔗 关联桥梁"按钮选择桥梁
- 桥梁移除：点击红色"删除"按钮移除桥梁关联
- 流程导航：支持保存草稿、返回上一步、完成项目创建
- 数据管理：桥梁信息实时显示，支持批量管理

---

## 图片14 (image14.jpg) - 关联桥梁选择弹窗

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                      ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│养护项目  │养护人员│序号│桥梁名称  │桥梁编号 │所在道路│管理单位              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│黄昭吉  │ 1 │XXX大桥   │CS-B-001│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│刘雨桐  │ 2 │刘雨桐    │CS-B-002│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│罗硯秋  │ 3 │罗硯秋    │CS-B-003│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **关联桥梁弹窗界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 弹窗尺寸：大尺寸弹窗，与主流程弹窗一致
  - 关闭按钮：白色 ⊗ 符号
- **搜索输入框**：
  - 样式：深色主题输入框设计
  - 占位符：白色文字"输入桥梁名称或编号"
  - 位置：弹窗顶部，全宽度
  - 背景：深色输入框背景
- **桥梁选择表格**：
  - 表格样式：深色主题表格设计
  - 表头：深色背景，白色文字
  - 列配置：复选框 | 养护项目 | 养护人员 | 序号 | 桥梁名称 | 桥梁编号 | 所在道路 | 管理单位
  - 边框：深色边框线分隔各行各列
- **多选复选框功能**：
  - 选择列：最左侧复选框列，支持多选
  - 选中状态：☑ 打勾显示，当前全部选中
  - 选中样式：选中行有视觉反馈
  - 批量操作：支持批量选择多个桥梁
- **桥梁信息展示**：
  - 数据行：深色背景，白色文字显示
  - 行高：适中的行高便于阅读
  - 内容：显示具体的桥梁基础信息
  - 管理单位：统一显示为"桥隧中心"
- **操作按钮组**：
  - 保存：左侧深色边框按钮
  - 上一步：中间蓝色按钮  
  - 提交：右侧蓝色按钮（确认选择）

### 当前选择状态
- **已选桥梁**：当前选中全部3座桥梁
- **桥梁详情**：
  - 第1座：XXXXXX大桥（CS-B-001），养护人员黄昭吉
  - 第2座：XXXXXX大桥（CS-B-002），养护人员刘雨桐  
  - 第3座：XXXXXX大桥（CS-B-003），养护人员罗硯秋
- **统一属性**：所有桥梁均位于枫林一路，由桥隧中心管理

### 组件清单
1. **深色主题搜索框组件** 
   - 深色主题输入框设计
   - 桥梁名称或编号搜索功能
2. **多选桥梁表格组件**
   - 复选框多选功能
   - 8列桥梁信息展示
3. **深色主题表格组件** 
   - 深色背景，白色文字
   - 桥梁详细信息展示
4. **选择确认按钮组** 
   - 保存、上一步、提交操作
   - 提交按钮确认桥梁选择

### 交互功能
- 搜索过滤：输入桥梁名称或编号进行搜索
- 多选操作：复选框支持批量选择桥梁
- 选择确认：点击提交按钮确认选中的桥梁
- 流程控制：支持保存草稿、返回上一步
- 实时反馈：选中状态实时显示

---

## 图片15 (image15.jpg) - 新增项目基本信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [关联病害] [实施信息] [竣工信息]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目名称:                      │ 项目类型:                                │
│ [请输入                     ]  │ [请选择                           ▼]   │
├─────────────────────────────────┼───────────────────────────────────────┤
│ 项目开始时间:                   │ 项目结束时间:                          │
│ [选择时间                   ▼] │ [选择时间                         ▼]   │
├─────────────────────────────────┼───────────────────────────────────────┤
│ 管理单位:                       │ 监理单位:                              │
│ [请选择                     ▼] │ [请输入                           ]    │
├─────────────────────────────────┼───────────────────────────────────────┤
│ 养护单位:                       │ 项目负责人:                            │
│ [请选择                     ▼] │ [请选择                           ▼]   │
├─────────────────────────────────┼───────────────────────────────────────┤
│ 联系方式:                       │ 工作量:                                │
│ [                           ]  │ [请输入                           ]    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目内容:                                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                   │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 附件:                                                                      │
│ ┌ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┐ │
│ │                           📁                                           │ │
│ │                    将文件拖拽或点击上传                                   │ │
│ └ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                          [保存] [下一步]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **新增项目弹窗界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 弹窗尺寸：大尺寸弹窗，支持多步骤表单
  - 关闭按钮：白色 ⊗ 符号
- **四步骤标签页导航**：
  - 标签样式：水平标签页设计
  - 激活状态：基本信息（蓝色下划线激活）
  - 未激活：关联病害、实施信息、竣工信息（白色文字）
  - 流程顺序：基本信息→关联病害→实施信息→竣工信息
- **双列表单布局**：
  - 布局方式：左右两列对称布局
  - 字段间距：适中的字段间距便于填写
  - 标签样式：左对齐标签，冒号结尾
  - 输入框：深色主题输入框，白色占位符文字
- **输入控件类型**：
  - 文本输入框：项目名称、监理单位、联系方式、工作量
  - 下拉选择框：项目类型、管理单位、养护单位、项目负责人
  - 日期选择器：项目开始时间、项目结束时间
  - 多行文本框：项目内容（大文本区域）
- **文件上传区域**：
  - 样式：虚线边框的上传区域
  - 图标：蓝色文件夹图标 📁
  - 提示文字：蓝色"将文件拖拽或点击上传"
  - 上传方式：支持拖拽和点击上传
- **操作按钮组**：
  - 保存：左侧深色边框按钮（保存草稿）
  - 下一步：右侧蓝色按钮（进入关联病害页）

### 表单字段结构
**左列字段：**
- **项目名称**：必填文本输入，项目标识
- **项目开始时间**：日期选择器，项目起始日期
- **管理单位**：下拉选择，项目管理方
- **养护单位**：下拉选择，具体执行方
- **联系方式**：文本输入，联系电话或邮箱
- **项目内容**：多行文本，详细工作描述

**右列字段：**
- **项目类型**：下拉选择，项目分类
- **项目结束时间**：日期选择器，项目截止日期
- **监理单位**：文本输入，监理方信息
- **项目负责人**：下拉选择，责任人员
- **工作量**：文本输入，工作量描述

### 组件清单
1. **深色主题标签页导航组件** 
   - 四步骤流程导航
   - 激活状态可视化
2. **双列表单布局组件**
   - 左右对称字段布局
   - 多种输入控件类型
3. **文件上传组件** 
   - 拖拽上传功能
   - 虚线边框设计
4. **表单操作按钮组** 
   - 保存草稿功能
   - 下一步流程控制

### 交互功能
- 表单填写：支持多种类型字段输入
- 文件上传：拖拽或点击上传附件
- 草稿保存：保存当前填写进度
- 流程导航：点击下一步进入关联病害页
- 表单验证：必填字段验证和格式检查

---

## 图片16 (image16.jpg) - 关联病害页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [关联病害] [实施信息] [竣工信息]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [🔗 关联病害]                                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│病害编号│病害部位│病害类型│病害数量│病害描述│操作                │
│ 1 │XXXXXX大桥│989│伸缩缝│伸缩缝缺失│7│XXXXXXX│删除                        │
│ 2 │XXXXXX大桥│988│伸缩缝│伸缩缝缺失│47│XXXXXXX│删除                       │
│ 3 │XXXXXX大桥│987│照明设施│照明设施缺失│42│XXXXXXX│删除                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **病害关联界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 标签激活：关联病害（蓝色下划线激活状态）
- **关联病害按钮**：
  - 按钮样式：蓝色实心按钮
  - 图标：链接图标 🔗 + "关联病害" 文字
  - 尺寸：height 44px，padding 12px 20px
  - 位置：表格上方左侧
- **病害信息表格**：
  - 表格样式：深色主题表格设计
  - 表头：深色背景，白色文字
  - 列配置：序号 | 桥梁名称 | 病害编号 | 病害部位 | 病害类型 | 病害数量 | 病害描述 | 操作
  - 边框：深色边框线分隔各行各列
- **病害数据行**：
  - 数据显示：深色背景，白色文字
  - 删除按钮：红色"删除"按钮，支持单个病害移除
  - 病害类型：不同病害类型可能有不同的显示样式
- **操作按钮组**：
  - 保存：左侧深色边框按钮
  - 上一步：中间蓝色按钮（返回基本信息页）
  - 下一步：右侧蓝色按钮（进入实施信息页）

### 组件清单
1. **关联病害按钮组件** 
   - 蓝色主题按钮
   - 病害选择弹窗触发
2. **病害信息表格组件**
   - 病害详细信息展示
   - 删除操作功能
3. **三按钮导航组** 
   - 保存、上一步、下一步操作
   - 流程控制功能

### 交互功能
- 病害关联：点击"🔗 关联病害"按钮选择病害
- 病害移除：点击红色"删除"按钮移除病害关联
- 流程导航：支持保存草稿、返回上一步、进入下一步
- 数据管理：病害信息实时显示，支持批量管理

---

## 图片17 (image17.jpg) - 实施信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [关联病害] [实施信息] [竣工信息]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加工作内容]                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **实施信息界面**：
  - 背景：深蓝色背景 #1e3a8a（深色主题）
  - 文字颜色：白色文字 #ffffff
  - 工作内容管理：动态添加和删除工作项
- **添加工作内容按钮**：
  - 按钮样式：蓝色实心按钮 #3b82f6
  - 图标：加号图标 + "添加工作内容" 文字
  - 尺寸：height 44px，padding 12px 20px
  - 位置：工作内容列表上方左侧
- **工作内容条目**：
  - 条目样式：
    - 背景：深色背景，与整体主题一致
    - 边框：深色边框，输入框样式
    - 文字：白色文字，显示工作内容名称
    - 布局：左侧内容，右侧取消按钮
  - 取消按钮：
    - 样式：红色文字按钮 "取消"
    - 位置：每个工作内容条目右侧
    - 功能：删除当前工作内容项
- **工作内容示例**：
  - 当前显示：两个"排水养护"工作项
  - 内容管理：支持添加、删除工作项
  - 流程导航：保存草稿、上一步、下一步

### 组件清单
1. **添加工作内容按钮** 
   - 蓝色按钮，触发添加工作内容功能
   - 位置：工作内容列表上方
2. **工作内容条目**
   - 显示工作内容名称（如"排水养护"）
   - 右侧红色取消按钮，支持删除
3. **流程控制按钮组** 
   - 保存：保存当前工作内容
   - 上一步：返回关联病害页
   - 下一步：进入竣工信息页

### 交互功能
- 添加工作：点击"+ 添加工作内容"按钮
- 删除工作：点击工作项右侧"取消"按钮
- 流程导航：支持保存草稿、返回上一步、进入下一步
- 数据持久化：工作内容信息实时保存

---

## 图片18 (image18.jpg) - 竣工信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增信息                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [关联病害] [实施信息] [竣工信息]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ *验收结论:                        │ *竣工单位自检情况:                       │
│ [请输入                        ] │ [请输入                              ] │
├─────────────────────────────────────────────────────────────────────────────┤
│ *现场验收情况:                    │ *管理方代表意见:                         │
│ [请输入                        ] │ [请输入                              ] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处置照片:                                                                   │
│ [处置前] [处置中] [处置后]                                                  │
│ ┌───────┐                                                                  │
│ │   +   │                                                                  │
│ │       │                                                                  │
│ └───────┘                                                                  │
│                                                                             │
│ 附件:                                                                       │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                            ☁️                                            │ │
│ │                    将文件拖拽或点击上传                                   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **竣工验收界面**：
  - 背景：深蓝色主题背景 #1e3a8a
  - 文字颜色：白色文字
  - 蓝色主题：激活元素使用蓝色高亮
  - 验收流程：四个关键验收环节
- **验收结论选择**：
  - 选项样式：
    - 合格：绿色标签 #10b981
    - 基本合格：黄色标签 #f59e0b
    - 不合格：红色标签 #ef4444
  - 结论影响：影响后续审批流程
- **表单布局**：
  - 左右两列布局设计
  - 输入框样式：深色边框，圆角设计
  - 占位符文字："请输入"
  - 必填验证：红色星号标识
- **字段排列**：
  - 左列：验收结论、现场验收情况
  - 右列：竣工单位自检情况、管理方代表意见
- **处置照片上传**：
  - 照片分类标签：
    - 处置前：灰色标签（未选中）
    - 处置中：灰色标签（未选中）
    - 处置后：蓝色标签（已选中）
  - 上传按钮：深色方形按钮，白色"+"号
  - 附件上传区域：
    - 虚线边框的大矩形区域
    - 云朵图标居中显示
    - 提示文字："将文件拖拽或点击上传"
- **照片预览网格**：
  - 网格布局：3列自适应
  - 照片卡片：
    - 尺寸：120px × 120px
    - 圆角：8px
    - 阴影：0 2px 4px rgba(0,0,0,0.1)
  - 删除按钮：右上角红色 × 按钮
- **底部按钮区域**：
  - 保存按钮：深色按钮
  - 上一步按钮：蓝色按钮
  - 提交按钮：蓝色按钮
  - 按钮排列：右对齐，水平排列

### 组件清单
1. **左右列表单组件**
   - 双列表单布局设计
   - 四个必填输入框组件
2. **标签页导航组件**
   - 四个标签页切换
   - 当前激活标签高亮显示
3. **分类照片上传组件**
   - 三个照片分类标签
   - 上传按钮和附件拖拽区域
4. **操作按钮组件**
   - 三个底部操作按钮
   - 按钮状态和样式区分

### 交互功能
- 标签页切换：点击标签切换页面
- 表单输入：四个必填字段的文本输入
- 照片分类：点击标签切换照片类型
- 照片上传：点击"+"按钮或拖拽上传
- 附件上传：拖拽文件到指定区域
- 表单操作：保存、上一步、提交功能

---

## 图片19 (image19.jpg) - 审批页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 审批                                                                  ⊗     │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁] [审批信息]                       │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  [I图标]审批记录                                                                    │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │序号│    审批环节    │处理人│审批状态│审批意见│处理人部门│  接收时间  │  办理时间  │ │
│ ├────┼──────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤ │
│ │ 1 │    开始申请    │黄昭言│  通过  │ 无异议 │ 养护公司 │2025-09-18│2025-09-18│ │
│ │   │              │     │        │       │         │   10:43  │   10:43  │ │
│ ├────┼──────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤ │
│ │ 2 │养护项目审批(一级)│刘雨桐│        │        │ XXX部门  │2025-09-18│2025-09-18│ │
│ │   │              │     │        │       │         │   10:43  │   10:43  │ │
│ ├────┼──────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤ │
│ │ 3 │养护项目审批(二级)│罗砚秋│        │        │          │2025-09-18│2025-09-18│ │
│ │   │              │     │        │       │         │   10:43  │   10:43  │ │
│ └────┴──────────────┴──────┴────────┴────────┴──────────┴──────────┴──────────┘ │
│                                                                             │
│  [I图标]审批处理                                                                    │
│                                                                             │
│ *处理意见:                                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                  │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                          [退回] [通过]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **审批流程界面**：
  - 背景：深蓝色主题背景 #1e3a8a
  - 文字颜色：白色文字
  - 蓝色主题：审批信息标签为当前激活状态
  - 分步导航：五个审批阶段标签页
- **审批记录表格**：
  - 表格样式：
    - 表头：深灰色背景，白色文字
    - 数据行：深蓝色背景，白色文字
    - 边框：白色细线分隔
    - 内边距：统一的单元格内边距
  - 状态显示：
    - 通过：显示为文字形式
    - 空白状态：未审批项目显示为空
  - 时间格式：YYYY-MM-DD HH:mm
- **处理意见输入区**：
  - 样式：多行文本域
    - 边框：1px solid #d1d5db
    - 聚焦：2px solid #8b5cf6
    - 圆角：6px，padding 12px
    - 最小高度：100px
  - 必填标识：红色星号 * #ef4444
  - 字数限制：最多500字，实时统计
- **审批操作按钮**：
  - 退回按钮：
    - 样式：红色渐变 (#ef4444 → #dc2626)
    - 图标：返回箭头 + "退回" 文字
    - 尺寸：height 40px，padding 12px 20px
  - 通过按钮：
    - 样式：绿色渐变 (#10b981 → #059669)
    - 图标：对勾图标 + "通过" 文字
    - 尺寸：height 40px，padding 12px 20px
- **审批处理区域**：
  - 分区标题："审批记录"和"审批处理"
  - 处理意见输入框：
    - 占位符："请输入"
    - 背景：深色边框的文本域
    - 尺寸：多行文本域，高度适中

### 组件清单
1. **标签页导航组件**
   - 五个审批阶段标签页
   - 当前激活标签高亮显示
2. **审批记录表格组件**
   - 多级审批环节显示
   - 表格数据列显示
3. **审批处理组件**
   - 处理意见输入框
   - 退回和通过按钮
4. **分区显示组件**
   - 审批记录区域
   - 审批处理区域

### 交互功能
- 标签页切换：点击标签切换不同阶段
- 表格数据查看：查看审批记录详情
- 意见输入：在文本域中输入处理意见
- 审批操作：点击退回或通过按钮
- 表单验证：必填项检查
- 关闭窗口：点击关闭按钮

---

## 图片20 (image20.jpg) - 审批信息页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 审批                                                                   ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁] [审批信息]                       │
│                                          ────────                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ [I图标]审批记录                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│┌───┬────────────────┬──────┬────────┬────────┬──────────┬──────────┬──────────┐│
││序号│    审批环节    │处理人│审批状态│审批意见│处理人部门│  接收时间│  办理时间││
│├───┼────────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤│
││ 1 │   开始申请     │黄昭言│  通过  │ 无异议 │ 养护公司 │2025-09-18│2025-09-18││
││   │               │      │        │        │          │  10:43   │  10:43   ││
│├───┼────────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤│
││ 2 │养护项目审批(一级)│刘雨桐│        │        │XXX部门   │2025-09-18│2025-09-18││
││   │               │      │        │        │          │  10:43   │  10:43   ││
│├───┼────────────────┼──────┼────────┼────────┼──────────┼──────────┼──────────┤│
││ 3 │养护项目审批(二级)│罗砚秋│        │        │          │2025-09-18│2025-09-18││
││   │               │      │        │        │          │  10:43   │  10:43   ││
│└───┴────────────────┴──────┴────────┴────────┴──────────┴──────────┴──────────┘│
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                        [保存]    [提交]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **审批信息弹窗界面**：
  - 背景：深蓝色主题 #1e3a8a，全屏遮罩
  - 弹窗主体：深蓝色背景，白色文字
  - 关闭按钮：右上角白色圆形 ⊗ 按钮
  - 只读模式：纯信息展示，不可编辑
- **标签页导航**：
  - 标签样式：白色文字，无边框设计
  - 激活状态：蓝色下划线 #3b82f6，高亮显示
  - 标签间距：均匀分布，左对齐
  - 导航区域：深蓝色背景
- **审批记录标题**：
  - 字体：白色粗体，左对齐
  - 背景：深蓝色，与整体主题一致
  - 间距：标准 16px 上下边距
- **审批信息表格**：
  - 整体样式：灰蓝色表格背景 #475569
  - 表头样式：
    - 背景：灰蓝色 #475569，白色文字
    - 字体：14px 加粗，居中对齐
    - 边框：深色分割线
  - 数据行样式：
    - 背景：深蓝色 #334155，白色文字
    - 行高：双行设计，支持长内容
    - 边框：深色网格线 #334155
  - 状态显示：
    - 通过：绿色文字 #10b981
    - 待审批：空白显示
    - 时间格式：YYYY-MM-DD HH:mm
- **底部操作区**：
  - 按钮样式：
    - 保存按钮：灰色边框 #6b7280，白色文字
    - 提交按钮：蓝色实心 #3b82f6，白色文字
    - 圆角：6px，标准按钮高度
  - 布局：右对齐，按钮间距 12px
  - 位置：底部固定位置

### 组件清单
1. **审批弹窗容器组件**
   - 深蓝色全屏遮罩背景
   - 白色关闭按钮，右上角定位
2. **标签页导航组件**
   - 5个标签页：基本信息、养护项目、病害养护、养护桥梁、审批信息
   - 激活状态蓝色下划线指示
3. **审批记录表格组件**
   - 灰蓝色表格样式，8列布局
   - 双行数据显示，支持长文本
   - 状态颜色区分（通过/待审批）
4. **底部操作栏组件**
   - 保存按钮（次要）+ 提交按钮（主要）
   - 右对齐布局

### 交互功能
- 标签页切换：点击切换不同信息页面
- 表格滚动：支持水平和垂直滚动查看
- 关闭弹窗：点击关闭按钮或遮罩区域
- 按钮操作：保存草稿或提交审批

---

## 图片21 (image21.jpg) - 桥梁养护维修主页

```
┌────────────────────────────────────────────────────────────────────┐
│ [🏗️ 桥梁养护维修] [🚇 隧道养护维修]                                │
│ [养护/保洁项目] [应急维修]                                         │
│                                                                    │
│ [项目名称 ▼] [项目类型 ▼] [是否超期 ▼] [养护单位 ▼] [查询] [重置] │
│                                                                    │
│ 序号 │ 项目名称      │ 项目类型 │ 任务完成量 │ 开始日期   │ 结束日期   │ 是否超期 │ 养护单位         │ 负责人 │ 操作     │
│ 001  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 吴知非 │ 查看 延期申请 │
│ 002  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 郭照临 │ 查看 延期申请 │
│ 003  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 李慕桔 │ 查看 延期申请 │
│ 004  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 林文龙 │ 查看 延期申请 │
│ 005  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 高枕书 │ 查看 延期申请 │
│ 006  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 徐桧桐 │ 查看         │
│ 007  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 何叔川 │ 查看         │
│ 008  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 郭云舟 │ 查看         │
│ 009  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 黄梓航 │ 查看         │
│ 010  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 赵景深 │ 查看         │
│ 011  │ XXXXXX项目    │ 应急养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 黄梓航 │ 查看         │
│ 012  │ XXXXXX项目    │ 应急养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 赵铁柱 │ 查看         │
│                                                                    │
│                                              < [1] 2 3 4 5 >      │
└────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深蓝色主题界面**：背景 #1e3a8a，白色文字
- **双级导航标签**：
  - 主标签：🏗️桥梁养护维修（激活）、🚇隧道养护维修
  - 子标签：养护/保洁项目（激活）、应急维修
- **筛选区域**：4个下拉选择器 + 查询/重置按钮
- **数据表格**：10列，显示项目信息和完成状态
- **状态显示**：绿色"否"、红色"是"表示超期状态
- **分页导航**：底部页码切换，当前页高亮

### 组件清单
1. **双级导航标签组件**
   - 主标签切换（桥梁/隧道养护）
   - 子标签切换（养护项目/应急维修）
2. **搜索筛选组件**
   - 4个下拉选择器（项目名称、类型、超期状态、养护单位）
   - 查询和重置按钮
3. **项目列表表格组件**
   - 10列数据展示
   - 超期状态颜色标识
4. **分页组件**
   - 页码导航按钮
   - 当前页高亮显示

### 交互功能
- 标签切换：主标签和子标签切换
- 条件筛选：多条件组合查询
- 项目查看：点击查看项目详情
- 延期申请：申请项目时间延期
- 分页浏览：切换页面查看更多数据

---

## 图片22 (image22.jpg) - 查看详情基本信息页

```
┌──────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                         ⊗   │
├──────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护]                                      │
├──────────────────────────────────────────────────────────────────────┤
│                                                                      │
│ 项目名称:                            项目类型:                       │
│ [XXXXXXXXXXXXX]                      [XXXXXXXXXXX]                   │
│                                                                      │
│ 项目开始时间:                        项目结束时间:                   │
│ [2025-09-18 10:34]                   [2025-09-18 10:34]             │
│                                                                      │
│ 管理单位:                            监理单位:                       │
│ [XXXXXXX]                            [XXXXXXXXX]                     │
│                                                                      │
│ 养护单位:                            项目负责人:                     │
│ [XXXXXXXXXXX]                        [张三]                          │
│                                                                      │
│ 联系方式:                            工作量:                         │
│ [XXXXXXXXXXX]                        [20]                            │
│                                                                      │
│ 项目内容:                                                            │
│ [XXXXXXXXXXX                                                       ] │
│ [                                                                  ] │
│ [                                                                  ] │
│                                                                      │
│ 附件:                                                                │
│                                                                      │
│                                                      [关闭]          │
└──────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **深蓝色模态弹窗**：背景 #1e3a8a，白色文字
- **三标签导航**：基本信息（激活）、养护项目、病害养护
- **双列表单布局**：左右对称排列，灰色输入框
- **多行文本区域**：项目内容大文本框
- **底部操作区**：关闭按钮右对齐

### 组件清单
1. **模态弹窗组件**
   - 标题栏带关闭按钮
   - 三标签页切换
2. **表单组件**
   - 8个字段输入框（双列布局）
   - 项目内容多行文本框
   - 附件上传区域
3. **操作按钮组件**
   - 关闭按钮

### 交互功能
- 标签切换：基本信息、养护项目、病害养护
- 表单展示：项目基本信息只读显示
- 弹窗关闭：点击关闭按钮或X关闭弹窗

---

## 图片23 (image23.jpg) - 查看详情养护项目页

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护]                                             │
│             ━━━━━━                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ [桥梁名称▼] [养护项目▼] [状态▼] [负责人▼] [查询] [重置]                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 完成量 12/32                                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│  桥梁名称  │  养护项目  │ 状态 │ 负责人 │  联系方式  │   完成时间   │操作│
├───┼─────────────┼─────────────┼──────┼────────┼─────────────┼──────────────┼────┤
│ 1 │XXXXXX大桥  │排水系统养护 │未完成│黄昭言  │15820007394  │2025-09-18 10:43│详情│
│ 2 │XXXXXX大桥  │排水系统养护 │审核中│刘雨桐  │13122238579  │2025-09-18 10:43│详情│
│ 3 │XXXXXX大桥  │排水系统养护 │退回  │罗颖秋  │19620059483  │2025-09-18 10:43│详情│
│ 4 │XXXXXX大桥  │上部结构养护 │复核中│林文龙  │19607559483  │2025-09-18 10:43│详情│
│ 5 │XXXXXX大桥  │上部结构养护 │已完成│高枕书  │18557189483  │2025-09-18 10:43│详情│
│ 6 │XXXXXX大桥  │上部结构养护 │已完成│徐桧桐  │19020017495  │2025-09-18 10:43│详情│
│007│XXXXXX大桥  │上部结构养护 │已完成│何叔川  │19020017495  │2025-09-18 10:43│详情│
│008│XXXXXX大桥  │上部结构养护 │已完成│郭云舟  │19020017495  │2025-09-18 10:43│详情│
│009│XXXXXX大桥  │上部结构养护 │已完成│黄梓航  │19020017495  │2025-09-18 10:43│详情│
│010│XXXXXX大桥  │上部结构养护 │已完成│赵景深  │19020017495  │2025-09-18 10:43│详情│
├─────────────────────────────────────────────────────────────────────────────┤
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af
  - 界面类型：全屏弹窗界面，无遮罩层
  - 整体色调：深蓝色主题，白色文字
- **顶部标题区域**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："查看详情"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px
- **标签页导航区域**：
  - 背景：与主背景相同的深蓝色
  - 激活标签："养护项目"，白色文字 + 蓝色下划线
  - 非激活标签："基本信息"、"病害养护"，灰白色文字
  - 下划线：蓝色实线，位于激活标签下方
- **筛选控制区域**：
  - 背景：深蓝色，与整体一致
  - 下拉框样式：深蓝色背景，白色边框，白色文字
  - 下拉箭头：白色 ▼ 符号
  - 按钮样式：蓝色实心按钮（查询）、蓝色边框按钮（重置）
  - 布局：水平排列，左对齐
- **统计信息区域**：
  - "完成量 12/32" 文字：白色，14px，左对齐
  - 背景：与主界面背景一致
- **数据表格区域**：
  - 表头背景：深蓝色 #1e40af，略深于主背景
  - 表头文字：白色，14px，居中对齐
  - 表格边框：细白色边框线分隔
  - 数据行背景：深蓝色，与主背景一致
  - 数据文字：白色 #ffffff，12px
  - 状态标签：彩色圆角标签（未完成-橙色，审核中-黄色，退回-红色，复核中-蓝色，已完成-绿色）
- **底部操作区域**：
  - 背景：深蓝色，与主背景一致
  - 关闭按钮：蓝色边框按钮，白色文字，右下角对齐

### 组件清单
1. **全屏界面容器组件**
   - 深蓝色渐变背景容器
   - 全屏显示，无边框圆角
   - 统一的深蓝色主题色调
2. **顶部标题栏组件**
   - 标题文字："查看详情"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置
   - 标题栏高度固定，与整体背景融合
3. **标签页切换组件**
   - 三个标签：基本信息、养护项目（当前激活）、病害养护
   - 激活标签：白色文字 + 蓝色下划线标识
   - 非激活标签：灰白色文字，无下划线
4. **筛选条件组件**
   - 四个下拉选择框：桥梁名称、养护项目、状态、负责人
   - 每个下拉框：深蓝背景 + 白色边框 + 白色文字 + 白色下拉箭头
   - 两个操作按钮：查询（蓝色实心）、重置（蓝色边框）
5. **完成进度显示组件**
   - 文字显示："完成量 12/32"
   - 白色文字，左对齐显示
   - 动态数据，实时更新
6. **数据表格组件**
   - 表头：8列标题（序号、桥梁名称、养护项目、状态、负责人、联系方式、完成时间、操作）
   - 表头样式：深蓝色背景，白色文字，细白色边框分隔
   - 数据行：10行养护项目记录
   - 数据行样式：深蓝色背景，白色文字
   - 状态标签：彩色圆角标签（5种状态对应不同颜色）
   - 操作列：蓝色"详情"链接按钮
7. **底部操作栏组件**
   - 关闭按钮：蓝色边框按钮，右下角位置
   - 按钮文字：白色"关闭"文字

### 交互功能
- **标签页切换交互**：
  - 点击标签切换不同数据视图（基本信息/养护项目/病害养护）
  - 激活标签显示蓝色下划线，切换时平滑移动
  - 切换标签时保持当前的筛选条件设置
- **筛选功能交互**：
  - 四个下拉框独立筛选：桥梁名称、养护项目、状态、负责人
  - 下拉框点击展开选项列表，支持键盘上下键选择
  - 查询按钮：点击执行筛选，更新表格数据和完成量统计
  - 重置按钮：一键清空所有筛选条件，恢复默认显示
- **表格数据交互**：
  - 表头列标题支持点击排序（升序/降序切换）
  - 表格行鼠标悬停时背景色略微变浅
  - 操作列"详情"按钮点击跳转到具体养护项目详情页
  - 状态标签显示不同颜色：未完成（橙色）、审核中（黄色）、退回（红色）、复核中（蓝色）、已完成（绿色）
- **完成量统计交互**：
  - 根据当前筛选条件实时计算和显示完成量（12/32）
  - 筛选条件变化时自动更新统计数字
- **界面关闭交互**：
  - 点击右上角⊗关闭按钮关闭当前界面
  - 点击右下角"关闭"按钮关闭当前界面
  - 支持ESC键快捷关闭界面
- **数据展示适配**：
  - 表格固定表头，数据区域可垂直滚动
  - 长文本内容自动换行或省略号显示
  - 联系方式等数字内容保持原格式显示

---

## 图片24 (image24.jpg) - 养护项目详情弹窗

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 养护项目详情                                                           ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 养护项目                                                                    │
│                                                                             │
│ 桥梁/隧道名称:                   │ 养护项目:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX项目                     ]     │
│                                 │                                       │
│ 项目开始时间:                    │ 项目结束时间:                           │
│ [2025-09-18 10:34             ] │ [2025-09-18 10:34               ]     │
│                                                                             │
│ 处置信息                                                                    │
│                                                                             │
│ 养护单位:                       │ 配置单位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX项目                     ]     │
│                                 │                                       │
│ 处置人员:                       │ 联系方式:                              │
│ [张三                         ] │ [13321207394                    ]     │
│                                                                             │
│ 处置时间:                                                                   │
│ [2025-09-18 10:34             ]                                            │
│                                                                             │
│ 处置备注:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                  │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 处置照片:                                                                   │
│ [现场照片] [人车照片] [养护前] [养护中] [养护后]                            │
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐                                   │
│ │                 │ │                 │                                   │
│ │                 │ │                 │                                   │
│ │     照片区域1    │ │     照片区域2    │                                   │
│ │                 │ │                 │                                   │
│ │                 │ │                 │                                   │
│ └─────────────────┘ └─────────────────┘                                   │
│                                                                             │
│ 审核信息                                                                    │
│                                                                             │
│ ┌─────┬─────────┬─────┬─────┬─────┬─────────┬─────────┬─────────┐          │
│ │序号 │审批环节  │处理人│审批状态│审批意见│处理人部门│接收时间  │办结时间  │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 1   │开始申请  │黄职高│通过  │无异议│养护公司  │2025-09-18│2025-09-18│          │
│ │     │         │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 2   │养护项目审批│刘明明│     │XXX部门│         │2025-09-18│2025-09-18│          │
│ │     │ (一级)   │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 3   │养护项目审批│罗敬政│     │     │         │2025-09-18│2025-09-18│          │
│ │     │ (二级)   │     │     │     │         │10:43    │10:43    │          │
│ └─────┴─────────┴─────┴─────┴─────┴─────────┴─────────┴─────────┘          │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：模态弹窗，全屏显示，深蓝色半透明遮罩
  - 整体色调：深蓝色主题，白色文字
- **顶部标题区域**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："养护项目详情"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px，可点击
- **养护项目信息区域**：
  - 区域标题："养护项目"，白色文字，14px 粗体
  - 输入框样式：深灰色背景 #4b5563，白色文字，圆角 6px，只读状态
  - 字段布局：2列网格布局，左右对齐
  - 字段间距：垂直间距 16px，水平间距 20px
- **处置信息区域**：
  - 区域标题："处置信息"，白色文字，14px 粗体
  - 普通输入框：深灰色背景，白色文字，只读状态
  - 处置备注框：深蓝色边框，深色背景，可编辑状态，多行文本框
  - 提示文字："请输入"，灰色占位符文字 #9ca3af
- **处置照片区域**：
  - 标题："处置照片"，白色文字，左对齐
  - 照片标签：5个标签按钮（现场照片、人车照片、养护前、养护中、养护后）
  - 激活标签："养护后"，蓝色背景 #3b82f6，白色文字
  - 非激活标签：深蓝色边框，白色文字，透明背景
  - 照片显示：2张照片并排显示，圆角边框，固定尺寸
- **审核信息区域**：
  - 区域标题："审核信息"，白色文字，14px 粗体
  - 表格样式：深蓝色背景，白色边框分隔线
  - 表头：8列（序号、审批环节、处理人、审批状态、审批意见、处理人部门、接收时间、办结时间）
  - 表头文字：白色，14px，居中对齐
  - 数据行：3行审批记录，白色文字，12px
  - 状态显示："通过"状态为绿色文字 #10b981
- **底部操作区域**：
  - 关闭按钮：深蓝色边框按钮，白色文字，右下角对齐

### 组件清单
1. **模态弹窗容器组件**
   - 深蓝色渐变背景，全屏遮罩
   - 居中显示，支持ESC键关闭
   - 垂直滚动支持，适应内容高度
2. **弹窗标题栏组件**
   - 标题文字："养护项目详情"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置，点击关闭弹窗
3. **养护项目信息表单组件**
   - 区域标题："养护项目"
   - 4个只读输入框：桥梁/隧道名称、养护项目、项目开始时间、项目结束时间
   - 2列网格布局，响应式对齐
4. **处置信息表单组件**
   - 区域标题："处置信息"
   - 5个只读输入框：养护单位、配置单位、处置人员、联系方式、处置时间
   - 1个可编辑多行文本框：处置备注（带占位符"请输入"）
5. **处置照片管理组件**
   - 照片分类标签：5个标签按钮切换不同照片类型
   - 当前激活标签："养护后"（蓝色背景）
   - 照片展示区：2张照片并排显示，支持点击查看大图
6. **审核流程表格组件**
   - 区域标题："审核信息"
   - 8列数据表格：完整的审批流程记录
   - 3行审批记录：开始申请、一级审批、二级审批
   - 状态标识：通过状态显示绿色文字
7. **底部操作栏组件**
   - 关闭按钮：深蓝色边框按钮，右下角位置

### 交互功能
- **弹窗控制**：点击关闭按钮或ESC键关闭弹窗，点击遮罩层不关闭
- **照片标签切换**：点击不同标签显示对应类型的照片
- **照片查看**：点击照片可放大查看，支持左右切换
- **表格滚动**：审核信息表格支持水平滚动，适应窄屏显示
- **文本框编辑**：处置备注框支持多行文本输入，自动高度调整
- **数据只读**：除处置备注外，其他字段均为只读显示状态

---

## 图片25 (image25.jpg) - 查看详情病害养护页

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────┬─────────┬─────────┐                                             │
│ │基本信息 │ 养护项目 │病害养护 │                                             │
│ └─────────┴─────────┴─────────┘                                             │
│                       ▔▔▔▔▔▔▔▔                                             │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                           │
│ │ 桥梁名称 ▼  │ │ 病害类型 ▼  │ │ 状态    ▼  │  [查询]  [重置]           │
│ └─────────────┘ └─────────────┘ └─────────────┘                           │
│                                                                             │
│ 完成量 12/32                                                               │
│                                                                             │
│ ┌─────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┬─────┐      │
│ │序号 │桥梁名称  │病害编号  │病害部位  │病害类型  │完成时间  │负责人│操作 │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 1   │XXXXXX大桥│  989    │伸缩缝   │伸缩缝缺失│2025-09-18│王景深│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 2   │XXXXXX大桥│  988    │伸缩缝   │伸缩缝缺失│2025-09-18│刘志强│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 3   │XXXXXX大桥│  987    │照明设施 │照明设施缺失│2025-09-18│赵临洲│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ └─────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────┴─────┘      │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：模态弹窗，全屏显示，深蓝色半透明遮罩
  - 整体色调：深蓝色主题，白色文字
- **顶部标题区域**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："查看详情"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px，可点击
- **选项卡导航区域**：
  - 三个选项卡：基本信息、养护项目、病害养护
  - 当前激活选项卡："病害养护"，蓝色下划线标识，白色文字
  - 非激活选项卡：灰白色文字 #e5e7eb，无下划线
  - 选项卡间距：水平均匀分布，每个选项卡宽度约120px
- **筛选条件区域**：
  - 三个下拉选择框：桥梁名称、病害类型、状态
  - 下拉框样式：深灰色背景 #4b5563，白色文字，右侧下拉箭头
  - 按钮样式：查询按钮（蓝色背景 #3b82f6），重置按钮（灰色边框）
  - 布局：左侧3个下拉框，右侧2个操作按钮
- **统计信息区域**：
  - 完成量显示："完成量 12/32"，白色文字，左对齐
  - 字体：14px，常规字重
- **数据表格区域**：
  - 表格样式：深蓝色背景，白色边框分隔线
  - 表头：8列（序号、桥梁名称、病害编号、病害部位、病害类型、完成时间、负责人、操作）
  - 表头文字：白色，14px，居中对齐，深蓝色背景 #1e40af
  - 数据行：3行病害记录，白色文字，12px
  - 行间距：每行高度约50px，包含上下内边距
  - 操作列：蓝色"详情"链接按钮
- **底部操作区域**：
  - 关闭按钮：深蓝色边框按钮，白色文字，右下角对齐
  - 按钮样式：圆角 6px，内边距 8px 16px

### 组件清单
1. **模态弹窗容器组件**
   - 深蓝色渐变背景，全屏遮罩
   - 居中显示，支持ESC键关闭
   - 垂直滚动支持，适应内容高度
2. **弹窗标题栏组件**
   - 标题文字："查看详情"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置，点击关闭弹窗
3. **选项卡导航组件**
   - 三个选项卡：基本信息、养护项目、病害养护
   - 当前激活："病害养护"，带蓝色下划线标识
   - 选项卡切换功能，点击切换不同内容视图
4. **筛选条件表单组件**
   - 三个下拉选择框：桥梁名称、病害类型、状态
   - 查询按钮：蓝色背景，触发数据筛选
   - 重置按钮：灰色边框，清空筛选条件
5. **统计信息显示组件**
   - 完成量统计："完成量 12/32"
   - 动态更新，根据筛选条件变化
6. **病害数据表格组件**
   - 8列数据表格：完整的病害养护记录
   - 3行数据记录：不同桥梁的病害信息
   - 操作列：蓝色"详情"链接，点击查看具体病害详情
   - 表格滚动：支持垂直滚动显示更多数据
7. **底部操作栏组件**
   - 关闭按钮：深蓝色边框按钮，右下角位置

### 交互功能
- **弹窗控制**：点击关闭按钮或ESC键关闭弹窗，点击遮罩层不关闭
- **选项卡切换**：点击不同选项卡切换到对应的内容视图（基本信息、养护项目、病害养护）
- **筛选功能**：
  - 下拉框选择：点击下拉框展开选项列表，选择筛选条件
  - 查询按钮：根据选择的条件筛选表格数据
  - 重置按钮：清空所有筛选条件，恢复默认显示
- **表格交互**：
  - 详情链接：点击"详情"按钮查看具体病害的详细信息
  - 表格滚动：当数据较多时支持垂直滚动浏览
  - 排序功能：表头支持点击排序（按时间、编号等）
- **数据统计**：完成量数字根据筛选条件实时更新显示
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果

---

## 图片26 (image26.jpg) - 病害详情页面

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 病害详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 病害信息                                                                    │
│                                                                             │
│ 桥梁/隧道名称:                   │ 病害部位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                 │                                       │
│ 病害类型:                       │ 病害位置:                              │
│ [XXXXXXXXX                    ] │ [XXXXXXXXX                      ]     │
│                                                                             │
│ 病害数量:                                                                   │
│ [12                           ]                                            │
│                                                                             │
│ 病害描述:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 上报人:                         │ 联系方式:                              │
│ [XXXXXX大桥                   ] │ [19321207394                    ]     │
│                                                                             │
│ 病害照片:                                                                   │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片1     │ │   照片2     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 处置信息                                                                    │
│                                                                             │
│ 养护单位:                       │ 管理单位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                                                             │
│ 处置人员:                       │ 联系方式:                              │
│ [刘三                         ] │ [19321207394                    ]     │
│                                                                             │
│ 处置时间:                                                                   │
│ [2025-09-18 10:34             ]                                            │
│                                                                             │
│ 处置说明:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                  │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 处置照片:                                                                   │
│ [现场照片] [人车照片] [维修前] [维修中] [维修后]                            │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片3     │ │   照片4     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 审核信息                                                                    │
│                                                                             │
│ ┌─────┬─────────┬─────┬─────┬─────┬─────────┬─────────┬─────────┐          │
│ │序号 │审批环节  │处理人│审批状态│审批意见│处理人部门│接收时间  │办结时间  │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 1   │开始申请  │高哲  │通过  │无异议│开办公司  │2025-09-18│2025-09-18│          │
│ │     │         │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 2   │养护项目审批│刘明明│     │     │XXX部门 │2025-09-18│2025-09-18│          │
│ │     │ (一级)   │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 3   │养护项目审批│罗政权│     │     │         │2025-09-18│2025-09-18│          │
│ │     │ (二级)   │     │     │     │         │10:43    │10:43    │          │
│ └─────┴─────────┴─────┴─────┴─────┴─────────┴─────────┴─────────┘          │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：模态弹窗，全屏显示，深蓝色半透明遮罩
  - 整体色调：深蓝色主题，白色文字
  - 内容布局：垂直滚动，三个主要信息区域
- **顶部标题区域**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："病害详情"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px，可点击
- **病害信息区域**：
  - 区域标题："病害信息"，白色文字，14px 粗体
  - 输入框样式：深灰色背景 #4b5563，白色文字，圆角 6px，只读状态
  - 字段布局：2列网格布局（除病害等级和病害描述为单列）
  - 字段间距：垂直间距 16px，水平间距 20px
  - 病害描述框：多行文本框，深灰色背景，只读状态
  - 病害照片：2张照片并排显示，固定尺寸，圆角边框
- **处置信息区域**：
  - 区域标题："处置信息"，白色文字，14px 粗体
  - 普通输入框：深灰色背景，白色文字，只读状态
  - 处置说明框：深蓝色边框，深色背景，可编辑状态，多行文本框
  - 提示文字："请输入"，灰色占位符文字 #9ca3af
  - 处置照片标签：5个标签按钮（现场照片、人车照片、处置前、处置中、处置后）
  - 激活标签："处置后"，蓝色背景 #3b82f6，白色文字
  - 非激活标签：深蓝色边框，白色文字，透明背景
  - 照片显示：2张照片并排显示，圆角边框，固定尺寸
- **审核信息区域**：
  - 区域标题："审核信息"，白色文字，14px 粗体
  - 表格样式：深蓝色背景，白色边框分隔线
  - 表头：8列（序号、审批环节、处理人、审批状态、审批意见、处理人部门、接收时间、办结时间）
  - 表头文字：白色，14px，居中对齐
  - 数据行：3行审批记录，白色文字，12px
  - 状态显示："通过"状态为绿色文字 #10b981
- **底部操作区域**：
  - 关闭按钮：深蓝色边框按钮，白色文字，右下角对齐

### 组件清单
1. **模态弹窗容器组件**
   - 深蓝色渐变背景，全屏遮罩
   - 居中显示，支持ESC键关闭
   - 垂直滚动支持，适应长内容高度
2. **弹窗标题栏组件**
   - 标题文字："病害详情"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置，点击关闭弹窗
3. **病害信息表单组件**
   - 区域标题："病害信息"
   - 6个只读输入框：桥梁/隧道名称、病害部位、病害类型、病害编号、病害等级、上报人、联系方式
   - 1个多行只读文本框：病害描述
   - 病害照片展示：2张照片并排显示
   - 2列网格布局（部分字段单列显示）
4. **处置信息表单组件**
   - 区域标题："处置信息"
   - 5个只读输入框：养护单位、管理单位、处置人员、联系方式、处置时间
   - 1个可编辑多行文本框：处置说明（带占位符"请输入"）
   - 处置照片管理：5个标签按钮切换不同照片类型，当前激活"处置后"
   - 照片展示区：2张照片并排显示
5. **审核流程表格组件**
   - 区域标题："审核信息"
   - 8列数据表格：完整的审批流程记录
   - 3行审批记录：开始申请、一级审批、二级审批
   - 状态标识：通过状态显示绿色文字
6. **底部操作栏组件**
   - 关闭按钮：深蓝色边框按钮，右下角位置

### 交互功能
- **弹窗控制**：点击关闭按钮或ESC键关闭弹窗，点击遮罩层不关闭
- **照片查看**：
  - 病害照片：点击照片可放大查看，支持左右切换
  - 处置照片：点击不同标签显示对应类型的照片，点击照片放大查看
- **表格滚动**：审核信息表格支持水平滚动，适应窄屏显示
- **文本框编辑**：处置说明框支持多行文本输入，自动高度调整
- **数据只读**：除处置说明外，其他字段均为只读显示状态
- **垂直滚动**：整个弹窗内容支持垂直滚动，适应长内容显示
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果

---

## 图片27 (image27.jpg) - 应急维修列表页面

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏗️ 桥梁养护维修    🌉 隧道养护维修                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 养护/应急项目                   应急维修                                     │
│ ──────────────────────────────────────────────────────────                 │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │桥梁名称    ▼│ │状态      ▼  │ │病害类型   ▼ │ │负责人     ▼ │ │养护单位   ▼ │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│                                                                             │
│                                                        [查询]  [重置]      │
│                                                                             │
│ ┌─────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┐ │
│ │序号 │桥梁名称  │上报人   │上报时间  │联系方式  │状态     │病害编号  │病害部位  │病害类型  │病害数量  │负责人   │养护单位│操作 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │001  │XXXXXX项目│向文革   │2025/09/01│13720186495│待处理  │00001   │XXXXX部位│XXXXX类型│71       │吴烟维   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │002  │XXXXXX项目│罗子安   │2025/09/01│18720269485│已处理  │00002   │XXXXX部位│XXXXX类型│85       │郭昭临   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │003  │XXXXXX项目│江萍丹   │2025/09/01│15820007394│退回    │00003   │XXXXX部位│XXXXX类型│73       │李露慧   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │004  │XXXXXX项目│朱洋军   │2025/09/01│13720089685│已处理  │00004   │XXXXX部位│XXXXX类型│44       │林文龙   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │005  │XXXXXX项目│王若谦   │2025/09/01│17702048593│已处理  │00005   │XXXXX部位│XXXXX类型│70       │高欣书   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │006  │XXXXXX项目│林司欣   │2025/09/01│16657168395│已处理  │00006   │XXXXX部位│XXXXX类型│39       │徐敏倩   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │007  │XXXXXX项目│郑晨达   │2025/09/01│13275528394│已处理  │00007   │XXXXX部位│XXXXX类型│43       │何敏川   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │008  │XXXXXX项目│张格高   │2025/09/01│14557139685│已处理  │00008   │XXXXX部位│XXXXX类型│1        │郭云舟   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │009  │XXXXXX项目│辛建军   │2025/09/01│18901079463│已处理  │00009   │XXXXX部位│XXXXX类型│2        │黄怡航   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │010  │XXXXXX项目│赵妍红   │2025/09/01│19057187395│已处理  │00010   │XXXXX部位│XXXXX类型│25       │赵晨涛   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │011  │XXXXXX项目│黄淑芬   │2025/09/01│15010086427│已处理  │00011   │XXXXX部位│XXXXX类型│99       │黄怡航   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │012  │XXXXXX项目│陈丽华   │2025/09/01│14502156394│已处理  │00012   │XXXXX部位│XXXXX类型│92       │赵钰廷   │长沙市桥梁管理处│查看 │ │
│ └─────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────┴─────┘ │
│                                                                             │
│                                                                             │
│                                    ◀  1  2  3  4  5  ▶                     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：全屏页面布局，深蓝色主题
  - 整体色调：深蓝色主题，白色文字
- **顶部导航区域**：
  - 两个主导航标签：桥梁养护维修（🏗️图标）、隧道养护维修（🌉图标）
  - 当前激活标签：桥梁养护维修，白色文字
  - 非激活标签：隧道养护维修，灰白色文字 #e5e7eb
- **子导航区域**：
  - 两个子选项卡：养护/应急项目、应急维修
  - 当前激活选项卡：应急维修，蓝色下划线标识，白色文字
  - 非激活选项卡：养护/应急项目，灰白色文字，无下划线
- **筛选条件区域**：
  - 五个下拉选择框：桥梁名称、状态、病害类型、负责人、养护单位
  - 下拉框样式：深灰色背景 #4b5563，白色文字，右侧下拉箭头
  - 按钮样式：查询按钮（蓝色背景 #3b82f6），重置按钮（灰色边框）
  - 布局：5个下拉框水平排列，右侧2个操作按钮
- **数据表格区域**：
  - 表格样式：深蓝色背景，白色边框分隔线
  - 表头：13列（序号、桥梁名称、上报人、上报时间、联系方式、状态、病害编号、病害部位、病害类型、病害数量、负责人、养护单位、操作）
  - 表头文字：白色，14px，居中对齐，深蓝色背景 #1e40af
  - 数据行：12行应急维修记录，白色文字，12px
  - 行间距：每行高度约40px，包含上下内边距
  - 状态显示：待处理（橙色 #f59e0b）、已处理（绿色 #10b981）、退回（红色 #ef4444）
  - 操作列：蓝色"查看"链接按钮
- **分页导航区域**：
  - 分页控件：居中显示，包含左箭头、页码1-5、右箭头
  - 当前页码：页码1，蓝色背景 #3b82f6，白色文字
  - 其他页码：深蓝色边框，白色文字，透明背景
  - 箭头按钮：左右箭头，白色文字，可点击

### 组件清单
1. **主导航标签组件**
   - 两个主标签：桥梁养护维修、隧道养护维修
   - 图标显示：🏗️和🌉图标
   - 当前激活：桥梁养护维修，白色文字
2. **子导航选项卡组件**
   - 两个选项卡：养护/应急项目、应急维修
   - 当前激活：应急维修，蓝色下划线标识
   - 选项卡切换功能
3. **筛选条件表单组件**
   - 五个下拉选择框：桥梁名称、状态、病害类型、负责人、养护单位
   - 查询按钮：蓝色背景，触发数据筛选
   - 重置按钮：灰色边框，清空筛选条件
4. **应急维修数据表格组件**
   - 13列数据表格：完整的应急维修记录
   - 12行数据记录：不同状态的应急维修项目
   - 状态标识：不同颜色显示不同状态
   - 操作列：蓝色"查看"链接，点击查看详情
   - 表格滚动：支持水平和垂直滚动
5. **分页导航组件**
   - 页码按钮：1-5页码，当前页高亮显示
   - 左右箭头：支持上一页下一页导航
   - 居中对齐显示

### 交互功能
- **导航切换**：
  - 主导航：点击桥梁/隧道标签切换不同养护类型
  - 子导航：点击选项卡切换养护项目和应急维修视图
- **筛选功能**：
  - 下拉框选择：点击下拉框展开选项列表，选择筛选条件
  - 查询按钮：根据选择的条件筛选表格数据
  - 重置按钮：清空所有筛选条件，恢复默认显示
- **表格交互**：
  - 查看链接：点击"查看"按钮查看具体应急维修详情
  - 表格滚动：支持水平滚动查看更多列，垂直滚动浏览更多数据
  - 排序功能：表头支持点击排序（按时间、状态等）
- **分页导航**：
  - 页码点击：点击不同页码跳转到对应页面
  - 箭头导航：点击左右箭头进行上一页下一页切换
  - 当前页高亮：当前页码蓝色背景高亮显示
- **状态识别**：不同状态用不同颜色标识，便于快速识别处理状态
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果

---

## 图片28 (image28.jpg) - 病害养护详情弹窗

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 病害养护详情                                                           ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 病害信息                                                                    │
│                                                                             │
│ 桥梁/隧道名称:                   │ 病害部位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                 │                                       │
│ 病害类型:                       │ 病害位置:                              │
│ [XXXXXXXXXX                   ] │ [XXXXXXXXXX                     ]     │
│                                                                             │
│ 病害数量:                                                                   │
│ [12                           ]                                            │
│                                                                             │
│ 病害描述:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 上报人:                         │ 联系方式:                              │
│ [XXXXXX大桥                   ] │ [19321207394                    ]     │
│                                                                             │
│ 病害照片:                                                                   │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片1     │ │   照片2     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 处置信息                                                                    │
│                                                                             │
│ 养护单位:                       │ 管理单位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                                                             │
│ 处置人员:                       │ 联系方式:                              │
│ [张三                         ] │ [19321207394                    ]     │
│                                                                             │
│ 处置时间:                                                                   │
│ [2025-09-18 10:34             ]                                            │
│                                                                             │
│ 处置说明:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                  │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 处置照片:                                                                   │
│ [现场照片] [人车照片] [维修前] [维修中] [维修后]                            │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片3     │ │   照片4     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 审核信息                                                                    │
│                                                                             │
│ ┌─────┬─────────┬─────┬─────┬─────┬─────────┬─────────┬─────────┐          │
│ │序号 │审批环节  │处理人│审批状态│审批意见│处理人部门│接收时间  │办结时间  │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 1   │开始申请  │黄昭言│通过  │无异议│养护公司  │2025-09-18│2025-09-18│          │
│ │     │         │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 2   │养护项目审批│刘雨桐│     │     │XXX部门 │2025-09-18│2025-09-18│          │
│ │     │ (一级)   │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 3   │养护项目审批│罗颖秋│     │     │         │2025-09-18│2025-09-18│          │
│ │     │ (二级)   │     │     │     │         │10:43    │10:43    │          │
│ └─────┴─────────┴─────┴─────┴─────┴─────────┴─────────┴─────────┘          │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：模态弹窗，全屏显示，深蓝色半透明遮罩
  - 整体色调：深蓝色主题，白色文字
  - 内容布局：垂直滚动，三个主要信息区域
- **顶部标题区域**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："病害养护详情"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px，可点击
- **病害信息区域**：
  - 区域标题："病害信息"，白色文字，14px 粗体
  - 输入框样式：深灰色背景 #4b5563，白色文字，圆角 6px，只读状态
  - 字段布局：2列网格布局（除病害数量和病害描述为单列）
  - 字段间距：垂直间距 16px，水平间距 20px
  - 字段列表：桥梁/隧道名称、病害部位、病害类型、病害位置、病害数量、病害描述、上报人、联系方式
  - 病害描述框：多行文本框，深灰色背景，只读状态
  - 病害照片：2张照片并排显示，固定尺寸，圆角边框
- **处置信息区域**：
  - 区域标题："处置信息"，白色文字，14px 粗体
  - 普通输入框：深灰色背景，白色文字，只读状态
  - 字段列表：养护单位、管理单位、处置人员、联系方式、处置时间
  - 处置说明框：深蓝色边框，深色背景，可编辑状态，多行文本框
  - 提示文字："请输入"，灰色占位符文字 #9ca3af
  - 处置照片标签：5个标签按钮（现场照片、人车照片、维修前、维修中、维修后）
  - 激活标签："维修后"，蓝色背景 #3b82f6，白色文字
  - 非激活标签：深蓝色边框，白色文字，透明背景
  - 照片显示：2张照片并排显示，圆角边框，固定尺寸
- **审核信息区域**：
  - 区域标题："审核信息"，白色文字，14px 粗体
  - 表格样式：深蓝色背景，白色边框分隔线
  - 表头：8列（序号、审批环节、处理人、审批状态、审批意见、处理人部门、接收时间、办结时间）
  - 表头文字：白色，14px，居中对齐
  - 数据行：3行审批记录，白色文字，12px
  - 状态显示："通过"状态为绿色文字 #10b981
  - 审批人员：黄昭言、刘雨桐、罗颖秋
- **底部操作区域**：
  - 关闭按钮：深蓝色边框按钮，白色文字，右下角对齐

### 组件清单
1. **模态弹窗容器组件**
   - 深蓝色渐变背景，全屏遮罩
   - 居中显示，支持ESC键关闭
   - 垂直滚动支持，适应长内容高度
2. **弹窗标题栏组件**
   - 标题文字："病害养护详情"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置，点击关闭弹窗
3. **病害信息表单组件**
   - 区域标题："病害信息"
   - 8个只读输入框：桥梁/隧道名称、病害部位、病害类型、病害位置、病害数量、上报人、联系方式
   - 1个多行只读文本框：病害描述
   - 病害照片展示：2张照片并排显示
   - 2列网格布局（部分字段单列显示）
4. **处置信息表单组件**
   - 区域标题："处置信息"
   - 5个只读输入框：养护单位、管理单位、处置人员、联系方式、处置时间
   - 1个可编辑多行文本框：处置说明（带占位符"请输入"）
   - 处置照片管理：5个标签按钮切换不同照片类型，当前激活"维修后"
   - 照片展示区：2张照片并排显示
5. **审核流程表格组件**
   - 区域标题："审核信息"
   - 8列数据表格：完整的审批流程记录
   - 3行审批记录：开始申请、一级审批、二级审批
   - 状态标识：通过状态显示绿色文字
   - 审批人员：黄昭言、刘雨桐、罗颖秋
6. **底部操作栏组件**
   - 关闭按钮：深蓝色边框按钮，右下角位置

### 交互功能
- **弹窗控制**：点击关闭按钮或ESC键关闭弹窗，点击遮罩层不关闭
- **照片查看**：
  - 病害照片：点击照片可放大查看，支持左右切换
  - 处置照片：点击不同标签显示对应类型的照片，点击照片放大查看
  - 标签切换：5个照片类型标签（现场照片、人车照片、维修前、维修中、维修后）
- **表格滚动**：审核信息表格支持水平滚动，适应窄屏显示
- **文本框编辑**：处置说明框支持多行文本输入，自动高度调整
- **数据只读**：除处置说明外，其他字段均为只读显示状态
- **垂直滚动**：整个弹窗内容支持垂直滚动，适应长内容显示
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果

---

## 图片29 (image29.jpg) - 桥梁养护维修审核页面

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐                                     │
│ │ 🌉 桥梁养护维修审核 │ │ 🚇 隧道养护维修审核 │                                     │
│ └─────────────────┘ └─────────────────┘                                     │
│                                                                             │
│ ┌──────────┐ ┌──────────┐                                                  │
│ │ 待审核    │ │ 已审核    │                                                  │
│ └──────────┘ └──────────┘                                                  │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 审核项 ▼    │ 发起人 ▼    │ 发起时间 ▼   │ 单位 ▼      │ [查询] [重置] │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────┬─────────────┬─────────┬─────────────┬─────────────┬─────────┐       │
│ │ 序号 │ 审核项       │ 发起人   │ 发起时间     │ 单位         │ 操作     │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 001 │ XXXXXX项目   │ 周文革   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 002 │ XXXXXX项目   │ 罗子安   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 003 │ XXXXXX项目   │ 江辞舟   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 004 │ XXXXXX项目   │ 朱建军   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 005 │ XXXXXX项目   │ 王若峰   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 006 │ XXXXXX项目   │ 林雨欣   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 007 │ XXXXXX项目   │ 郑辰逸   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 008 │ XXXXXX项目   │ 张格然   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 009 │ XXXXXX项目   │ 李建军   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 010 │ XXXXXX项目   │ 赵晓红   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 011 │ XXXXXX项目   │ 黄淑芬   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ ├─────┼─────────────┼─────────┼─────────────┼─────────────┼─────────┤       │
│ │ 012 │ XXXXXX项目   │ 陈丽华   │ 2025/09/01  │ 长沙市桥梁管理处│ 审核     │       │
│ │     │             │         │ 16:16       │             │         │       │
│ └─────┴─────────────┴─────────┴─────────────┴─────────────┴─────────┘       │
│                                                                             │
│                                         ⬅ 1  2  3  4  5 ➡                  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：全屏主页面，内容区域居中显示
  - 整体色调：深蓝色主题，白色文字
  - 内容布局：垂直布局，包含导航、筛选、表格、分页四个区域
- **顶部导航标签区域**：
  - 主标签：2个标签按钮（桥梁养护维修审核、隧道养护维修审核）
  - 激活标签："桥梁养护维修审核"，深蓝色背景 #1e40af，白色文字，圆角 8px
  - 非激活标签："隧道养护维修审核"，透明背景，深蓝色边框，白色文字
  - 标签图标：桥梁🌉、隧道🚇，14px 图标
  - 标签间距：水平间距 12px
- **二级标签区域**：
  - 子标签：2个标签按钮（待审核、已审核）
  - 激活标签："待审核"，深蓝色底边线 #3b82f6，白色文字，16px
  - 非激活标签："已审核"，透明背景，灰色文字 #9ca3af
  - 标签间距：水平间距 24px，底部间距 16px
- **筛选区域**：
  - 背景：深灰色背景 #374151，圆角 8px，内边距 16px
  - 筛选框：4个下拉选择框，深蓝色背景 #1e40af，白色文字
  - 筛选框标题：审核项、发起人、发起时间、单位，12px 白色文字
  - 下拉箭头：白色 ▼ 符号，右侧对齐
  - 按钮样式：
    - 查询按钮：蓝色背景 #3b82f6，白色文字，圆角 6px，内边距 8px 16px
    - 重置按钮：透明背景，蓝色边框，白色文字，圆角 6px
  - 筛选框布局：4个筛选框等宽分布，按钮右侧对齐
- **数据表格区域**：
  - 表格背景：深蓝色半透明背景 #1e40af80
  - 表头样式：深蓝色背景 #1e3a8a，白色文字，14px 粗体，居中对齐
  - 表头列：6列（序号、审核项、发起人、发起时间、单位、操作）
  - 数据行样式：交替行背景（深蓝色 #1e40af20 和透明），白色文字，12px
  - 行高：48px，内边距 12px
  - 边框：白色细边框 1px，分隔各行各列
  - 序号列：3位数字格式（001、002...），居中对齐
  - 审核项列：项目名称"XXXXXX项目"，左对齐
  - 发起人列：真实姓名（周文革、罗子安、江辞舟等），居中对齐
  - 发起时间列：日期时间格式"2025/09/01 16:16"，居中对齐
  - 单位列："长沙市桥梁管理处"，左对齐
  - 操作列：蓝色"审核"链接按钮，居中对齐，可点击
- **分页器区域**：
  - 位置：表格底部，右对齐
  - 样式：深蓝色背景，白色文字
  - 分页按钮：⬅ 左箭头、页码数字、➡ 右箭头
  - 当前页：页码1，蓝色背景 #3b82f6，白色文字
  - 其他页码：透明背景，白色文字，悬停时蓝色背景
  - 总页数：显示1-5页，每页显示12条记录

### 组件清单
1. **主导航标签组件**
   - 2个主标签：桥梁养护维修审核、隧道养护维修审核
   - 标签图标：桥梁🌉、隧道🚇图标
   - 激活状态：深蓝色背景，白色文字
   - 非激活状态：透明背景，深蓝色边框
2. **二级标签组件**
   - 2个子标签：待审核、已审核
   - 激活状态：深蓝色底边线，白色文字
   - 非激活状态：透明背景，灰色文字
3. **筛选表单组件**
   - 4个下拉筛选框：审核项、发起人、发起时间、单位
   - 筛选框样式：深蓝色背景，白色文字，下拉箭头
   - 2个操作按钮：查询（蓝色背景）、重置（透明边框）
4. **数据表格组件**
   - 6列表格：序号、审核项、发起人、发起时间、单位、操作
   - 12行数据记录：001-012编号的审核项目
   - 表头样式：深蓝色背景，白色粗体文字
   - 数据行：交替背景色，白色文字，统一行高
   - 操作列：蓝色"审核"链接按钮
5. **分页导航组件**
   - 页码按钮：1-5页数字按钮
   - 导航箭头：左右箭头按钮
   - 当前页标识：蓝色背景高亮显示
   - 每页12条记录，总共5页数据

### 交互功能
- **标签切换**：
  - 主标签：点击"隧道养护维修审核"切换到隧道审核页面
  - 二级标签：点击"已审核"切换到已审核列表
- **筛选功能**：
  - 4个下拉筛选框：点击展开选项列表，支持单选
  - 查询按钮：执行筛选查询，刷新表格数据
  - 重置按钮：清空所有筛选条件，恢复默认显示
- **表格操作**：
  - 审核按钮：点击进入具体审核页面或弹窗
  - 表格排序：点击表头支持按发起时间等字段排序
  - 行悬停：鼠标悬停时行背景色变化
- **分页导航**：
  - 页码点击：跳转到指定页面
  - 左右箭头：上一页/下一页翻页
  - 页面加载：异步加载对应页面数据
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果
- **数据加载**：支持异步加载，显示加载状态和空数据提示

---

## 图片30 (image30.jpg) - 审批弹窗

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 审批                                                                   ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌──────────┐ ┌──────────┐                                                  │
│ │ 病害信息   │ │ 审批信息   │                                                  │
│ └──────────┘ └──────────┘                                                  │
│                                                                             │
│                                                                             │
│  [I图标]审批记录                                                                │
│                                                                             │
│ ┌─────┬─────────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┐ │
│ │ 序号 │ 审批环节     │ 处理人   │ 审批状态 │ 审批意见 │处理人部门│接收时间  │办理时│ │
│ │     │             │         │         │         │         │         │间   │ │
│ ├─────┼─────────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┤ │
│ │  1  │ 开始申请     │ 黄昭言   │ 通过     │ 无异议   │ 养护公司 │2025-09-18│2025-│ │
│ │     │             │         │         │         │         │10:43    │09-18│ │
│ │     │             │         │         │         │         │         │10:43│ │
│ ├─────┼─────────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┤ │
│ │  2  │养护项目审批  │ 刘雨桐   │         │         │XXX部门  │2025-09-18│2025-│ │
│ │     │ (一级)       │         │         │         │         │10:43    │09-18│ │
│ │     │             │         │         │         │         │         │10:43│ │
│ ├─────┼─────────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┤ │
│ │  3  │养护项目审批  │ 罗颖秋   │         │         │         │2025-09-18│2025-│ │
│ │     │ (二级)       │         │         │         │         │10:43    │09-18│ │
│ │     │             │         │         │         │         │         │10:43│ │
│ └─────┴─────────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────┘ │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│ [I图标]审批处理                                                                     │
│                                                                             │
│ *处理意见:                                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                   │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│                                                          [退回] [通过]     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与系统整体风格一致
  - 界面类型：模态弹窗，全屏显示，深蓝色半透明遮罩
  - 整体色调：深蓝色主题，白色文字
  - 内容布局：垂直滚动，包含标签、审批记录表格、审批处理三个区域
- **弹窗标题栏**：
  - 背景：深蓝色 #1e3a8a，与整体背景一致
  - 标题文字："审批"，白色 #ffffff，16px，左对齐
  - 关闭按钮：白色圆形 ⊗ 符号，右上角，18px，可点击
- **标签切换区域**：
  - 标签样式：2个标签按钮（病害信息、审批信息）
  - 激活标签："审批信息"，深蓝色底边线 #3b82f6，白色文字，16px
  - 非激活标签："病害信息"，透明背景，灰色文字 #9ca3af
  - 标签间距：水平间距 24px，底部间距 20px
- **审批记录区域**：
  - 区域标题："审批记录"，白色文字，16px 粗体，居中显示
  - 表格背景：深蓝色半透明背景 #1e40af80
  - 表头样式：深蓝色背景 #1e3a8a，白色文字，14px 粗体，居中对齐
  - 表头列：8列（序号、审批环节、处理人、审批状态、审批意见、处理人部门、接收时间、办理时间）
  - 数据行样式：深蓝色背景，白色文字，12px
  - 行高：60px，内边距 12px（适应多行内容）
  - 边框：白色细边框 1px，分隔各行各列
  - 序号列：数字格式（1、2、3），居中对齐
  - 审批环节列：文本内容（开始申请、养护项目审批(一级)、养护项目审批(二级)），左对齐
  - 处理人列：真实姓名（黄昭言、刘雨桐、罗颖秋），居中对齐
  - 审批状态列：第一行显示"通过"，其他行为空，居中对齐
  - 审批意见列：第一行显示"无异议"，其他行为空，左对齐
  - 处理人部门列：第一行"养护公司"，第二行"XXX部门"，第三行为空，左对齐
  - 接收时间列：统一时间"2025-09-18 10:43"，居中对齐
  - 办理时间列：统一时间"2025-09-18 10:43"，居中对齐
- **审批处理区域**：
  - 区域标题："审批处理"，白色文字，16px 粗体，左对齐
  - 处理意见标签："*处理意见:"，白色文字，14px，红色星号标识必填
  - 文本框样式：深蓝色边框 #3b82f6，深色背景 #374151，白色文字
  - 占位符文字："请输入"，灰色 #9ca3af，左上角对齐
  - 文本框尺寸：宽度100%，高度120px，圆角 6px，内边距 12px
  - 底部按钮区域：右对齐，按钮间距 12px
  - 退回按钮：深蓝色边框 #1e40af，透明背景，白色文字，圆角 6px
  - 通过按钮：蓝色背景 #3b82f6，白色文字，圆角 6px，内边距 8px 24px

### 组件清单
1. **模态弹窗容器组件**
   - 深蓝色渐变背景，全屏遮罩
   - 居中显示，支持ESC键关闭
   - 垂直滚动支持，适应长内容高度
2. **弹窗标题栏组件**
   - 标题文字："审批"，左侧显示
   - 关闭按钮：白色⊗符号，右上角固定位置，点击关闭弹窗
3. **标签切换组件**
   - 2个标签：病害信息、审批信息
   - 激活状态：深蓝色底边线，白色文字
   - 非激活状态：透明背景，灰色文字
   - 点击切换显示对应内容区域
4. **审批记录表格组件**
   - 区域标题："审批记录"，居中显示
   - 8列数据表格：完整的审批流程记录
   - 3行审批记录：开始申请、一级审批、二级审批
   - 表头样式：深蓝色背景，白色粗体文字
   - 数据行：深蓝色背景，白色文字，支持多行内容
   - 审批人员：黄昭言、刘雨桐、罗颖秋
   - 状态显示：第一行"通过"状态，其他待处理
5. **审批处理表单组件**
   - 区域标题："审批处理"
   - 处理意见文本框：必填字段，红色星号标识
   - 占位符提示："请输入"
   - 多行文本输入，支持长文本内容
6. **底部操作栏组件**
   - 2个操作按钮：退回、通过
   - 退回按钮：边框样式，用于驳回审批
   - 通过按钮：填充样式，用于批准审批
   - 右下角对齐布局

### 交互功能
- **弹窗控制**：点击关闭按钮或ESC键关闭弹窗，点击遮罩层不关闭
- **标签切换**：
  - 点击"病害信息"标签切换到病害详情视图
  - 点击"审批信息"标签显示当前审批记录视图
- **表格显示**：
  - 审批记录表格显示完整的审批流程
  - 支持水平滚动，适应窄屏显示
  - 显示已完成和待处理的审批环节
- **审批操作**：
  - 处理意见框：支持多行文本输入，必填验证
  - 退回按钮：点击驳回当前审批，需要填写处理意见
  - 通过按钮：点击批准审批，可选填处理意见
  - 表单验证：提交前验证必填字段
- **数据更新**：
  - 审批操作后更新审批记录表格
  - 实时显示审批状态变化
  - 自动记录审批时间和处理人信息
- **响应式布局**：适配不同屏幕尺寸，保持良好的视觉效果

---

## 图片31 (image31.jpg) - 桥梁养护维修项目管理页面

### 界面结构布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐                                     │
│ │ A 桥梁养护维修   │ │ 🚇 隧道养护维修   │                                     │
│ └─────────────────┘ └─────────────────┘                                     │
│                                                                             │
│ ┌─────────────┐ ┌──────────┐                                               │
│ │ 养护/保洁项目 │ │ 应急维修  │                                               │
│ └─────────────┘ └──────────┘                                               │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │  ┌───────────┐  ┌───────────┐  ┌───────────┐  ┌───────────┐  ┌───────────┐ │ │
│ │  │按项目名称  │  │按项目类型  │  │按项目状态  │  │按是否超期  │  │按养护单位  │ │ │
│ │  └───────────┘  └───────────┘  └───────────┘  └───────────┘  └───────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌───┬──────────────┬────────┬────────┬─────────┬──────────┬──────────┬─────┬─────────┬────────┬────────────┐│
│ │序号│项目名称       │项目类型 │项目状态 │项目任务  │项目开始   │项目结束   │是否 │养护单位  │负责人   │操作         ││
│ │   │              │        │        │完成量   │时间      │时间      │超期 │         │        │            ││
│ ├───┼──────────────┼────────┼────────┼─────────┼──────────┼──────────┼─────┼─────────┼────────┼────────────┤│
│ │ 1 │XXXXXX9月养护项目│月度养护 │待启动   │0/32     │2025-09-01│2025-09-30│否   │XXX单位   │张三     │详情         ││
│ ├───┼──────────────┼────────┼────────┼─────────┼──────────┼──────────┼─────┼─────────┼────────┼────────────┤│
│ │ 2 │XXXXXX项目     │保洁项目 │进行中   │30/56    │2025-08-01│2025-08-30│否   │XXX单位   │张三     │详情 延期申请││
│ ├───┼──────────────┼────────┼────────┼─────────┼──────────┼──────────┼─────┼─────────┼────────┼────────────┤│
│ │ 3 │XXXXXX项目     │应急养护 │超期     │12/56    │2025-08-01│2025-08-30│否   │XXX单位   │张三     │详情 延期申请││
│ ├───┼──────────────┼────────┼────────┼─────────┼──────────┼──────────┼─────┼─────────┼────────┼────────────┤│
│ │ 4 │XXXXXX项目     │月度养护 │已完成   │55/55    │2025-08-01│2025-08-30│是   │XXX单位   │张三     │详情         ││
│ ├───┼──────────────┼────────┼────────┼─────────┼──────────┼──────────┼─────┼─────────┼────────┼────────────┤│
│ │ 5 │XXXXXX项目     │月度养护 │超期完成 │56/56    │2025-08-01│2025-08-30│否   │XXX单位   │张三     │详情         ││
│ └───┴──────────────┴────────┴────────┴─────────┴──────────┴──────────┴─────┴─────────┴────────┴────────────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 样式布局说明（基于图1样式风格）
- **整体界面设计**：
  - 主背景：深蓝色渐变背景 #1e3a8a 到 #1e40af，与图1保持一致的深蓝主题
  - 界面类型：全屏主页面，内容区域居中显示
  - 整体色调：深蓝色主题，白色文字，保持图1的专业感
  - 内容布局：垂直布局，包含主导航、二级导航、筛选、表格四个区域
- **顶部主导航标签区域**：
  - 主标签：2个标签按钮（桥梁养护维修、隧道养护维修）
  - 激活标签："A 桥梁养护维修"，深蓝色背景 #1e40af，白色文字，圆角 8px
  - 非激活标签："🚇 隧道养护维修"，透明背景，深蓝色边框 #3b82f6，白色文字
  - 标签图标：A字母图标、隧道🚇图标，14px 图标
  - 标签间距：水平间距 12px，采用图1的间距规范
- **二级导航标签区域**：
  - 子标签：2个标签按钮（养护/保洁项目、应急维修）
  - 激活标签："养护/保洁项目"，深蓝色底边线 #3b82f6，白色文字，16px
  - 非激活标签："应急维修"，透明背景，灰色文字 #9ca3af
  - 标签间距：水平间距 24px，底部间距 16px，沿用图1设计
- **筛选区域**：
  - 背景：深灰色背景 #374151，圆角 8px，内边距 16px，与图1筛选区域一致
  - 筛选框：5个输入框，深蓝色背景 #1e40af，白色文字
  - 筛选框标题：按项目名称、按项目类型、按项目状态、按是否超期、按养护单位，12px 白色文字
  - 输入框样式：深蓝色边框 #3b82f6，白色占位符文字，圆角 4px
  - 筛选框布局：5个筛选框等宽分布，保持图1的整齐布局
- **数据表格区域**：
  - 表格背景：深蓝色半透明背景 #1e40af80，与图1表格风格统一
  - 表头样式：深蓝色背景 #1e3a8a，白色文字，14px 粗体，居中对齐
  - 表头列：11列（序号、项目名称、项目类型、项目状态、项目任务完成量、项目开始时间、项目结束时间、是否超期、养护单位、负责人、操作）
  - 数据行样式：交替行背景（深蓝色 #1e40af20 和透明），白色文字，12px
  - 行高：48px，内边距 12px，保持图1的行高规范
  - 边框：白色细边框 1px，分隔各行各列，与图1边框样式一致
  - 序号列：数字格式（1、2、3、4、5），居中对齐
  - 项目名称列：项目名称"XXXXXX9月养护项目"、"XXXXXX项目"，左对齐
  - 项目类型列：类型信息（月度养护、保洁项目、应急养护），居中对齐
  - 项目状态列：状态信息（待启动、进行中、超期、已完成、超期完成），居中对齐
  - 任务完成量列：进度格式（0/32、30/56、12/56、55/55、56/56），蓝色数字，居中对齐
  - 时间列：日期格式"2025-09-01"到"2025-09-30"，居中对齐
  - 是否超期列："是"或"否"，居中对齐
  - 养护单位列："XXX单位"，左对齐
  - 负责人列："张三"，居中对齐
  - 操作列：蓝色链接按钮"详情"、"延期申请"，居中对齐，可点击

### 组件清单
1. **主导航标签组件**
   - 2个主标签：桥梁养护维修（带A图标）、隧道养护维修（带🚇图标）
   - 激活状态：深蓝色背景，白色文字，采用图1的激活样式
   - 非激活状态：透明背景，深蓝色边框，与图1保持一致
2. **二级导航标签组件**
   - 2个子标签：养护/保洁项目、应急维修
   - 激活状态：深蓝色底边线，白色文字，沿用图1设计
   - 非激活状态：透明背景，灰色文字
3. **筛选表单组件**
   - 5个筛选输入框：按项目名称、按项目类型、按项目状态、按是否超期、按养护单位
   - 输入框样式：深蓝色背景，白色文字，参考图1的输入框设计
   - 布局方式：水平等宽分布，与图1筛选区域布局一致
4. **数据表格组件**
   - 11列表格：完整的项目管理信息
   - 5行数据记录：不同类型的养护项目
   - 表头样式：深蓝色背景，白色粗体文字，采用图1的表头风格
   - 数据行：交替背景色，白色文字，统一行高，与图1表格样式一致
   - 进度显示：任务完成量用蓝色数字突出显示
   - 操作列：蓝色链接按钮，支持详情查看和延期申请

### 交互功能
- **标签切换**：
  - 主标签：点击"隧道养护维修"切换到隧道管理页面
  - 二级标签：点击"应急维修"切换到应急维修列表
- **筛选功能**：
  - 5个筛选输入框：支持文本输入和选择筛选
  - 实时筛选：输入内容后自动刷新表格数据
  - 多条件组合：支持多个筛选条件同时生效
- **表格操作**：
  - 详情按钮：点击查看项目详细信息
  - 延期申请按钮：针对进行中或超期项目，点击申请延期
  - 表格排序：点击表头支持按各字段排序
  - 行悬停：鼠标悬停时行背景色变化
- **数据显示**：
  - 进度显示：任务完成量以分数形式显示（已完成/总数）
  - 状态标识：不同项目状态用不同颜色区分
  - 超期提醒：超期项目特殊标识和颜色提示
- **响应式布局**：适配不同屏幕尺寸，保持图1的响应式设计风格
- **数据加载**：支持异步加载，显示加载状态和空数据提示，与图1保持一致

---

## 图片32 (image32.jpg) - 延期申请弹窗界面（深蓝色主题）

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                         深蓝色渐变背景 (#1e3a8a → #1e40af)                                                    │
│                                                                                                                                 │
│     ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐     │
│     │                                                                                                                     │     │
│     │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐  │     │
│     │  │  延期申请                                                                                              ⊗  │  │     │
│     │  ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────┤  │     │
│     │  │                                                                                                             │  │     │
│     │  │  ℹ️ 延期申请                                                                                               │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  项目开始时间 *                              项目结束时间 *                                              │  │     │
│     │  │  ┌─────────────────────────────┐            ┌─────────────────────────────┐                              │  │     │
│     │  │  │ 2025-07-01                 📅│            │ 2025-07-30                 📅│                              │  │     │
│     │  │  └─────────────────────────────┘            └─────────────────────────────┘                              │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  延期结束时间 *                                                                                           │  │     │
│     │  │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐  │  │     │
│     │  │  │ 请输入项目名称                                                                                         │  │  │     │
│     │  │  └─────────────────────────────────────────────────────────────────────────────────────────────────────┘  │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  延期说明 *                                                                                               │  │     │
│     │  │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │ xxxxx                                                                                                 │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  └─────────────────────────────────────────────────────────────────────────────────────────────────────┘  │  │     │
│     │  │                                                                                                             │  │     │
│     │  │                                            ┌─────────┐                                                    │  │     │
│     │  │                                            │  ✓ 提交  │                                                    │  │     │
│     │  │                                            └─────────┘                                                    │  │     │
│     │  │                                                                                                             │  │     │
│     │  └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘  │     │
│     │                                                                                                                     │     │
│     └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘     │
│                                                                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 界面设计风格（采用图片1深蓝色主题）：

#### 32.1 整体界面设计
- **背景遮罩**：深蓝色渐变背景 (#1e3a8a → #1e40af)，与图1完全一致
- **弹窗容器**：深蓝色背景 (#1e40af)，圆角 12px，居中显示
- **阴影效果**：深色阴影，增强层次感
- **整体色调**：深蓝色主题，白色文字系统

#### 32.2 弹窗标题栏
- **背景**：深蓝色 (#1e3a8a)，与图1标题栏保持一致
- **标题文字**："延期申请"，白色 (#ffffff)，16px，粗体，左对齐
- **关闭按钮**：白色圆形 ⊗ 符号，18px，右上角，悬停变红色

#### 32.3 信息提示区域  
- **图标**：ℹ️ 信息图标，蓝色主题
- **文字**："延期申请"，白色 (#ffffff)，14px，与图标左对齐
- **功能**：明确弹窗用途，与图1风格保持一致

#### 32.4 表单字段区域
- **标签样式**：
  - 字体：白色 (#ffffff)，14px，粗体
  - 必填标识：红色星号 (#ef4444)，位置在标签后
  - 左对齐显示，与图1表单标签保持一致

- **项目开始时间***：
  - 输入框背景：深灰色 (#4b5563)，与图1输入框风格一致
  - 文字颜色：白色 (#ffffff)
  - 边框：圆角 6px，深色边框
  - 日历图标：白色 📅，右侧显示
  - 布局：左侧50%宽度

- **项目结束时间***：
  - 样式与项目开始时间完全一致
  - 布局：右侧50%宽度，与开始时间并排

- **延期结束时间***：
  - 输入框：深灰色背景 (#4b5563)，白色文字
  - 占位符："请输入项目名称"，灰色 (#9ca3af)
  - 全宽显示，圆角 6px

- **延期说明***：
  - 多行文本框：深灰色背景 (#4b5563)，白色文字
  - 最小高度：120px，可调整大小
  - 当前内容："xxxxx"，白色显示
  - 圆角 6px，与图1多行文本框风格一致

#### 32.5 操作按钮区域
- **提交按钮**：
  - 背景：蓝色渐变 (#3b82f6)，与图1按钮风格一致
  - 文字：白色 (#ffffff)，14px，粗体
  - 图标：白色勾选 ✓，左侧显示
  - 尺寸：120px x 40px，圆角 6px
  - 位置：居中显示
  - 悬停效果：亮度增加 110%

### 颜色风格规范（完全采用图片1风格）：
- **主背景色**：深蓝色渐变 (#1e3a8a → #1e40af)
- **弹窗背景**：深蓝色 (#1e40af)
- **文字颜色**：白色 (#ffffff)
- **输入框背景**：深灰色 (#4b5563)
- **输入框文字**：白色 (#ffffff)
- **占位符文字**：灰色 (#9ca3af)
- **必填标识**：红色 (#ef4444)
- **按钮背景**：蓝色 (#3b82f6)
- **边框颜色**：深灰色 (#374151)

### 组件复用（与图1保持一致）：
1. **深蓝色模态弹窗容器**：与图1详情弹窗相同风格
2. **深色主题表单控件**：输入框、文本域、按钮样式统一
3. **白色文字系统**：标签、输入文字、按钮文字一致
4. **统一圆角规范**：6px圆角，与图1保持一致
5. **一致的间距系统**：基于16px网格的间距布局

---

## 总结

本文档完整绘制了桥梁养护管理系统32个界面的ASCII结构图，涵盖了：

### 主要功能模块
1. **项目管理**：新增、编辑、查看养护项目（图1-18）
2. **执行管理**：项目执行状态跟踪（图19-28）
3. **审批管理**：多级审批流程（图29-31）
4. **系统管理**：延期申请等（图32）

### 项目类型
- **养护项目**：月度养护、定期维护
- **保洁项目**：桥梁清洁保养
- **应急项目**：紧急维修处理
- **竣工项目**：项目验收管理

### 界面特点
- **统一布局**：顶部导航+选项卡+内容区+操作按钮
- **表格展示**：数据列表采用表格形式，支持筛选分页
- **弹窗交互**：详情查看和编辑操作使用模态弹窗
- **流程管控**：多步骤向导式操作，审批流程可视化

这套系统界面设计完整、功能全面，体现了现代化桥梁养护管理的专业需求。
