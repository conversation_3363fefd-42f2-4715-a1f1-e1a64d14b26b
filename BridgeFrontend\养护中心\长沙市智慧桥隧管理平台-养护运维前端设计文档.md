# 长沙市智慧桥隧管理平台-养护运维前端设计文档

## 1. 系统概述

### 1.1 文档说明
本文档基于《养护运维.md》功能需求和《桥梁养护管理系统界面结构图.md》原型结构，详细阐述长沙市智慧桥隧管理平台养护运维模块的前端设计实现方案。

### 1.2 功能模块结构
```
养护运维
├── 养护项目
│   ├── 月度养护（公司业务人员）
│   ├── 保洁项目（公司业务人员）
│   ├── 应急养护（公司业务人员）
│   └── 预防养护（桥隧监管人员）
├── 养护维修
│   ├── 养护/保洁项目
│   └── 应急维修
├── 养护维修审核
│   ├── 公司管理员初审
│   └── 桥隧中心复核
└── 延期申请
    └── 项目延期处理
```

### 1.3 系统设计风格
采用深蓝色主题设计，确保界面一致性：
- **主色调**：深蓝色系 (#1e3a8a, #3b82f6)
- **背景色**：深蓝色渐变背景 (#1e3a8a → #1e40af)
- **文字颜色**：白色 (#ffffff)
- **状态色彩**：蓝色（草稿）、黄色（审批中）、绿色（通过）、红色（拒绝）
- **按钮样式**：蓝色实心按钮、深色边框按钮、红色删除按钮
- **表格样式**：深色背景表格，白色边框分隔线
- **弹窗样式**：深蓝色背景弹窗，白色关闭按钮

---

## 2. 养护项目模块

### 2.1 养护项目主列表页（图片1 - image1.jpg）

#### 2.1.1 界面结构
**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片1

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ [🏗️ 桥梁养护] [🚇 隧道养护]                                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ [项目名称▼] [项目类型▼] [状态│全部▼] 时间 2025/09/01 至 2025/09/01▼ [查询][重置] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 新增养护项目]                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│项目名称│项目类型│状态│开始日期│结束日期│养护单位│负责人│操作              │
│001│XXXXXX项目│月度养护│[草稿]│2025/09/01│2025/09/30│长沙市桥梁管理处│吴知非│修改 删除│
│002│XXXXXX项目│月度养护│[审批中]│2025/09/01│2025/09/30│长沙市桥梁管理处│郭照临│查看 审批│
│003│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│李慕桔│查看    │
│004│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│林文龙│查看    │
│005│XXXXXX项目│月度养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│高枕书│查看    │
│006│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│徐桧桐│查看    │
│007│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│何叔川│查看    │
│008│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│郭云舟│查看    │
│009│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│黄梓航│查看    │
│010│XXXXXX项目│保洁项目│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│赵景深│查看    │
│011│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│黄梓航│查看    │
│012│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│赵铁柱│查看    │
│013│XXXXXX项目│应急养护│[审批通过]│2025/09/01│2025/09/30│长沙市桥梁管理处│吴海燕│查看    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                  < [1] 2 3 4 5 >            │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 2.1.2 功能设计

**页面用途**：养护项目的主入口页面，展示所有养护项目的统一管理界面

**核心功能点**：
1. **双标签导航**：桥梁养护/隧道养护，切换不同基础设施类型
2. **多维度筛选**：项目名称、项目类型、状态、时间范围组合查询
3. **项目类型支持**：月度养护、保洁项目、应急养护、预防养护
4. **状态管理**：草稿、审批中、审批通过、审批拒绝四种状态
5. **权限控制**：根据用户角色显示不同操作按钮

**数据联动关系**：
- 新增项目 → 跳转至月度养护基本信息页（图片2）
- 修改项目 → 根据项目类型跳转不同编辑页面
- 查看项目 → 只读模式查看项目详情
- 审批项目 → 跳转审批页面（图片19）

#### 2.1.3 组件设计

**1. 顶部导航标签组件**
- 功能：桥梁/隧道切换
- 数据源：从路由参数或用户偏好获取
- 交互：点击切换，重新加载对应数据

**2. 筛选表单组件**
- 字段：项目名称、项目类型、状态、时间范围
- 验证：时间范围校验，开始时间不能大于结束时间
- 联动：项目类型影响状态选项

**3. 数据表格组件**
- 列配置：序号、项目名称、项目类型、状态、开始日期、结束日期、养护单位、负责人、操作
- 排序：支持时间字段排序
- 状态渲染：不同状态显示不同颜色标签

**4. 操作按钮组件**
- 新增：固定显示蓝色主按钮
- 修改/删除：仅草稿状态显示
- 查看：所有状态均显示
- 审批：仅审批中状态且有权限用户显示

**5. 分页组件**
- 样式：蓝色主题
- 功能：页码跳转、上下页切换
- 数据：每页显示条数可配置

### 2.2 月度养护项目创建流程

#### 2.2.1 基本信息页（图片2 - image2.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片2

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ *项目名称:                          │ *项目类型:                            │
│ [请输入                           ] │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *项目开始时间:                      │ *项目结束时间:                        │
│ [选择时间                         ] │ [选择时间                  ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *管理单位:                          │ 监理单位:                             │
│ [请选择                    ▼]      │ [请输入                    ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *养护单位:                          │ *项目负责人:                          │
│ [请选择                    ▼]      │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *联系方式:                          │                                       │
│ [输入框                           ] │                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目内容:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 附件:                                                                       │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                            ☁️                                            │ │
│ │                    将文件拖拽或点击上传                                   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                      [保存] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **步骤导航**：4步骤流程的第一步，展示进度
- **表单验证**：必填字段标红星，实时验证
- **数据联动**：选择养护单位后自动加载对应负责人列表
- **文件上传**：支持拖拽上传，预览已上传文件

**数据流向**：
- 保存 → 暂存草稿到后台，保持当前页面
- 下一步 → 保存数据并跳转至养护项目配置页（图片3）

#### 2.2.2 养护项目配置页（图片3 - image3.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片3

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **动态列表**：可添加多个养护项目，支持删除
- **项目名称**：每行一个养护项目名称输入框
- **预设选项**：提供常用养护项目快速选择
- **数据验证**：防止重复添加相同项目

**数据流向**：
- 上一步 → 返回基本信息页，保持已填写数据
- 下一步 → 跳转至病害养护页（图片4）

#### 2.2.3 病害养护配置页（图片4 - image4.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片4

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [关联病害]                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│病害编号│病害部位│病害类型│病害数量│病害描述│操作                │
│ 1 │XXXXXX大桥│989│伸缩缝│伸缩缝缺失│7│XXXXXXX│删除                        │
│ 2 │XXXXXX大桥│988│伸缩缝│伸缩缝缺失│47│XXXXXXX│删除                       │
│ 3 │XXXXXX大桥│987│照明设施│照明设施缺失│42│XXXXXXX│删除                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                ────────────────              │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **病害关联**：点击"关联病害"按钮打开病害选择弹窗
- **病害展示**：表格显示已关联的病害信息
- **删除功能**：支持取消关联特定病害
- **数据来源**：从病害管理系统获取可关联的病害记录

**数据流向**：
- 关联病害 → 打开病害选择弹窗，支持多选
- 下一步 → 跳转至养护桥梁配置页（图片5）

#### 2.2.4 养护桥梁配置页（图片5 - image5.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片5

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护] [养护桥梁]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ [关联桥梁]                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│桥梁编号│所在道路│管理单位│养护内容│养护人员│操作                │
│ 1 │XXXXXX大桥│CS-B-001│枫林一路│桥隧中心│排水系统养护│黄昭言│删除            │
│ 2 │XXXXXX大桥│CS-B-002│枫林一路│桥隧中心│上部结构养护│刘雨桐│删除            │
│ 3 │XXXXXX大桥│CS-B-003│枫林一路│桥隧中心│排水系统养护│罗砚秋│删除            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **桥梁关联**：点击"关联桥梁"按钮打开桥梁选择弹窗（图片6）
- **综合信息**：展示桥梁基本信息和养护配置
- **最终提交**：完成所有配置后提交项目审核
- **表格结构**：序号│桥梁名称│桥梁编号│所在道路│管理单位│养护内容│养护人员│操作

**数据流向**：
- 关联桥梁 → 打开桥梁选择弹窗（图片6）
- 提交 → 项目状态变更为"审批中"，进入审批流程

#### 2.2.5 关联桥梁选择弹窗（图片6 - image6.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片6

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                       ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☐│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位                  │
│☐│XXXXXX大桥│黄昭言│1│XXX大桥│CS-B-001│枫林一路│桥隧中心                   │
│☐│XXXXXX大桥│刘雨桐│2│刘雨桐│CS-B-002│枫林一路│桥隧中心                    │
│☐│XXXXXX大桥│罗砚秋│3│罗砚秋│CS-B-003│枫林一路│桥隧中心                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                            [确定] [取消]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **搜索过滤**：支持按桥梁名称或编号搜索
- **多选功能**：复选框支持选择多个桥梁
- **数据展示**：显示桥梁基本信息和已配置的养护信息
- **弹窗操作**：确定按钮确认选择，取消按钮关闭弹窗

### 2.3 保洁项目创建流程

#### 2.3.1 保洁项目基本信息页（图片7 - image7.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片7

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ *项目名称:                          │ *项目类型:                            │
│ [请输入                           ] │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *项目开始时间:                      │ *项目结束时间:                        │
│ [选择时间                         ] │ [选择时间                  ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *管理单位:                          │ 监理单位:                             │
│ [请选择                    ▼]      │ [请输入                    ]          │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *养护单位:                          │ *项目负责人:                          │
│ [请选择                    ▼]      │ [请选择                    ▼]        │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ *联系方式:                          │                                       │
│ [输入框                           ] │                                       │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 工作量:                             │                                       │
│ [请输入                           ] │                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                      [保存] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **简化流程**：保洁项目采用3步流程（基本信息→保洁项目→保洁桥梁）
- **工作量字段**：增加工作量输入，用于保洁工作计算
- **表单字段**：与月度养护基本信息页字段类似，但增加工作量字段

**数据流向**：
- 保存 → 暂存草稿到后台，保持当前页面
- 下一步 → 保存数据并跳转至保洁项目配置页（图片8）

#### 2.3.2 保洁项目配置页（图片8 - image8.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片8

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                    ] [7] 天/1次               [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                    ] [30] 天/1次              [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **保洁频次**：每个保洁项目增加频次配置（X天/1次）
- **频次输入**：数字输入框+单位后缀组合
- **项目管理**：支持添加多个保洁项目，每个项目独立设置频次

**数据流向**：
- 上一步 → 返回基本信息页，保持已填写数据
- 下一步 → 跳转至保洁桥梁配置页（图片9）

#### 2.3.3 保洁桥梁配置页（图片9 - image9.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片9

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [保洁项目] [保洁桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [🌉 关联桥梁]                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称│桥梁编号│所在道路│管理单位│保洁内容│保洁人员│操作                │
│ 1 │XXXXXX大桥│CS-B-001│枫林一路│桥隧中心│排水系统养护│黄昭言│删除            │
│ 2 │XXXXXX大桥│CS-B-002│枫林一路│桥隧中心│上部结构养护│刘雨桐│删除            │
│ 3 │XXXXXX大桥│CS-B-003│枫林一路│桥隧中心│排水系统养护│罗砚秋│删除            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **保洁桥梁配置**：保洁项目的第三步，配置具体的保洁桥梁信息
- **关联桥梁操作**：点击"🌉 关联桥梁"按钮打开保洁桥梁选择弹窗（图片10）
- **保洁内容字段**：显示分配给每个桥梁的保洁内容（排水系统养护、上部结构养护等）
- **保洁人员字段**：显示分配给每个桥梁的保洁人员姓名
- **表格结构**：序号│桥梁名称│桥梁编号│所在道路│管理单位│保洁内容│保洁人员│操作
- **删除功能**：支持删除已关联的桥梁

**数据流向**：
- 关联桥梁 → 打开保洁桥梁选择弹窗（图片10）
- 提交 → 完成保洁项目创建，状态变更为"审批中"

#### 2.3.4 保洁桥梁关联弹窗（图片10 - image10.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片10

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ✕    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                       ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位                  │
│☑│XXXXXX大桥│黄昭言│1│XXX大桥│CS-B-001│枫林一路│桥隧中心                   │
│☑│XXXXXX大桥│刘雨桐│2│刘雨桐│CS-B-002│枫林一路│桥隧中心                    │
│☑│XXXXXX大桥│罗砚秋│3│罗砚秋│CS-B-003│枫林一路│桥隧中心                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                            [确定] [取消]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **桥梁搜索功能**：支持按桥梁名称或编号搜索过滤
- **多选功能**：复选框支持选择多个桥梁，已选中状态显示☑
- **保洁信息显示**：显示养护项目和养护人员信息
- **表格结构**：☑│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位
- **弹窗操作**：确定按钮确认选择，取消按钮关闭弹窗

### 2.4 应急养护项目创建流程

#### 2.4.1 应急养护基本信息页（图片11 - image11.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片11

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目名称:                          │ 项目类型:                            │
│ [请输入                         ] │ [请选择                           ▼] │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 项目开始时间:                      │ 项目结束时间:                        │
│ [选择时间                       ▼] │ [选择时间                         ▼] │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 管理单位:                          │ 监理单位:                             │
│ [请选择                         ▼] │ [请输入                           ]  │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 养护单位:                          │ 项目负责人:                          │
│ [请选择                         ▼] │ [请选择                           ▼] │
├─────────────────────────────────────┼───────────────────────────────────────┤
│ 联系方式:                          │ 工作量:                              │
│ [                               ] │ [请输入                           ]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 项目内容:                                                                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                   │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ 附件:                                                                      │
│ ┌ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┐ │
│ │                           📁                                           │ │
│ │                    将文件拖拽或点击上传                                   │ │
│ └ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ─ ┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                          [保存] [下一步]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **简化流程**：应急养护项目简化为3步流程（基本信息→应急项目→养护桥梁）
- **去除病害环节**：应急养护不需要病害关联步骤，直接配置应急处理项目
- **强调紧急性**：项目类型默认为应急养护，强调快速响应和处理
- **工作量字段**：增加工作量输入，用于应急工作计算

#### 2.4.2 应急项目配置页（图片12 - image12.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片12

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [+ 添加项目]                                                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [排水养护                                                           ] [取消] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [下一步]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **应急项目管理**：添加应急处理项目，重点关注紧急养护内容
- **快速配置**：简化项目配置流程，支持快速添加应急处理项目
- **项目删除**：支持删除不需要的应急项目

#### 2.4.3 应急项目养护桥梁配置页（图片13 - image13.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片13

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 新增项目                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [应急项目] [养护桥梁]                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ [🔗 关联桥梁]                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│桥梁名称    │桥梁编号  │所在道路│管理单位│保洁内容    │保洁人员│操作     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 1 │XXXXXX大桥  │CS-B-001 │枫林一路│桥梁中心│排水系统养护│黄明言  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 2 │XXXXXX大桥  │CS-B-002 │枫林一路│桥梁中心│上部结构养护│刘雨桐  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 3 │XXXXXX大桥  │CS-B-003 │枫林一路│桥梁中心│排水系统养护│罗现冰  │删除     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                              [保存] [上一步] [完成]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **应急桥梁配置**：应急养护项目的第三步，配置需要应急处理的桥梁
- **关联桥梁操作**：点击"🔗 关联桥梁"按钮打开应急桥梁选择弹窗（图片14）
- **保洁内容字段**：显示分配给每个桥梁的应急处理内容
- **保洁人员字段**：显示分配给每个桥梁的应急处理人员
- **表格结构**：序号│桥梁名称│桥梁编号│所在道路│管理单位│保洁内容│保洁人员│操作
- **完成按钮**：最后一步使用"完成"按钮而非"提交"

**数据流向**：
- 关联桥梁 → 打开应急桥梁选择弹窗（图片14）
- 完成 → 应急养护项目创建完成，状态变更为"审批中"

#### 2.4.4 应急项目关联桥梁弹窗（图片14 - image14.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片14

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 关联桥梁                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [输入桥梁名称或编号                                                      ] │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│养护项目  │养护人员│序号│桥梁名称  │桥梁编号 │所在道路│管理单位              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│黄昭吉  │ 1 │XXX大桥   │CS-B-001│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│刘雨桐  │ 2 │刘雨桐    │CS-B-002│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│☑│XXXXXX大桥│罗硯秋  │ 3 │罗硯秋    │CS-B-003│枫林一路│桥隧中心              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                                                                             │
│                                                                             │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                            [确定] [取消]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **应急桥梁选择**：专门用于应急养护项目的桥梁选择弹窗
- **搜索过滤功能**：支持按桥梁名称或编号搜索
- **多选功能**：复选框支持选择多个需要应急处理的桥梁，已选中状态显示☑
- **应急信息显示**：显示养护项目和养护人员信息
- **表格结构**：☑│养护项目│养护人员│序号│桥梁名称│桥梁编号│所在道路│管理单位
- **弹窗操作**：确定按钮确认选择，取消按钮关闭弹窗

### 2.5 预防养护项目创建流程

#### 2.5.1 预防养护基本信息页（图片15 - image15.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片15

**功能特点**：
- **用户权限**：仅桥隧监管人员可创建
- **直接生效**：提交后直接生效，无需审批流程
- **包含步骤**：基本信息→关联病害→实施信息→竣工信息

#### 2.5.2 关联病害页（图片16 - image16.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片16

#### 2.5.3 实施信息页（图片17 - image17.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片17

#### 2.5.4 竣工信息页（图片18 - image18.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片18

### 2.6 项目审批功能

#### 2.6.1 审批页面（图片19 - image19.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片19

**功能设计**：
- **审批信息录入**：处置意见、审批结果（通过/退回）
- **审批流程**：公司业务人员提交→桥隧中心审批
- **状态变更**：审批通过后项目状态变为"审批通过"

#### 2.6.2 审批信息页（图片20 - image20.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片20

---

## 3. 养护维修模块

### 3.1 养护维修主页（图片21 - image21.jpg）

#### 3.1.1 界面结构
**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片21

```
┌────────────────────────────────────────────────────────────────────┐
│ [🏗️ 桥梁养护维修] [🚇 隧道养护维修]                                │
│ [养护/保洁项目] [应急维修]                                         │
│                                                                    │
│ [项目名称 ▼] [项目类型 ▼] [是否超期 ▼] [养护单位 ▼] [查询] [重置] │
│                                                                    │
│ 序号 │ 项目名称      │ 项目类型 │ 任务完成量 │ 开始日期   │ 结束日期   │ 是否超期 │ 养护单位         │ 负责人 │ 操作     │
│ 001  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 吴知非 │ 查看 延期申请 │
│ 002  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 郭照临 │ 查看 延期申请 │
│ 003  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 李慕桔 │ 查看 延期申请 │
│ 004  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 林文龙 │ 查看 延期申请 │
│ 005  │ XXXXXX项目    │ 月度养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 高枕书 │ 查看 延期申请 │
│ 006  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 否       │ 长沙市桥梁管理处 │ 徐桧桐 │ 查看         │
│ 007  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 何叔川 │ 查看         │
│ 008  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 郭云舟 │ 查看         │
│ 009  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 黄梓航 │ 查看         │
│ 010  │ XXXXXX项目    │ 保洁项目 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 赵景深 │ 查看         │
│ 011  │ XXXXXX项目    │ 应急养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 黄梓航 │ 查看         │
│ 012  │ XXXXXX项目    │ 应急养护 │ 0/32      │ 2025/09/01 │ 2025/09/30 │ 是       │ 长沙市桥梁管理处 │ 赵铁柱 │ 查看         │
│                                                                    │
│                                              < [1] 2 3 4 5 >      │
└────────────────────────────────────────────────────────────────────┘
```

#### 3.1.2 功能设计

**页面用途**：查看已审批通过的养护/保洁项目执行情况，支持延期申请

**核心功能点**：
1. **双级导航**：桥梁/隧道 + 养护项目/应急维修标签切换
2. **项目状态跟踪**：显示任务完成量（已完成/总数量）
3. **超期管理**：根据结束时间自动判断是否超期
4. **延期申请**：对未完成项目可发起延期申请

**项目状态规则**：
- **待启动**：项目中任务项没有提交记录
- **进行中**：项目中任务有1条及以上提交记录
- **超期**：项目结束日期小于当前时间
- **已完成**：项目中任务全部完成
- **超期完成**：任务全部完成但超过结束时间

**数据联动关系**：
- 查看 → 跳转项目详情弹窗（图片22-26）
- 延期申请 → 打开延期申请弹窗（图片32）

### 3.2 项目详情查看

#### 3.2.1 基本信息详情页（图片22 - image22.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片22

```
┌──────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                         ⊗   │
├──────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护]                                      │
├──────────────────────────────────────────────────────────────────────┤
│                                                                      │
│ 项目名称:                            项目类型:                       │
│ [XXXXXXXXXXXXX]                      [XXXXXXXXXXX]                   │
│                                                                      │
│ 项目开始时间:                        项目结束时间:                   │
│ [2025-09-18 10:34]                   [2025-09-18 10:34]             │
│                                                                      │
│ 管理单位:                            监理单位:                       │
│ [XXXXXXX]                            [XXXXXXXXX]                     │
│                                                                      │
│ 养护单位:                            项目负责人:                     │
│ [XXXXXXXXXXX]                        [张三]                          │
│                                                                      │
│ 联系方式:                            工作量:                         │
│ [XXXXXXXXXXX]                        [20]                            │
│                                                                      │
│ 项目内容:                                                            │
│ [XXXXXXXXXXX                                                       ] │
│ [                                                                  ] │
│ [                                                                  ] │
│                                                                      │
│ 附件:                                                                │
│                                                                      │
│                                                      [关闭]          │
└──────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **只读展示**：所有字段均为只读状态，展示项目基本信息
- **三标签切换**：基本信息、养护项目、病害养护（根据项目类型动态显示）

#### 3.2.2 养护项目详情页（图片23 - image23.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片23

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│ [基本信息] [养护项目] [病害养护]                                             │
│             ━━━━━━                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ [桥梁名称▼] [养护项目▼] [状态▼] [负责人▼] [查询] [重置]                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 完成量 12/32                                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│序号│  桥梁名称  │  养护项目  │ 状态 │ 负责人 │  联系方式  │   完成时间   │操作│
├───┼─────────────┼─────────────┼──────┼────────┼─────────────┼──────────────┼────┤
│ 1 │XXXXXX大桥  │排水系统养护 │未完成│黄昭言  │15820007394  │2025-09-18 10:43│详情│
│ 2 │XXXXXX大桥  │排水系统养护 │审核中│刘雨桐  │13122238579  │2025-09-18 10:43│详情│
│ 3 │XXXXXX大桥  │排水系统养护 │退回  │罗颖秋  │19620059483  │2025-09-18 10:43│详情│
│ 4 │XXXXXX大桥  │上部结构养护 │复核中│林文龙  │19607559483  │2025-09-18 10:43│详情│
│ 5 │XXXXXX大桥  │上部结构养护 │已完成│高枕书  │18557189483  │2025-09-18 10:43│详情│
│ 6 │XXXXXX大桥  │上部结构养护 │已完成│徐桧桐  │19020017495  │2025-09-18 10:43│详情│
│007│XXXXXX大桥  │上部结构养护 │已完成│何叔川  │19020017495  │2025-09-18 10:43│详情│
│008│XXXXXX大桥  │上部结构养护 │已完成│郭云舟  │19020017495  │2025-09-18 10:43│详情│
│009│XXXXXX大桥  │上部结构养护 │已完成│黄梓航  │19020017495  │2025-09-18 10:43│详情│
│010│XXXXXX大桥  │上部结构养护 │已完成│赵景深  │19020017495  │2025-09-18 10:43│详情│
├─────────────────────────────────────────────────────────────────────────────┤
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **任务完成量统计**：显示总体完成进度（12/32）
- **筛选查询**：支持按桥梁、项目、状态、负责人筛选
- **状态展示**：未完成、审核中、退回、复核中、已完成等状态
- **详情查看**：点击"详情"查看具体任务执行情况（图片24）

**任务状态流程**：
未完成 → 审核中 → 复核中 → 已完成
              ↓
             退回（需重新处理）

#### 3.2.3 养护项目详情弹窗（图片24 - image24.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片24

**功能设计**：
- **任务详情**：显示具体养护任务的执行详情
- **处置记录**：记录任务执行过程和结果
- **审批流程**：展示任务的审批状态和审批意见

#### 3.2.4 查看详情病害养护页（图片25 - image25.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片25

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 查看详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────┬─────────┬─────────┐                                             │
│ │基本信息 │ 养护项目 │病害养护 │                                             │
│ └─────────┴─────────┴─────────┘                                             │
│                       ▔▔▔▔▔▔▔▔                                             │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                           │
│ │ 桥梁名称 ▼  │ │ 病害类型 ▼  │ │ 状态    ▼  │  [查询]  [重置]           │
│ └─────────────┘ └─────────────┘ └─────────────┘                           │
│                                                                             │
│ 完成量 12/32                                                               │
│                                                                             │
│ ┌─────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┬─────┐      │
│ │序号 │桥梁名称  │病害编号  │病害部位  │病害类型  │完成时间  │负责人│操作 │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 1   │XXXXXX大桥│  989    │伸缩缝   │伸缩缝缺失│2025-09-18│王景深│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 2   │XXXXXX大桥│  988    │伸缩缝   │伸缩缝缺失│2025-09-18│刘志强│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤      │
│ │ 3   │XXXXXX大桥│  987    │照明设施 │照明设施缺失│2025-09-18│赵临洲│详情 │      │
│ │     │         │         │         │         │10:43    │     │     │      │
│ └─────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────┴─────┘      │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **病害养护详情查看**：项目详情弹窗的第三个标签页，专门展示病害养护相关信息
- **三标签导航**：基本信息、养护项目、病害养护，当前激活"病害养护"标签
- **筛选功能**：支持按桥梁名称、病害类型、状态进行筛选查询
- **完成量统计**：显示病害养护的完成进度（12/32）
- **病害表格**：序号│桥梁名称│病害编号│病害部位│病害类型│完成时间│负责人│操作
- **详情操作**：点击"详情"按钮查看具体病害详细信息（图片26）

**数据流向**：
- 详情按钮 → 打开病害详情页面（图片26）
- 关闭 → 返回养护维修主页

#### 3.2.5 病害详情页面（图片26 - image26.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片26

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 病害详情                                                               ⊗    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 病害信息                                                                    │
│                                                                             │
│ 桥梁/隧道名称:                   │ 病害部位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                 │                                       │
│ 病害类型:                       │ 病害编号:                              │
│ [XXXXXXXXX                    ] │ [XXXXXXXXX                      ]     │
│                                                                             │
│ 病害等级:                                                                   │
│ [12                           ]                                            │
│                                                                             │
│ 病害描述:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 上报人:                         │ 联系方式:                              │
│ [XXXXXX大桥                   ] │ [19321207394                    ]     │
│                                                                             │
│ 病害照片:                                                                   │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片1     │ │   照片2     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 处置信息                                                                    │
│                                                                             │
│ 养护单位:                       │ 管理单位:                              │
│ [XXXXXX大桥                   ] │ [XXXXXX部位                     ]     │
│                                                                             │
│ 处置人员:                       │ 联系方式:                              │
│ [刘三                         ] │ [19321207394                    ]     │
│                                                                             │
│ 处置时间:                                                                   │
│ [2025-09-18 10:34             ]                                            │
│                                                                             │
│ 处置说明:                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 请输入                                                                  │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 处置照片:                                                                   │
│ [现场照片] [人车照片] [处置前] [处置中] [处置后]                            │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐                                           │
│ │             │ │             │                                           │
│ │   照片3     │ │   照片4     │                                           │
│ │             │ │             │                                           │
│ └─────────────┘ └─────────────┘                                           │
│                                                                             │
│ 审核信息                                                                    │
│                                                                             │
│ ┌─────┬─────────┬─────┬─────┬─────┬─────────┬─────────┬─────────┐          │
│ │序号 │审批环节  │处理人│审批状态│审批意见│处理人部门│接收时间  │办结时间  │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 1   │开始申请  │高哲  │通过  │无异议│开办公司  │2025-09-18│2025-09-18│          │
│ │     │         │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 2   │养护项目审批│刘明明│     │     │XXX部门 │2025-09-18│2025-09-18│          │
│ │     │ (一级)   │     │     │     │         │10:43    │10:43    │          │
│ ├─────┼─────────┼─────┼─────┼─────┼─────────┼─────────┼─────────┤          │
│ │ 3   │养护项目审批│罗政权│     │     │         │2025-09-18│2025-09-18│          │
│ │     │ (二级)   │     │     │     │         │10:43    │10:43    │          │
│ └─────┴─────────┴─────┴─────┴─────┴─────────┴─────────┴─────────┘          │
│                                                                             │
│                                                              [关闭]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **病害详情展示**：显示具体病害的完整详细信息
- **病害信息区域**：桥梁名称、病害部位、病害类型、病害编号、病害等级、病害描述、上报人、联系方式
- **病害照片区域**：显示病害现场照片，支持照片查看和放大
- **处置信息区域**：养护单位、管理单位、处置人员、联系方式、处置时间、处置说明
- **处置照片区域**：5个照片类型标签（现场照片、人车照片、处置前、处置中、处置后），支持照片切换查看
- **审核信息区域**：审批流程表格，包含序号│审批环节│处理人│审批状态│审批意见│处理人部门│接收时间│办结时间
- **字段显示**：大部分字段为只读显示，处置说明可能支持编辑

**审核流程表格**：
- **表格结构**：8列表格显示完整审批链条
- **审批环节**：开始申请、养护项目审批（一级）、养护项目审批（二级）
- **流程跟踪**：记录每个环节的处理人、状态、意见、时间等信息
- **部门显示**：显示处理人所属部门

**功能特点**：
- **完整病害档案**：包含病害从发现到处置的完整信息链条
- **照片管理**：分类展示病害照片和处置照片，便于对比查看
- **处置跟踪**：详细记录处置过程和结果
- **审批流程**：完整的审批流程可追溯性
- **数据完整性**：确保病害处置的可追溯性

### 3.3 应急维修管理

#### 3.3.1 应急维修列表页（图片27 - image27.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片27

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏗️ 桥梁养护维修    🌉 隧道养护维修                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 养护/应急项目                   应急维修                                     │
│ ──────────────────────────────────────────────────────                 │
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │桥梁名称    ▼│ │状态      ▼  │ │病害类型   ▼ │ │负责人     ▼ │ │养护单位   ▼ │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│                                                                             │
│                                                        [查询]  [重置]      │
│                                                                             │
│ ┌─────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────────┬─────┐ │
│ │序号 │桥梁名称  │上报人   │上报时间  │联系方式  │状态     │病害编号  │病害部位  │病害类型  │病害数量  │负责人   │养护单位│操作 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │001  │XXXXXX项目│向文革   │2025/09/01│13720186495│待处理  │00001   │XXXXX部位│XXXXX类型│71       │吴烟维   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │002  │XXXXXX项目│罗子安   │2025/09/01│18720269485│已处理  │00002   │XXXXX部位│XXXXX类型│85       │郭昭临   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │003  │XXXXXX项目│江萍丹   │2025/09/01│15820007394│退回    │00003   │XXXXX部位│XXXXX类型│73       │李露慧   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │004  │XXXXXX项目│朱洋军   │2025/09/01│13720089685│已处理  │00004   │XXXXX部位│XXXXX类型│44       │林文龙   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │005  │XXXXXX项目│王若谦   │2025/09/01│17702048593│已处理  │00005   │XXXXX部位│XXXXX类型│70       │高欣书   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │006  │XXXXXX项目│林司欣   │2025/09/01│16657168395│已处理  │00006   │XXXXX部位│XXXXX类型│39       │徐敏倩   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │007  │XXXXXX项目│郑晨达   │2025/09/01│13275528394│已处理  │00007   │XXXXX部位│XXXXX类型│43       │何敏川   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │008  │XXXXXX项目│张格高   │2025/09/01│14557139685│已处理  │00008   │XXXXX部位│XXXXX类型│1        │郭云舟   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │009  │XXXXXX项目│辛建军   │2025/09/01│18901079463│已处理  │00009   │XXXXX部位│XXXXX类型│2        │黄怡航   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │010  │XXXXXX项目│赵妍红   │2025/09/01│19057187395│已处理  │00010   │XXXXX部位│XXXXX类型│25       │赵晨涛   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │011  │XXXXXX项目│黄淑芬   │2025/09/01│15010086427│已处理  │00011   │XXXXX部位│XXXXX类型│99       │黄怡航   │长沙市桥梁管理处│查看 │ │
│ ├─────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────────┼─────┼─────┤ │
│ │012  │XXXXXX项目│陈丽华   │2025/09/01│14502156394│已处理  │00012   │XXXXX部位│XXXXX类型│92       │赵钰廷   │长沙市桥梁管理处│查看 │ │
│ └─────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────────┴─────┴─────┘ │
│                                                                             │
│                                                                             │
│                                    ◀  1  2  3  4  5  ▶                     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **应急维修展示**：显示桥隧中心判定的应急维修病害数据
- **多维度筛选**：桥梁名称、状态、病害类型、负责人、养护单位
- **状态管理**：待处理、处理审批中、退回、已处理
- **详细信息**：包含病害信息、上报信息、处置信息

**应急维修状态流程**：
- **待处理**：研判分派养护单位时的默认状态
- **处理审批中**：养护单位处理上报后，需要公司审批和桥隧中心复核
- **退回**：公司或桥隧中心审批退回
- **已完成**：桥隧中心复核通过

#### 3.3.2 应急维修详情弹窗（图片28 - image28.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片28

**功能设计**：
- **病害信息**：显示上报的病害基础信息（只读）
- **处置信息**：处置人员填写的处置记录
- **审批信息**：展示审批流程和审批意见

---

## 4. 养护维修审核模块

### 4.1 养护维修审核主页（图片29 - image29.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片29

**功能设计**：
- **双重角色**：公司管理员初审 + 桥隧中心复核
- **审核流程**：养护维修人员提交→公司管理员审核→桥隧中心复核
- **数据展示**：显示需要审核的养护维修和延期申请数据

**审核流程**：
```
养护维修人员提交 → 公司管理员初审 → 桥隧中心复核 → 完成
                       ↓              ↓
                     退回           退回
```

### 4.2 审批弹窗（图片30 - image30.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片30

**功能设计**：
- **审批意见录入**：文本框输入审批意见
- **审批结果选择**：通过/退回选择
- **审批流程记录**：记录审批历史和流转过程

---

## 5. 延期申请模块

### 5.1 延期申请主页（图片31 - image31.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片31

**功能设计**：
- **延期项目管理**：展示可延期申请的项目列表
- **申请条件**：非已完成、超期完成状态的项目可申请延期
- **多次延期**：支持对同一项目多次延期申请

### 5.2 延期申请弹窗（图片32 - image32.jpg）

**原型结构来源**：《桥梁养护管理系统界面结构图.md》图片32

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                         深蓝色渐变背景 (#1e3a8a → #1e40af)                                                    │
│                                                                                                                                 │
│     ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐     │
│     │                                                                                                                     │     │
│     │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────┐  │     │
│     │  │  延期申请                                                                                              ⊗  │  │     │
│     │  ├─────────────────────────────────────────────────────────────────────────────────────────────────────────────┤  │     │
│     │  │                                                                                                             │  │     │
│     │  │  ℹ️ 延期申请                                                                                               │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  项目开始时间 *                              项目结束时间 *                                              │  │     │
│     │  │  ┌─────────────────────────────┐            ┌─────────────────────────────┐                              │  │     │
│     │  │  │ 2025-07-01                 📅│            │ 2025-07-30                 📅│                              │  │     │
│     │  │  └─────────────────────────────┘            └─────────────────────────────┘                              │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  延期结束时间 *                                                                                           │  │     │
│     │  │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐  │  │     │
│     │  │  │ 请输入项目名称                                                                                         │  │  │     │
│     │  │  └─────────────────────────────────────────────────────────────────────────────────────────────────────┘  │  │     │
│     │  │                                                                                                             │  │     │
│     │  │  延期说明 *                                                                                               │  │     │
│     │  │  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────┐  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │ xxxxx                                                                                                 │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  │                                                                                                       │  │  │     │
│     │  │  └─────────────────────────────────────────────────────────────────────────────────────────────────────┘  │  │     │
│     │  │                                                                                                             │  │     │
│     │  │                                            ┌─────────┐                                                    │  │     │
│     │  │                                            │  ✓ 提交  │                                                    │  │     │
│     │  │                                            └─────────┘                                                    │  │     │
│     │  │                                                                                                             │  │     │
│     │  └─────────────────────────────────────────────────────────────────────────────────────────────────────────────┘  │     │
│     │                                                                                                                     │     │
│     └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘     │
│                                                                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

**功能设计**：
- **时间展示**：显示原项目开始时间和结束时间（只读）
- **延期时间设置**：设置新的项目结束时间
- **延期说明**：必填，说明延期原因
- **提交审核**：提交给桥隧中心审核
- **审核流程**：公司业务人员申请→桥隧中心审核→批准延期

---

## 6. 技术实现要点

### 6.1 前端技术栈
- **框架**：Vue.js 2.x + Element UI
- **状态管理**：Vuex
- **路由管理**：Vue Router
- **HTTP客户端**：Axios
- **构建工具**：Webpack + Vue CLI

### 6.2 组件架构设计

#### 6.2.1 通用组件库
设计可复用的基础组件，包括：
- **BaseTable** - 通用表格组件
- **BaseForm** - 通用表单组件
- **BaseModal** - 通用弹窗组件
- **BasePagination** - 通用分页组件
- **BaseSelect** - 通用选择器组件
- **BaseDatePicker** - 通用日期选择器
- **BaseFileUpload** - 通用文件上传组件
- **StatusTag** - 状态标签组件

#### 6.2.2 业务组件库
针对养护运维业务设计专用组件：
- **ProjectStep** - 项目步骤导航组件
- **ProjectFilter** - 项目筛选组件
- **ProjectTable** - 项目表格组件
- **ProjectForm** - 项目表单组件
- **BridgeSelector** - 桥梁选择器组件
- **DiseaseSelector** - 病害选择器组件
- **ApprovalForm** - 审批表单组件

#### 6.2.3 页面组件结构
- **项目管理页面**：列表页、创建页（多步骤）、详情页
- **维修管理页面**：维修列表页、应急维修页、详情页
- **审核管理页面**：审核列表页、审核详情页
- **延期申请页面**：延期列表页、申请页

### 6.3 状态管理设计

#### 6.3.1 状态管理结构

**State设计**：
- **项目相关**：项目列表、当前项目、项目类型、项目状态
- **维修相关**：维修列表、应急维修列表
- **审核相关**：审核列表
- **延期相关**：延期申请列表
- **基础数据**：桥梁列表、病害列表、组织机构列表、用户列表

**数据流向**：
- 通过Actions异步获取数据
- 通过Mutations同步更新状态
- 通过Getters获取计算后的状态

### 6.4 路由设计

**主路由结构**：
- `/maintenance` - 养护运维主入口
- `/maintenance/project` - 养护项目列表
- `/maintenance/project/create` - 新增养护项目
- `/maintenance/repair` - 养护维修列表
- `/maintenance/approval` - 养护维修审核
- `/maintenance/extension` - 延期申请

**路由特性**：
- 支持路由懒加载优化性能
- 支持页面缓存（keepAlive）
- 支持角色权限控制
- 支持面包屑导航

### 6.5 API设计

#### 6.5.1 接口设计规范

**项目管理接口**：
- `GET /maintenance/project/list` - 获取项目列表
- `POST /maintenance/project` - 创建项目
- `PUT /maintenance/project/{id}` - 更新项目
- `GET /maintenance/project/{id}` - 获取项目详情
- `DELETE /maintenance/project/{id}` - 删除项目
- `POST /maintenance/project/{id}/submit` - 提交项目审核
- `POST /maintenance/project/{id}/approve` - 审批项目

**维修管理接口**：
- `GET /maintenance/repair/list` - 获取维修列表
- `GET /maintenance/emergency/list` - 获取应急维修列表
- `POST /maintenance/repair/{id}/process` - 处理维修任务

**审核管理接口**：
- `GET /maintenance/approval/list` - 获取待审核列表
- `POST /maintenance/approval/{id}/audit` - 审核操作

**延期申请接口**：
- `GET /maintenance/extension/list` - 获取延期申请列表
- `POST /maintenance/extension/apply` - 提交延期申请
- `POST /maintenance/extension/{id}/approve` - 审批延期申请

### 6.6 权限控制设计

#### 6.6.1 角色权限矩阵

基于现有系统的角色体系，养护运维模块的权限分配如下：

| 功能模块 | admin (超管) | bridge_supervisor (桥隧监管员) | company_admin (公司管理员) | company_staff (公司业务员) |
|---------|-------------|-------------------------------|--------------------------|-------------------------|
| 月度养护项目 | 全部权限 | 查看、审批 | 查看、审批、导出 | 创建、修改、查看、删除 |
| 保洁项目 | 全部权限 | 查看、审批 | 查看、审批、导出 | 创建、修改、查看、删除 |
| 应急养护项目 | 全部权限 | 查看、审批 | 查看、审批、导出 | 创建、修改、查看、删除 |
| 预防养护项目 | 全部权限 | 创建、修改、查看、审批 | 查看、导出 | 查看 |
| 养护维修 | 全部权限 | 查看、复核、导出 | 查看、初审、导出 | 查看、执行 |
| 延期申请 | 全部权限 | 审批、查看 | 查看、导出 | 申请、查看 |

#### 6.6.2 权限标识规范

遵循现有系统的权限标识格式 `模块:资源:操作`，养护运维模块权限定义：

**养护项目权限**：
- `maintenance:project:list` - 查看项目列表
- `maintenance:project:query` - 查询项目
- `maintenance:project:add` - 新增项目
- `maintenance:project:edit` - 编辑项目
- `maintenance:project:remove` - 删除项目
- `maintenance:project:export` - 导出项目
- `maintenance:project:approve` - 审批项目
- `maintenance:project:submit` - 提交项目

**养护维修权限**：
- `maintenance:repair:list` - 查看维修列表
- `maintenance:repair:query` - 查询维修
- `maintenance:repair:execute` - 执行维修
- `maintenance:repair:audit` - 审核维修
- `maintenance:repair:export` - 导出维修记录

**延期申请权限**：
- `maintenance:extension:list` - 查看延期列表
- `maintenance:extension:query` - 查询延期申请
- `maintenance:extension:add` - 提交延期申请
- `maintenance:extension:approve` - 审批延期申请
- `maintenance:extension:export` - 导出延期记录

#### 6.6.3 权限控制实现

**1. 基于现有架构的权限实现**

使用现有的权限验证机制：

```vue
<!-- 模板中使用指令控制 -->
<el-button 
  v-hasPermi="['maintenance:project:add']"
  type="primary" 
  @click="handleAdd">
  新增项目
</el-button>

<el-button 
  v-hasPermi="['maintenance:project:approve']"
  type="success" 
  @click="handleApprove">
  审批
</el-button>

<!-- 基于角色的控制 -->
<el-button 
  v-hasRole="['admin', 'bridge_supervisor']"
  type="warning" 
  @click="handleReview">
  复核
</el-button>
```

**2. 路由权限配置**

```javascript
// 养护运维路由配置
{
  path: '/maintenance',
  component: Layout,
  redirect: '/maintenance/project',
  name: 'Maintenance',
  meta: {
    title: '养护运维',
    icon: 'maintenance'
  },
  children: [
    {
      path: 'project',
      component: () => import('@/views/maintenance/project/index'),
      name: 'MaintenanceProject',
      meta: { 
        title: '养护项目',
        permissions: ['maintenance:project:list']
      }
    },
    {
      path: 'project/create',
      component: () => import('@/views/maintenance/project/create'),
      name: 'ProjectCreate',
      hidden: true,
      meta: { 
        title: '新增项目',
        activeMenu: '/maintenance/project',
        permissions: ['maintenance:project:add']
      }
    },
    {
      path: 'repair',
      component: () => import('@/views/maintenance/repair/index'),
      name: 'MaintenanceRepair',
      meta: { 
        title: '养护维修',
        permissions: ['maintenance:repair:list']
      }
    },
    {
      path: 'audit',
      component: () => import('@/views/maintenance/audit/index'),
      name: 'MaintenanceAudit',
      meta: { 
        title: '养护维修审核',
        permissions: ['maintenance:repair:audit']
      }
    },
    {
      path: 'extension',
      component: () => import('@/views/maintenance/extension/index'),
      name: 'MaintenanceExtension',
      meta: { 
        title: '延期申请',
        permissions: ['maintenance:extension:list']
      }
    }
  ]
}
```

**3. 组件中的权限检查**

```javascript
// 在组件方法中检查权限
methods: {
  // 检查是否有新增权限
  checkAddPermission() {
    return this.$auth.hasPermi(['maintenance:project:add']);
  },
  
  // 检查是否有审批权限
  checkApprovePermission() {
    return this.$auth.hasPermi(['maintenance:project:approve']);
  },
  
  // 检查是否是管理员角色
  checkAdminRole() {
    return this.$auth.hasRole(['admin']);
  },
  
  // 根据权限显示不同按钮
  getActionButtons(row) {
    let buttons = [];
    
    // 查看按钮 - 所有用户都可以查看
    buttons.push({
      label: '查看',
      type: 'primary',
      handler: () => this.handleView(row)
    });
    
    // 编辑按钮 - 仅创建者和管理员可编辑草稿状态
    if (row.status === 'draft' && 
        (this.$auth.hasPermi(['maintenance:project:edit']) || 
         row.createBy === this.$store.state.user.id)) {
      buttons.push({
        label: '编辑',
        type: 'warning',
        handler: () => this.handleEdit(row)
      });
    }
    
    // 审批按钮 - 仅有审批权限的用户可见
    if (row.status === 'pending' && 
        this.$auth.hasPermi(['maintenance:project:approve'])) {
      buttons.push({
        label: '审批',
        type: 'success',
        handler: () => this.handleApprove(row)
      });
    }
    
    return buttons;
  }
}
```

**4. 数据权限控制**

```javascript
// 基于用户角色过滤数据
computed: {
  // 根据用户角色决定可见的项目
  visibleProjects() {
    if (this.$auth.hasRole(['admin'])) {
      // 超级管理员可以看到所有项目
      return this.projectList;
    } else if (this.$auth.hasRole(['bridge_supervisor'])) {
      // 桥隧监管员可以看到所有需要审批的项目
      return this.projectList.filter(item => 
        ['pending', 'approved', 'rejected'].includes(item.status)
      );
    } else if (this.$auth.hasRole(['company_admin'])) {
      // 公司管理员可以看到本公司的所有项目
      return this.projectList.filter(item => 
        item.companyId === this.$store.state.user.companyId
      );
    } else {
      // 普通业务员只能看到自己创建的项目
      return this.projectList.filter(item => 
        item.createBy === this.$store.state.user.id
      );
    }
  }
}
```

### 6.7 深色主题样式设计规范

#### 6.7.1 颜色规范（与巡检记录页面保持一致）

**主色调系统**：
- **主要背景色**：#091A4B（与框架$themeColor保持一致）
- **次要背景色**：rgba(9, 26, 75, 0.9)
- **蓝色强调色**：#5C9DFF（与框架$blue保持一致）
- **浅蓝色强调**：#74a7f5（与框架$light-blue保持一致）
- **TAB未选中背景**：#1B2A56（深蓝色背景）
- **TAB选中背景**：linear-gradient(135deg, #334067 0%, #2a3558 100%)
- **筛选区域背景**：linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%)

**文字颜色系统**：
- **主要文字**：#f8fafc（白色主文字）
- **次要文字**：#e2e8f0（灰白色次要文字）
- **静默文字**：#94a3b8（静默状态文字）
- **占位符文字**：rgba(255, 255, 255, 0.5)（半透明白色）

**边框颜色系统**：
- **主要边框**：#374151（深灰色边框）
- **浅色边框**：#4b5563（浅灰色边框）
- **强调边框**：rgba(255, 255, 255, 0.2)（半透明白色边框）
- **TAB边框**：rgba(255, 255, 255, 0.15)（TAB边框特效）

**表格颜色系统**：
- **表头背景**：linear-gradient(180deg, #67718F 0%, #7B85A3 100%)
- **表格行背景**：linear-gradient(180deg, #243066 0%, #1C2A4E 100%)
- **表格边框**：1px solid rgba(255, 255, 255, 0.1)
- **表格悬停**：rgba(255, 255, 255, 0.05)

**状态色彩规范**：
- **成功状态**：#10b981（绿色）
- **警告状态**：#f59e0b（黄色）
- **危险状态**：#ef4444（红色）
- **信息状态**：#3b82f6（蓝色）
- **草稿状态**：#3b82f6（蓝色背景）+ #ffffff（白色文字）
- **审批中状态**：#eab308（黄色背景）+ #1f2937（深色文字）
- **通过状态**：#22c55e（绿色背景）+ #ffffff（白色文字）
- **拒绝状态**：#ef4444（红色背景）+ #ffffff（白色文字）

#### 6.7.2 TAB切换组件样式（与巡检记录保持一致）

**TAB切换样式**：
- **未选中TAB**：
  - 背景：#1B2A56（深蓝色背景）
  - 文字：rgba(255, 255, 255, 0.7)，14px，正常字重
  - 圆角：10px（与设计图保持一致）
  - 内边距：12px
  - 高度：48px
  - 右边距：10px（TAB间距）
  - 边框：使用box-shadow模拟，rgba(255, 255, 255, 0.15)
  - 悬停：background: #1a2d5a，文字颜色变为rgba(255, 255, 255, 0.9)
- **选中TAB**：
  - 背景：linear-gradient(135deg, #334067 0%, #2a3558 100%)
  - 文字：#fff，14px，正常字重
  - 圆角：10px
  - 边框：使用box-shadow模拟，rgba(255, 255, 255, 0.25)
  - 外部阴影：0 2px 8px rgba(0, 123, 255, 0.2)
- **图标和文字布局**：
  - 图标尺寸：16x16px
  - 图标与文字间距：8px
  - 使用flex布局，水平排列，左对齐
  - 最小宽度：92px

#### 6.7.3 筛选区域组件样式（与巡检记录保持一致）

**筛选区域样式**：
- **容器背景**：linear-gradient(135deg, #1B2A56 0%, #2A3B6B 100%)
- **边框**：1px solid rgba(255, 255, 255, 0.2)
- **圆角**：10px（与TAB按钮相同）
- **内边距**：16px 20px
- **最小高度**：60px
- **特殊边框效果**：
  - 使用伪元素实现左上角和右下角的亮边框效果
  - 左上角和右下角：rgba(255, 255, 255, 0.8)亮边框
  - 边框长度：30px

**筛选控件样式**：
- **下拉选择框**：
  - 背景：rgba(255, 255, 255, 0.1)
  - 边框：1px solid rgba(255, 255, 255, 0.2)
  - 圆角：8px
  - 文字：#f8fafc，16px，正常字重
  - 高度：40px
  - 占位符：rgba(255, 255, 255, 0.5)
  - 悬停：border-color: rgba(255, 255, 255, 0.4)
  - 聚焦：border-color: #409EFF，box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2)

**操作按钮样式**：
- **主要按钮（查询）**：
  - 背景：linear-gradient(135deg, #409EFF 0%, #337ECC 100%)
  - 文字：#FFF，14px，正常字重
  - 高度：36px
  - 内边距：0 16px
  - 圆角：6px
  - 悬停：background: linear-gradient(135deg, #66B1FF 0%, #409EFF 100%)，transform: translateY(-1px)
- **次要按钮（重置）**：
  - 背景：rgba(255, 255, 255, 0.1)
  - 边框：1px solid rgba(255, 255, 255, 0.3)
  - 文字：rgba(255, 255, 255, 0.9)，14px
  - 悬停：background: rgba(255, 255, 255, 0.15)，border-color: rgba(255, 255, 255, 0.4)

#### 6.7.4 表格组件样式（与巡检记录保持一致）

**表格容器样式**：
- **背景**：透明
- **边框**：无边框设计
- **圆角**：无圆角
- **阴影**：无阴影效果

**表头样式**：
- **背景**：linear-gradient(180deg, #67718F 0%, #7B85A3 100%)
- **文字**：#FFF，16px，正常字重（400）
- **字体**："PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei"
- **行高**：140%（22.4px）
- **高度**：44px
- **内边距**：14px 24px
- **边框**：1px solid rgba(255, 255, 255, 0.1)
- **对齐**：居中对齐

**数据行样式**：
- **背景**：linear-gradient(180deg, #243066 0%, #1C2A4E 100%)
- **文字**：#FFF，14px，正常字重（400）
- **字体**："PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei"
- **行高**：120%（16.8px）
- **内边距**：8px 12px
- **边框**：1px solid rgba(255, 255, 255, 0.1)
- **对齐**：居中对齐
- **悬停效果**：rgba(255, 255, 255, 0.05)
- **最后一行**：无底部边框

**操作按钮样式**：
- **文字按钮**：
  - 颜色：#FFF
  - 字体大小：14px
  - 行高：120%
  - 高度：20px
  - 内边距：2px 6px
  - 背景：无
  - 边框：无
  - 圆角：4px
  - 悬停：color: #E0E0E0，background: rgba(255, 255, 255, 0.1)
- **按钮间距**：8px
- **按钮布局**：水平排列，居中对齐

#### 6.7.5 分页组件样式（与巡检记录保持一致）

**分页容器样式**：
- **布局**：居中对齐
- **上边距**：24px
- **字体**："PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei"

**页码按钮样式**：
- **背景**：var(--inspection-bg-tertiary)（#334155）
- **文字颜色**：var(--inspection-text-secondary)（#e2e8f0）
- **边框**：1px solid var(--inspection-border)（#374151）
- **圆角**：6px
- **间距**：0 4px
- **当前页码**：
  - 背景：var(--inspection-primary)（#5C9DFF）
  - 文字颜色：var(--inspection-text-primary)（#f8fafc）
  - 边框：var(--inspection-primary)
- **悬停效果**：
  - 背景：var(--inspection-primary-light)（#74a7f5）
  - 文字颜色：var(--inspection-text-primary)
  - 边框：var(--inspection-primary-light)

**前进后退按钮**：
- **背景**：var(--inspection-bg-tertiary)
- **文字颜色**：var(--inspection-text-secondary)
- **边框**：1px solid var(--inspection-border)
- **圆角**：6px
- **悬停效果**：与页码按钮一致

**辅助信息样式**：
- **总数和跳转文字**：var(--inspection-text-secondary)

#### 6.7.6 页面布局样式（与巡检记录保持一致）

**页面容器**：
- **背景**：透明（继承框架主题色）
- **最小高度**：100vh
- **文字颜色**：var(--inspection-text-primary)

**内容容器**：
- **高度**：calc(100vh - 84px)（减去框架header高度64px和底部margin20px）
- **布局**：flex纵向布局
- **内边距**：0 20px 20px 20px
- **盒模型**：border-box

**区域间距**：
- **TAB到筛选区域**：4px
- **筛选区域到表格**：12px
- **表格到分页**：24px

#### 6.7.7 弹窗组件样式（与巡检记录保持一致）

**弹窗容器**：
- **遮罩层**：rgba(0, 0, 0, 0.6)
- **弹窗背景**：#091A4B（与主要背景色一致）
- **圆角**：12px
- **阴影**：0 8px 32px rgba(0, 0, 0, 0.4)
- **最大宽度**：90vw
- **最大高度**：90vh
- **进入动画**：Vue transition，scale(0.95) → scale(1) + opacity 0 → 1，0.3s ease-out

**弹窗标题栏**：
- **背景**：#091A4B
- **文字**：#f8fafc，18px，正常字重
- **高度**：56px
- **内边距**：0 24px
- **底边框**：1px solid #374151
- **关闭按钮**：
  - 尺寸：32x32px
  - 图标：⊗，#94a3b8，18px
  - 悬停：#ffffff图标，#ef4444背景
  - 圆角：50%

**弹窗内容区域**：
- **背景**：#091A4B
- **内边距**：24px
- **最大高度**：calc(90vh - 120px)
- **滚动条**：深色主题样式

#### 6.7.8 输入框组件样式（与巡检记录保持一致）

**输入框样式**：
- **背景**：rgba(255, 255, 255, 0.1)
- **边框**：1px solid rgba(255, 255, 255, 0.2)
- **文字**：#f8fafc，16px，正常字重
- **占位符**：rgba(255, 255, 255, 0.5)，16px
- **圆角**：8px
- **内边距**：12px 16px
- **高度**：40px
- **字体**："PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "Hiragino Sans GB", "Microsoft YaHei"
- **悬停**：border-color: rgba(255, 255, 255, 0.4)
- **聚焦**：border-color: #409EFF，box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2)

**下拉选择框**：
- **基础样式**：与输入框相同
- **下拉箭头**：rgba(255, 255, 255, 0.7)，悬停变为rgba(255, 255, 255, 0.9)
- **清除按钮**：rgba(255, 255, 255, 0.5)，悬停变为rgba(255, 255, 255, 0.8)
- **下拉面板**：
  - 背景：rgba(9, 26, 75, 0.9)
  - 边框：1px solid #374151
  - 圆角：8px
  - 阴影：var(--inspection-shadow-lg)
- **选项样式**：
  - 文字：#e2e8f0，16px，正常字重
  - 悬停：背景#334155，文字#f8fafc
  - 选中：背景#5C9DFF，文字#f8fafc，字重500

#### 6.7.9 状态标签组件样式（与巡检记录保持一致）

**状态标签基础样式**：
- **圆角**：4px
- **内边距**：4px 8px
- **字体大小**：12px
- **字体粗细**：500（中等粗细）
- **最小宽度**：60px
- **对齐方式**：居中对齐
- **显示方式**：inline-block

**状态颜色方案**：
- **草稿状态**：背景#3b82f6，文字#ffffff
- **审批中状态**：背景#eab308，文字#1f2937
- **通过状态**：背景#22c55e，文字#ffffff
- **拒绝状态**：背景#ef4444，文字#ffffff
- **超期状态**：背景#f59e0b，文字#ffffff，CSS keyframes闪烁动画（1.5s无限循环）
- **成功状态**：背景rgba(16, 185, 129, 0.2)，文字#10b981，边框1px solid rgba(16, 185, 129, 0.3)
- **警告状态**：背景rgba(245, 158, 11, 0.2)，文字#f59e0b，边框1px solid rgba(245, 158, 11, 0.3)
- **危险状态**：背景rgba(239, 68, 68, 0.2)，文字#ef4444，边框1px solid rgba(239, 68, 68, 0.3)

#### 6.7.10 文件上传组件样式（与巡检记录保持一致）

**上传区域样式**：
- **背景**：rgba(255, 255, 255, 0.1)
- **边框**：2px dashed rgba(255, 255, 255, 0.2)
- **圆角**：8px
- **最小高度**：120px
- **内边距**：24px
- **对齐方式**：居中对齐

**拖拽状态**：
- **边框**：2px dashed #5C9DFF
- **背景**：rgba(92, 157, 255, 0.1)

**上传提示**：
- **图标颜色**：#94a3b8，48px
- **主要文字**："将文件拖拽或点击上传"，#f8fafc，16px
- **辅助文字**："支持 jpg、png、pdf 格式，单个文件不超过 10MB"，#94a3b8，12px

**文件列表样式**：
- **文件项背景**：rgba(255, 255, 255, 0.1)
- **圆角**：4px
- **内边距**：8px 12px
- **文件名**：#f8fafc，14px
- **文件大小**：#94a3b8，12px
- **删除按钮**：#ef4444，悬停时变亮

#### 6.7.11 交互动画规范（与巡检记录保持一致）

**过渡动画时长**：
- **标准过渡**：0.2s ease-in-out（Element UI默认）
- **快速过渡**：0.1s ease-out
- **慢速过渡**：0.3s ease-in-out
- **TAB切换动画**：0.3s ease

**悬停效果**：
- **按钮悬停**：transform: translateY(-1px)，0.2s过渡
- **表格行悬停**：背景色变化为rgba(255, 255, 255, 0.05)，0.1s过渡
- **筛选控件悬停**：边框颜色变化，0.3s过渡
- **操作按钮悬停**：颜色变化 + 轻微背景色，0.2s过渡

**点击反馈**：
- **主要按钮点击**：transform: translateY(0)，从悬停状态的-1px回到原位
- **次要按钮点击**：轻微的透明度变化

**Vue动画实现方案**：
- **弹窗动画**：`<transition name="dialog-fade">`，scale(0.95) → scale(1) + opacity 0 → 1，0.3s ease-out
- **列表动画**：`<transition-group name="list">`，支持增删改操作
- **加载动画**：Element UI的el-loading指令，与主题色保持一致
- **状态变化动画**：颜色过渡，0.2s ease-in-out

#### 6.7.12 数据验证规范（与巡检记录保持一致）

**基本字段验证**：
- **项目名称**：必填，2-50个字符
- **项目类型**：必填，从预设选项中选择
- **开始时间**：必填，日期格式
- **结束时间**：必填，必须大于开始时间
- **管理单位**：必填，从组织机构中选择
- **养护单位**：必填，从组织机构中选择
- **项目负责人**：必填，从人员列表中选择
- **联系方式**：必填，手机号格式验证

**自定义验证规则**：
- **时间范围验证**：结束时间必须大于开始时间
- **延期时间验证**：延期结束时间必须大于原结束时间
- **工作量验证**：数字格式，大于0
- **文件上传验证**：文件类型和大小限制

**验证时机**：
- 实时验证：输入框失焦触发
- 提交验证：表单提交前整体验证
- 联动验证：关联字段变化时触发

#### 6.7.13 响应式设计规范（与巡检记录保持一致）

**断点设置**：
- **超大屏**：>= 1920px，最大内容宽度1600px，16px基础字体
- **大屏PC**：1600px - 1919px，最大内容宽度1400px，16px基础字体
- **标准PC**：1366px - 1599px，完整功能展示，15px基础字体
- **小屏PC**：1200px - 1365px，适当压缩间距，15px基础字体
- **平板端**：768px - 1199px，侧边栏可收起，14px基础字体
- **手机端**：< 768px，堆叠布局，底部导航，14px基础字体

**布局适配**：
- **表格响应式**：
  - 桌面端：完整表格显示
  - 平板端：固定重要列，其余列水平滚动
  - 手机端：卡片式布局，每行数据变为卡片
- **弹窗适配**：
  - 超大屏/大屏PC：固定尺寸居中，最大宽度1200px
  - 标准PC/小屏PC：宽度80%，最大宽度1000px
  - 平板端：宽度90%，高度适应
  - 手机端：全屏显示，顶部标题栏
- **表单适配**：
  - 超大屏/大屏PC：三列布局，充分利用宽屏空间
  - 标准PC/小屏PC：双列布局
  - 平板端：单列布局，标签上置
  - 手机端：全宽输入框，增大触控区域

**组件尺寸适配**：
- **按钮高度**：
  - 超大屏/大屏PC：44px
  - 标准PC/小屏PC：40px  
  - 平板端/手机端：44px（触控优化）
- **表格行高**：
  - 超大屏/大屏PC：52px
  - 标准PC/小屏PC：48px
  - 平板端：44px
  - 手机端：卡片式，高度自适应
- **内边距适配**：
  - 超大屏/大屏PC：24px
  - 标准PC/小屏PC：20px
  - 平板端：16px
  - 手机端：12px

**触控优化**：
- **最小点击区域**：44x44px
- **按钮间距**：最小8px
- **滑动手势**：支持左右滑动切换标签

#### 6.7.14 无障碍访问规范（与巡检记录保持一致）

**键盘导航**：
- **Tab顺序**：逻辑顺序，可通过Tab键遍历所有交互元素
- **焦点样式**：明显的焦点指示器，#3b82f6外边框2px
- **快捷键**：
  - Ctrl+S：保存表单
  - Esc：关闭弹窗
  - Enter：确认操作
  - 空格：选择/取消选择

**屏幕阅读器支持**：
- **语义化标签**：使用正确的HTML标签
- **ARIA标签**：
  - aria-label：描述元素用途
  - aria-describedby：关联说明文字
  - role：定义元素角色
- **状态通知**：
  - aria-live：动态内容更新通知
  - 表单验证错误的语音提示

**颜色对比度**：
- **文字对比度**：
  - 白色文字 (#ffffff) 在深蓝背景 (#1e3a8a)：对比度 > 7:1
  - 灰色文字 (#9ca3af) 在深蓝背景：对比度 > 4.5:1
- **状态标识**：不仅依赖颜色，还使用图标和文字
- **错误提示**：红色 + 错误图标 + 文字说明

**动画控制**：
- **尊重用户偏好**：检测 prefers-reduced-motion
- **可关闭动画**：提供动画开关选项
- **无闪烁**：避免超过3Hz的闪烁效果

---

## 7. 总结

本前端设计文档严格按照《养护运维.md》功能需求和《桥梁养护管理系统界面结构图.md》原型结构设计，确保了：

### 7.1 完整性保证
- 涵盖养护项目、养护维修、养护维修审核、延期申请四大核心模块
- 支持月度养护、保洁项目、应急养护、预防养护四种项目类型
- 包含完整的32个页面/弹窗，无遗漏原型图内容
- 实现了完整的业务流程闭环

### 7.2 原型一致性  
- 严格按照原型图的ASCII结构复制，结构100%匹配
- 准确复现所有表格字段名称（包括保洁内容、保洁人员等关键字段）
- 正确实现步骤流程（保洁项目3步骤、应急养护3步骤等）
- 确保所有页面跳转关系和按钮文案的准确性

### 7.3 技术可行性
- 采用成熟的Vue.js + Element UI技术栈
- 设计了完善的组件架构和状态管理
- 提供了详细的API接口和权限控制方案
- 补充了完整的深色主题样式规范

### 7.4 用户体验
- 深蓝色主题统一设计风格，与巡检记录页面完全一致的颜色和组件样式规范
- 支持多维度筛选和实时搜索
- 提供完善的表单验证和错误提示
- 响应式设计适配不同设备

### 7.5 样式一致性
本文档的样式规范严格按照巡检记录页面的实际实现进行设计，确保了：
- **颜色系统一致性**：与框架主题色#091A4B、强调色#5C9DFF等保持完全一致
- **组件样式一致性**：TAB切换、筛选区域、表格、分页器等核心组件与巡检记录页面使用相同的样式规范
- **交互动画一致性**：悬停效果、过渡动画、按钮反馈等与现有系统保持一致
- **字体规范一致性**：使用相同的字体系统"PingFang SC"，字号、行高、字重保持统一
- **布局规范一致性**：页面容器、区域间距、组件尺寸与巡检记录页面保持一致

这种设计方法确保了养护运维模块能够无缝融入现有的长沙市智慧桥隧管理平台，为用户提供一致的使用体验。

本文档为长沙市智慧桥隧管理平台养护运维模块的前端开发提供了完整、详细、可执行的设计指导。
