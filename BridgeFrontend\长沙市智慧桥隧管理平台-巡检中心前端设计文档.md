# 长沙市智慧桥隧管理平台 - 巡检中心前端设计文档

规则说明：
frontend技术栈：
前端框架: 主要使用Vue.js (Vue 2.x为主)
UI组件库: Element UI (PC端) 
数据可视化: ECharts图表库
HTTP请求: Axios
状态管理: Vuex (Vue 2) 
路由管理: Vue Router

开发要求：
1. 本文档中，所有下拉框的内容，你需要仔细识别是静态数据还是联动加载其他请求的数据，均需要满足先查询获取结果，若无结果，则以示例中的值作为默认值。
2. 你开发的页面样式风格要一致，且与当前工程结构的总体样式风格一致
3. 开发本工程时，完成每项工作均需要在功能点后方标记完成状态，只完成部分的功能，要将状态更新到具体的功能点，指明哪些未完成
4. 所有涉及请求后端的接口，均需要做预留适配，后端还未提供详细接口，所以接口url先预制，加个特殊处理，如果请求失败了，页面不要直接抛出异常，而是使用默认值
5. 所有测试使用的默认值，统一放在一个地方，方便后续维护
6. 有很多页面布局和字段大部分是一样的，你需要考虑是否可以复用，优先复用当前已开发的组件和页面

## 1. 项目概述

### 1.1 系统架构
- **平台名称**：智慧桥隧管理平台
- **模块名称**：巡检中心模块
- **技术要求**：现代化前端框架 + 组件库 + 类型系统
- **响应式设计**：支持桌面端和移动端适配

### 1.2 功能模块概述
巡检中心模块包含三个核心功能：
- **巡检记录管理**：日常巡检记录查看、日志管理、报告生成
- **病害及处置管理**：病害上报、判定、处置、复核全流程管理  
- **巡检统计分析**：巡检数据统计分析和可视化展示

### 1.3 用户角色权限
- **监管人员（中心）**：查看所有巡检记录、病害处置审批、统计分析
- **业务人员（公司）**：查看本单位巡检记录、病害上报和处置

## 2. 页面详细设计

### 2.1 巡检记录管理模块

#### 2.1.1 巡检记录主列表页面（基于image1截图）

**页面路径**：`/inspection/records`
**数据来源**：`GET /api/inspection/records`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 > 巡检中心 > 巡检记录          │
├─────────────────────────────────────────────────────┤
│ 【桥梁巡检】【隧道巡检】 TAB切换                     │
├─────────────────────────────────────────────────────┤
│ 🔍 巡检记录                           【查询】【重置】 │
├─────────────────────────────────────────────────────┤
│ [桥梁名称▼] [巡检单位▼] [巡检月度📅] [🔍查询][🔄重置]│
├─────────────────────────────────────────────────────┤
│序号│桥梁名称│巡检人员│巡检单位│联系方式│本月缺巡数量│上次巡检时间│操作│
│ 1 │橘子洲大桥│张明,李华│长沙市桥梁管理处│13800138000│3次│2023-06-25 09:30│日志 报告│
│ 2 │银盆岭大桥│王亮,赵丽│湖南省交通工程检测中心│13900139000│0次│2023-06-28 14:15│日志 报告│
│ 3 │猴子石大桥│陈杰,刘丹│长沙市市政工程公司│13700137000│1次│2023-06-27 10:45│日志 报告│
│ 4 │三汊矶大桥│黄昌,周敏│湖南省桥梁检测研究院│13600136000│2次│2023-06-26 16:20│日志 报告│
│ 5 │福元路大桥│吴刚,郑琳│长沙市桥梁管理处│13500135000│0次│2023-06-29 08:50│日志 报告│
├─────────────────────────────────────────────────────┤
│ 显示第 1 至 5 条，共 24 条记录        [1] 2 3 4 5 > │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **标签页组件**：支持桥梁/隧道巡检切换
- **搜索表单**：桥梁名称、巡检单位、巡检月度筛选条件
- **下拉选择器**：桥梁名称、巡检单位选择（联动加载）
- **日期选择器**：支持年月选择的巡检月度筛选
- **数据表格**：支持排序、筛选的数据展示
- **分页器**：支持页码跳转和页大小设置

**数据字段**：
巡检记录列表数据包含以下字段：序号、桥梁/隧道名称、巡检人员（多人逗号分隔）、巡检单位、联系方式（多人逗号分隔）、本月缺巡数量、上次巡检时间、操作按钮。

**页面交互逻辑**：
1. **TAB切换**：切换桥梁/隧道巡检时重新加载对应数据
2. **搜索功能**：支持多条件组合查询，巡检月度默认当前月份
3. **操作按钮**：
   - 日志：点击弹出巡检日历视图
   - 报告：点击弹出报告列表选择

#### 2.1.2 巡检日志日历视图弹窗（基于image2截图）

**页面路径**：弹窗组件，从巡检记录列表点击"日志"触发
**数据来源**：`GET /api/inspection/logs/:bridgeId?month=YYYY-MM`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 橘子洲大桥 - 2023年06月巡检日志                ✕    │
├─────────────────────────────────────────────────────┤
│ 图例说明                                           │
│ ● 日常巡检  ● 经常巡检  ● 中心巡检  ● 未巡检       │
├─────────────────────────────────────────────────────┤
│              < 2023年06月 >                        │
├─────────────────────────────────────────────────────┤
│ 日  一  二  三  四  五  六                           │
│28  29  30  31   1   2   3                           │
│ 4   5   6   7   8   9  10                           │
│11  12  13  14  15  16  17                           │
│18  19  20  21  22  23  24                           │
│25  26  27  28  29  30   1                           │
├─────────────────────────────────────────────────────┤
│ 请点击日期查看详情                                   │
│ 2023年06月22日 - 巡检记录                           │
│ ● 日常巡检                              09:20      │
│ 巡检人员：李华                                      │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **弹窗组件**：模态弹窗，支持关闭操作
- **日历组件**：支持月份切换，不同颜色标识巡检类型
- **图例组件**：显示不同巡检类型的颜色说明
- **详情展示区**：点击日期显示当日巡检记录

**数据字段**：
日历数据包含：日期、巡检类型（日常巡检、经常巡检、中心巡检、未巡检）、巡检时间、巡检人员、巡检详情ID。

**页面交互逻辑**：
1. **月份切换**：支持查看历史月份巡检记录
2. **日期点击**：点击有巡检记录的日期显示详情
3. **巡检记录点击**：点击巡检记录可查看详细信息

#### 2.1.3 巡检日志详情视图弹窗（基于image3截图）

**页面路径**：弹窗组件，从日历视图点击巡检记录触发
**数据来源**：`GET /api/inspection/logs/:logId`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 橘子洲大桥 - 2023年06月巡检日志                ✕    │
├─────────────────────────────────────────────────────┤
│              < 2023年06月 >                        │
│ [日历组件显示，当前选中日期高亮]                     │
├─────────────────────────────────────────────────────┤
│ 请点击日期查看详情                                   │
│ 2023年06月22日 - 巡检记录                           │
│ ● 日常巡检                              09:20      │
│ 巡检人员：李华                                      │
├─────────────────────────────────────────────────────┤
│                                        [关闭]      │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **日历状态保持**：保持日历当前月份和选中状态
- **巡检记录展示**：显示当日所有巡检记录
- **详情链接**：点击可进入具体巡检详情

#### 2.1.4 巡检基本信息详情弹窗（基于image4截图）

**页面路径**：弹窗组件，从巡检记录点击进入
**数据来源**：`GET /api/inspection/detail/:logId`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ ℹ️ 基本信息                                    ✕    │
├─────────────────────────────────────────────────────┤
│ 巡检对象            │ 巡检类型                      │
│ 橘子洲大桥          │ 日常巡检                      │
├─────────────────────────────────────────────────────┤
│ 巡检人员            │ 巡检单位                      │
│ 张三                │ 桥梁巡检养护单位              │
├─────────────────────────────────────────────────────┤
│ 巡检上报时间        │ 巡检方式                      │
│ 2025-07-21 12:30    │ 步行                          │
├─────────────────────────────────────────────────────┤
│ 巡检项目            │ 巡检说明                      │
│ 日常巡检模板        │ XXXXXXXXX                     │
├─────────────────────────────────────────────────────┤
│ 巡检上报位置                                       │
│ [地图组件显示巡检位置]                              │
├─────────────────────────────────────────────────────┤
│ 巡检上报图片                                       │
│ [📷] [📷]                                          │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **信息展示表单**：只读模式的表单组件
- **地图组件**：显示巡检上报位置
- **图片预览组件**：支持巡检图片查看

**数据字段**：
巡检详情数据包含：巡检对象、巡检类型、巡检人员、巡检单位、巡检上报时间、巡检方式、巡检项目、巡检说明、位置坐标、巡检图片列表。

#### 2.1.5 病害信息详情弹窗（基于image4扩展）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ ℹ️ 病害信息                                          │
├─────────────────────────────────────────────────────┤
│ 病害部位            │ 病害类型                      │
│ 桥路连接位置        │ 桥头沉降跳车                  │
├─────────────────────────────────────────────────────┤
│ 病害数量            │ 病害描述                      │
│ 1处                 │ 桥头沉降跳车XXXX              │
├─────────────────────────────────────────────────────┤
│ 病害位置                                           │
│ [地图组件显示病害位置]                              │
├─────────────────────────────────────────────────────┤
│ 病害图片                                           │
│ [📷] [📷] [📷]                                     │
├─────────────────────────────────────────────────────┤
│ 病害状态                                           │
│ 未处理/处理中/已处理                                │
└─────────────────────────────────────────────────────┘
```

#### 2.1.6 巡检报告列表弹窗（基于image5截图）

**页面路径**：弹窗组件，从巡检记录列表点击"报告"触发
**数据来源**：`GET /api/inspection/reports/:bridgeId`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 序号  桥梁名称   巡检日期   巡检人员   联系方式   报告生成时间   操作 │
├─────────────────────────────────────────────────────┤
│  1   东洲湘江大桥  2025-07-28   罗展   18273455512  2025-07-28   查看 │
│  2   二环东路跨黄白路立 2025-07-28 罗展 18273455512 2025-07-28  查看 │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **弹窗列表**：显示该桥梁的所有巡检报告
- **查看按钮**：点击下载或预览报告

**数据字段**：
报告列表数据包含：序号、桥梁名称、巡检日期、巡检人员、联系方式、报告生成时间、操作。

#### 2.1.7 巡检报告详情页面（基于image6截图）

**页面路径**：新页面，从报告列表点击"查看"打开
**数据来源**：`GET /api/inspection/reports/:reportId`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│                                        [下载]      │
├─────────────────────────────────────────────────────┤
│桥梁名称    │人行天桥     巡检日期  2025-07-23       │
│检查项      │是否完好     病害类型  病害数量  病害部位  病害描述│
│抗震设施    │是          │         │         │         │         │
│附属设施    │是          │         │         │         │         │
│支座        │是          │         │         │         │         │
│人行道      │是          │         │         │         │         │
│栏杆和护栏  │是          │         │         │         │         │
│桥路连接位置│是          │         │         │         │         │
│桥面铺装    │是          │         │         │         │         │
│排水系统    │是          │         │         │         │         │
│伸缩装置    │是          │         │         │         │         │
│下部结构    │是          │         │         │         │         │
│上部结构    │是          │         │         │         │         │
├─────────────────────────────────────────────────────┤
│其他危及行人、行船、行车安全病害                      │
├─────────────────────────────────────────────────────┤
│桥梁保护区域内施工                                   │
├─────────────────────────────────────────────────────┤
│巡查人     │胡艳红      巡检日期   2025-07-23        │
├─────────────────────────────────────────────────────┤
│                                        [关闭]      │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **报告展示表格**：规范化的巡检报告格式
- **下载功能**：支持PDF等格式下载
- **打印功能**：支持浏览器打印

### 2.2 病害及处置管理模块

#### 2.2.1 病害及处置主列表页面（基于image7截图）

**页面路径**：`/inspection/diseases`
**数据来源**：`GET /api/inspection/diseases`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 / 检测工程 / 病害及处置        │
├─────────────────────────────────────────────────────┤
│ 【桥梁病害】【隧道病害】 TAB切换                     │
├─────────────────────────────────────────────────────┤
│ ▼ 查询条件                                         │
│ [按桥梁名称] [按上报属性▼] [按病害类型▼] [按上报时间📅]│
├─────────────────────────────────────────────────────┤
│ [+ 新增] [批量验收]                   [重置] [查询] │
├─────────────────────────────────────────────────────┤
│☐│序号│桥梁名称│上报属性│病害编号│病害部位│病害类型│病害状态│病害位置│病害数量│病害描述│上报人│上报时间│所属单位│联系方式│操作│
│☐│ 1 │橘子洲大桥│日常巡检│000001│伸缩缝│伸缩缝失效│判定中│XXX位置│2│XXXXXX│张三│2025-07-12 12:30│XXX单位│13139103912│查看 判定│
│☐│ 2 │橘子洲大桥│中心巡检│000002│照明设施│照明设施缺失│计划中│XXX位置│1│XXXXXX│张三│2025-07-12 12:30│XXX单位│13139103912│查看│
│☐│ 3 │橘子洲大桥│定检│000003│伸缩缝│伸缩缝失效│处置中│XXX位置│1│XXXXXX│张三│2025-07-12 12:30│XXX单位│13139103912│查看│
│☐│ 4 │三汊矶大桥│上级交办│000004│照明设施│照明设施缺失│验收中│XXX位置│1│XXXXXX│张三│2025-07-12 12:30│XXX单位│13139103912│查看 复核│
│☐│ 5 │三汊矶大桥│日常巡检│000005│照明设施│照明设施缺失│已归档│XXX位置│1│XXXXXX│张三│2025-07-12 12:30│XXX单位│13139103912│查看│
├─────────────────────────────────────────────────────┤
│ 显示第 1 至 3 条，共 24 条记录         [1] 2 3 4 > │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **标签页组件**：支持桥梁/隧道病害切换
- **高级搜索表单**：支持多条件筛选
- **批量操作**：支持批量选择和批量验收
- **状态标识**：不同病害状态用不同颜色标识
- **操作按钮**：根据病害状态动态显示操作按钮

**数据字段**：
病害列表数据包含：序号、桥梁名称、上报属性、病害编号、病害部位、病害类型、病害状态、病害位置、病害数量、病害描述、上报人、上报时间、所属单位、联系方式。

**页面交互逻辑**：
1. **状态筛选**：支持按病害状态筛选（判定中、计划中、处置中、验收中、已归档）
2. **操作按钮逻辑**：
   - 判定中：显示"查看"、"判定"
   - 计划中：显示"查看"
   - 处置中：显示"查看"
   - 验收中：显示"查看"、"复核"
   - 已归档：显示"查看"

#### 2.2.2 病害处置流程详情页面（基于image8截图）

**页面路径**：`/inspection/diseases/detail/:id`
**数据来源**：`GET /api/inspection/diseases/:id`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 / 检测工程 / 病害及处置        │
├─────────────────────────────────────────────────────┤
│ 🔶 病害管理处置流程                                  │
│ [●]――[●]――[○]――[○]――[○]                          │
│ 上报   判定   处置   复核   归档                     │
│ 处理人: 李四                                        │
│ 2025-07-12 16:30                                    │
├─────────────────────────────────────────────────────┤
│ ℹ️ 病害基本信息                                      │
├─────────────────────────────────────────────────────┤
│ 巡检人员             │ 巡检单位                     │
│ 张三                 │ 长沙市政养护单位              │
├─────────────────────────────────────────────────────┤
│ 上报属性             │ 所属所在/隧道                │
│ 日常巡检             │ 橘子洲大桥                   │
├─────────────────────────────────────────────────────┤
│ 病害类型             │ 病害数量                     │
│ 裂缝                 │ 3处                          │
├─────────────────────────────────────────────────────┤
│ 病害位置                                           │
│ 承载桁+200m，距止测所约53米，共3处桥面的裂缝          │
├─────────────────────────────────────────────────────┤
│ 病害描述                                           │
│ 桥面板出现3处的裂缝，最大宽度30.3mm，长度1.2m，可能影响结构耐久性，建议及时处理，由后勤党委委员会综合指挥水利院。│
├─────────────────────────────────────────────────────┤
│ 病害照片                                           │
│ [📷] [📷] [📷] [📷]                                │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **流程步骤组件**：显示当前处理阶段，已完成/当前/未开始状态
- **信息展示表单**：病害基本信息只读展示
- **图片展示组件**：支持病害图片预览

#### 2.2.3 病害判定信息页面（基于image9截图）

**页面路径**：病害详情页面内的判定信息模块
**数据来源**：病害详情接口数据的判定部分

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 🔶 判定信息                                          │
├─────────────────────────────────────────────────────┤
│ * 判定类型                                          │
│ ○ 日常养护及小修  ● 应急维修  ○ 养护范围外           │
├─────────────────────────────────────────────────────┤
│ 判定说明                      │ 要求完成时间          │
│ [XXXXX_____________]         │ [📅]                 │
├─────────────────────────────────────────────────────┤
│ 判定人                       │ 判定时间              │
│ [系统自动获取]                │ [系统自动获取]        │
├─────────────────────────────────────────────────────┤
│                 [保存] [✓ 提交] [← 返回]             │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **单选按钮组**：判定类型选择
- **文本输入框**：判定说明输入
- **日期选择器**：要求完成时间选择
- **自动填充字段**：判定人和判定时间系统自动获取

#### 2.2.4 病害判定操作页面（基于image10截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 / 检测工程 / 病害及处置        │
├─────────────────────────────────────────────────────┤
│ 🔶 病害管理处置流程                                  │
│ [●]――[●]――[○]――[○]――[○]                          │
├─────────────────────────────────────────────────────┤
│ ℹ️ 病害基本信息                                      │
│ [病害基本信息展示区域]                               │
├─────────────────────────────────────────────────────┤
│ 🔶 判定信息                                          │
├─────────────────────────────────────────────────────┤
│ * 判定类型                                          │
│ ○ 日常养护及小修  ● 应急维修  ○ 养护范围外           │
├─────────────────────────────────────────────────────┤
│ 判定说明                      │ 要求完成时间          │
│ [XXXXX_____________]         │ [📅]                 │
├─────────────────────────────────────────────────────┤
│ 判定人                       │ 判定时间              │
│ [张三]                       │ [2025-07-12 12:30]   │
├─────────────────────────────────────────────────────┤
│                 [保存] [✓ 提交] [← 返回]             │
└─────────────────────────────────────────────────────┘
```

#### 2.2.5 日常养护处置信息页面（基于image11截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ ○ 日常养护及小修  ● 应急维修  ○ 养护范围外           │
├─────────────────────────────────────────────────────┤
│ 判定说明                      │ 判定人                │
│ XXXXX                        │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 要求完成时间                                        │
│ 2025-07-13 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 🔶 处置信息                                          │
├─────────────────────────────────────────────────────┤
│ 处置方式                      │ 处置人                │
│ 日常养护                      │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 联系方式                                           │
│ 13146887654                                        │
├─────────────────────────────────────────────────────┤
│ 提交时间                                           │
│ 2025-07-15 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 处置说明                                           │
│ XXXXXXX                                            │
├─────────────────────────────────────────────────────┤
│ [现场照片] [人车照片] [处置前] [处置中] [处置后]    │
│ [📷]      [📷]      [📷]    [📷]    [📷]          │
├─────────────────────────────────────────────────────┤
│                                        [← 返回]     │
└─────────────────────────────────────────────────────┘
```

#### 2.2.6 专项大中修处置信息页面（基于image12截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 🔶 处置信息                                          │
├─────────────────────────────────────────────────────┤
│ 处置方式                     │ 专项大中修项目         │
│ 专项大中修                   │ XXX大中修项目          │
├─────────────────────────────────────────────────────┤
│ 判定说明                     │ 判定人                │
│ XXXXX                       │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 判定时间                                           │
│ 2025-07-12 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 要求完成时间                                        │
│ 2025-07-13 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 🔶 处置信息                                          │
├─────────────────────────────────────────────────────┤
│ 处置方式                     │ 处置人                │
│ 应急维修                     │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 联系方式                                           │
│ 13146887654                                        │
├─────────────────────────────────────────────────────┤
│ 提交时间                                           │
│ 2025-07-15 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 处置说明                                           │
│ XXXXXXX                                            │
├─────────────────────────────────────────────────────┤
│ [现场照片] [人车照片] [处置前] [处置中] [处置后]    │
│ [📷]      [📷]      [📷]    [📷]    [📷]          │
└─────────────────────────────────────────────────────┘
```

#### 2.2.7 应急维修处置信息页面（基于image13截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 判定说明                     │ 判定人                │
│ XXXXX                       │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 判定时间                                           │
│ 2025-07-12 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 要求完成时间                                        │
│ 2025-07-13 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 🔶 处置信息                                          │
├─────────────────────────────────────────────────────┤
│ 处置方式                     │ 处置人                │
│ 应急维修                     │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 联系方式                                           │
│ 13146887654                                        │
├─────────────────────────────────────────────────────┤
│ 提交时间                                           │
│ 2025-07-15 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 处置说明                                           │
│ XXXXXXX                                            │
├─────────────────────────────────────────────────────┤
│ [现场照片] [人车照片] [维修前] [维修中] [维修后]    │
│ [📷]      [📷]      [📷]    [📷]    [📷]          │
├─────────────────────────────────────────────────────┤
│ 🔶 复核信息                                          │
├─────────────────────────────────────────────────────┤
│ 复核结果                     │ 复核人                │
│ 通过                        │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 复核时间                                           │
│ 2025-07-15 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 复核意见                                           │
│ 通过                                               │
├─────────────────────────────────────────────────────┤
│ 🔶 归档信息                                          │
├─────────────────────────────────────────────────────┤
│ 归档时间                                           │
│ 2025-07-12 12:30                                   │
└─────────────────────────────────────────────────────┘
```

#### 2.2.8 病害复核操作页面（基于image14截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 处置说明                                           │
│ XXXXXXX                                            │
├─────────────────────────────────────────────────────┤
│ [现场照片] [人车照片] [处置前] [处置中] [处置后]    │
│ [📷]      [📷]      [📷]    [📷]    [📷]          │
├─────────────────────────────────────────────────────┤
│ 🔶 复核信息                                          │
├─────────────────────────────────────────────────────┤
│ 复核结果                     │ 复核人                │
│ [通过 不通过▼]               │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 复核时间                                           │
│ 2025-07-15 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 复核意见                                           │
│ [_____________________________________________]    │
├─────────────────────────────────────────────────────┤
│                                [保存] [✓ 提交]      │
└─────────────────────────────────────────────────────┘
```

#### 2.2.9 批量复核操作弹窗（基于image15截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ ℹ️ 批量复核处置                                ✕    │
├─────────────────────────────────────────────────────┤
│ 复核结果 *                                         │
│ [通过▼]                                            │
├─────────────────────────────────────────────────────┤
│ 复核意见 *                                         │
│ [_____________________________________________]    │
├─────────────────────────────────────────────────────┤
│ 复核人                       │ 复核时间              │
│ [系统自动获取]                │ [系统自动获取]        │
├─────────────────────────────────────────────────────┤
│                        [保存] [✓ 提交]             │
└─────────────────────────────────────────────────────┘
```

#### 2.2.10 病害归档完成页面（基于image16截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ [现场照片] [人车照片] [处置前] [处置中] [处置后]    │
│ [📷]      [📷]      [📷]    [📷]    [📷]          │
├─────────────────────────────────────────────────────┤
│ 🔶 复核信息                                          │
├─────────────────────────────────────────────────────┤
│ 复核结果                     │ 复核人                │
│ 通过                        │ 张三                  │
├─────────────────────────────────────────────────────┤
│ 复核时间                                           │
│ 2025-07-12 12:30                                   │
├─────────────────────────────────────────────────────┤
│ 复核意见                                           │
│ 通过                                               │
├─────────────────────────────────────────────────────┤
│ 🔶 归档信息                                          │
├─────────────────────────────────────────────────────┤
│ 归档时间                                           │
│ 2025-07-12 12:30                                   │
├─────────────────────────────────────────────────────┤
│                                        [← 返回]     │
└─────────────────────────────────────────────────────┘
```

#### 2.2.11 新增病害信息页面（基于image17截图）

**页面路径**：`/inspection/diseases/create`
**数据来源**：表单数据提交到 `POST /api/inspection/diseases`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 / 检测工程 / 病害及处置        │
├─────────────────────────────────────────────────────┤
│ 🔶 病害管理处置流程                                  │
│ [●]――[○]――[○]――[○]――[○]                          │
│ 上报   判定   处置   复核   归档                     │
├─────────────────────────────────────────────────────┤
│ ℹ️ 病害基本信息                                      │
├─────────────────────────────────────────────────────┤
│ 巡检人员             │ 巡检单位                     │
│ 张三                 │ 长沙市政养护单位              │
├─────────────────────────────────────────────────────┤
│ 上报属性             │ 上报时间                     │
│ 日常巡检             │ 2025-07-12 10:30:25         │
├─────────────────────────────────────────────────────┤
│ 所属桥梁/隧道        │ 病害部位                     │
│ 橘子洲大桥           │ 桥面板                       │
├─────────────────────────────────────────────────────┤
│ 病害类型             │ 病害数量                     │
│ 裂缝                 │ 3处                          │
├─────────────────────────────────────────────────────┤
│ 病害位置                                           │
│ 承载桁+200m，距止测所约53米，共3处桥面的裂缝          │
├─────────────────────────────────────────────────────┤
│ 病害描述                                           │
│ 桥面板出现3处的裂缝，最大宽度30.3mm，长度1.2m，可能影响结构耐久性，建议及时处理，由后勤党委委员会综合指挥水利院。│
├─────────────────────────────────────────────────────┤
│ 病害照片                                           │
│ [📷] [📷] [📷] [📷]                                │
└─────────────────────────────────────────────────────┘
```

#### 2.2.12 新增病害详情页面（基于image18截图）

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ ℹ️ 新增病害信息                                ✕    │
├─────────────────────────────────────────────────────┤
│ 桥梁/隧道名称 *              │ 上报属性 *             │
│ [选择桥梁▼]                 │ [上级交办▼]            │
├─────────────────────────────────────────────────────┤
│ 病害部位 *                  │ 病害类型 *             │
│ [照明设施▼]                 │ [选择类型▼]            │
├─────────────────────────────────────────────────────┤
│ 病害数量 *                  │ 病害位置 *             │
│ 1                          │ xxxx位置               │
├─────────────────────────────────────────────────────┤
│ 病害描述 *                                         │
│ [_____________________________________________]    │
├─────────────────────────────────────────────────────┤
│ 整改期限                    │ 交办领导               │
│ [请选择▼]                   │ [_________________]    │
├─────────────────────────────────────────────────────┤
│ 附件                                               │
│ [点击上传▼]                                        │
├─────────────────────────────────────────────────────┤
│                        [保存] [✓ 提交]             │
└─────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **级联选择器**：桥梁选择后联动加载病害部位
- **文件上传**：支持附件上传
- **表单验证**：必填字段验证

### 2.3 巡检统计分析模块

#### 2.3.1 桥梁巡检统计页面（基于image19截图）

**页面路径**：`/inspection/statistics`
**数据来源**：`GET /api/inspection/statistics`

**页面结构**：
```
┌─────────────────────────────────────────────────────┐
│ 智慧桥隧管理平台  首页 > 巡检中心 > 巡检统计          │
├─────────────────────────────────────────────────────┤
│ 【桥梁统计】【隧道统计】                            │
├─────────────────────────────────────────────────────┤
│ [桥梁名称▼] [时间范围▼] [区域▼] [项目部▼]          │
├─────────────────────────────────────────────────────┤
│ 应巡次数（养护公司）  实巡次数（养护公司）  巡检完成率（养护公司）  桥梁中心巡检次 │
│      1,234               912                74%              517       │
├─────────────────────────────────────────────────────┤
│ 病害总量    未处理病害    处理中病害    已处理病害      │
│    512         40           60          412        │
├─────────────────────────────────────────────────────┤
│ 巡检趋势分析                        各区巡检完成情况    │
│ [折线图显示月度巡检趋势]              [柱状图显示完成率] │
├─────────────────────────────────────────────────────┤
│ 最近巡检记录                        病害类型分布       │
│ 序号│桥梁名称│巡检日期│巡检人员│联系方式│发现问题│操作    [饼图显示类型分布] │
│  1 │橘子洲大桥│2025-07-21│张三│13148394812│0│详情     │
│  2 │三汊矶大桥│2025-07-20│李四│15729302846│3│详情     桥梁病害数量TOP10    │
│  3 │橘子洲大桥│2025-07-21│张三│13148394812│0│详情     [横向条形图]        │
└─────────────────────────────────────────────────────┘
```

#### 2.3.2 隧道巡检统计页面（基于image20截图）

**页面结构**：类似桥梁统计，但数据源和展示内容针对隧道巡检数据。

**UI功能需求**：
- **数据卡片**：关键指标数据展示
- **图表组件**：使用ECharts展示趋势分析、完成情况、类型分布等
- **数据表格**：最近巡检记录列表
- **筛选器**：支持多维度数据筛选

#### 2.3.3 综合统计仪表板页面（基于image21截图）

![综合统计仪表板](./file_jpg/image21.jpg)

**页面功能**：巡检数据的综合统计仪表板，集成多个统计维度于一页

**页面结构分析**：
```
┌─────────────────────────────────────────────────────────┐
│ 最近巡检记录                        病害类型分布         │
│ ┌─────────────────────────────┐    ┌─────────────────┐ │
│ │序号│桥梁名称│巡检日期│巡检人员│    │    [饼图]       │ │
│ │ 1 │橘子洲大桥│2025-07-21│张三│    │   病害: 13     │ │
│ │ 2 │三汊矶大桥│2025-07-20│李四│    │   下沉: 37     │ │
│ │ 3 │橘子洲大桥│2025-07-21│张三│    │   开裂: 49     │ │
│ │...│...      │...      │... │    │                 │ │
│ └─────────────────────────────┘    └─────────────────┘ │
│                                                         │
│ 桥梁病害数量TOP10                                       │
│ ┌─────────────────────────────────────────────────────┐ │
│ │桥梁A ████████████████████████████████████████ 97    │ │
│ │桥梁B ████████████████████████████████████ 83        │ │
│ │桥梁C ████████████████████████████████ 79            │ │
│ │桥梁D ██████████████████████████ 75                  │ │
│ │桥梁E ███████████████████████ 63                     │ │
│ │桥梁F ████████████████ 52                           │ │
│ │桥梁G ██████████ 31                                 │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

**UI功能需求**：
- **最近巡检记录表格**：
  - 显示最新的巡检记录列表
  - 包含序号、桥梁名称、巡检日期、巡检人员、联系方式、发现问题数量
  - 提供详情查看操作
  - 支持分页展示
- **病害类型分布饼图**：
  - 展示不同病害类型的数量分布
  - 使用不同颜色区分病害类型（病害、下沉、开裂等）
  - 显示具体数量和占比信息
- **桥梁病害数量TOP10柱状图**：
  - 横向条形图展示各桥梁的病害数量排名
  - 按病害数量从高到低排序
  - 条形图显示具体的数值标签

**数据接口需求**：
- **最近巡检记录**：`GET /api/inspection/recent-records`
- **病害类型统计**：`GET /api/inspection/statistics/damage-type-summary`
- **桥梁病害排行**：`GET /api/inspection/statistics/bridge-damage-ranking`

**技术实现要点**：
- **响应式布局**：使用CSS Grid实现三个区域的响应式排列
- **图表组件**：基于ECharts实现饼图和横向条形图
- **数据表格**：支持分页、排序和详情跳转功能
- **数据刷新**：支持定时刷新和手动刷新统计数据

## 3. 数据流和页面联动关系

### 3.1 巡检记录模块数据流

1. **主列表 → 日志视图**：
   - 数据流：巡检记录列表 → 点击日志 → 加载该桥梁月度巡检日历数据
   - 接口调用：`GET /api/inspection/logs/:bridgeId?month=YYYY-MM`

2. **日志视图 → 详情弹窗**：
   - 数据流：日历视图 → 点击日期 → 显示当日巡检记录 → 点击记录 → 加载详细信息
   - 接口调用：`GET /api/inspection/detail/:logId`

3. **主列表 → 报告列表**：
   - 数据流：巡检记录列表 → 点击报告 → 加载该桥梁所有报告列表
   - 接口调用：`GET /api/inspection/reports/:bridgeId`

### 3.2 病害处置模块数据流

1. **列表 → 详情页面**：
   - 数据流：病害列表 → 点击查看/判定/复核 → 根据病害状态显示不同操作界面
   - 接口调用：`GET /api/inspection/diseases/:id`

2. **判定流程**：
   - 数据流：判定中状态 → 选择判定类型 → 保存判定结果 → 状态流转到对应下一环节
   - 接口调用：`PUT /api/inspection/diseases/:id/judge`

3. **复核流程**：
   - 数据流：验收中状态 → 填写复核意见 → 提交复核结果 → 状态流转到已归档
   - 接口调用：`PUT /api/inspection/diseases/:id/review`

### 3.3 关键数据联动

1. **桥梁选择联动**：
   - 桥梁/隧道TAB切换 → 重新加载对应类型数据
   - 桥梁名称筛选 → 联动加载该桥梁相关选项（巡检单位、人员等）

2. **状态流转联动**：
   - 病害状态变更 → 操作按钮动态显示
   - 判定结果 → 处置方式联动（日常养护/应急维修/范围外）

3. **统计数据联动**：
   - 筛选条件变化 → 图表数据实时更新
   - 时间范围选择 → 趋势图数据重新计算

## 4. 技术实现要求

### 4.1 关键组件设计

#### 4.1.1 流程步骤组件
**功能需求**：显示病害处置流程的当前阶段，支持不同状态的视觉效果。
**适用场景**：病害详情页面的流程展示。

#### 4.1.2 日历组件
**功能需求**：支持月份切换，不同类型巡检的颜色标识，点击交互。
**适用场景**：巡检日志查看。

#### 4.1.3 图片预览组件
**功能需求**：支持多图片展示、预览、放大功能。
**适用场景**：巡检图片、病害图片、处置图片展示。

#### 4.1.4 地图组件
**功能需求**：显示位置信息，支持标记点展示。
**适用场景**：巡检位置、病害位置展示。

### 4.2 API接口规范

**巡检记录接口**：
- 获取巡检记录列表：`GET /api/inspection/records?type=bridge&page=1&size=10`
- 获取巡检日志：`GET /api/inspection/logs/:bridgeId?month=YYYY-MM`
- 获取巡检详情：`GET /api/inspection/detail/:logId`
- 获取巡检报告：`GET /api/inspection/reports/:bridgeId`

**病害处置接口**：
- 获取病害列表：`GET /api/inspection/diseases?type=bridge&status=all`
- 获取病害详情：`GET /api/inspection/diseases/:id`
- 新增病害：`POST /api/inspection/diseases`
- 病害判定：`PUT /api/inspection/diseases/:id/judge`
- 病害复核：`PUT /api/inspection/diseases/:id/review`
- 批量复核：`PUT /api/inspection/diseases/batch-review`

**统计分析接口**：
- 获取统计数据：`GET /api/inspection/statistics?type=bridge&timeRange=month`

### 4.3 状态管理设计

**状态结构**：
- 巡检记录列表：当前页面的巡检数据
- 病害列表数据：当前页面的病害数据
- 当前详情数据：正在查看的详细信息
- 筛选条件：各页面的搜索筛选状态
- 用户权限：操作权限配置

## 5. 开发工作量评估

### 5.1 页面开发工作量

**复杂页面（1.5天/个）**：
- 巡检记录主列表：1.5天
- 病害处置主列表：1.5天
- 病害详情流程页面：1.5天
- 统计分析页面：1.5天

**中等页面（1天/个）**：
- 各种详情弹窗：8个 × 1天 = 8天
- 操作弹窗页面：4个 × 1天 = 4天

**简单页面（0.5天/个）**：
- 信息展示页面：8个 × 0.5天 = 4天

**页面开发小计**：20天

### 5.2 组件开发工作量

- **流程步骤组件**：2天
- **日历组件**：3天
- **图片预览组件**：2天
- **地图组件**：2天
- **图表组件集成**：2天

**组件开发小计**：11天

### 5.3 联调测试工作量

- **接口联调**：8天
- **功能测试**：5天
- **Bug修复**：3天

**联调测试小计**：16天

### 5.4 总工作量评估

**总计**：47天

**建议团队配置**：
- **前端开发**：2-3人
- **开发周期**：6-7周

## 6. 注意事项和建议

### 6.1 开发注意事项

1. **权限控制**：不同角色用户的功能权限要严格控制
2. **状态流转**：病害处置流程的状态变更要保证数据一致性
3. **图片处理**：大量图片的加载优化和压缩处理
4. **日历组件**：月份数据的缓存和按需加载
5. **统计图表**：大数据量的图表渲染性能优化

### 6.2 用户体验建议

1. **加载状态**：列表和图表数据加载时的骨架屏效果
2. **操作反馈**：重要操作的确认提示和结果反馈
3. **数据刷新**：关键数据的自动刷新和手动刷新
4. **响应式设计**：移动端的适配和交互优化

### 6.3 维护性建议

1. **组件复用**：相似功能页面的组件抽象和复用
2. **状态码统一**：病害状态、巡检类型等枚举值的统一管理
3. **接口错误处理**：统一的接口异常处理和降级方案
4. **数据mock**：开发阶段的测试数据模拟

这份设计文档详细描述了巡检中心模块的所有页面功能和技术实现要求，每个页面都基于对应的截图进行了结构设计，并说明了数据来源、组件需求和交互逻辑，为前端开发提供了完整的指导。
